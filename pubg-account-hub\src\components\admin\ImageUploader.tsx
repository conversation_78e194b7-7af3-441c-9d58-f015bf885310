import React, { useState, useEffect, useRef, useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Upload, X, RefreshCw, Check, Loader2 } from "lucide-react";
import { uploadImage } from "@/services/imgbb";
import ReactCrop, { centerCrop, makeAspectCrop, Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import { DEFAULT_HERO_IMAGE } from "@/services/firestore";

// Function to generate a blob from an image with a crop applied
function getCroppedImg(
  image: HTMLImageElement,
  crop: PixelCrop,
  fileName: string = 'cropped.jpg',
  qualityFactor: number = 0.98
): Promise<Blob> {
  const canvas = document.createElement('canvas');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  
  // Set to the actual pixel dimensions of the crop for high resolution
  canvas.width = crop.width * scaleX;
  canvas.height = crop.height * scaleY;
  
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('No 2d context');
  }

  // Use high quality rendering
  ctx.imageSmoothingQuality = 'high';
  ctx.imageSmoothingEnabled = true;

  ctx.drawImage(
    image,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width * scaleX,
    crop.height * scaleY
  );

  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'));
          return;
        }
        resolve(blob);
      },
      'image/jpeg',
      qualityFactor
    );
  });
}

// Image Uploader Component
interface ImageUploaderProps {
  imageUrl?: string; // Old prop (keeping for backward compatibility)
  currentImage?: string; // New prop name
  onChange?: (e: any) => void; // Old handler (keeping for backward compatibility)
  onImageChanged?: (url: string) => void; // New handler
  label?: string;
  fieldName?: string;
  placeholder?: string;
  placeholderText?: string; // New prop name
  aspectRatio?: number;
  className?: string;
  buttonLabels?: {
    apply?: string;
    upload?: string;
    delete?: string;
    cancel?: string;
    crop?: string;
    uploading?: string;
    labelUrl?: string;
  };
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ 
  imageUrl = "", 
  currentImage = "",
  onChange,
  onImageChanged,
  label = "صورة",
  fieldName = "image",
  placeholder = "أدخل رابط الصورة",
  placeholderText,
  aspectRatio = 16 / 9,
  className = "",
  buttonLabels = {}
}) => {
  const { toast } = useToast();
  const { t } = useTranslation('common');
  
  // Create a unique ID for the file input to avoid conflicts when multiple instances are used
  const inputId = useRef(`image-upload-${Math.random().toString(36).substr(2, 9)}`);
  // Use currentImage if provided, fall back to imageUrl for backward compatibility
  const [previewUrl, setPreviewUrl] = useState(currentImage || imageUrl);
  const [tempUrl, setTempUrl] = useState(currentImage || imageUrl); // New state for URL input
  const [isUploading, setIsUploading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [imgSrc, setImgSrc] = useState<string | null>(null);
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const [isValidImage, setIsValidImage] = useState(true);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const [isTestingUrl, setIsTestingUrl] = useState(false);
  
  // Default button labels
  const defaultLabels = {
    apply: t('profile.apply_url') || "تطبيق الرابط",
    upload: t('profile.upload_photo') || "رفع صورة",
    delete: t('common.delete') || "حذف الصورة",
    cancel: t('common.cancel') || "إلغاء",
    crop: t('common.upload_image') || "رفع الصورة",
    uploading: t('common.uploading') || "جاري الرفع...",
    labelUrl: t('profile.photo_url') || "رابط الصورة"
  };
  
  // Merge default labels with provided ones
  const labels = { ...defaultLabels, ...buttonLabels };
  
  useEffect(() => {
    // Update preview URL when either prop changes
    setPreviewUrl(currentImage || imageUrl);
    setTempUrl(currentImage || imageUrl);
  }, [imageUrl, currentImage]);

  const onSelectFile = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: t('common.error') || "خطأ",
        description: t('common.select_valid_image') || "يرجى اختيار ملف صورة صالح",
        variant: "destructive",
      });
      return;
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: t('common.error') || "خطأ",
        description: t('common.image_size_limit') || "حجم الصورة يجب أن يكون أقل من 10 ميجابايت",
        variant: "destructive",
      });
      return;
    }
    
    const reader = new FileReader();
    reader.addEventListener('load', () => {
      setImgSrc(reader.result?.toString() || '');
      setIsDialogOpen(true);
    });
    reader.readAsDataURL(file);
  }, [toast, t]);

  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    
    // Initialize with centered crop
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        aspectRatio,
        width,
        height
      ),
      width,
      height
    );
    
    setCrop(crop);
  }, [aspectRatio]);

  const handleCropComplete = useCallback(async () => {
    if (!imgRef.current || !completedCrop) return;
    
    try {
      setIsUploading(true);
      
      // Always use high quality
      const qualityFactor = 0.98;
      
      const croppedBlob = await getCroppedImg(
        imgRef.current, 
        completedCrop, 
        `cropped_image.jpg`,
        qualityFactor
      );
      
      const file = new File([croppedBlob], `cropped_image.jpg`, { 
        type: "image/jpeg" 
      });
      
      const imageUrl = await uploadImage(file);
      
      // Support both old and new callback styles
      if (onChange) {
        // Create a mock event to pass to the onChange handler (old style)
        const mockEvent = { 
          target: { 
            name: fieldName, 
            value: imageUrl 
          } 
        };
        onChange(mockEvent);
      }
      
      // Call the new callback if provided
      if (onImageChanged) {
        onImageChanged(imageUrl);
      }
      
      setPreviewUrl(imageUrl);
      setIsDialogOpen(false);
      
      toast({
        title: t('common.success') || "تم بنجاح",
        description: t('common.image_uploaded') || "تم رفع الصورة بنجاح",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: t('common.error') || "خطأ",
        description: t('common.image_upload_failed') || "فشل في رفع الصورة",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      setImgSrc(null);
    }
  }, [completedCrop, fieldName, onChange, onImageChanged, toast, t]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempUrl(e.target.value);
    setIsValidImage(true); // Reset validation
  };

  const handleApplyUrl = async () => {
    try {
      setIsTestingUrl(true);
      // If the temp URL is empty and field is heroImage, set it to default
      if (!tempUrl && fieldName === "heroImage") {
        setPreviewUrl(DEFAULT_HERO_IMAGE);
        
        // Support both old and new callback styles
        if (onChange) {
          onChange({ target: { name: fieldName, value: DEFAULT_HERO_IMAGE } });
        }
        if (onImageChanged) {
          onImageChanged(DEFAULT_HERO_IMAGE);
        }
        
        setIsTestingUrl(false);
        return;
      }
      
      // If URL is empty for other fields, just leave it empty
      if (!tempUrl) {
        setPreviewUrl("");
        if (onChange) {
          onChange({ target: { name: fieldName, value: "" } });
        }
        if (onImageChanged) {
          onImageChanged("");
        }
        setIsTestingUrl(false);
        return;
      }
      
      // Test if the URL is a valid image before applying
      const isValid = await testImageUrl(tempUrl);
      
      if (isValid) {
        setPreviewUrl(tempUrl);
        
        // Call the appropriate callbacks
        if (onChange) {
          onChange({ target: { name: fieldName, value: tempUrl } });
        }
        if (onImageChanged) {
          onImageChanged(tempUrl);
        }
        
        setIsValidImage(true);
      } else {
        setIsValidImage(false);
        toast({
          title: t('common.error') || "خطأ",
          description: t('common.image_invalid_url') || "رابط الصورة غير صالح",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error validating image URL:", error);
      setIsValidImage(false);
      toast({
        title: t('common.error') || "خطأ",
        description: t('common.image_validation_failed') || "فشل التحقق من رابط الصورة",
        variant: "destructive",
      });
    } finally {
      setIsTestingUrl(false);
    }
  };

  // Helper function to test if an image URL is valid
  const testImageUrl = (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    });
  };

  const handleImageError = () => {
    setIsValidImage(false);
    setPreviewUrl("");
    setTempUrl("");
  };

  // This function will trigger the file input click
  const handleUploadButtonClick = () => {
    const fileInput = document.getElementById(inputId.current);
    if (fileInput) {
      fileInput.click();
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="grid grid-cols-1 gap-3">
        {/* Image Preview Section */}
        {previewUrl && isValidImage && (
          <div className="relative overflow-hidden rounded-lg border border-pubg-gray/30 bg-pubg-black/30">
            <img
              src={previewUrl}
              alt={label}
              className="w-full h-auto object-cover rounded-lg"
              style={{ aspectRatio: `${aspectRatio}` }}
              onError={handleImageError}
              onLoad={(e) => {
                const img = e.target as HTMLImageElement;
                setImageDimensions({
                  width: img.naturalWidth,
                  height: img.naturalHeight
                });
                setIsValidImage(true);
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end justify-between p-3">
              <div className="text-white text-xs">
                {isValidImage && imageDimensions.width > 0 && (
                  <span>{imageDimensions.width} × {imageDimensions.height}px</span>
                )}
              </div>
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="h-8 w-8 rounded-full bg-red-500/80 hover:bg-red-500 shadow-md"
                onClick={() => {
                  const mockEvent = { target: { name: fieldName, value: "" } };
                  if (onChange) onChange(mockEvent);
                  if (onImageChanged) onImageChanged("");
                  setPreviewUrl("");
                  setTempUrl("");
                }}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">{labels.delete}</span>
              </Button>
            </div>
          </div>
        )}
        
        {/* Image URL Input and Upload Controls */}
        <div className="space-y-2">
          <Label htmlFor={fieldName} className="text-sm text-gray-200">{labels.labelUrl}</Label>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                id={fieldName}
                name={fieldName}
                value={tempUrl}
                onChange={handleInputChange}
                placeholder={placeholderText || placeholder}
                className="admin-input pr-4"
                dir="ltr"
              />
            </div>
            
            <Button
              type="button"
              variant="outline"
              size="icon"
              className="flex-shrink-0 h-10 w-10 border-pubg-gray/30 hover:border-green-500 hover:bg-green-500/10 hover:text-green-500"
              onClick={handleApplyUrl}
              disabled={isTestingUrl}
            >
              {isTestingUrl ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Check className="h-4 w-4" />
              )}
              <span className="sr-only">{labels.apply}</span>
            </Button>
            
            <Button
              type="button"
              variant="orange"
              size="icon"
              className="flex-shrink-0 h-10 w-10"
              onClick={handleUploadButtonClick}
            >
              <Upload className="h-4 w-4" />
              <span className="sr-only">{labels.upload}</span>
            </Button>
          </div>
          
          {!isValidImage && (
            <p className="text-red-500 text-xs pt-1">
              {t('common.invalid_image_url') || "رابط الصورة غير صالح. يرجى التحقق من الرابط أو استخدام خيار رفع صورة."}
            </p>
          )}
        </div>
          
        {/* Hidden File Input */}
        <input
          id={inputId.current}
          type="file"
          accept="image/*"
          onChange={onSelectFile}
          className="hidden"
        />
        
        {/* Image Cropping Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto p-4 md:p-6 glass-card">
            <DialogHeader>
              <DialogTitle className="flex items-center text-xl font-bold">
                <div className="w-1 h-6 bg-pubg-orange ml-3 rounded-full"></div>
                {t('common.edit_image') || "تعديل الصورة"}
              </DialogTitle>
              <DialogDescription className="text-sm text-muted-foreground pt-1">
                {t('common.edit_image_desc') || "قم بتعديل حجم وموضع الصورة ثم اضغط على رفع الصورة"}
              </DialogDescription>
            </DialogHeader>
            
            <div className="mt-2 mb-4 overflow-hidden rounded-lg border border-pubg-gray/30 bg-pubg-gray/5">
              {imgSrc && (
                <ReactCrop
                  crop={crop}
                  onChange={(_, percentCrop) => setCrop(percentCrop)}
                  onComplete={(c) => setCompletedCrop(c)}
                  aspect={aspectRatio}
                  className="max-h-[60vh] mx-auto"
                >
                  <img
                    ref={imgRef}
                    src={imgSrc}
                    alt={t('common.crop_image') || "صورة للقص"}
                    className="max-w-full max-h-[60vh] mx-auto object-contain"
                    onLoad={onImageLoad}
                  />
                </ReactCrop>
              )}
            </div>
            
            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 sm:gap-3">
              <Button
                type="button"
                variant="ghost"
                onClick={() => {
                  setIsDialogOpen(false);
                  setImgSrc(null);
                }}
                className="w-full sm:w-auto"
              >
                {labels.cancel}
              </Button>
              <Button
                type="button"
                variant="orange"
                disabled={isUploading || !completedCrop?.width || !completedCrop?.height}
                onClick={handleCropComplete}
                className="w-full sm:w-auto"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    {labels.uploading}
                  </>
                ) : (
                  <>
                    <Check className="ml-2 h-4 w-4" />
                    {labels.crop}
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default ImageUploader; 