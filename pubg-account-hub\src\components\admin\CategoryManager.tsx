
import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Pencil, Trash2, Save, X } from "lucide-react";
import { CategoryModel, getCategories, addCategory, updateCategory, deleteCategory } from "@/services/firestore";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const CategoryManager = () => {
  const { toast } = useToast();
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingCategory, setEditingCategory] = useState<CategoryModel | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [formData, setFormData] = useState<Partial<CategoryModel>>({
    name: "",
    type: "account",
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const categoriesData = await getCategories();
        setCategories(categoriesData);
        setIsLoading(false);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load categories",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSelectChange = (value: string) => {
    setFormData({ ...formData, type: value as "account" | "blog" | "uc" });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (editingCategory) {
        // Update existing category
        await updateCategory(editingCategory.id!, formData);
        
        // Update local state
        setCategories(
          categories.map((category) =>
            category.id === editingCategory.id
              ? { ...category, ...formData }
              : category
          )
        );
        
        toast({
          title: "Success",
          description: "Category updated successfully",
        });
        
        setEditingCategory(null);
      } else {
        // Add new category
        const id = await addCategory(formData as Omit<CategoryModel, "id" | "createdAt" | "updatedAt">);
        
        // Update local state
        setCategories([...categories, { ...formData, id } as CategoryModel]);
        
        toast({
          title: "Success",
          description: "Category added successfully",
        });
        
        setIsAdding(false);
      }
      
      // Reset form
      setFormData({
        name: "",
        type: "account",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save category",
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  const handleEdit = (category: CategoryModel) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      type: category.type,
    });
    setIsAdding(false);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this category?")) {
      setIsLoading(true);
      
      try {
        await deleteCategory(id);
        
        // Update local state
        setCategories(categories.filter((category) => category.id !== id));
        
        toast({
          title: "Success",
          description: "Category deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete category",
          variant: "destructive",
        });
      }
      
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setEditingCategory(null);
    setIsAdding(false);
    setFormData({
      name: "",
      type: "account",
    });
  };

  const translateType = (type: string) => {
    switch (type) {
      case "account": return "حسابات";
      case "blog": return "مدونة";
      case "uc": return "باقات UC";
      default: return type;
    }
  };

  if (isLoading) {
    return <div className="text-center py-8">Loading categories...</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white">إدارة التصنيفات</h2>
        {!isAdding && !editingCategory && (
          <Button
            onClick={() => setIsAdding(true)}
            className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
          >
            <Plus className="ml-2 h-4 w-4" />
            إضافة تصنيف جديد
          </Button>
        )}
      </div>

      {(isAdding || editingCategory) && (
        <div className="glass-card rounded-xl p-6 mb-8">
          <h3 className="text-lg font-bold text-white mb-4">
            {editingCategory ? "تعديل التصنيف" : "إضافة تصنيف جديد"}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">اسم التصنيف</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="type">نوع التصنيف</Label>
                <Select 
                  value={formData.type}
                  onValueChange={handleSelectChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع التصنيف" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="account">حسابات</SelectItem>
                    <SelectItem value="blog">مدونة</SelectItem>
                    <SelectItem value="uc">باقات UC</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex justify-end space-s-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="ml-2"
              >
                <X className="ml-2 h-4 w-4" />
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-pubg-blue text-white hover:bg-pubg-blue/90"
              >
                <Save className="ml-2 h-4 w-4" />
                {editingCategory ? "تحديث" : "إضافة"}
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Categories List */}
      <div className="space-y-4">
        {categories.length > 0 ? (
          categories.map((category) => (
            <div
              key={category.id}
              className="glass-card rounded-lg p-4 flex items-center justify-between"
            >
              <div>
                <h3 className="text-white font-medium">{category.name}</h3>
                <p className="text-sm text-muted-foreground">
                  نوع: {translateType(category.type)}
                </p>
              </div>
              
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEdit(category)}
                  className="text-pubg-blue hover:text-pubg-blue hover:bg-pubg-blue/10"
                >
                  <Pencil size={18} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDelete(category.id!)}
                  className="text-red-500 hover:text-red-500 hover:bg-red-500/10"
                >
                  <Trash2 size={18} />
                </Button>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 border border-dashed border-border rounded-lg">
            <p className="text-muted-foreground">
              لا توجد تصنيفات متاحة. أضف تصنيفًا جديدًا للبدء.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryManager;
