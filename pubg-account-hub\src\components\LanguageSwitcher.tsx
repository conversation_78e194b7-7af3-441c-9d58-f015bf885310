import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { ChevronDown, Check } from 'lucide-react';

const FLAGS = {
  en: "/images/flags/us.png",
  ar: "/images/flags/eg.png"
};

// Create a global variable to track language change state
export const isChangingLanguageGlobal = {
  value: false
};

// Expose to window for access in HTML
if (typeof window !== 'undefined') {
  (window as any).isChangingLanguageGlobal = isChangingLanguageGlobal;
}

export const LanguageSwitcher = () => {
  const { t, i18n } = useTranslation('common');
  const currentLanguage = i18n.language;
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.lang-switcher-container')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const changeLanguage = (lng: string) => {
    // Don't do anything if already changing language or selecting current language
    if (isChangingLanguage || lng === currentLanguage) return;
    
    // Set loading state
    setIsChangingLanguage(true);
    isChangingLanguageGlobal.value = true;
    setIsAnimating(true);
    
    // Set language direction
    document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
    
    // Save the language preference for both systems
    localStorage.setItem('i18nextLng', lng);
    localStorage.setItem('language', lng);
    
    // Set language loader text
    const loaderTextElement = document.getElementById('language-loader-text');
    if (loaderTextElement) {
      loaderTextElement.textContent = t('language.changing');
    }
    
    // Change language in i18n
    i18n.changeLanguage(lng);
    
    // Show the loader immediately
    const languageLoader = document.getElementById('language-loader');
    if (languageLoader) {
      languageLoader.classList.add('active');
    }
    
    // Add a small delay before refreshing to ensure preferences are saved
    setTimeout(() => {
      // Redirect with query param to trigger loader
      const currentPath = window.location.pathname + window.location.hash;
      window.location.href = `${currentPath}?lang_change=true`;
    }, 800);
  };

  // Animation variants
  const switcherVariants: Variants = {
    initial: { scale: 1 },
    hover: { scale: 1.05, transition: { duration: 0.2 } },
    tap: { scale: 0.97, transition: { duration: 0.1 } },
    active: { backgroundColor: "rgba(255, 255, 255, 0.1)", boxShadow: "0 0 15px rgba(255, 255, 255, 0.15)" }
  };

  const iconVariants: Variants = {
    initial: { rotate: 0 },
    active: { rotate: 180, transition: { duration: 0.3 } },
  };

  const menuVariants: Variants = {
    hidden: { 
      opacity: 0, 
      y: -10, 
      scale: 0.95, 
      transformOrigin: "top center"
    },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1, 
      transformOrigin: "top center",
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 20,
        duration: 0.2
      }
    },
    exit: { 
      opacity: 0, 
      y: -10, 
      scale: 0.95,
      transition: { duration: 0.15 } 
    }
  };

  const flagVariants: Variants = {
    initial: { scale: 1, rotate: 0 },
    hover: { scale: 1.1, transition: { duration: 0.2 } },
    changing: { 
      rotateY: [0, 180, 360],
      scale: [1, 1.2, 1],
      transition: { 
        rotateY: { duration: 0.8, ease: "easeInOut" },
        scale: { duration: 0.8, ease: "easeInOut" }
      }
    },
    menuOpen: {
      rotate: [0, -5, 5, -3, 3, 0],
      scale: [1, 1.15, 1.1],
      transition: {
        rotate: { duration: 0.5, ease: "easeInOut" },
        scale: { duration: 0.4, ease: "easeOut" }
      }
    }
  };

  const itemVariants: Variants = {
    initial: { opacity: 0, x: -20 },
    animate: (custom: number) => ({
      opacity: 1,
      x: 0,
      transition: { 
        delay: custom * 0.1,
        duration: 0.3,
        ease: "easeOut"
      }
    }),
    exit: { opacity: 0, x: 20, transition: { duration: 0.2 } },
    hover: { 
      backgroundColor: "rgba(255, 255, 255, 0.08)",
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.98, transition: { duration: 0.1 } }
  };

  return (
    <div className="lang-switcher-container relative z-50">
      {/* Main Switcher Button */}
      <motion.div
        className="neo-lang-switcher flex items-center gap-2 px-2.5 py-2 rounded-full cursor-pointer"
        onClick={() => !isChangingLanguage && setIsOpen(!isOpen)}
        variants={switcherVariants}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
        animate={isOpen ? "active" : isAnimating ? { scale: [1, 1.1, 1] } : "initial"}
        transition={{ duration: 0.5 }}
      >
        {/* Flag */}
        <motion.div 
          className="relative flag-container"
          variants={flagVariants}
          initial="initial"
          whileHover={!isAnimating && !isOpen ? "hover" : ""}
          animate={isAnimating ? "changing" : isOpen ? "menuOpen" : "initial"}
          onAnimationComplete={() => setIsAnimating(false)}
        >
          <motion.div className="absolute inset-0 rounded-md glow-effect" />
          <img 
            src={FLAGS[currentLanguage as keyof typeof FLAGS]} 
            alt={currentLanguage}
            className="h-5 w-5 rounded-sm object-cover border border-white/10 shadow-lg" 
          />
        </motion.div>

        {/* Text */}
        <span className="text-sm font-medium text-white hidden sm:inline">
            {currentLanguage === 'ar' ? 'العربية' : 'English'}
          </span>

        {/* Chevron */}
        <motion.div
          variants={iconVariants}
          animate={isOpen ? "active" : "initial"}
        >
          <ChevronDown className="h-4 w-4 text-white/70" />
        </motion.div>
      </motion.div>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute mt-2 right-0 w-32 neo-lang-menu rounded-xl overflow-hidden"
            variants={menuVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{ 
              pointerEvents: isOpen ? "auto" : "none" 
            }}
          >
            <div className="neo-lang-menu-header py-2 px-2 border-b border-white/5">
              <div className="text-xs font-medium text-white/70 text-center">
                {t('language.switch')}
              </div>
            </div>

            <div className="py-1">
              {/* English Option */}
              <motion.div
                className={`neo-lang-item flex items-center gap-2 px-3 py-2 ${
                  currentLanguage === 'en' ? 'neo-lang-active' : ''
                } ${isChangingLanguage ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
                onClick={() => !isChangingLanguage && changeLanguage('en')}
                variants={itemVariants}
                custom={0}
                initial="initial"
                animate="animate"
                exit="exit"
                whileHover={!isChangingLanguage ? "hover" : undefined}
                whileTap={!isChangingLanguage ? "tap" : undefined}
              >
                <div className="relative flag-wrapper">
                  <div className="lang-flag-glow absolute inset-0" />
                  <img 
                    src={FLAGS.en} 
                    alt="English"
                    className="h-5 w-5 rounded-sm object-cover border border-white/10 shadow-lg"
                  />
                  {currentLanguage === 'en' && (
                    <motion.div 
                      className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full flex items-center justify-center border border-white/20"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 500 }}
                    >
                      <Check className="h-2 w-2 text-white" />
                    </motion.div>
                  )}
                </div>
                <span className="text-sm font-medium text-white">English</span>
              </motion.div>

              {/* Arabic Option */}
              <motion.div
                className={`neo-lang-item flex items-center gap-2 px-3 py-2 ${
                  currentLanguage === 'ar' ? 'neo-lang-active' : ''
                } ${isChangingLanguage ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
                onClick={() => !isChangingLanguage && changeLanguage('ar')}
                variants={itemVariants}
                custom={1}
                initial="initial"
                animate="animate"
                exit="exit"
                whileHover={!isChangingLanguage ? "hover" : undefined}
                whileTap={!isChangingLanguage ? "tap" : undefined}
              >
                <div className="relative flag-wrapper">
                  <div className="lang-flag-glow absolute inset-0" />
                  <img 
                    src={FLAGS.ar} 
                    alt="العربية" 
                    className="h-5 w-5 rounded-sm object-cover border border-white/10 shadow-lg"
                  />
                  {currentLanguage === 'ar' && (
                    <motion.div 
                      className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full flex items-center justify-center border border-white/20"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 500 }}
                    >
                      <Check className="h-2 w-2 text-white" />
                    </motion.div>
                  )}
                </div>
                <span className="text-sm font-medium text-white">العربية</span>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSwitcher; 