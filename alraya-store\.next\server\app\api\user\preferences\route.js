(()=>{var e={};e.id=828,e.ids=[828],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26392:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>_,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>y});var n={};s.r(n),s.d(n,{GET:()=>a,POST:()=>d,PUT:()=>p});var t=s(96559),c=s(48088),o=s(37719),u=s(32190);let i={"user-id":{id:"1",user_id:"user-id",preferred_currency_code:"USD",display_currency_code:"USD",enable_currency_conversion:!0,conversion_confirmation_required:!0,preferences:{},created_at:new Date().toISOString(),updated_at:new Date().toISOString()}};async function a(e){try{let{searchParams:r}=new URL(e.url),s=r.get("userId");if(!s)return u.NextResponse.json({success:!1,error:"User ID is required"},{status:400});let n=i[s];if(!n)return u.NextResponse.json({success:!0,preferences:{userId:s,preferredCurrency:"USD",displayCurrency:"USD",enableCurrencyConversion:!0,conversionConfirmationRequired:!0},isDefault:!0});let t={id:n.id,userId:n.user_id,preferredCurrency:n.preferred_currency_code,displayCurrency:n.display_currency_code,enableCurrencyConversion:n.enable_currency_conversion,conversionConfirmationRequired:n.conversion_confirmation_required,preferences:n.preferences||{},createdAt:new Date(n.created_at),updatedAt:new Date(n.updated_at)};return u.NextResponse.json({success:!0,preferences:t})}catch(e){return console.error("Unexpected error in GET /api/user/preferences:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function d(e){try{let{userId:r,preferences:s}=await e.json();if(!r)return u.NextResponse.json({success:!1,error:"User ID is required"},{status:400});if(!s)return u.NextResponse.json({success:!1,error:"Preferences data is required"},{status:400});let n=["USD","SDG","EGP","EUR","GBP","SAR","AED"];if(s.preferredCurrency&&!n.includes(s.preferredCurrency))return u.NextResponse.json({success:!1,error:"Invalid preferred currency"},{status:400});if(s.displayCurrency&&!n.includes(s.displayCurrency))return u.NextResponse.json({success:!1,error:"Invalid display currency"},{status:400});s.preferredCurrency,s.displayCurrency,s.enableCurrencyConversion,s.conversionConfirmationRequired,s.preferences;let t=i[r],c={id:t?.id||"1",user_id:r,preferred_currency_code:s.preferredCurrency||"USD",display_currency_code:s.displayCurrency||"USD",enable_currency_conversion:!1!==s.enableCurrencyConversion,conversion_confirmation_required:!1!==s.conversionConfirmationRequired,preferences:s.preferences||{},created_at:t?.created_at||new Date().toISOString(),updated_at:new Date().toISOString()};i[r]=c;let o={id:c.id,userId:c.user_id,preferredCurrency:c.preferred_currency_code,displayCurrency:c.display_currency_code,enableCurrencyConversion:c.enable_currency_conversion,conversionConfirmationRequired:c.conversion_confirmation_required,preferences:c.preferences||{},createdAt:new Date(c.created_at),updatedAt:new Date(c.updated_at)};return u.NextResponse.json({success:!0,message:"User preferences saved successfully",preferences:o})}catch(e){return console.error("Unexpected error in POST /api/user/preferences:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e){try{let{userId:r}=await e.json();if(!r)return u.NextResponse.json({success:!1,error:"User ID is required"},{status:400});let{data:s,error:n}=await supabase.from("user_preferences").upsert({user_id:r,preferred_currency_code:"USD",display_currency_code:"USD",enable_currency_conversion:!0,conversion_confirmation_required:!0,preferences:{}}).select().single();if(n)return console.error("Error resetting user preferences:",n),u.NextResponse.json({success:!1,error:"Failed to reset user preferences"},{status:500});let t={id:s.id,userId:s.user_id,preferredCurrency:s.preferred_currency_code,displayCurrency:s.display_currency_code,enableCurrencyConversion:s.enable_currency_conversion,conversionConfirmationRequired:s.conversion_confirmation_required,preferences:s.preferences||{},createdAt:new Date(s.created_at),updatedAt:new Date(s.updated_at)};return u.NextResponse.json({success:!0,message:"User preferences reset to defaults",preferences:t})}catch(e){return console.error("Unexpected error in PUT /api/user/preferences:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let f=new t.AppRouteRouteModule({definition:{kind:c.RouteKind.APP_ROUTE,page:"/api/user/preferences/route",pathname:"/api/user/preferences",filename:"route",bundlePath:"app/api/user/preferences/route"},resolvedPagePath:"D:\\VS-projects\\try\\alraya-store\\app\\api\\user\\preferences\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:l,workUnitAsyncStorage:y,serverHooks:_}=f;function v(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:y})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),n=r.X(0,[447,580],()=>s(26392));module.exports=n})();