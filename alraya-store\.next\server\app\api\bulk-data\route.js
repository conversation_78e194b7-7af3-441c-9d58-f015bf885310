(()=>{var e={};e.id=855,e.ids=[855],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4209:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>A,serverHooks:()=>k,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>D});var o={};r.r(o),r.d(o,{GET:()=>f});var i=r(96559),n=r(48088),a=r(37719),s=r(32190);let l=[{title:"FREE FIRE",subtitle:"شحن جواهر",gradient:"from-blue-500 via-blue-600 to-purple-600",icon:"\uD83D\uDD25"},{title:"شحن عبر الـ ID",subtitle:"شحن فوري",gradient:"from-orange-500 via-red-500 to-red-600",icon:"⚡"},{title:"شحن جواهر بدوم",subtitle:"FREE FIRE",gradient:"from-green-500 via-emerald-500 to-blue-600",icon:"\uD83D\uDC8E"},{title:"شحن جواهر فوري",subtitle:"FREE FIRE",gradient:"from-purple-500 via-pink-500 to-pink-600",icon:"\uD83C\uDFAE"},{title:"PUBG Mobile",subtitle:"شحن UC",gradient:"from-yellow-500 via-orange-500 to-red-600",icon:"\uD83C\uDFAF"},{title:"Call of Duty",subtitle:"شحن CP",gradient:"from-gray-600 via-gray-700 to-black",icon:"\uD83D\uDD2B"}],c=[{id:1,title:"يمكنك الآن شحن جميع الألعاب",subtitle:"من خلال تطبيق واحد فقط",buttonText:"ابدأ اللعب الآن",gradient:"from-pink-100 via-yellow-200 to-yellow-400",image:"https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg"},{id:2,title:"أسرع خدمة شحن في السودان",subtitle:"شحن فوري خلال دقائق معدودة",buttonText:"اشحن الآن",gradient:"from-blue-100 via-purple-200 to-purple-400",image:"https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg"},{id:3,title:"عروض حصرية وخصومات مميزة",subtitle:"وفر أكثر مع عروضنا الخاصة",buttonText:"اكتشف العروض",gradient:"from-green-100 via-emerald-200 to-emerald-400",image:"https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg"}],d={id:"pubg-mobile-uc",name:"شحن يوسي PUBG Mobile",nameEnglish:"PUBG Mobile UC Top-up",description:"شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً بأفضل الأسعار",descriptionEnglish:"Instant UC top-up for PUBG Mobile - Get your UC instantly at the best prices",category:"ألعاب الموبايل",basePrice:25,estimatedTime:"فوري",productType:"digital",processingType:"instant",digitalConfig:{autoDeliver:!0,codeType:"game_code",deliveryInstructions:"سيتم إرسال الكود إلى حسابك فوراً بعد الدفع. استخدم الكود في اللعبة لشحن UC.",expiryDays:30},fields:[{id:"pubg-player-id",type:"text",name:"player_id",label:"معرف اللاعب",placeholder:"أدخل معرف اللاعب...",required:!0,validation:{minLength:8,maxLength:12,pattern:"^[0-9]+$"},sortOrder:0,isActive:!0},{id:"pubg-server",type:"dropdown",name:"server",label:"الخادم",placeholder:"اختر الخادم...",required:!0,options:["الشرق الأوسط","أوروبا","آسيا","أمريكا الشمالية","أمريكا الجنوبية"],sortOrder:1,isActive:!0}],packages:[{id:"pubg-uc-60",name:"60 يوسي",amount:"60 UC",price:5,originalPrice:6,discount:17,popular:!1,isActive:!0,sortOrder:0,digitalCodes:[]},{id:"pubg-uc-325",name:"325 يوسي",amount:"325 UC",price:25,originalPrice:30,discount:17,popular:!0,isActive:!0,sortOrder:1,digitalCodes:[]},{id:"pubg-uc-660",name:"660 يوسي",amount:"660 UC",price:50,originalPrice:60,discount:17,popular:!1,isActive:!0,sortOrder:2,digitalCodes:[]},{id:"pubg-uc-1800",name:"1800 يوسي",amount:"1800 UC",price:120,originalPrice:150,discount:20,popular:!1,isActive:!0,sortOrder:3,digitalCodes:[]}],features:["\uD83D\uDE80 تسليم فوري للأكواد","\uD83D\uDCAF ضمان الجودة والأمان","\uD83D\uDD12 معاملات آمنة ومشفرة","\uD83D\uDCF1 يعمل على جميع الأجهزة","\uD83C\uDFAE دعم فني متخصص","\uD83D\uDCB3 طرق دفع متعددة"],tags:["pubg","mobile","uc","شحن","ألعاب"],isActive:!0,isFeatured:!0,createdAt:new Date,updatedAt:new Date},p={id:"free-fire-diamonds",name:"شحن جواهر Free Fire",nameEnglish:"Free Fire Diamonds Top-up",description:"شحن فوري لجواهر Free Fire - احصل على الجواهر بأسرع وقت وأفضل الأسعار",descriptionEnglish:"Instant Free Fire Diamonds top-up - Get your diamonds quickly at the best prices",category:"ألعاب الموبايل",basePrice:10,estimatedTime:"فوري",productType:"digital",processingType:"instant",digitalConfig:{autoDeliver:!0,codeType:"game_code",deliveryInstructions:"سيتم شحن الجواهر مباشرة إلى حسابك في اللعبة خلال دقائق.",expiryDays:7},fields:[{id:"ff-player-id",type:"number",name:"player_id",label:"معرف اللاعب",labelEnglish:"Player ID",placeholder:"أدخل معرف اللاعب...",required:!0,validation:{min:1e8,max:0x2540be3ff},sortOrder:0,isActive:!0}],packages:[{id:"ff-diamonds-100",name:"100 جوهرة",amount:"100 \uD83D\uDC8E",price:10,originalPrice:12,discount:17,popular:!1,isActive:!0,sortOrder:0,digitalCodes:[]},{id:"ff-diamonds-520",name:"520 جوهرة",amount:"520 \uD83D\uDC8E",price:50,originalPrice:60,discount:17,popular:!0,isActive:!0,sortOrder:1,digitalCodes:[]},{id:"ff-diamonds-1080",name:"1080 جوهرة",amount:"1080 \uD83D\uDC8E",price:100,originalPrice:120,discount:17,popular:!1,isActive:!0,sortOrder:2,digitalCodes:[]}],features:["\uD83D\uDE80 شحن فوري ومباشر","\uD83D\uDC8E جواهر أصلية 100%","\uD83D\uDD12 آمن ومضمون","\uD83D\uDCF1 لجميع الأجهزة","\uD83C\uDFAE دعم فني 24/7"],tags:["free fire","diamonds","جواهر","شحن","ألعاب"],isActive:!0,isFeatured:!0,createdAt:new Date,updatedAt:new Date},u=[d,p,{id:"google-play-gift-card",name:"بطاقة هدايا Google Play",nameEnglish:"Google Play Gift Card",description:"بطاقات هدايا Google Play الرقمية - استخدمها لشراء التطبيقات والألعاب والمحتوى الرقمي",descriptionEnglish:"Digital Google Play Gift Cards - Use them to buy apps, games, and digital content",category:"بطاقات الهدايا",basePrice:10,estimatedTime:"فوري",productType:"digital",processingType:"instant",digitalConfig:{autoDeliver:!0,codeType:"coupon",deliveryInstructions:"استخدم الكود في متجر Google Play لإضافة الرصيد إلى حسابك.",expiryDays:365},fields:[{id:"gp-email",type:"email",name:"email",label:"البريد الإلكتروني",labelEnglish:"Email Address",placeholder:"أدخل بريدك الإلكتروني...",required:!0,sortOrder:0,isActive:!0}],packages:[{id:"gp-usd-10",name:"$10 USD",nameArabic:"10 دولار",amount:"$10 USD",price:10,popular:!1,isActive:!0,sortOrder:0,digitalCodes:[]},{id:"gp-usd-25",name:"$25 USD",nameArabic:"25 دولار",amount:"$25 USD",price:25,popular:!0,isActive:!0,sortOrder:1,digitalCodes:[]},{id:"gp-usd-50",name:"$50 USD",nameArabic:"50 دولار",amount:"$50 USD",price:50,popular:!1,isActive:!0,sortOrder:2,digitalCodes:[]}],features:["\uD83C\uDF81 بطاقة هدايا رقمية","\uD83D\uDE80 تسليم فوري","\uD83C\uDF0D صالحة عالمياً","\uD83D\uDCF1 لجميع أجهزة Android","\uD83D\uDD12 آمنة ومضمونة"],tags:["google play","gift card","بطاقة هدايا","تطبيقات"],isActive:!0,isFeatured:!1,createdAt:new Date,updatedAt:new Date},{id:"tiktok-coins",name:"شحن عملات TikTok",nameEnglish:"TikTok Coins Top-up",description:"شحن فوري لعملات TikTok - ادعم المبدعين المفضلين لديك واحصل على المزيد من المزايا",descriptionEnglish:"Instant TikTok Coins top-up - Support your favorite creators and get more features",category:"وسائل التواصل",basePrice:5,estimatedTime:"فوري",productType:"digital",processingType:"instant",digitalConfig:{autoDeliver:!0,codeType:"gift_code",deliveryInstructions:"سيتم إضافة العملات إلى حسابك في TikTok فوراً بعد الدفع.",expiryDays:30},fields:[{id:"tiktok-username",type:"text",name:"username",label:"اسم المستخدم في TikTok",placeholder:"أدخل اسم المستخدم...",required:!0,validation:{minLength:3,maxLength:30},sortOrder:0,isActive:!0}],packages:[{id:"tiktok-coins-100",name:"100 عملة",amount:"100 Coins",price:5,originalPrice:6,discount:17,popular:!1,isActive:!0,sortOrder:0,digitalCodes:[]},{id:"tiktok-coins-500",name:"500 عملة",amount:"500 Coins",price:20,originalPrice:25,discount:20,popular:!0,isActive:!0,sortOrder:1,digitalCodes:[]},{id:"tiktok-coins-1000",name:"1000 عملة",amount:"1000 Coins",price:35,originalPrice:45,discount:22,popular:!1,isActive:!0,sortOrder:2,digitalCodes:[]}],features:["\uD83D\uDE80 شحن فوري ومباشر","\uD83D\uDCB0 عملات أصلية 100%","\uD83D\uDD12 آمن ومضمون","\uD83D\uDCF1 لجميع الأجهزة","\uD83C\uDF81 ادعم المبدعين المفضلين"],tags:["tiktok","coins","عملات","شحن","وسائل التواصل"],isActive:!0,isFeatured:!0,createdAt:new Date,updatedAt:new Date}];async function m(e){try{return console.log("Skipping localStorage initialization on server-side"),console.log("Server-side: returning default templates"),y(u,e)}catch(t){return console.error("Error loading products:",t),y(u,e)}}function y(e,t){let r=Array.isArray(e)?e.filter(e=>e&&"object"==typeof e):[];return t?r.filter(e=>{if(!e.name||!e.category||t.category&&e.category!==t.category||t.productType&&e.productType!==t.productType||t.processingType&&e.processingType!==t.processingType||void 0!==t.isActive&&e.isActive!==t.isActive||void 0!==t.isFeatured&&e.isFeatured!==t.isFeatured)return!1;if(t.search){let r=t.search.toLowerCase(),o=e.name&&e.name.toLowerCase().includes(r),i=e.description&&e.description.toLowerCase().includes(r);if(!o&&!i)return!1}return!0}):r}r(12977);let g={SDG:{code:"SDG",name:"الجنيه السوداني",symbol:"ج.س.",isRTL:!0},EGP:{code:"EGP",name:"الجنيه المصري",symbol:"ج.م.",isRTL:!0},USD:{code:"USD",name:"الدولار الأمريكي",symbol:"$",isRTL:!1}};async function f(e){try{console.log("\uD83D\uDE80 Loading bulk application data...");let{searchParams:t}=new URL(e.url),r=t.get("userId"),[o,i,n,a,d]=await Promise.all([C(),b(),v(),r?h(r):Promise.resolve(void 0),r?E(r):Promise.resolve(void 0)]),p={success:!0,data:{products:o,currencies:i,exchangeRates:n,gameCards:l,slides:c,walletData:a,userPreferences:d},timestamp:new Date,version:"1.0.0"};return console.log("✅ Bulk data loaded successfully:",{products:o.length,currencies:i.length,gameCards:l.length,slides:c.length,hasWalletData:!!a,hasUserPreferences:!!d}),s.NextResponse.json(p)}catch(t){console.error("❌ Failed to load bulk data:",t);let e={success:!1,data:{},timestamp:new Date,error:t instanceof Error?t.message:"Unknown error occurred"};return s.NextResponse.json(e,{status:500})}}async function C(){try{return await m({isActive:!0})}catch(e){return console.error("Failed to load products:",e),[]}}async function b(){try{return Object.entries(g).map(([e,t])=>({code:e,name:t.name,symbol:t.symbol,isRTL:t.isRTL}))}catch(e){return console.error("Failed to load currencies:",e),[]}}async function v(){try{return{USD:1,SDG:450,EGP:30.8}}catch(e){return console.error("Failed to load exchange rates:",e),{USD:1}}}async function h(e){try{return}catch(e){console.error("Failed to load wallet data:",e);return}}async function E(e){try{return{preferredCurrency:"USD",theme:"dark",language:"ar",notifications:!0,autoRefresh:!1}}catch(e){console.error("Failed to load user preferences:",e);return}}let A=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/bulk-data/route",pathname:"/api/bulk-data",filename:"route",bundlePath:"app/api/bulk-data/route"},resolvedPagePath:"D:\\VS-projects\\try\\alraya-store\\app\\api\\bulk-data\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:R,workUnitAsyncStorage:D,serverHooks:k}=A;function S(){return(0,a.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:D})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12907:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},12977:(e,t,r)=>{"use strict";r.d(t,{formatCurrency:()=>i,getCurrencyDisplayInfo:()=>n});var o=r(12907);let i=(0,o.registerClientReference)(function(){throw Error("Attempted to call formatCurrency() from the server but formatCurrency is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","formatCurrency"),n=(0,o.registerClientReference)(function(){throw Error("Attempted to call getCurrencyDisplayInfo() from the server but getCurrencyDisplayInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","getCurrencyDisplayInfo");(0,o.registerClientReference)(function(){throw Error("Attempted to call parseCurrencyAmount() from the server but parseCurrencyAmount is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","parseCurrencyAmount"),(0,o.registerClientReference)(function(){throw Error("Attempted to call convertCurrencyAmount() from the server but convertCurrencyAmount is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","convertCurrencyAmount"),(0,o.registerClientReference)(function(){throw Error("Attempted to call calculateConversionWithFees() from the server but calculateConversionWithFees is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","calculateConversionWithFees"),(0,o.registerClientReference)(function(){throw Error("Attempted to call calculateCrossRate() from the server but calculateCrossRate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","calculateCrossRate"),(0,o.registerClientReference)(function(){throw Error("Attempted to call validateCurrencyCode() from the server but validateCurrencyCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","validateCurrencyCode"),(0,o.registerClientReference)(function(){throw Error("Attempted to call validateExchangeRate() from the server but validateExchangeRate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","validateExchangeRate"),(0,o.registerClientReference)(function(){throw Error("Attempted to call validateConversionAmount() from the server but validateConversionAmount is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","validateConversionAmount"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getDefaultCurrency() from the server but getDefaultCurrency is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","getDefaultCurrency"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getEnabledCurrencies() from the server but getEnabledCurrencies is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","getEnabledCurrencies"),(0,o.registerClientReference)(function(){throw Error("Attempted to call CURRENCY_DECIMAL_PLACES() from the server but CURRENCY_DECIMAL_PLACES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","CURRENCY_DECIMAL_PLACES"),(0,o.registerClientReference)(function(){throw Error("Attempted to call RTL_CURRENCIES() from the server but RTL_CURRENCIES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","RTL_CURRENCIES"),(0,o.registerClientReference)(function(){throw Error("Attempted to call MAJOR_CURRENCIES() from the server but MAJOR_CURRENCIES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","MAJOR_CURRENCIES"),(0,o.registerClientReference)(function(){throw Error("Attempted to call MIDDLE_EAST_CURRENCIES() from the server but MIDDLE_EAST_CURRENCIES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts","MIDDLE_EAST_CURRENCIES")},21083:(e,t,r)=>{"use strict";function o(e,t,r){let o;let{showSymbol:n=!0,decimalPlaces:a,locale:s="en-US"}=r||{};o="string"==typeof t?i(t):t;let l=a??2,c=e.toLocaleString(s,{minimumFractionDigits:l,maximumFractionDigits:l});if(!n)return c;let d=o.symbol||o.code;return o.isRTL?`${c} ${d}`:`${d}${c}`}function i(e){return({USD:{code:"USD",name:"US Dollar",symbol:"$",arabicName:"الدولار الأمريكي",isRTL:!1},SDG:{code:"SDG",name:"Sudanese Pound",symbol:"ج.س.",arabicName:"الجنيه السوداني",isRTL:!0},EGP:{code:"EGP",name:"Egyptian Pound",symbol:"ج.م.",arabicName:"الجنيه المصري",isRTL:!0},SAR:{code:"SAR",name:"Saudi Riyal",symbol:"ر.س",arabicName:"الريال السعودي",isRTL:!0},AED:{code:"AED",name:"UAE Dirham",symbol:"د.إ",arabicName:"الدرهم الإماراتي",isRTL:!0},EUR:{code:"EUR",name:"Euro",symbol:"€",arabicName:"اليورو",isRTL:!1},GBP:{code:"GBP",name:"British Pound",symbol:"\xa3",arabicName:"الجنيه الإسترليني",isRTL:!1}})[e]||{code:e,name:e,symbol:e,isRTL:!1}}r.d(t,{formatCurrency:()=>o,getCurrencyDisplayInfo:()=>i})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48285:(e,t,r)=>{Promise.resolve().then(r.bind(r,12977))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83197:(e,t,r)=>{Promise.resolve().then(r.bind(r,21083))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,580],()=>r(4209));module.exports=o})();