import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/contexts/AuthContext";
import { useCart } from "@/contexts/CartContext";
import { useConfig, useDiscordLink, usePageVisibility } from "@/contexts/ConfigContext";
import { ShoppingCart, Menu, X, User, LogOut, MessageCircle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { currentUser, isAdmin, logout } = useAuth();
  const { itemCount, isLoading } = useCart();
  const discordLink = useDiscordLink();
  const { isPageVisible, siteName } = useConfig();
  const location = useLocation();
  const { t } = useTranslation('common');

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
    // Log user info for debugging
    if (currentUser) {
      console.log("User data in Navbar:", { 
        photoURL: currentUser.photoURL,
        displayName: currentUser.displayName,
        email: currentUser.email,
        uid: currentUser.uid,
        metadata: currentUser.metadata,
        providerData: currentUser.providerData && currentUser.providerData[0] ? currentUser.providerData[0] : 'none'
      });
    }
  }, [location, currentUser]);

  // Navigation links with page IDs
  const navLinks = [
    { id: "home", name: t('navbar.home'), path: "/" },
    { id: "accounts", name: t('navbar.accounts'), path: "/accounts" },
    { id: "uc-store", name: t('navbar.uc-store'), path: "/uc-store" },
    { id: "mods", name: "Hacks - هاكات", path: "/mods" },
    { id: "about", name: t('navbar.about'), path: "/about" },
    { id: "blog", name: t('navbar.blog'), path: "/blog" },
    { id: "contact", name: t('navbar.contact'), path: "/contact" },
  ];

  // Filter links based on visibility
  const visibleNavLinks = navLinks.filter(link => isPageVisible(link.id));

  // Parse the site name to handle any formatting
  const parseSiteName = () => {
    // If the site name contains a space, assume the second part might be styled differently
    const parts = siteName.split(' ');
    if (parts.length > 1) {
      return (
        <>
          {parts[0]}<span className="text-pubg-orange">{parts.slice(1).join(' ')}</span>
        </>
      );
    }
    return siteName; // Return as is if no space
  };

  return (
    <header
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-card/90 backdrop-blur-md shadow-md py-2"
          : "bg-transparent py-4"
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        {/* Logo */}
        <Link to="/" className="flex items-center">
          <h1 className="text-2xl font-bold text-white">{parseSiteName()}</h1>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-1 space-x-reverse">
          {visibleNavLinks.map((link) => (
            <Link
              key={link.path}
              to={link.path}
              className={`px-4 py-2 mx-1 rounded-lg text-sm font-medium transition-all duration-300 ${
                location.pathname === link.path
                  ? "text-pubg-orange"
                  : "text-white hover:text-pubg-orange"
              }`}
            >
              {link.name}
            </Link>
          ))}
        </nav>

        {/* User Actions */}
        <div className="hidden md:flex items-center gap-1">
          {/* Language Switcher */}
          <LanguageSwitcher />
          
          <Link to="/cart" className="relative ml-2">
            <Button variant="ghost" size="icon" className="text-white hover:text-pubg-orange hover:bg-transparent">
              <ShoppingCart size={20} />
              {!isLoading && itemCount > 0 && (
                <span className="absolute -top-1 -left-1 flex items-center justify-center w-5 h-5 bg-pubg-orange text-pubg-dark text-xs font-bold rounded-full">
                  {itemCount}
                </span>
              )}
            </Button>
          </Link>
          
          {discordLink && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <motion.div
                    className="relative inline-block"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-white relative overflow-hidden group bg-[#5865F2]/10 hover:bg-[#5865F2]/20 ml-1"
                      onClick={() => window.open(discordLink, '_blank')}
                    >
                      {/* Discord logo with pulse effect */}
                      <div className="relative z-10">
                        <svg 
                          width="20" 
                          height="20" 
                          viewBox="0 0 71 55" 
                          fill="none" 
                          xmlns="http://www.w3.org/2000/svg"
                          className="text-[#5865F2] group-hover:text-white transition-colors duration-300"
                        >
                          <path d="M60.1045 4.8978C55.5792 2.8214 50.7265 1.2916 45.6527 0.41542C45.5603 0.39851 45.468 0.440769 45.4204 0.525289C44.7963 1.6353 44.105 3.0834 43.6209 4.2216C38.1637 3.4046 32.7345 3.4046 27.3892 4.2216C26.905 3.0581 26.1886 1.6353 25.5617 0.525289C25.5141 0.443589 25.4218 0.40133 25.3294 0.41542C20.2584 1.2888 15.4057 2.8186 10.8776 4.8978C10.8384 4.9147 10.8048 4.9429 10.7825 4.9795C1.57795 18.7309 -0.943561 32.1443 0.293408 45.3914C0.299005 45.4562 0.335386 45.5182 0.385761 45.5576C6.45866 50.0174 12.3413 52.7249 18.1147 54.5195C18.2071 54.5477 18.305 54.5139 18.3638 54.4378C19.7295 52.5728 20.9469 50.6063 21.9907 48.5383C22.0523 48.4172 21.9935 48.2735 21.8676 48.2256C19.9366 47.4931 18.0979 46.6 16.3292 45.5858C16.1893 45.5041 16.1781 45.304 16.3068 45.2082C16.679 44.9293 17.0513 44.6391 17.4067 44.3461C17.471 44.2926 17.5606 44.2813 17.6362 44.3151C29.2558 49.6202 41.8354 49.6202 53.3179 44.3151C53.3935 44.2785 53.4831 44.2898 53.5502 44.3433C53.9057 44.6363 54.2779 44.9293 54.6529 45.2082C54.7816 45.304 54.7732 45.5041 54.6333 45.5858C52.8646 46.6197 51.0259 47.4931 49.0921 48.2228C48.9662 48.2707 48.9102 48.4172 48.9718 48.5383C50.038 50.6034 51.2554 52.5699 52.5959 54.435C52.6519 54.5139 52.7526 54.5477 52.845 54.5195C58.6464 52.7249 64.529 50.0174 70.6019 45.5576C70.6551 45.5182 70.6887 45.459 70.6943 45.3942C72.1747 30.0791 68.2147 16.7757 60.1968 4.9823C60.1772 4.9429 60.1437 4.9147 60.1045 4.8978ZM23.7259 37.3253C20.2276 37.3253 17.3451 34.1136 17.3451 30.1693C17.3451 26.225 20.1717 23.0133 23.7259 23.0133C27.308 23.0133 30.1626 26.2532 30.1066 30.1693C30.1066 34.1136 27.28 37.3253 23.7259 37.3253ZM47.3178 37.3253C43.8196 37.3253 40.9371 34.1136 40.9371 30.1693C40.9371 26.225 43.7636 23.0133 47.3178 23.0133C50.9 23.0133 53.7545 26.2532 53.6986 30.1693C53.6986 34.1136 50.9 37.3253 47.3178 37.3253Z" 
                          fill="currentColor"
                          />
                        </svg>
                        <motion.div 
                          className="absolute -inset-1 bg-[#5865F2] opacity-30 rounded-full blur"
                          animate={{ 
                            scale: [1, 1.2, 1],
                            opacity: [0.3, 0.2, 0.3]
                          }}
                          transition={{ 
                            duration: 2, 
                            repeat: Infinity,
                            repeatType: "reverse" 
                          }}
                        />
                      </div>
                      <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#5865F2] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-200 origin-left" />
                    </Button>
                    <motion.span 
                      className="absolute top-0 right-0 flex h-3 w-3"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5, type: "spring" }}
                    >
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-[#43b581] opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-3 w-3 bg-[#43b581]"></span>
                    </motion.span>
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-discord-blue text-white border-none shadow-lg">
                  <p className="flex items-center">
                    <span className="inline-block w-2 h-2 rounded-full bg-[#43b581] mr-2"></span>
                    {t('navbar.join_discord')}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {currentUser ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white hover:text-pubg-orange hover:bg-transparent">
                  {currentUser && currentUser.photoURL ? (
                    <Avatar className="h-9 w-9 border-2 border-pubg-orange" key={`desktop-${currentUser.photoURL}-${Date.now()}`}>
                      <AvatarImage 
                        src={currentUser.photoURL} 
                        alt={currentUser.displayName || t('navbar.profile')} 
                        className="object-cover"
                        style={{ width: '100%', height: '100%' }}
                      />
                      <AvatarFallback className="bg-pubg-blue text-white">
                        {currentUser.displayName ? currentUser.displayName.charAt(0).toUpperCase() : "U"}
                      </AvatarFallback>
                    </Avatar>
                  ) : (
                    <User size={20} />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>{t('navbar.profile')}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link to="/profile" className="cursor-pointer">
                    {t('navbar.profile')}
                  </Link>
                </DropdownMenuItem>
                {isAdmin && (
                  <DropdownMenuItem asChild>
                    <Link to="/admin" className="cursor-pointer">
                      {t('navbar.admin')}
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-500 focus:text-red-500"
                  onClick={() => logout()}
                >
                  <LogOut className="ml-2 h-4 w-4" />
                  {t('navbar.logout')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button asChild variant="default" className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90">
              <Link to="/login">{t('navbar.login')}</Link>
            </Button>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden flex items-center">
          <LanguageSwitcher />
          <Link to="/cart" className="relative ml-4">
            <Button variant="ghost" size="icon" className="text-white hover:text-pubg-orange hover:bg-transparent">
              <ShoppingCart size={20} />
              {!isLoading && itemCount > 0 && (
                <span className="absolute -top-1 -left-1 flex items-center justify-center w-5 h-5 bg-pubg-orange text-pubg-dark text-xs font-bold rounded-full">
                  {itemCount}
                </span>
              )}
            </Button>
          </Link>
          
          {discordLink && (
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:text-[#5865F2] hover:bg-transparent ml-2"
              onClick={() => window.open(discordLink, '_blank')}
            >
              <svg 
                width="20" 
                height="20" 
                viewBox="0 0 71 55" 
                fill="none" 
                xmlns="http://www.w3.org/2000/svg"
                className="text-[#5865F2] transition-colors duration-300"
              >
                <path d="M60.1045 4.8978C55.5792 2.8214 50.7265 1.2916 45.6527 0.41542C45.5603 0.39851 45.468 0.440769 45.4204 0.525289C44.7963 1.6353 44.105 3.0834 43.6209 4.2216C38.1637 3.4046 32.7345 3.4046 27.3892 4.2216C26.905 3.0581 26.1886 1.6353 25.5617 0.525289C25.5141 0.443589 25.4218 0.40133 25.3294 0.41542C20.2584 1.2888 15.4057 2.8186 10.8776 4.8978C10.8384 4.9147 10.8048 4.9429 10.7825 4.9795C1.57795 18.7309 -0.943561 32.1443 0.293408 45.3914C0.299005 45.4562 0.335386 45.5182 0.385761 45.5576C6.45866 50.0174 12.3413 52.7249 18.1147 54.5195C18.2071 54.5477 18.305 54.5139 18.3638 54.4378C19.7295 52.5728 20.9469 50.6063 21.9907 48.5383C22.0523 48.4172 21.9935 48.2735 21.8676 48.2256C19.9366 47.4931 18.0979 46.6 16.3292 45.5858C16.1893 45.5041 16.1781 45.304 16.3068 45.2082C16.679 44.9293 17.0513 44.6391 17.4067 44.3461C17.471 44.2926 17.5606 44.2813 17.6362 44.3151C29.2558 49.6202 41.8354 49.6202 53.3179 44.3151C53.3935 44.2785 53.4831 44.2898 53.5502 44.3433C53.9057 44.6363 54.2779 44.9293 54.6529 45.2082C54.7816 45.304 54.7732 45.5041 54.6333 45.5858C52.8646 46.6197 51.0259 47.4931 49.0921 48.2228C48.9662 48.2707 48.9102 48.4172 48.9718 48.5383C50.038 50.6034 51.2554 52.5699 52.5959 54.435C52.6519 54.5139 52.7526 54.5477 52.845 54.5195C58.6464 52.7249 64.529 50.0174 70.6019 45.5576C70.6551 45.5182 70.6887 45.459 70.6943 45.3942C72.1747 30.0791 68.2147 16.7757 60.1968 4.9823C60.1772 4.9429 60.1437 4.9147 60.1045 4.8978ZM23.7259 37.3253C20.2276 37.3253 17.3451 34.1136 17.3451 30.1693C17.3451 26.225 20.1717 23.0133 23.7259 23.0133C27.308 23.0133 30.1626 26.2532 30.1066 30.1693C30.1066 34.1136 27.28 37.3253 23.7259 37.3253ZM47.3178 37.3253C43.8196 37.3253 40.9371 34.1136 40.9371 30.1693C40.9371 26.225 43.7636 23.0133 47.3178 23.0133C50.9 23.0133 53.7545 26.2532 53.6986 30.1693C53.6986 34.1136 50.9 37.3253 47.3178 37.3253Z" 
                fill="currentColor"
                />
              </svg>
              <span className="absolute -top-1 -right-1 flex h-2 w-2">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-[#43b581] opacity-75"></span>
                <span className="relative inline-flex rounded-full h-2 w-2 bg-[#43b581]"></span>
              </span>
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="text-white hover:text-pubg-orange hover:bg-transparent"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-background/95 backdrop-blur-md border-b border-border"
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-2">
                {visibleNavLinks.map((link) => (
                  <Link
                    key={link.path}
                    to={link.path}
                    className={`px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 ${
                      location.pathname === link.path
                        ? "bg-accent text-pubg-orange"
                        : "text-white hover:bg-accent/50 hover:text-pubg-orange"
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {link.name}
                  </Link>
                ))}

                {currentUser ? (
                  <>
                    <Link
                      to="/profile"
                      className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 text-white hover:bg-accent/50 hover:text-pubg-orange"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {t('navbar.profile')}
                    </Link>
                    {isAdmin && (
                      <Link
                        to="/admin"
                        className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 text-white hover:bg-accent/50 hover:text-pubg-orange"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {t('navbar.admin')}
                      </Link>
                    )}
                    <button
                      className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 text-red-500 hover:bg-red-500/10 flex items-center"
                      onClick={() => {
                        logout();
                        setIsMenuOpen(false);
                      }}
                    >
                      <LogOut className="ml-2 h-5 w-5" />
                      {t('navbar.logout')}
                    </button>
                  </>
                ) : (
                  <Link
                    to="/login"
                    className="px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {t('navbar.login')}
                  </Link>
                )}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Navbar;
