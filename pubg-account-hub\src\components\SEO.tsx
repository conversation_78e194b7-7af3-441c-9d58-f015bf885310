import React from "react";
import { Helmet } from "react-helmet-async";
import { useConfig } from "@/contexts/ConfigContext";
import { siteMetadata } from "@/lib/metadata";

interface SEOProps {
  title?: string;
  description?: string;
  canonical?: string;
  ogImage?: string;
  ogType?: "website" | "article" | "product";
  keywords?: string[];
  children?: React.ReactNode;
}

const SEO: React.FC<SEOProps> = ({
  title,
  description,
  canonical,
  ogImage = "/images/og-default.jpg",
  ogType = "website",
  keywords,
  children,
}) => {
  const { siteName } = useConfig();

  // Generate dynamic description if not provided
  const defaultDescription = siteMetadata.description;
  const metaDescription = description || defaultDescription;

  // Dynamic site title with siteName
  const siteTitle = title ? `${title} | ${siteName}` : siteMetadata.title;

  // Combine default keywords with page-specific keywords
  const allKeywords = [...siteMetadata.keywords, ...(keywords || [])];
  const keywordsString = allKeywords.join(", ");

  const siteUrl = window.location.origin;
  const currentUrl = canonical || window.location.href;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{siteTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={keywordsString} />
      <meta name="author" content={siteMetadata.author} />
      <link rel="canonical" href={currentUrl} />

      {/* Language and Region */}
      <meta httpEquiv="content-language" content="ar-SA" />
      <meta name="geo.region" content="EG" />
      <meta name="geo.country" content="Egypt" />

      {/* Open Graph Tags */}
      <meta property="og:site_name" content={siteName} />
      <meta property="og:title" content={siteTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={`${siteUrl}${ogImage}`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:locale" content="ar_SA" />
      <meta property="og:locale:alternate" content="en_US" />

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={siteTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={`${siteUrl}${ogImage}`} />
      <meta name="twitter:site" content={siteMetadata.twitterHandle} />

      {/* Favicon - Dynamic based on site name */}
      <link rel="icon" href="/favicon.ico" />

      {/* Additional SEO Tags */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />

      {/* Mobile Optimization */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />

      {children}
    </Helmet>
  );
};

export default SEO; 