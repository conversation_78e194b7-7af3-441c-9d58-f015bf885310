import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Save, Eye, EyeOff } from "lucide-react";
import {
  PageVisibilityModel,
  getPageVisibilitySettings,
  updatePageVisibility,
  initializePageVisibilitySettings,
} from "@/services/firestore";

const PageVisibilityManager = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [pages, setPages] = useState<PageVisibilityModel[]>([]);

  useEffect(() => {
    const fetchPages = async () => {
      try {
        // Initialize default settings if needed
        await initializePageVisibilitySettings();
        
        // Get current settings
        const pagesData = await getPageVisibilitySettings();
        
        // Sort pages by order
        const sortedPages = pagesData.sort((a, b) => {
          return (a.order || 99) - (b.order || 99);
        });
        
        setPages(sortedPages);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching page visibility settings:", error);
        toast({
          title: "خطأ",
          description: "فشل في تحميل إعدادات إظهار الصفحات",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchPages();
  }, [toast]);

  const handleToggleVisibility = async (pageId: string, isVisible: boolean) => {
    try {
      // Update in Firestore
      await updatePageVisibility(pageId, isVisible);
      
      // Update local state
      setPages(prev => 
        prev.map(page => 
          page.pageId === pageId 
            ? { ...page, isVisible } 
            : page
        )
      );
      
      toast({
        title: "تم بنجاح",
        description: `تم ${isVisible ? 'إظهار' : 'إخفاء'} الصفحة بنجاح`,
        duration: 2000, // 2 seconds
      });
    } catch (error) {
      console.error("Error updating page visibility:", error);
      toast({
        title: "خطأ",
        description: "فشل في تحديث حالة إظهار الصفحة",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-pubg-orange"></div>
        <span className="mr-2 text-sm md:text-base">جاري تحميل الإعدادات...</span>
      </div>
    );
  }

  return (
    <div>
      <h2 className="admin-title">إدارة إظهار الصفحات</h2>
      
      <Card className="admin-card">
        <CardHeader className="px-3 py-4 md:p-6">
          <CardTitle className="flex items-center text-white admin-subtitle">
            <Eye className="ml-2 h-5 w-5 text-pubg-orange" />
            إظهار وإخفاء الصفحات
          </CardTitle>
          <CardDescription className="text-xs md:text-sm">
            التحكم في إظهار وإخفاء الصفحات في موقع الويب
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
          <div className="bg-muted/20 rounded-lg p-3 md:p-4 mb-4 text-xs md:text-sm">
            <p className="text-white">
              يمكنك التحكم في إظهار أو إخفاء الصفحات من القائمة. الصفحات المخفية لن تظهر في شريط التنقل ولن تكون متاحة للزوار.
            </p>
          </div>
          
          <div className="space-y-4">
            {pages.map((page) => (
              <div 
                key={page.pageId} 
                className="flex items-center justify-between bg-card p-3 md:p-4 rounded-lg border border-border"
              >
                <div className="flex items-center">
                  {page.isVisible ? (
                    <Eye className="ml-2 h-4 w-4 text-green-500" />
                  ) : (
                    <EyeOff className="ml-2 h-4 w-4 text-red-500" />
                  )}
                  <span className="text-sm md:text-base font-medium text-white">
                    {page.pageName}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    id={`visibility-${page.pageId}`}
                    checked={page.isVisible}
                    onCheckedChange={(checked) => 
                      handleToggleVisibility(page.pageId, checked)
                    }
                  />
                  <Label 
                    htmlFor={`visibility-${page.pageId}`}
                    className="text-xs md:text-sm text-muted-foreground mr-2"
                  >
                    {page.isVisible ? 'ظاهرة' : 'مخفية'}
                  </Label>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
        
        <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-border">
          <div className="text-xs md:text-sm text-muted-foreground w-full text-center">
            ملاحظة: التغييرات تطبق فورًا عند تغيير حالة أي صفحة
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default PageVisibilityManager; 