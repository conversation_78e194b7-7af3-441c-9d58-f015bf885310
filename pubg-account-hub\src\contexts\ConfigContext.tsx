import React, { createContext, useState, useContext, useEffect } from "react";
import { 
  getContactInfo, 
  ContactInfoModel, 
  getPageVisibilitySettings, 
  PageVisibilityModel,
  initializePageVisibilitySettings,
  getSettings,
  SettingsModel
} from "@/services/firestore";

interface ConfigContextType {
  telegramUsername: string;
  contactInfo: ContactInfoModel | null;
  pageVisibility: Record<string, boolean>;
  isLoading: boolean;
  isPageVisible: (pageId: string) => boolean;
  siteName: string;
  socialLinks: string[];
}

const defaultUsername = "altyb_anony"; // Default fallback username
const defaultSiteName = "PUBG STORE"; // Default site name

const ConfigContext = createContext<ConfigContextType>({
  telegramUsername: defaultUsername,
  contactInfo: null,
  pageVisibility: {},
  isLoading: true,
  isPageVisible: () => true, // By default, all pages are visible
  siteName: defaultSiteName,
  socialLinks: [],
});

export const useConfig = () => useContext(ConfigContext);

export const useTelegramUsername = () => {
  const { telegramUsername } = useContext(ConfigContext);
  return telegramUsername;
};

export const useDiscordLink = () => {
  const { contactInfo } = useContext(ConfigContext);
  return contactInfo?.discord || "";
};

export const usePageVisibility = (pageId: string): boolean => {
  const { isPageVisible } = useContext(ConfigContext);
  return isPageVisible(pageId);
};

export const ConfigProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [contactInfo, setContactInfo] = useState<ContactInfoModel | null>(null);
  const [pageVisibilityMap, setPageVisibilityMap] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [siteName, setSiteName] = useState<string>(defaultSiteName);
  const [socialLinks, setSocialLinks] = useState<string[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Load contact info
        const info = await getContactInfo();
        setContactInfo(info);

        // Initialize and load page visibility settings
        await initializePageVisibilitySettings();
        const pagesData = await getPageVisibilitySettings();
        
        // Convert to a map for easier lookup
        const visibilityMap: Record<string, boolean> = {};
        pagesData.forEach(page => {
          visibilityMap[page.pageId] = page.isVisible;
        });
        
        setPageVisibilityMap(visibilityMap);
        
        // Load site name from settings
        const settingsData = await getSettings();
        if (settingsData && settingsData.siteName) {
          setSiteName(settingsData.siteName);
        }
        
        // Extract social links from contact info
        const links: string[] = [];
        if (info?.facebook) links.push(info.facebook);
        if (info?.twitter) links.push(info.twitter);
        if (info?.instagram) links.push(info.instagram);
        setSocialLinks(links);
      } catch (error) {
        console.error("Failed to load configuration data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Extract the username from the Telegram link
  const getTelegramUsername = (): string => {
    if (!contactInfo?.telegramLink) return defaultUsername;
    
    try {
      // Extract username from URL
      const url = new URL(contactInfo.telegramLink);
      if (url.hostname === "t.me") {
        return url.pathname.replace(/^\//, ''); // Remove leading slash
      }
      
      // If we can't parse the URL or it's not a t.me link, return the default
      return defaultUsername;
    } catch (error) {
      // If the URL is invalid, check if it's just a username
      if (contactInfo.telegramLink.startsWith('@')) {
        return contactInfo.telegramLink.substring(1); // Remove @ symbol
      }
      return defaultUsername;
    }
  };

  // Function to check if a page is visible
  const isPageVisible = (pageId: string): boolean => {
    // Special case for admin and login pages - always visible
    if (pageId === 'admin' || pageId === 'login' || pageId === 'profile') {
      return true;
    }
    
    // If we don't have visibility info for this page, default to visible
    return pageVisibilityMap[pageId] !== false;
  };

  const telegramUsername = getTelegramUsername();

  const value: ConfigContextType = {
    telegramUsername,
    contactInfo,
    pageVisibility: pageVisibilityMap,
    isLoading,
    isPageVisible,
    siteName,
    socialLinks,
  };

  return (
    <ConfigContext.Provider value={value}>
      {children}
    </ConfigContext.Provider>
  );
}; 