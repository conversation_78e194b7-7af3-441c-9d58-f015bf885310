"use client"

import { cn } from "@/lib/utils"
import { Check, CreditCard, FileText, Wallet } from "lucide-react"

interface Step {
  id: number
  title: string
  description: string
  icon: React.ReactNode
}

interface ProgressStepperProps {
  currentStep: 1 | 2 | 3
  className?: string
}

export function ProgressStepper({ currentStep, className }: ProgressStepperProps) {
  const steps: Step[] = [
    {
      id: 1,
      title: "التفاصيل",
      description: "اختر مبلغ الشحن",
      icon: <Wallet className="h-5 w-5" />
    },
    {
      id: 2,
      title: "ملخص الطلب",
      description: "أدخل بياناتك",
      icon: <FileText className="h-5 w-5" />
    },
    {
      id: 3,
      title: "الدفع",
      description: "أكمل عملية الدفع",
      icon: <CreditCard className="h-5 w-5" />
    }
  ]

  return (
    <div className={cn("w-full", className)}>
      {/* Mobile Stepper - Horizontal */}
      <div className="block lg:hidden">
        <div className="flex items-center justify-between mb-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              {/* Step Circle */}
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300",
                    step.id < currentStep
                      ? "bg-gradient-to-r from-yellow-400 to-orange-500 border-yellow-400 text-slate-900"
                      : step.id === currentStep
                      ? "bg-slate-800 border-yellow-400 text-yellow-400"
                      : "bg-slate-700 border-slate-600 text-slate-400"
                  )}
                >
                  {step.id < currentStep ? (
                    <Check className="h-5 w-5" />
                  ) : (
                    step.icon
                  )}
                </div>
                <div className="mt-2 text-center">
                  <p
                    className={cn(
                      "text-xs font-medium",
                      step.id <= currentStep ? "text-yellow-400" : "text-slate-400"
                    )}
                  >
                    {step.title}
                  </p>
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "flex-1 h-0.5 mx-4 transition-all duration-300",
                    step.id < currentStep
                      ? "bg-gradient-to-r from-yellow-400 to-orange-500"
                      : "bg-slate-600"
                  )}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Desktop Stepper - Vertical */}
      <div className="hidden lg:block">
        <div className="space-y-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-start">
              {/* Step Circle */}
              <div
                className={cn(
                  "flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 flex-shrink-0",
                  step.id < currentStep
                    ? "bg-gradient-to-r from-yellow-400 to-orange-500 border-yellow-400 text-slate-900"
                    : step.id === currentStep
                    ? "bg-slate-800 border-yellow-400 text-yellow-400"
                    : "bg-slate-700 border-slate-600 text-slate-400"
                )}
              >
                {step.id < currentStep ? (
                  <Check className="h-6 w-6" />
                ) : (
                  step.icon
                )}
              </div>

              {/* Step Content */}
              <div className="mr-4 flex-1">
                <h3
                  className={cn(
                    "text-lg font-semibold",
                    step.id <= currentStep ? "text-yellow-400" : "text-slate-400"
                  )}
                >
                  {step.title}
                </h3>
                <p
                  className={cn(
                    "text-sm mt-1",
                    step.id <= currentStep ? "text-slate-300" : "text-slate-500"
                  )}
                >
                  {step.description}
                </p>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="absolute right-6 mt-12 w-0.5 h-6 bg-slate-600" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
