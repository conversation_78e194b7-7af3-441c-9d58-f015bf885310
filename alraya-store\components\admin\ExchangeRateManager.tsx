"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  DollarSign,
  Loader2,
  Save,
  History
} from "lucide-react"
import { 
  Currency, 
  ExchangeRate,
  ExchangeRateUpdateRequest,
  CurrencyDisplay
} from "@/lib/types"
import { formatCurrency } from "@/lib/utils/currency"
import { cn } from "@/lib/utils"

interface ExchangeRateManagerProps {
  className?: string
}

export function ExchangeRateManager({ className }: ExchangeRateManagerProps) {
  const [currencies, setCurrencies] = useState<CurrencyDisplay[]>([])
  const [exchangeRates, setExchangeRates] = useState<Record<Currency, number>>({})
  const [editableRates, setEditableRates] = useState<Record<Currency, string>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [baseCurrency] = useState<Currency>("USD")

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Load currencies
      const currenciesResponse = await fetch('/api/currencies?active=true')
      const currenciesData = await currenciesResponse.json()

      if (!currenciesData.success) {
        throw new Error('Failed to load currencies')
      }

      setCurrencies(currenciesData.currencies)

      // Load exchange rates
      const ratesResponse = await fetch(`/api/exchange-rates?base=${baseCurrency}`)
      const ratesData = await ratesResponse.json()

      if (!ratesData.success) {
        throw new Error('Failed to load exchange rates')
      }

      setExchangeRates(ratesData.rates)
      setLastUpdated(new Date(ratesData.timestamp))

      // Initialize editable rates
      const editableRatesInit: Record<Currency, string> = {}
      Object.entries(ratesData.rates).forEach(([currency, rate]) => {
        editableRatesInit[currency as Currency] = rate.toString()
      })
      setEditableRates(editableRatesInit)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRateChange = (currency: Currency, value: string) => {
    setEditableRates(prev => ({
      ...prev,
      [currency]: value
    }))
  }

  const validateRates = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    Object.entries(editableRates).forEach(([currency, rateStr]) => {
      const rate = parseFloat(rateStr)
      
      if (isNaN(rate) || rate <= 0) {
        errors.push(`Invalid rate for ${currency}: must be a positive number`)
      }

      if (currency === baseCurrency && rate !== 1) {
        errors.push(`Base currency ${baseCurrency} rate must be 1.0`)
      }

      if (rate > 1000000) {
        errors.push(`Rate for ${currency} seems unusually high`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  const handleUpdateRates = async () => {
    const validation = validateRates()
    if (!validation.isValid) {
      setError(validation.errors.join(', '))
      return
    }

    setIsUpdating(true)
    setError(null)
    setSuccess(null)

    try {
      const rateUpdates = Object.entries(editableRates)
        .filter(([currency]) => currency !== baseCurrency)
        .map(([currency, rateStr]) => ({
          fromCurrency: baseCurrency,
          toCurrency: currency as Currency,
          rate: parseFloat(rateStr)
        }))

      const request: ExchangeRateUpdateRequest = {
        rates: rateUpdates,
        effectiveDate: new Date(),
        source: 'admin_manual'
      }

      const response = await fetch('/api/exchange-rates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to update rates')
      }

      setSuccess(`Successfully updated ${data.updatedRates.length} exchange rates`)
      setLastUpdated(new Date())
      
      // Update local state
      const newRates: Record<Currency, number> = {}
      Object.entries(editableRates).forEach(([currency, rateStr]) => {
        newRates[currency as Currency] = parseFloat(rateStr)
      })
      setExchangeRates(newRates)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update rates')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleRefreshRates = async () => {
    await loadData()
    setSuccess('Exchange rates refreshed')
  }

  const getChangeIndicator = (currency: Currency) => {
    const currentRate = exchangeRates[currency]
    const editableRate = parseFloat(editableRates[currency] || '0')
    
    if (!currentRate || !editableRate || currentRate === editableRate) {
      return null
    }

    const change = ((editableRate - currentRate) / currentRate) * 100
    const isIncrease = change > 0

    return (
      <Badge 
        variant={isIncrease ? "default" : "destructive"}
        className="text-xs"
      >
        {isIncrease ? '+' : ''}{change.toFixed(2)}%
      </Badge>
    )
  }

  const isRateStale = (lastUpdate: Date | null): boolean => {
    if (!lastUpdate) return true
    const hoursSinceUpdate = (Date.now() - lastUpdate.getTime()) / (1000 * 60 * 60)
    return hoursSinceUpdate > 24
  }

  if (isLoading) {
    return (
      <Card className={cn("bg-slate-800/50 backdrop-blur-xl border-slate-700/50", className)}>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
          <span className="ml-2 text-slate-400">Loading exchange rates...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("bg-slate-800/50 backdrop-blur-xl border-slate-700/50", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl text-white flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-400" />
            Exchange Rate Manager
          </CardTitle>
          <div className="flex items-center gap-2">
            {lastUpdated && (
              <div className="flex items-center gap-1 text-sm text-slate-400">
                <Clock className="h-4 w-4" />
                <span>
                  Updated {lastUpdated.toLocaleTimeString()}
                  {isRateStale(lastUpdated) && (
                    <Badge variant="destructive" className="ml-2 text-xs">
                      Stale
                    </Badge>
                  )}
                </span>
              </div>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={handleRefreshRates}
              disabled={isLoading || isUpdating}
              className="bg-slate-600/50 border-slate-500 text-white hover:bg-slate-500/50"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Alerts */}
        {error && (
          <Alert className="bg-red-900/20 border-red-700/50">
            <AlertTriangle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-100">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-900/20 border-green-700/50">
            <CheckCircle className="h-4 w-4 text-green-400" />
            <AlertDescription className="text-green-100">{success}</AlertDescription>
          </Alert>
        )}

        {/* Base Currency Info */}
        <div className="p-4 bg-blue-900/20 border border-blue-700/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign className="h-5 w-5 text-blue-400" />
            <span className="font-medium text-blue-100">Base Currency: {baseCurrency}</span>
          </div>
          <p className="text-sm text-blue-200">
            All exchange rates are relative to {baseCurrency}. 
            For example, if SDG rate is 450, it means 1 USD = 450 SDG.
          </p>
        </div>

        {/* Exchange Rates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {currencies
            .filter(currency => currency.code !== baseCurrency)
            .map((currency) => (
              <div
                key={currency.code}
                className="p-4 bg-slate-700/30 rounded-lg border border-slate-600/50"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold text-white">
                      {currency.symbol}
                    </span>
                    <div>
                      <div className="font-medium text-white">{currency.code}</div>
                      <div className="text-xs text-slate-400">{currency.name}</div>
                    </div>
                  </div>
                  {getChangeIndicator(currency.code)}
                </div>

                <div className="space-y-2">
                  <Label className="text-slate-300 text-sm">
                    1 {baseCurrency} = ? {currency.code}
                  </Label>
                  <Input
                    type="number"
                    value={editableRates[currency.code] || ''}
                    onChange={(e) => handleRateChange(currency.code, e.target.value)}
                    className="bg-slate-600/50 border-slate-500 text-white"
                    placeholder="0.00"
                    step="0.000001"
                    min="0"
                  />
                  {exchangeRates[currency.code] && (
                    <div className="text-xs text-slate-400">
                      Current: {exchangeRates[currency.code].toFixed(6)}
                    </div>
                  )}
                </div>
              </div>
            ))}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-600/50">
          <div className="text-sm text-slate-400">
            {currencies.length - 1} currencies configured
          </div>
          
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setEditableRates(
                Object.fromEntries(
                  Object.entries(exchangeRates).map(([currency, rate]) => [currency, rate.toString()])
                )
              )}
              disabled={isUpdating}
              className="bg-slate-600/50 border-slate-500 text-white hover:bg-slate-500/50"
            >
              Reset Changes
            </Button>

            <Button
              onClick={handleUpdateRates}
              disabled={isUpdating || !validateRates().isValid}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Rates
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
