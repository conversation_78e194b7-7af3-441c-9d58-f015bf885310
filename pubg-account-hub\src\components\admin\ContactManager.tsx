import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Save, AlertCircle } from "lucide-react";
import { ContactInfoModel, getContactInfo, updateContactInfo } from "@/services/firestore";
import { Alert, AlertDescription } from "@/components/ui/alert";

const ContactManager = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<ContactInfoModel>({
    telegramLink: "",
    whatsappNumber: "",
    email: "",
    facebook: "",
    instagram: "",
    twitter: "",
    discord: "",
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const contactData = await getContactInfo();
        if (contactData) {
          setFormData(contactData);
        }
        setIsLoading(false);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load contact information",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await updateContactInfo(formData);
      
      toast({
        title: "نجاح",
        description: "تم تحديث معلومات التواصل بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث معلومات التواصل",
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  if (isLoading) {
    return <div className="text-center py-8">جاري تحميل معلومات التواصل...</div>;
  }

  return (
    <div>
      <div className="mb-4">
        <h2 className="text-xl font-bold text-white">إدارة معلومات التواصل</h2>
        <p className="text-muted-foreground">
          قم بتحديث روابط التواصل الاجتماعي ومعلومات الاتصال. جميع الحقول اختيارية.
        </p>
      </div>

      <Alert className="mb-6 bg-blue-950/20 border-blue-500/50">
        <AlertCircle className="h-5 w-5 text-blue-500" />
        <AlertDescription className="text-muted-foreground text-sm">
          جميع الحقول في هذا النموذج اختيارية. يمكنك ملء الحقول التي ترغب في عرضها فقط.
        </AlertDescription>
      </Alert>

      <div className="glass-card rounded-xl p-4 sm:p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="telegramLink">رابط تلجرام (اختياري)</Label>
              <Input
                id="telegramLink"
                name="telegramLink"
                value={formData.telegramLink}
                onChange={handleInputChange}
                placeholder="https://t.me/yourusername"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="whatsappNumber">رقم واتساب (اختياري)</Label>
              <Input
                id="whatsappNumber"
                name="whatsappNumber"
                value={formData.whatsappNumber}
                onChange={handleInputChange}
                placeholder="+201234567890"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني (اختياري)</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="discord">رابط ديسكورد (اختياري)</Label>
              <Input
                id="discord"
                name="discord"
                value={formData.discord || ""}
                onChange={handleInputChange}
                placeholder="https://discord.gg/yourdiscord"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="facebook">رابط فيسبوك (اختياري)</Label>
              <Input
                id="facebook"
                name="facebook"
                value={formData.facebook}
                onChange={handleInputChange}
                placeholder="https://facebook.com/yourpage"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="instagram">رابط انستغرام (اختياري)</Label>
              <Input
                id="instagram"
                name="instagram"
                value={formData.instagram}
                onChange={handleInputChange}
                placeholder="https://instagram.com/yourusername"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="twitter">رابط تويتر (اختياري)</Label>
              <Input
                id="twitter"
                name="twitter"
                value={formData.twitter}
                onChange={handleInputChange}
                placeholder="https://twitter.com/yourusername"
              />
            </div>
          </div>
          
          <div className="flex justify-end mt-6">
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
            >
              <Save className="ml-2 h-4 w-4" />
              حفظ التغييرات
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactManager;
