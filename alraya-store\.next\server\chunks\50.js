exports.id=50,exports.ids=[50],exports.modules={1247:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},7396:(e,t,s)=>{"use strict";s.d(t,{GlobalChatProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useGlobalChat() from the server but useGlobalChat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\components\\chat\\GlobalChatProvider.tsx","useGlobalChat");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call GlobalChatProvider() from the server but GlobalChatProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\components\\chat\\GlobalChatProvider.tsx","GlobalChatProvider")},8870:(e,t,s)=>{"use strict";s.d(t,{Y:()=>a});var r=s(43210);function a({userId:e,userType:t,selectedChatUserId:s}){let[a,l]=(0,r.useState)([]),[n,i]=(0,r.useState)([]),[o,d]=(0,r.useState)(!1),[c,m]=(0,r.useState)(!1),[u,x]=(0,r.useState)(!1),[h,f]=(0,r.useState)([]),[g,p]=(0,r.useState)(0),[b,v]=(0,r.useState)(!1),[j,N]=(0,r.useState)(null);return(0,r.useRef)(null),(0,r.useRef)(null),(0,r.useCallback)(async r=>{d(!0),N(null);try{let r=[{id:"1",userId:"customer"===t?e:s||"",adminId:"admin"===t?e:void 0,message:"مرحباً! كيف يمكنني مساعدتك؟",senderType:"admin",messageType:"text",isRead:!0,createdAt:new Date(Date.now()-36e5)},{id:"2",userId:"customer"===t?e:s||"",message:"أريد شحن حساب كلاش أوف كلانز",senderType:"customer",messageType:"text",isRead:!0,createdAt:new Date(Date.now()-3e6)}];l(r)}catch(e){N("فشل في تحميل الرسائل"),console.error("Error loading messages:",e)}finally{d(!1)}},[e,t,s]),(0,r.useCallback)(async()=>{if("admin"===t){m(!0),N(null);try{let e=[{userId:"user1",customerName:"أحمد محمد",customerEmail:"<EMAIL>",unreadCount:3,isOnline:!0,lastSeen:new Date,activeOrders:[],createdAt:new Date(Date.now()-864e5)},{userId:"user2",customerName:"سارة علي",customerEmail:"<EMAIL>",unreadCount:1,isOnline:!1,lastSeen:new Date(Date.now()-36e5),activeOrders:[],createdAt:new Date(Date.now()-1728e5)}];i(e);let t=e.reduce((e,t)=>e+t.unreadCount,0);p(t)}catch(e){N("فشل في تحميل المحادثات"),console.error("Error loading chat rooms:",e)}finally{m(!1)}}},[t]),{messages:a,isLoadingMessages:o,chatRooms:n,isLoadingRooms:c,sendMessage:(0,r.useCallback)(async(r,a)=>{if(r.trim())try{let n={userId:"customer"===t?e:s||"",adminId:"admin"===t?e:void 0,message:r.trim(),senderType:t,orderId:a,messageType:"text",isRead:!1,id:Date.now().toString(),createdAt:new Date};l(e=>[...e,n])}catch(e){N("فشل في إرسال الرسالة"),console.error("Error sending message:",e)}},[e,t,s]),markAsRead:(0,r.useCallback)(async e=>{try{l(t=>t.map(t=>e.includes(t.id)?{...t,isRead:!0}:t))}catch(t){console.error("Error marking messages as read:",t),l(t=>t.map(t=>e.includes(t.id)?{...t,isRead:!1}:t))}},[]),isOnline:u,typingUsers:h,unreadCount:g,isConnected:b,error:j}}},21083:(e,t,s)=>{"use strict";function r(e,t,s){let r;let{showSymbol:l=!0,decimalPlaces:n,locale:i="en-US"}=s||{};r="string"==typeof t?a(t):t;let o=n??2,d=e.toLocaleString(i,{minimumFractionDigits:o,maximumFractionDigits:o});if(!l)return d;let c=r.symbol||r.code;return r.isRTL?`${d} ${c}`:`${c}${d}`}function a(e){return({USD:{code:"USD",name:"US Dollar",symbol:"$",arabicName:"الدولار الأمريكي",isRTL:!1},SDG:{code:"SDG",name:"Sudanese Pound",symbol:"ج.س.",arabicName:"الجنيه السوداني",isRTL:!0},EGP:{code:"EGP",name:"Egyptian Pound",symbol:"ج.م.",arabicName:"الجنيه المصري",isRTL:!0},SAR:{code:"SAR",name:"Saudi Riyal",symbol:"ر.س",arabicName:"الريال السعودي",isRTL:!0},AED:{code:"AED",name:"UAE Dirham",symbol:"د.إ",arabicName:"الدرهم الإماراتي",isRTL:!0},EUR:{code:"EUR",name:"Euro",symbol:"€",arabicName:"اليورو",isRTL:!1},GBP:{code:"GBP",name:"British Pound",symbol:"\xa3",arabicName:"الجنيه الإسترليني",isRTL:!1}})[e]||{code:e,name:e,symbol:e,isRTL:!1}}s.d(t,{formatCurrency:()=>r,getCurrencyDisplayInfo:()=>a})},24934:(e,t,s)=>{"use strict";s.d(t,{$:()=>d,r:()=>o});var r=s(60687),a=s(43210),l=s(8730),n=s(24224),i=s(96241);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...n},d)=>{let c=a?l.DX:"button";return(0,r.jsx)(c,{className:(0,i.cn)(o({variant:t,size:s,className:e})),ref:d,...n})});d.displayName="Button"},32517:(e,t,s)=>{"use strict";s.d(t,{CurrencyProvider:()=>d,D:()=>m,H:()=>c});var r=s(60687),a=s(43210),l=s(54278),n=s(34991);let i=(0,a.createContext)(void 0),o={USD:1,SDG:450,EGP:30.8};function d({children:e}){let[t,s]=(0,a.useState)("USD"),[d,c]=(0,a.useState)(!1),m=(0,n.ZN)(),u=(0,n.I1)(),x=m.length>0?m:Object.values(l.yR),h=Object.keys(u).length>0?u:o,f=async e=>{c(!0);try{s(e),localStorage.setItem("selectedCurrency",e)}catch(e){console.error("Failed to update currency preference:",e)}finally{c(!1)}};return(0,r.jsx)(i.Provider,{value:{selectedCurrency:t,availableCurrencies:x,updateCurrency:f,exchangeRates:h,isLoading:d},children:e})}function c(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useCurrency must be used within a CurrencyProvider");return e}function m(){let{selectedCurrency:e,exchangeRates:t}=c(),s=(s,r)=>s*(t[r||e]||1);return{convertPrice:s,formatPrice:(t,r)=>{let a=r||e,n=s(t,a),i=l.yR[a];return i?i.isRTL?`${n.toLocaleString()} ${i.symbol}`:`${i.symbol}${n.toLocaleString()}`:`${n.toFixed(2)} ${a}`},selectedCurrency:e,exchangeRates:t}}},34991:(e,t,s)=>{"use strict";s.d(t,{I1:()=>d,NN:()=>h,QT:()=>i,R$:()=>g,ZN:()=>o,b5:()=>c,cd:()=>f,rU:()=>x,uH:()=>m,zB:()=>u});var r=s(26787),a=s(59350);let l={data:null,isLoading:!1,isInitialized:!1,error:null,lastUpdated:null,isOffline:!1},n=(0,r.v)()((0,a.Zr)((e,t)=>({...l,loadBulkData:async()=>{let s=t();if(s.isLoading)return;let r=new Date(Date.now()-3e5);if(!(s.lastUpdated&&new Date(s.lastUpdated)>r)){e({isLoading:!0,error:null});try{let t=await fetch("/api/bulk-data",{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let s=await t.json();if(!s.success)throw Error(s.error||"Failed to load application data");e({data:s.data,isLoading:!1,isInitialized:!0,error:null,lastUpdated:new Date,isOffline:!1})}catch(s){console.error("Failed to load bulk data:",s),t().data?e({isLoading:!1,error:"Using cached data - offline mode",isOffline:!0}):e({isLoading:!1,error:s instanceof Error?s.message:"Failed to load data",isOffline:!0})}}},updateData:s=>{let r=t().data;r&&e({data:{...r,...s},lastUpdated:new Date})},setError:t=>{e({error:t})},clearData:()=>{e(l)},refreshData:async()=>{e({lastUpdated:null}),await t().loadBulkData()},setOfflineMode:t=>{e({isOffline:t})}}),{name:"alraya-app-data",storage:(0,a.KU)(()=>localStorage),partialize:e=>({data:e.data,lastUpdated:e.lastUpdated,isInitialized:e.isInitialized}),onRehydrateStorage:()=>e=>{e&&(e.isLoading=!1,e.error=null,e.isOffline=!1)}})),i=()=>n(e=>e.data?.products||[]),o=()=>n(e=>e.data?.currencies||[]),d=()=>n(e=>e.data?.exchangeRates||{}),c=()=>n(e=>e.data?.gameCards||[]),m=()=>n(e=>e.data?.slides||[]),u=()=>n(e=>e.isLoading),x=()=>n(e=>e.error),h=()=>n(e=>e.isInitialized),f=()=>n(e=>e.loadBulkData),g=()=>n(e=>e.refreshData)},39919:(e,t,s)=>{Promise.resolve().then(s.bind(s,70338)),Promise.resolve().then(s.bind(s,94914)),Promise.resolve().then(s.bind(s,32517)),Promise.resolve().then(s.bind(s,52581))},45468:(e,t,s)=>{"use strict";s.d(t,{k:()=>E});var r=s(60687),a=s(43210),l=s(24934),n=s(68988),i=s(59821),o=s(69327),d=s(28559),c=s(58887),m=s(58869),u=s(19080),x=s(79351),h=s(49153),f=s(11860),g=s(99270),p=s(41312),b=s(27900),v=s(5336),j=s(93508),N=s(71057),y=s(23928),w=s(19169),C=s(48340),D=s(40228),S=s(48730),P=s(13861),A=s(8870),R=s(94511),$=s(54278);function E({isOpen:e=!0,onClose:t,userId:s,userName:b,userEmail:v,userRole:j,position:N="bottom-right",isMinimized:y=!1,onToggleMinimize:w,initialCustomerId:C,initialOrderId:D,initialChatRoomId:S}){let P;if(!j||!["admin","customer"].includes(j))return console.error("ChatModal: Invalid or missing userRole"),null;if(!s)return console.error("ChatModal: Missing required userId"),null;let[$,E]=(0,a.useState)("customer"===j?"admin-support":null),[L,U]=(0,a.useState)(""),[G,O]=(0,a.useState)(""),[z,M]=(0,a.useState)("chat"),[F,B]=(0,a.useState)(null),[V,Y]=(0,a.useState)(""),[_,q]=(0,a.useState)("all"),H=(0,a.useRef)(null),K=(0,a.useRef)(null),Z="admin"===j,Q="admin"===j,X="admin"===j,J="admin"===j,{messages:W,chatRooms:ee,isLoadingMessages:et,isLoadingRooms:es,sendMessage:er,markAsRead:ea,typingUsers:el,unreadCount:en,error:ei}=(0,A.Y)({userId:s,userType:j,selectedChatUserId:"admin"===j?$||void 0:"admin-support"}),[eo,ed]=(0,a.useState)([]),[ec,em]=(0,a.useState)(!1),eu=e=>{if(!X){console.warn("ChatModal: Unauthorized attempt to select customer");return}E(e),M("chat");let t=W.filter(t=>!t.isRead&&"customer"===t.senderType&&t.userId===e).map(e=>e.id);t.length>0&&ea(t)},ex=async()=>{if(!L.trim()||!$)return;let e=L.trim();U("");try{await er(e),K.current?.focus()}catch(e){console.error("Error sending message:",e)}},eh=ee.filter(e=>e.customerName.toLowerCase().includes(G.toLowerCase())||e.customerEmail.toLowerCase().includes(G.toLowerCase())),ef=ee.find(e=>e.userId===$)||($===C&&C?{id:C,userId:C,customerName:C.includes("@")?C.split("@")[0]:"عميل",customerEmail:C,lastMessage:null,lastMessageTime:new Date,unreadCount:0,isOnline:!0,lastSeen:new Date,activeOrders:[],isTyping:!1}:null);return e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 z-40",onClick:t}),(0,r.jsxs)("div",{className:`
        fixed ${(()=>{switch(N){case"bottom-left":return"bottom-4 left-4";case"center":return"top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2";default:return"bottom-4 right-4"}})()} z-50
        ${y?"w-80 h-16":"w-[500px] h-[700px]"}
        bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-600/50
        transition-all duration-300 ease-in-out
        max-w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)]
        flex flex-col
      `,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-slate-600/30 bg-gradient-to-r from-slate-800/95 to-slate-700/95 rounded-t-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:["chat"!==z&&ef&&(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>M("chat"),className:"flex items-center gap-2 px-3 py-1.5 hover:bg-slate-700 hover:scale-105 transition-all duration-200 rounded-lg",title:"العودة للمحادثة",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-slate-300 hover:text-white"}),(0,r.jsx)("span",{className:"text-slate-300 hover:text-white text-sm font-medium hidden sm:inline",children:"المحادثة"})]}),"chat"===z&&ef&&(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>{X&&(E(null),M("chat"))},className:"flex items-center gap-2 px-3 py-1.5 hover:bg-slate-700 hover:scale-105 transition-all duration-200 rounded-lg",title:"العودة لقائمة العملاء",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-slate-300 hover:text-white"}),(0,r.jsx)("span",{className:"text-slate-300 hover:text-white text-sm font-medium hidden sm:inline",children:"العملاء"})]}),(0,r.jsxs)("div",{className:"w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center",children:["chat"===z&&(0,r.jsx)(c.A,{className:"h-4 w-4 text-white"}),"customer"===z&&(0,r.jsx)(m.A,{className:"h-4 w-4 text-white"}),"orders"===z&&(0,r.jsx)(u.A,{className:"h-4 w-4 text-white"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold text-white text-sm",children:["chat"===z&&(ef?ef.customerName:"المحادثات"),"customer"===z&&"ملف العميل","orders"===z&&"طلبات العميل"]}),ef&&"chat"===z&&(0,r.jsx)("p",{className:"text-xs text-slate-400",children:ef.isOnline?"متصل الآن":"غير متصل"}),ef&&"chat"!==z&&(0,r.jsx)("p",{className:"text-xs text-slate-400",children:ef.customerName})]}),en>0&&"chat"===z&&(0,r.jsx)(i.E,{className:"bg-green-500 text-white text-xs px-1.5 py-0.5",children:en})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[Z&&ef&&"chat"===z&&(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{Z&&M("customer")},className:"p-1.5 hover:bg-slate-700",title:"عرض ملف العميل",children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-slate-400"})}),Q&&ef&&"chat"===z&&(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{Q&&M("orders")},className:"p-1.5 hover:bg-slate-700",title:"عرض طلبات العميل",children:(0,r.jsx)(u.A,{className:"h-4 w-4 text-slate-400"})}),w&&(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:w,className:"p-1.5 hover:bg-slate-700",children:y?(0,r.jsx)(x.A,{className:"h-4 w-4 text-slate-400"}):(0,r.jsx)(h.A,{className:"h-4 w-4 text-slate-400"})}),(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:t,className:"p-1.5 hover:bg-slate-700",children:(0,r.jsx)(f.A,{className:"h-4 w-4 text-slate-400"})})]})]}),!y&&(0,r.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[J&&(0,r.jsxs)("div",{className:`${$?"hidden md:flex md:w-56":"flex w-full md:w-56"} border-r border-slate-600/30 flex-col bg-slate-900/20`,children:[(0,r.jsx)("div",{className:"p-3 border-b border-slate-600/30",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,r.jsx)(n.p,{value:G,onChange:e=>O(e.target.value),placeholder:"بحث العملاء...",className:"bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 pl-10 text-sm h-9 rounded-xl"})]})}),(0,r.jsx)(o.F,{className:"flex-1",children:es?(0,r.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-green-400"})}):0===eh.length?(0,r.jsxs)("div",{className:"text-center p-6",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-slate-500 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-slate-400 text-sm",children:"لا توجد محادثات"}),(0,r.jsx)("p",{className:"text-slate-500 text-xs mt-1",children:"ابدأ محادثة مع العملاء"})]}):(0,r.jsx)("div",{className:"divide-y divide-slate-700/20",children:eh.map(e=>(0,r.jsx)("div",{onClick:()=>eu(e.userId),className:`
                          p-4 cursor-pointer transition-all duration-200
                          hover:bg-slate-700/40 hover:scale-[1.02]
                          ${$===e.userId?"bg-gradient-to-r from-green-500/20 to-green-600/20 border-r-2 border-green-500":""}
                        `,children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"relative flex-shrink-0",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center text-white font-bold text-sm shadow-lg",children:e.customerName.charAt(0)}),e.isOnline&&(0,r.jsx)("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-slate-800 animate-pulse"})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("h4",{className:"font-semibold text-white truncate text-sm",children:e.customerName}),e.unreadCount>0&&(0,r.jsx)("div",{className:"bg-green-500 text-white text-xs font-bold rounded-full min-w-[18px] h-5 flex items-center justify-center px-1.5 shadow-sm",children:e.unreadCount>9?"9+":e.unreadCount})]}),e.lastMessage&&(0,r.jsxs)("p",{className:"text-xs text-slate-400 truncate mb-1",children:["admin"===e.lastMessage.senderType&&(0,r.jsx)("span",{className:"text-green-400",children:"أنت: "}),e.lastMessage.message]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[e.activeOrders.length>0&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(u.A,{className:"w-3 h-3 text-orange-400"}),(0,r.jsxs)("span",{className:"text-xs text-orange-400 font-medium",children:[e.activeOrders.length," طلب نشط"]})]}),(0,r.jsx)("span",{className:"text-xs text-slate-500",children:(0,R.Yq)(e.lastMessage?.createdAt||new Date,"time")})]})]})]})},e.userId))})})]}),(0,r.jsx)("div",{className:`${J?$?"flex w-full":"hidden md:flex md:flex-1":"flex w-full"} flex-col bg-gradient-to-b from-slate-900/10 to-slate-900/30`,children:!J||$?(0,r.jsxs)(r.Fragment,{children:["chat"===z&&(0,r.jsx)(T,{selectedCustomer:"customer"===j?{customerName:"الدعم الفني",userId:"admin-support"}:ef,messages:W,messageInput:L,setMessageInput:U,handleSendMessage:ex,handleKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),ex())},messagesEndRef:H,inputRef:K,isLoadingMessages:et,typingUsers:el,error:ei,userRole:j}),Z&&"customer"===z&&ef&&(0,r.jsx)(k,{customer:(e=>{let t=ee.find(t=>t.userId===e);return!t&&e===C&&C?{id:C,name:C.includes("@")?C.split("@")[0]:"عميل",email:C,phone:"+************",joinDate:new Date().toISOString(),lastLogin:new Date().toISOString(),isVerified:!0,totalOrders:1,totalSpent:0,accountStatus:"active"}:t?{id:e,name:t.customerName,email:t.customerEmail||`${t.customerName.toLowerCase().replace(" ",".")}@example.com`,phone:t.customerPhone||"+************",joinDate:"2024-01-15T10:30:00Z",lastLogin:"2024-06-25T14:20:00Z",isVerified:!0,totalOrders:t.activeOrders.length+Math.floor(10*Math.random()),totalSpent:t.activeOrders.reduce((e,t)=>e+(t.totalPrice||0),0)+Math.floor(5e3*Math.random()),accountStatus:"active"}:null})(ef.userId),selectedCustomer:ef}),Q&&"orders"===z&&ef&&(0,r.jsx)(I,{orders:(P=eo,V&&(P=P.filter(e=>e.id.toLowerCase().includes(V.toLowerCase())||e.templateName.toLowerCase().includes(V.toLowerCase()))),"all"!==_&&(P=P.filter(e=>e.status===_)),P.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())),isLoading:ec,searchQuery:V,setSearchQuery:Y,statusFilter:_,setStatusFilter:q,getOrderStatusBadge:e=>({style:({pending:"bg-yellow-500/20 text-yellow-400 border-yellow-500/30",processing:"bg-blue-500/20 text-blue-400 border-blue-500/30",completed:"bg-green-500/20 text-green-400 border-green-500/30",cancelled:"bg-red-500/20 text-red-400 border-red-500/30"})[e],label:({pending:"في الانتظار",processing:"قيد المعالجة",completed:"مكتمل",cancelled:"ملغي"})[e]}),selectedOrderId:F,setSelectedOrderId:B})]}):(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.A,{className:"h-16 w-16 text-slate-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"مرحباً بك في نظام الدعم"}),(0,r.jsx)("p",{className:"text-slate-400",children:"اختر عميل من القائمة لبدء المحادثة"})]})})})]})]})]}):null}function T({selectedCustomer:e,messages:t,messageInput:s,setMessageInput:a,handleSendMessage:i,handleKeyPress:d,messagesEndRef:c,inputRef:m,isLoadingMessages:u,typingUsers:x,error:h,userRole:f}){return(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)(o.F,{className:"flex-1 p-4",children:[h&&(0,r.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 text-red-300 p-3 rounded-xl text-sm mb-4",children:h}),u?(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"})}):0===t.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-3xl flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg",children:e?.customerName.charAt(0)}),(0,r.jsx)("h4",{className:"text-xl font-bold text-white mb-2",children:e?.customerName}),(0,r.jsx)("p",{className:"text-slate-400 text-sm",children:"ابدأ محادثة مع العميل"})]})}):(0,r.jsxs)(r.Fragment,{children:[t.map(e=>(0,r.jsx)(L,{message:e},e.id)),(0,r.jsx)("div",{ref:c})]}),x.length>0&&(0,r.jsx)("div",{className:"flex justify-start mb-4",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-slate-700 to-slate-800 px-4 py-3 rounded-2xl rounded-bl-md text-sm text-slate-300 shadow-lg border border-slate-600/30",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),(0,r.jsxs)("span",{className:"text-xs font-medium",children:[e?.customerName," يكتب..."]})]})})})]}),(0,r.jsx)("div",{className:"border-t border-slate-600/30 p-4 bg-gradient-to-r from-slate-800/50 to-slate-700/50",children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(n.p,{ref:m,value:s,onChange:e=>a(e.target.value),onKeyPress:d,placeholder:"customer"===f?"اكتب رسالتك للدعم الفني...":`اكتب رسالة إلى ${e?.customerName}...`,className:"bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 text-sm rounded-xl px-4 py-3 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20",maxLength:1e3}),(0,r.jsx)(l.$,{onClick:i,disabled:!s.trim(),className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-3 rounded-xl shadow-lg hover:shadow-green-500/25 transition-all duration-200",size:"sm",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})]})})]})}function L({message:e}){let t="customer"===e.senderType;return(0,r.jsx)("div",{className:`flex ${t?"justify-start":"justify-end"} mb-4`,children:(0,r.jsxs)("div",{className:`
        max-w-[85%] px-4 py-3 text-sm shadow-lg
        ${t?"bg-gradient-to-br from-slate-700 to-slate-800 text-white rounded-2xl rounded-bl-md border border-slate-600/30":"bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl rounded-br-md shadow-green-500/25"}
        animate-in slide-in-from-bottom-2 duration-300
      `,children:[(0,r.jsx)("p",{className:"leading-relaxed font-medium",children:e.message}),(0,r.jsxs)("div",{className:`
          flex items-center gap-1 mt-2 text-xs
          ${t?"text-slate-400":"text-green-100"}
        `,children:[(0,r.jsx)("span",{children:(0,R.Yq)(e.createdAt,"time")}),!t&&(0,r.jsx)("div",{className:"flex items-center ml-1",children:(0,r.jsx)(v.A,{className:"h-3 w-3"})})]})]})})}function k({customer:e,selectedCustomer:t}){return e?(0,r.jsx)("div",{className:"h-full flex flex-col",children:(0,r.jsx)(o.F,{className:"flex-1 p-5",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-3xl flex items-center justify-center text-white font-bold text-3xl mx-auto mb-4 shadow-2xl",children:e.name.charAt(0)}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 bg-slate-800/50 px-4 py-2 rounded-full border border-slate-600/30",children:[(0,r.jsx)(j.A,{className:"w-4 h-4 text-green-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-400",children:"عميل مُفعّل"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-500/10 to-blue-600/10 p-4 rounded-2xl text-center border border-blue-500/20",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 text-blue-400"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-white",children:e.totalOrders})]}),(0,r.jsx)("p",{className:"text-sm text-blue-300 font-medium",children:"إجمالي الطلبات"})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-green-500/10 to-green-600/10 p-4 rounded-2xl text-center border border-green-500/20",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 text-green-400"}),(0,r.jsx)("span",{className:"text-lg font-bold text-white",children:(0,$.vv)(e.totalSpent,"SDG")})]}),(0,r.jsx)("p",{className:"text-sm text-green-300 font-medium",children:"إجمالي المشتريات"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-white font-bold mb-4 flex items-center gap-2 text-lg",children:[(0,r.jsx)(w.A,{className:"h-5 w-5 text-blue-400"}),"معلومات الاتصال"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gradient-to-r from-blue-500/10 to-blue-600/10 rounded-xl border border-blue-500/20",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center",children:(0,r.jsx)(w.A,{className:"h-5 w-5 text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-300 text-sm font-medium",children:"البريد الإلكتروني"}),(0,r.jsx)("p",{className:"text-slate-300 text-sm",children:e.email})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl border border-green-500/20",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-green-500/20 rounded-xl flex items-center justify-center",children:(0,r.jsx)(C.A,{className:"h-5 w-5 text-green-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-green-300 text-sm font-medium",children:"رقم الهاتف"}),(0,r.jsx)("p",{className:"text-slate-300 text-sm",children:e.phone})]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-white font-bold mb-4 flex items-center gap-2 text-lg",children:[(0,r.jsx)(D.A,{className:"h-5 w-5 text-purple-400"}),"معلومات الحساب"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-slate-800/50 rounded-xl",children:[(0,r.jsx)("span",{className:"text-slate-300 font-medium",children:"تاريخ التسجيل"}),(0,r.jsx)("span",{className:"text-white",children:(0,R.Yq)(e.joinDate)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-slate-800/50 rounded-xl",children:[(0,r.jsx)("span",{className:"text-slate-300 font-medium",children:"آخر تسجيل دخول"}),(0,r.jsx)("span",{className:"text-white",children:(0,R.Yq)(e.lastLogin,"relative")})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-slate-800/50 rounded-xl",children:[(0,r.jsx)("span",{className:"text-slate-300 font-medium",children:"حالة الحساب"}),(0,r.jsx)(i.E,{className:"bg-green-500/20 text-green-400 border-green-500/30",children:"نشط"})]})]})]})]})})}):null}function I({orders:e,isLoading:t,searchQuery:s,setSearchQuery:a,statusFilter:d,setStatusFilter:c,getOrderStatusBadge:m,selectedOrderId:x,setSelectedOrderId:h}){return(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsx)("div",{className:"p-4 border-b border-slate-600/30 bg-slate-800/50",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-400"}),(0,r.jsx)("h3",{className:"text-lg font-bold text-white",children:"طلبات العميل"}),(0,r.jsxs)(i.E,{className:"bg-blue-500/20 text-blue-400 border-blue-500/30",children:[e.length," طلب"]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,r.jsx)(n.p,{value:s,onChange:e=>a(e.target.value),placeholder:"البحث في الطلبات...",className:"bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 text-sm pl-10 rounded-xl"})]}),(0,r.jsxs)("select",{value:d,onChange:e=>c(e.target.value),className:"bg-slate-700/50 border border-slate-600/50 text-white text-sm rounded-xl px-3 py-2 focus:border-blue-500/50",children:[(0,r.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,r.jsx)("option",{value:"pending",children:"في الانتظار"}),(0,r.jsx)("option",{value:"processing",children:"قيد المعالجة"}),(0,r.jsx)("option",{value:"completed",children:"مكتمل"}),(0,r.jsx)("option",{value:"cancelled",children:"ملغي"})]})]})]})}),(0,r.jsx)(o.F,{className:"flex-1 p-4",children:t?(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"})}):0===e.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.A,{className:"h-16 w-16 text-slate-500 mx-auto mb-4"}),(0,r.jsx)("h4",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد طلبات"}),(0,r.jsx)("p",{className:"text-slate-400",children:"لم يقم العميل بأي طلبات بعد"})]})}):(0,r.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,r.jsxs)("div",{onClick:()=>h(x===e.id?null:e.id),className:`
                  p-4 bg-gradient-to-r from-slate-800/50 to-slate-700/30 rounded-xl border cursor-pointer
                  transition-all duration-200 hover:scale-[1.02] hover:shadow-lg
                  ${x===e.id?"border-blue-500/50 bg-gradient-to-r from-blue-500/10 to-blue-600/10":"border-slate-600/30 hover:border-slate-500/50"}
                `,children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-semibold text-white mb-1",children:["#",e.id]}),(0,r.jsx)("p",{className:"text-slate-300 text-sm",children:e.templateName})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)(i.E,{className:m(e.status).style,children:m(e.status).label}),(0,r.jsx)("p",{className:"text-slate-400 text-xs mt-1",children:(0,R.Yq)(e.createdAt)})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-green-400"}),(0,r.jsx)("span",{className:"text-green-400 font-semibold",children:(0,$.vv)(e.totalPrice||0,"SDG")})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 text-slate-400"}),(0,r.jsx)("span",{className:"text-slate-400 text-sm",children:(0,R.Yq)(e.createdAt,"relative")})]})]}),(0,r.jsx)(l.$,{variant:"ghost",size:"sm",className:"text-blue-400 hover:text-blue-300",children:(0,r.jsx)(P.A,{className:"h-4 w-4"})})]}),x===e.id&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-slate-600/30 space-y-3",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-slate-400 text-xs mb-1",children:"نوع الخدمة"}),(0,r.jsx)("p",{className:"text-white text-sm",children:e.serviceType})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-slate-400 text-xs mb-1",children:"المنصة"}),(0,r.jsx)("p",{className:"text-white text-sm",children:e.platform})]})]}),e.userDetails&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-slate-400 text-xs mb-2",children:"تفاصيل التسليم"}),(0,r.jsxs)("div",{className:"bg-slate-800/50 p-3 rounded-lg space-y-1",children:[(0,r.jsxs)("p",{className:"text-slate-300 text-sm",children:[(0,r.jsx)("span",{className:"text-slate-400",children:"الاسم:"})," ",e.userDetails.fullName]}),(0,r.jsxs)("p",{className:"text-slate-300 text-sm",children:[(0,r.jsx)("span",{className:"text-slate-400",children:"البريد:"})," ",e.userDetails.email]}),e.userDetails.phone&&(0,r.jsxs)("p",{className:"text-slate-300 text-sm",children:[(0,r.jsx)("span",{className:"text-slate-400",children:"الهاتف:"})," ",e.userDetails.phone]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{size:"sm",className:"bg-blue-500 hover:bg-blue-600 text-white",children:"تحديث الحالة"}),(0,r.jsx)(l.$,{size:"sm",variant:"outline",className:"border-slate-600 text-slate-300",children:"إرسال رسالة"})]})]})]},e.id))})})]})}s(59885)},54278:(e,t,s)=>{"use strict";s.d(t,{vv:()=>l,yR:()=>a,zN:()=>n});var r=s(21083);let a={SDG:{code:"SDG",name:"الجنيه السوداني",symbol:"ج.س.",isRTL:!0},EGP:{code:"EGP",name:"الجنيه المصري",symbol:"ج.م.",isRTL:!0},USD:{code:"USD",name:"الدولار الأمريكي",symbol:"$",isRTL:!1}};function l(e,t){return(0,r.formatCurrency)(e,t)}async function n(){return Object.values(a)}},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>c});var r=s(37413),a=s(4010),l=s.n(a);s(82704);var n=s(7396),i=s(63887),o=s(79891),d=s(6931);let c={metadataBase:new URL(process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"),title:"رايه شوب - أفضل متجر للشحن الفوري",description:"رايه شوب - متجرك الموثوق لشحن الألعاب والبطاقات الرقمية بأفضل الأسعار وأسرع خدمة",keywords:"شحن ببجي, بطاقات آيتونز, شحن فوري, ألعاب, بطاقات رقمية",icons:{icon:[{url:"/favicon.ico",type:"image/x-icon"},{url:"/icon-192x192.png",sizes:"192x192",type:"image/png"},{url:"/icon-512x512.png",sizes:"512x512",type:"image/png"}],apple:[{url:"/apple-touch-icon.png",sizes:"180x180",type:"image/png"}],other:[{rel:"manifest",url:"/site.webmanifest"}]},openGraph:{title:"رايه شوب - أفضل متجر للشحن الفوري",description:"متجرك الموثوق لشحن الألعاب والبطاقات الرقمية",type:"website",url:"https://raya-shop.com",siteName:"رايه شوب",images:[{url:"/logo-with-background.png",width:1200,height:630,alt:"رايه شوب - أفضل متجر للشحن الفوري"}]},twitter:{card:"summary_large_image",title:"رايه شوب - أفضل متجر للشحن الفوري",description:"متجرك الموثوق لشحن الألعاب والبطاقات الرقمية",images:["/logo-with-background.png"]},generator:"v0.dev"};function m({children:e}){return(0,r.jsx)("html",{lang:"ar",dir:"rtl",className:"dark",children:(0,r.jsx)("body",{className:l().className,children:(0,r.jsx)(o.DataProvider,{children:(0,r.jsx)(i.CurrencyProvider,{children:(0,r.jsxs)(n.GlobalChatProvider,{children:[e,(0,r.jsx)(d.Toaster,{position:"top-center",richColors:!0})]})})})})})}},59821:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(60687);s(43210);var a=s(24224),l=s(96241);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,l.cn)(n({variant:t}),e),...s})}},59885:(e,t,s)=>{"use strict";function r(){return[]}function a(e,t,s,r){}function l(e){let t=[];if(e.status&&e.status.length>0&&(t=t.filter(t=>e.status.includes(t.status))),e.processingType&&e.processingType.length>0&&(t=t.filter(t=>e.processingType.includes(t.processingType))),e.dateRange&&(t=t.filter(t=>{let s=new Date(t.createdAt);return s>=e.dateRange.start&&s<=e.dateRange.end})),e.templateId&&(t=t.filter(t=>t.templateId===e.templateId)),e.search){let s=e.search.toLowerCase();t=t.filter(e=>e.id.toLowerCase().includes(s)||e.templateName.toLowerCase().includes(s)||e.userDetails.fullName.toLowerCase().includes(s)||e.userDetails.email.toLowerCase().includes(s))}return t.map(e=>({id:e.id,templateName:e.templateName,customerName:e.userDetails.fullName,customerEmail:e.userDetails.email,totalPrice:e.pricing.totalPrice,currency:e.pricing.currency,status:e.status,processingType:e.processingType,createdAt:e.createdAt,priority:e.priority}))}function n(){let e=[],t=new Date;return t.setHours(0,0,0,0),{total:e.length,pending:e.filter(e=>"pending"===e.status).length,processing:e.filter(e=>"processing"===e.status).length,completed:e.filter(e=>"completed"===e.status).length,failed:e.filter(e=>"failed"===e.status).length,cancelled:e.filter(e=>"cancelled"===e.status).length,todayOrders:e.filter(e=>new Date(e.createdAt)>=t).length,avgProcessingTime:function(e){let t=e.filter(e=>"completed"===e.status&&e.completedAt);return 0===t.length?0:t.reduce((e,t)=>e+(new Date(t.completedAt).getTime()-new Date(t.createdAt).getTime()),0)/t.length/36e5}(e),revenue:function(e){let t={};return e.filter(e=>"completed"===e.status).forEach(e=>{let s=e.pricing.currency;t[s]=(t[s]||0)+e.pricing.totalPrice}),t}(e)}}function i(e){return[]}s.d(t,{FK:()=>i,Ls:()=>o,NC:()=>n,ND:()=>a,Nn:()=>r,zt:()=>l}),s(89878);function o(e){}},63887:(e,t,s)=>{"use strict";s.d(t,{CurrencyProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call CurrencyProvider() from the server but CurrencyProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\contexts\\CurrencyContext.tsx","CurrencyProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useCurrency() from the server but useCurrency is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\contexts\\CurrencyContext.tsx","useCurrency"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useCurrencyConverter() from the server but useCurrencyConverter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\contexts\\CurrencyContext.tsx","useCurrencyConverter")},68988:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687),a=s(43210),l=s(96241);let n=a.forwardRef(({className:e,type:t,...s},a)=>(0,r.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));n.displayName="Input"},69327:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(60687),a=s(43210),l=s(68123),n=s(96241);let i=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(l.bL,{ref:a,className:(0,n.cn)("relative overflow-hidden",e),...s,children:[(0,r.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,r.jsx)(o,{}),(0,r.jsx)(l.OK,{})]}));i.displayName=l.bL.displayName;let o=a.forwardRef(({className:e,orientation:t="vertical",...s},a)=>(0,r.jsx)(l.VM,{ref:a,orientation:t,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...s,children:(0,r.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})}));o.displayName=l.VM.displayName},70338:(e,t,s)=>{"use strict";s.d(t,{GlobalChatProvider:()=>o,X:()=>i});var r=s(60687),a=s(43210),l=s(45468);let n=(0,a.createContext)(null);function i(){let e=(0,a.useContext)(n);return e||{user:null,isEnabled:!1,isOpen:!1,setUser:()=>{},setEnabled:()=>{},openChat:()=>{},closeChat:()=>{}}}function o({children:e}){let[t,s]=(0,a.useState)(null),[i,o]=(0,a.useState)(!0),[d,c]=(0,a.useState)(!1),[m,u]=(0,a.useState)(!1),x=()=>c(!1);return m?(0,r.jsxs)(n.Provider,{value:{user:t,isEnabled:i,isOpen:d,setUser:s,setEnabled:o,openChat:()=>c(!0),closeChat:x},children:[e,t&&"admin"!==t.role&&(0,r.jsx)(l.k,{isOpen:d,onClose:x,userId:t.id,userName:t.name,userEmail:t.email,userRole:"customer",position:"center"})]}):(0,r.jsx)(r.Fragment,{children:e})}},79891:(e,t,s)=>{"use strict";s.d(t,{DataProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call DataProvider() from the server but DataProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\components\\providers\\DataProvider.tsx","DataProvider")},82704:()=>{},86775:(e,t,s)=>{Promise.resolve().then(s.bind(s,7396)),Promise.resolve().then(s.bind(s,79891)),Promise.resolve().then(s.bind(s,63887)),Promise.resolve().then(s.bind(s,6931))},87335:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},89878:(e,t,s)=>{"use strict";s.d(t,{Tr:()=>o}),s(21083);class r{set(e,t,s){let r=`${e}-${t}`;this.cache.set(r,{rate:s,timestamp:new Date})}get(e,t){let s=`${e}-${t}`,r=this.cache.get(s);return r?Date.now()-r.timestamp.getTime()>this.CACHE_DURATION?(this.cache.delete(s),null):r.rate:null}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map,this.CACHE_DURATION=3e5}}let a=new r;async function l(e,t,s=!0){if(e===t)return 1;if(s){let s=a.get(e,t);if(null!==s)return s}try{let r=await i(e,t);return s&&a.set(e,t,r),r}catch(s){throw console.error(`Failed to get exchange rate for ${e} to ${t}:`,s),Error(`Exchange rate not available for ${e} to ${t}`)}}async function n(e,t,s){let r=await l(t,s);return{convertedAmount:e*r,exchangeRate:r,timestamp:new Date}}async function i(e,t){let s={USD:{USD:1,SDG:450,EGP:30.8,SAR:3.75,AED:3.67,EUR:.85,GBP:.73},SDG:{USD:1/450,SDG:1,EGP:30.8/450},EGP:{USD:1/30.8,EGP:1,SDG:450/30.8}},r=s[e]?.[t];if(void 0===r){let r=s.USD?.[e],a=s.USD?.[t];if(r&&a)return a/r;throw Error(`Exchange rate not found for ${e} to ${t}`)}return r}async function o(e,t,s){let r=await n(e,t,s);return{originalAmount:e,originalCurrency:t,convertedAmount:r.convertedAmount,targetCurrency:s,exchangeRate:r.exchangeRate,conversionFee:0,timestamp:r.timestamp}}},94511:(e,t,s)=>{"use strict";function r(e){let t=new Date(e);if(isNaN(t.getTime()))return"تاريخ غير صحيح";let s=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0"),l=String(t.getHours()).padStart(2,"0"),n=String(t.getMinutes()).padStart(2,"0");return`${a}/${r}/${s} ${l}:${n}`}function a(e){let t=new Date(e);if(isNaN(t.getTime()))return"وقت غير صحيح";let s=String(t.getHours()).padStart(2,"0"),r=String(t.getMinutes()).padStart(2,"0");return`${s}:${r}`}s.d(t,{Yq:()=>function e(t,s){let l=new Date(t);if(isNaN(l.getTime()))return"تاريخ غير صحيح";switch(s){case"time":return a(l);case"datetime":return r(l);case"relative":return function(t){let s=new Date(t),r=new Date().getTime()-s.getTime(),a=Math.floor(r/6e4),l=Math.floor(r/36e5),n=Math.floor(r/864e5);return a<1?"الآن":a<60?`منذ ${a} دقيقة`:l<24?`منذ ${l} ساعة`:n<30?`منذ ${n} يوم`:e(s)}(l);default:let n=l.getFullYear(),i=String(l.getMonth()+1).padStart(2,"0"),o=String(l.getDate()).padStart(2,"0");return`${o}/${i}/${n}`}},fU:()=>a,r6:()=>r})},94914:(e,t,s)=>{"use strict";s.d(t,{DataProvider:()=>i});var r=s(60687),a=s(43210),l=s(34991);function n({children:e,fallback:t=null}){let[s,l]=(0,a.useState)(!1);return s?(0,r.jsx)(r.Fragment,{children:e}):(0,r.jsx)(r.Fragment,{children:t})}function i({children:e}){return(0,r.jsx)(n,{fallback:(0,r.jsx)(d,{}),children:(0,r.jsx)(o,{children:e})})}function o({children:e}){let t=(0,l.zB)(),s=(0,l.rU)(),a=(0,l.NN)();(0,l.cd)();let n=(0,l.R$)();return t&&!a?(0,r.jsx)(d,{}):s&&!a?(0,r.jsx)(c,{error:s,onRetry:n}):(0,r.jsxs)(r.Fragment,{children:[e,(0,r.jsx)(m,{})]})}function d(){return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-6",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"w-20 h-20 mx-auto bg-yellow-400 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-2xl font-bold text-slate-900",children:"رايه"})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-yellow-400",children:"جاري تحميل البيانات..."}),(0,r.jsx)("p",{className:"text-slate-300 text-sm",children:"يتم تحضير جميع البيانات للاستخدام بدون اتصال"})]}),(0,r.jsxs)("div",{className:"flex justify-center space-x-2 rtl:space-x-reverse",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-100"}),(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-200"})]})]})})}function c({error:e,onRetry:t}){return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-6 max-w-md mx-auto px-4",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"w-20 h-20 mx-auto bg-red-500 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-2xl",children:"⚠️"})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-red-400",children:"فشل في تحميل البيانات"}),(0,r.jsx)("p",{className:"text-slate-300 text-sm",children:e})]}),(0,r.jsx)("button",{onClick:t,className:"bg-yellow-400 hover:bg-yellow-500 text-slate-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-200",children:"إعادة المحاولة"}),(0,r.jsx)("p",{className:"text-slate-400 text-xs",children:"تأكد من اتصالك بالإنترنت وحاول مرة أخرى"})]})})}function m(){let e=(0,l.rU)();return e?.includes("offline")||e?.includes("cached")?(0,r.jsx)("div",{className:"fixed top-0 left-0 right-0 bg-orange-500 text-white text-center py-2 text-sm z-50",children:(0,r.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("span",{children:"\uD83D\uDCE1"}),(0,r.jsx)("span",{children:"وضع عدم الاتصال - يتم استخدام البيانات المحفوظة"})]})}):null}},96241:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(49384),a=s(82348);function l(...e){return(0,a.QP)((0,r.$)(e))}}};