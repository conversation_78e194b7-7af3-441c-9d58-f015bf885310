import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Plus, Pencil, Trash2, Save, X, Sparkles, List, Percent, Tag, Trash, Image as ImageIcon, Settings } from "lucide-react";
import { 
  UCPackageModel, 
  addUCPackage, 
  updateUCPackage, 
  getUCPackages, 
  deleteUCPackage,
  UCStoreSettingsModel,
  getUCStoreSettings,
  updateUCStoreSettings
} from "@/services/firestore";
import { parseUCPackageDetails, getSavedModel } from "@/services/gemini";
import ModelSelector from "./ModelSelector";
import ImageUploader from "./ImageUploader";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { number } from "zod";
import { getUserFriendlyErrorMessage } from "@/lib/utils";
import { parseUCPackageFromAI } from "@/lib/ai-helpers";

// Default image for UC packages when none is provided
const DEFAULT_UC_IMAGE = "https://media.fab.com/image_previews/gallery_images/290b6414-e320-45c5-bb96-98d780ae2601/d27cce68-7be5-446a-beb7-b674b122f93b.jpeg";

// Default image for UC store hero section
const DEFAULT_UC_HERO_IMAGE = "/images/uc-store-default-bg.jpg";

// Extended UC Package Model
interface ExtendedUCPackageModel extends UCPackageModel {
  priceLocal?: number | string;
  localCurrencySymbol?: string;
  originalPriceUSD?: number | string;
  originalPriceLocal?: number | string;
  discountUSD?: number | string;
  discountLocal?: number | string;
  highlights?: string[];
  category?: string;
  description?: string;
  description_en?: string;
  notes?: string[];
  notes_en?: string[];
  // Legacy fields for backward compatibility
  originalPrice?: number;
  discountPercent?: number;
  localCurrencyCode?: string;
}

// Extended UC Package Model for the form
interface FormUCPackageModel {
  amount: number | string;
  priceUSD: number | string;
  priceLocal: number | string;
  localCurrencySymbol: string;
  image: string;
  featured: boolean;
  originalPriceUSD: number | string;
  originalPriceLocal: number | string;
  discountUSD: number | string;
  discountLocal: number | string;
  highlights?: string[];
  category?: string;
  id?: string;
  description?: string;
  description_en?: string;
  notes?: string[];
  notes_en?: string[];
}

// Create a custom clearable input component
const ClearableInput = ({ 
  value, 
  onChange, 
  onClear, 
  ...props 
}: { 
  value: string | number; 
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; 
  onClear: () => void; 
} & React.InputHTMLAttributes<HTMLInputElement>) => {
  return (
    <div className="relative">
      <Input
        value={value}
        onChange={onChange}
        className="pr-8"
        {...props}
      />
      {value && (
        <button
          type="button"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-5 w-5 rounded-full bg-gray-500/20 flex items-center justify-center text-gray-400 hover:bg-gray-500/30 hover:text-gray-100"
          onClick={onClear}
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </div>
  );
};

const UCPackagesManager = () => {
  const { toast } = useToast();
  const [packages, setPackages] = useState<ExtendedUCPackageModel[]>([]);
  const [selectedPackages, setSelectedPackages] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [editingPackage, setEditingPackage] = useState<ExtendedUCPackageModel | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showBulkAddDialog, setShowBulkAddDialog] = useState(false);
  const [telegramPost, setTelegramPost] = useState("");
  const [isAiProcessing, setIsAiProcessing] = useState(false);
  const [bulkUCData, setBulkUCData] = useState("");
  const [isBulkProcessing, setIsBulkProcessing] = useState(false);
  const [highlightInput, setHighlightInput] = useState("");
  const [selectedModel, setSelectedModel] = useState(getSavedModel());
  const [localCurrencyCode, setLocalCurrencyCode] = useState<string>("EGP");
  const [exchangeRate, setExchangeRate] = useState<number>(31.0);
  // State for the active tab
  const [activeTab, setActiveTab] = useState("packages");
  // Add state for UC Store settings
  const [ucStoreSettings, setUCStoreSettings] = useState<UCStoreSettingsModel>({
    heroImage: "https://static.vecteezy.com/system/resources/previews/007/684/902/non_2x/esports-gaming-banner-background-design-with-abstract-shape-vector.jpg",
    heroTitle: "شحن رصيد UC للعبة ببجي",
    heroSubtitle: "أسهل وأسرع طريقة لشحن رصيد UC للعبة ببجي - اختر الباقة المناسبة لك"
  });
  // Update formData to use the new interface
  const [formData, setFormData] = useState<FormUCPackageModel>({
    amount: "",
    priceUSD: "",
    priceLocal: "",
    localCurrencySymbol: "EGP",
    image: "",
    featured: false,
    originalPriceUSD: "",
    originalPriceLocal: "",
    discountUSD: "",
    discountLocal: "",
    highlights: ["instant_delivery", "support", "secure_payment"],
    category: "Gaming",
    description: "",
    description_en: "",
    notes: ["", "", ""],
    notes_en: ["", "", ""]
  });

  const location = useLocation();
  const navigate = useNavigate();
  
  // Get the UC tab from URL query params or default to "packages"
  const getUCTabFromUrl = () => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('uc_section');
    return tab === 'settings' ? 'settings' : 'packages';
  };
  
  // Initialize activeTab from URL
  useEffect(() => {
    setActiveTab(getUCTabFromUrl());
  }, []);
  
  // Update URL when tab changes
  const handleTabChange = (value) => {
    setActiveTab(value);
    const params = new URLSearchParams(location.search);
    
    // Keep the main tab parameter
    const mainTab = params.get('tab') || 'uc';
    
    // Update section parameter
    params.set('tab', mainTab);
    params.set('uc_section', value);
    
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });
  };
  
  // Update active tab if URL changes
  useEffect(() => {
    const newTab = getUCTabFromUrl();
    if (newTab !== activeTab) {
      setActiveTab(newTab);
    }
  }, [location.search]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log("Fetching UC packages data...");
        const packagesData = await getUCPackages();
        console.log("UC packages data:", packagesData);
        setPackages(packagesData);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching UC packages:", error);
        toast({
          title: "Error",
          description: "Failed to load UC packages data: " + (error instanceof Error ? error.message : String(error)),
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Add useEffect for fetching UC Store settings
  useEffect(() => {
    const fetchUCStoreSettings = async () => {
      try {
        const settings = await getUCStoreSettings();
        if (settings) {
          // Set default hero image if none is provided
          if (!settings.heroImage || settings.heroImage.trim() === '') {
            settings.heroImage = DEFAULT_UC_HERO_IMAGE;
          }
          setUCStoreSettings(settings);
        }
      } catch (error) {
        console.error("Error fetching UC Store settings:", error);
        toast({
          title: "خطأ",
          description: "فشل في تحميل إعدادات متجر UC",
          variant: "destructive",
        });
      }
    };

    fetchUCStoreSettings();
  }, [toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      setFormData({ ...formData, [name]: (e.target as HTMLInputElement).checked });
    } else if (type === "number") {
      const numValue = Number(value);
      setFormData({ ...formData, [name]: numValue });
      
      // Handle currency conversions
      if (name === "priceUSD") {
        updateLocalPrice(numValue);
      } else if (name === "priceEGP") {
        updatePriceUSD(numValue);
      }
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleTelegramPostChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTelegramPost(e.target.value);
  };

  const handleBulkUCDataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setBulkUCData(e.target.value);
  };

  const handleProcessWithAI = async () => {
    if (!telegramPost.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى لصق محتوى منشور تيليجرام أولاً",
        variant: "destructive",
      });
      return;
    }

    setIsAiProcessing(true);
    try {
      const parsedData = await parseUCPackageDetails(telegramPost, selectedModel);
      setFormData({
        ...formData,
        ...parsedData,
        image: parsedData.image || DEFAULT_UC_IMAGE, // Use default image if none provided
      });
      
      toast({
        title: "تم بنجاح",
        description: "تم معالجة المنشور وملء النموذج بالبيانات",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في المعالجة بالذكاء الاصطناعي: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setIsAiProcessing(false);
    }
  };

  const handleHighlightAdd = () => {
    if (highlightInput.trim() !== "") {
      setFormData({
        ...formData,
        highlights: [...(formData.highlights || []), highlightInput.trim()]
      });
      setHighlightInput("");
    }
  };

  const handleHighlightRemove = (index: number) => {
    const newHighlights = [...(formData.highlights || [])];
    newHighlights.splice(index, 1);
    setFormData({ ...formData, highlights: newHighlights });
  };

  const calculateDiscountFromOriginal = () => {
    // Calculate discount for USD
    const originalPriceUSD = typeof formData.originalPriceUSD === 'string' ? parseFloat(formData.originalPriceUSD) : formData.originalPriceUSD;
    const priceUSD = typeof formData.priceUSD === 'string' ? parseFloat(formData.priceUSD) : formData.priceUSD;
    
    if (originalPriceUSD && originalPriceUSD > 0 && priceUSD && priceUSD > 0) {
      const discountUSD = Math.round(((originalPriceUSD - priceUSD) / originalPriceUSD) * 100);
      setFormData(prev => ({ ...prev, discountUSD: discountUSD.toString() }));
    }
    
    // Calculate discount for local currency
    const originalPriceLocal = typeof formData.originalPriceLocal === 'string' ? parseFloat(formData.originalPriceLocal) : formData.originalPriceLocal;
    const priceLocal = typeof formData.priceLocal === 'string' ? parseFloat(formData.priceLocal) : formData.priceLocal;
    
    if (originalPriceLocal && originalPriceLocal > 0 && priceLocal && priceLocal > 0) {
      const discountLocal = Math.round(((originalPriceLocal - priceLocal) / originalPriceLocal) * 100);
      setFormData(prev => ({ ...prev, discountLocal: discountLocal.toString() }));
    }
  };

  const calculateOriginalFromDiscount = () => {
    // Calculate original price from USD discount
    const discountUSD = typeof formData.discountUSD === 'string' ? parseFloat(formData.discountUSD) : formData.discountUSD;
    const priceUSD = typeof formData.priceUSD === 'string' ? parseFloat(formData.priceUSD) : formData.priceUSD;
    
    if (discountUSD && discountUSD > 0 && priceUSD && priceUSD > 0) {
      const originalPriceUSD = Math.round((priceUSD / (1 - discountUSD / 100)) * 100) / 100;
      setFormData(prev => ({ ...prev, originalPriceUSD: originalPriceUSD.toString() }));
    }
    
    // Calculate original price from local currency discount
    const discountLocal = typeof formData.discountLocal === 'string' ? parseFloat(formData.discountLocal) : formData.discountLocal;
    const priceLocal = typeof formData.priceLocal === 'string' ? parseFloat(formData.priceLocal) : formData.priceLocal;
    
    if (discountLocal && discountLocal > 0 && priceLocal && priceLocal > 0) {
      const originalPriceLocal = Math.round((priceLocal / (1 - discountLocal / 100)) * 100) / 100;
      setFormData(prev => ({ ...prev, originalPriceLocal: originalPriceLocal.toString() }));
    }
  };

  const handleLocalCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalCurrencyCode(value);
  };

  const handleExchangeRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rate = parseFloat(e.target.value);
    if (!isNaN(rate) && rate > 0) {
      setExchangeRate(rate);
    }
  };

  const convertUSDtoLocal = (amount: number | string): number => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return Math.round((numAmount || 0) * exchangeRate * 100) / 100;
  };

  const convertLocalToUSD = (amount: number | string): number => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return Math.round(((numAmount || 0) / exchangeRate) * 100) / 100;
  };

  const updatePriceUSD = (localPrice: number | string) => {
    const usdPrice = convertLocalToUSD(localPrice);
    setFormData(prev => ({
      ...prev,
      priceUSD: usdPrice
    }));
  };

  const updateLocalPrice = (usdPrice: number | string) => {
    const localPrice = convertUSDtoLocal(usdPrice);
    setFormData(prev => ({
      ...prev,
      priceEGP: localPrice
    }));
  };

  const handleProcessBulkUCData = async () => {
    if (!bulkUCData.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال قائمة بأسعار وكميات UC أولاً",
        variant: "destructive",
      });
      return;
    }

    setIsBulkProcessing(true);
    try {
      // Split the input by new lines to process each line
      const lines = bulkUCData.trim().split('\n').filter(line => line.trim() !== '');
      
      if (lines.length === 0) {
        throw new Error("لم يتم العثور على بيانات صالحة للمعالجة");
      }

      const createdPackages: ExtendedUCPackageModel[] = [];
      const errors: string[] = [];

      // Process each line
      for (const [index, line] of lines.entries()) {
        try {
          // Priority 1: Process format "XXX UC = YYY EGP" or "XXXUC=YYYج"
          const equalFormat = line.match(/(\d+)\s*UC\s*=\s*(\d+)/i) || line.match(/(\d+)\s*=\s*(\d+)/i);
          
          if (equalFormat && equalFormat.length >= 3) {
            const amount = parseInt(equalFormat[1]);
            const priceLocal = parseInt(equalFormat[2]);
            
            if (amount <= 0 || priceLocal <= 0) {
              errors.push(`السطر ${index + 1}: قيم غير صالحة`);
              continue;
            }
            
            // Convert local to USD using the current exchange rate
            const priceUSD = convertLocalToUSD(priceLocal);
            
            // Generate dynamic marketing data based on package size
            const originalPriceLocal = Math.round(priceLocal * (1 + (Math.random() * 0.3 + 0.1))); // 10-40% higher
            const originalPriceUSD = Math.round((originalPriceLocal / exchangeRate) * 100) / 100;
            const discountLocal = Math.round(((originalPriceLocal - priceLocal) / originalPriceLocal) * 100);
            const discountUSD = discountLocal; // Same discount percentage for both currencies
            
            // Generate appropriate highlight for different sized packages
            let packageSpecificHighlight = "";
            if (amount < 300) {
              packageSpecificHighlight = "Best for beginners";
            } else if (amount < 1000) {
              packageSpecificHighlight = "Most popular choice";
            } else if (amount < 3000) {
              packageSpecificHighlight = "Great value package";
            } else {
              packageSpecificHighlight = "Premium UC bundle";
            }
            
            const newPackage = {
              amount,
              priceLocal,
              priceUSD,
              localCurrencySymbol: localCurrencyCode,
              image: DEFAULT_UC_IMAGE,
              featured: amount > 1000, // Feature larger packages
              originalPriceLocal,
              originalPriceUSD,
              discountLocal,
              discountUSD,
              highlights: ["Instant delivery", "24/7 support", "Secure payment", packageSpecificHighlight],
              category: "Gaming"
            };
            
            const id = await addUCPackage(newPackage as Omit<ExtendedUCPackageModel, "id" | "createdAt" | "updatedAt">);
            createdPackages.push({ ...newPackage, id } as ExtendedUCPackageModel);
            continue;
          }
          
          // Priority 2: Process format "600 UC - 50 EGP" or similar
          const ucMatch = line.match(/(\d+)\s*UC/i);
          const egpMatch = line.match(/(\d+)\s*(?:EGP|ج)/i);
          
          if (ucMatch && egpMatch) {
            const amount = parseInt(ucMatch[1]);
            const priceEGP = parseInt(egpMatch[1]);
            
            if (amount <= 0 || priceEGP <= 0) {
              errors.push(`السطر ${index + 1}: قيم غير صالحة`);
              continue;
            }
            
            // Simple USD conversion (approximate)
            const priceUSD = Math.round((priceEGP / 31) * 100) / 100;
            
            // Generate dynamic marketing data based on package size
            const originalPrice = Math.round(priceEGP * (1 + (Math.random() * 0.3 + 0.1))); // 10-40% higher
            const discountPercent = Math.round(((originalPrice - priceEGP) / originalPrice) * 100);
            
            // Generate appropriate highlight for different sized packages
            let packageSpecificHighlight = "";
            if (amount < 300) {
              packageSpecificHighlight = "Best for beginners";
            } else if (amount < 1000) {
              packageSpecificHighlight = "Most popular choice";
            } else if (amount < 3000) {
              packageSpecificHighlight = "Great value package";
            } else {
              packageSpecificHighlight = "Premium UC bundle";
            }
            
            const newPackage = {
              amount,
              priceEGP,
              priceUSD,
              image: DEFAULT_UC_IMAGE,
              featured: amount > 1000, // Feature larger packages
              originalPrice,
              discountPercent,
              highlights: ["Instant delivery", "24/7 support", "Secure payment", packageSpecificHighlight],
              category: "Gaming"
            };
            
            const id = await addUCPackage(newPackage as Omit<ExtendedUCPackageModel, "id" | "createdAt" | "updatedAt">);
            createdPackages.push({ ...newPackage, id } as ExtendedUCPackageModel);
            continue;
          }
          
          // Priority 3: Try to parse simple numbers: first number is amount, second is price
          const numbers = line.match(/\d+/g);
          if (numbers && numbers.length >= 2) {
            const amount = parseInt(numbers[0]);
            const priceEGP = parseInt(numbers[1]);
            
            // Simple USD conversion (approximate)
            const priceUSD = Math.round((priceEGP / 31) * 100) / 100;
            
            // Generate dynamic marketing data based on package size
            const originalPrice = Math.round(priceEGP * (1 + (Math.random() * 0.3 + 0.1))); // 10-40% higher
            const discountPercent = Math.round(((originalPrice - priceEGP) / originalPrice) * 100);
            
            // Generate appropriate highlight for different sized packages
            let packageSpecificHighlight = "";
            if (amount < 300) {
              packageSpecificHighlight = "Best for beginners";
            } else if (amount < 1000) {
              packageSpecificHighlight = "Most popular choice";
            } else if (amount < 3000) {
              packageSpecificHighlight = "Great value package";
            } else {
              packageSpecificHighlight = "Premium UC bundle";
            }
            
            const newPackage = {
              amount,
              priceEGP,
              priceUSD,
              image: DEFAULT_UC_IMAGE,
              featured: amount > 1000, // Feature larger packages
              originalPrice,
              discountPercent,
              highlights: ["Instant delivery", "24/7 support", "Secure payment", packageSpecificHighlight],
              category: "Gaming"
            };
            
            const id = await addUCPackage(newPackage as Omit<ExtendedUCPackageModel, "id" | "createdAt" | "updatedAt">);
            createdPackages.push({ ...newPackage, id } as ExtendedUCPackageModel);
          } else {
            errors.push(`السطر ${index + 1}: تنسيق غير صالح`);
          }
        } catch (error) {
          errors.push(`السطر ${index + 1}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      // Update the packages state with the newly created packages
      if (createdPackages.length > 0) {
        setPackages([...packages, ...createdPackages]);
        toast({
          title: "نجاح",
          description: `تم إضافة ${createdPackages.length} باقة بنجاح`,
        });
        setBulkUCData("");
        setShowBulkAddDialog(false);
      } else {
        toast({
          title: "تحذير",
          description: "لم يتم إنشاء أي باقات. تحقق من تنسيق البيانات",
          variant: "destructive",
        });
      }

      if (errors.length > 0) {
        console.error("Bulk processing errors:", errors);
        toast({
          title: "تحذير",
          description: `حدثت ${errors.length} أخطاء أثناء المعالجة`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في معالجة البيانات: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setIsBulkProcessing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log("Submitting form data:", formData);
      
      // Process the formData to ensure all number fields are actually numbers before submitting
      const processedData: any = {
        amount: Number(formData.amount),
        priceUSD: Number(formData.priceUSD),
        priceLocal: Number(formData.priceLocal),
        localCurrencySymbol: formData.localCurrencySymbol,
        image: formData.image && formData.image.trim() !== "" ? formData.image : DEFAULT_UC_IMAGE,
        featured: formData.featured,
        highlights: formData.highlights,
        category: formData.category
      };
      
      // Add optional fields only if they have values
      if (formData.originalPriceUSD) {
        processedData.originalPriceUSD = Number(formData.originalPriceUSD);
      }
      
      if (formData.originalPriceLocal) {
        processedData.originalPriceLocal = Number(formData.originalPriceLocal);
      }
      
      if (formData.discountUSD) {
        processedData.discountUSD = Number(formData.discountUSD);
      }
      
      if (formData.discountLocal) {
        processedData.discountLocal = Number(formData.discountLocal);
      }
      
      // Add description and notes if they have values
      if (formData.description && formData.description.trim() !== "") {
        processedData.description = formData.description.trim();
      }
      
      if (formData.description_en && formData.description_en.trim() !== "") {
        processedData.description_en = formData.description_en.trim();
      }
      
      if (formData.notes && formData.notes.some(note => note.trim() !== "")) {
        processedData.notes = formData.notes.filter(note => note.trim() !== "");
      }
      
      if (formData.notes_en && formData.notes_en.some(note => note.trim() !== "")) {
        processedData.notes_en = formData.notes_en.filter(note => note.trim() !== "");
      }
      
      if (editingPackage) {
        // Update existing package
        console.log(`Updating UC package with ID: ${editingPackage.id}`);
        await updateUCPackage(editingPackage.id!, processedData);
        console.log("UC package updated successfully");
        
        // Update local state
        setPackages(
          packages.map((pkg) =>
            pkg.id === editingPackage.id
              ? { ...pkg, ...processedData }
              : pkg
          )
        );
        
        toast({
          title: "Success",
          description: "UC package updated successfully",
        });
        
        setEditingPackage(null);
      } else {
        // Add new package
        console.log("Adding new UC package");
        const id = await addUCPackage(processedData as Omit<ExtendedUCPackageModel, "id" | "createdAt" | "updatedAt">);
        console.log(`New UC package created with ID: ${id}`);
        
        // Update local state
        setPackages([...packages, { ...processedData, id } as ExtendedUCPackageModel]);
        
        toast({
          title: "Success",
          description: "UC package added successfully",
        });
        
        setShowAddDialog(false);
      }
      
      // Reset form
      setFormData({
        amount: "",
        priceUSD: "",
        priceLocal: "",
        localCurrencySymbol: "EGP",
        image: "",
        featured: false,
        originalPriceUSD: "",
        originalPriceLocal: "",
        discountUSD: "",
        discountLocal: "",
        highlights: ["Instant delivery", "24/7 support", "Secure payment"],
        category: "Gaming"
      });
      setTelegramPost("");
    } catch (error) {
      console.error("Error adding/updating UC package:", error);
      toast({
        title: "Error",
        description: "Failed to save UC package: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  const handleEdit = (ucPackage: ExtendedUCPackageModel) => {
    setEditingPackage(ucPackage);
    
    // Ensure notes arrays have at least 3 items
    const ensureNotesLength = (notes: string[] | undefined): string[] => {
      if (!notes || notes.length === 0) {
        return ["", "", ""];
      }
      
      // Make a copy to avoid modifying the original
      const notesCopy = [...notes];
      
      // Ensure we have at least 3 notes
      while (notesCopy.length < 3) {
        notesCopy.push("");
      }
      
      return notesCopy;
    };
    
    // Ensure highlights are properly formatted (lowercase with underscores)
    const formatHighlights = (highlights: string[] | undefined): string[] => {
      if (!highlights || highlights.length === 0) {
        return ["instant_delivery", "support", "secure_payment"];
      }
      
      return highlights.map(highlight => {
        // Convert to lowercase and replace spaces with underscores
        const formatted = highlight.toLowerCase().replace(/ /g, '_');
        
        // If it's one of our standard highlights, use the standard format
        if (formatted === "instant_delivery" || formatted === "24/7_support" || formatted === "24/7support") {
          return "instant_delivery";
        } else if (formatted === "support" || formatted === "24/7_support" || formatted === "24/7support") {
          return "support";
        } else if (formatted === "secure_payment" || formatted === "secure" || formatted === "payment") {
          return "secure_payment";
        }
        
        return formatted;
      });
    };
    
    setFormData({
      id: ucPackage.id,
      amount: ucPackage.amount.toString(),
      priceUSD: ucPackage.priceUSD.toString(),
      priceLocal: ucPackage.priceLocal?.toString() || ucPackage.priceEGP?.toString() || "",
      localCurrencySymbol: ucPackage.localCurrencySymbol || ucPackage.localCurrencyCode || "EGP",
      image: ucPackage.image || "",
      featured: ucPackage.featured || false,
      originalPriceUSD: ucPackage.originalPriceUSD?.toString() || "",
      originalPriceLocal: ucPackage.originalPriceLocal?.toString() || ucPackage.originalPrice?.toString() || "",
      discountUSD: ucPackage.discountUSD?.toString() || "",
      discountLocal: ucPackage.discountLocal?.toString() || ucPackage.discountPercent?.toString() || "",
      highlights: formatHighlights(ucPackage.highlights),
      category: ucPackage.category || "Gaming",
      description: ucPackage.description || "",
      description_en: ucPackage.description_en || "",
      notes: ensureNotesLength(ucPackage.notes),
      notes_en: ensureNotesLength(ucPackage.notes_en)
    });
    
    setShowAddDialog(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this UC package?")) {
      setIsLoading(true);
      
      try {
        await deleteUCPackage(id);
        
        // Update local state
        setPackages(packages.filter((pkg) => pkg.id !== id));
        
        toast({
          title: "Success",
          description: "UC package deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete UC package",
          variant: "destructive",
        });
      }
      
      setIsLoading(false);
    }
  };

  const handleSelectPackage = (id: string) => {
    setSelectedPackages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedPackages.size === packages.length) {
      setSelectedPackages(new Set());
    } else {
      setSelectedPackages(new Set(packages.map(pkg => pkg.id!)));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedPackages.size === 0) return;

    if (window.confirm(`هل أنت متأكد من حذف ${selectedPackages.size} باقة UC؟`)) {
      setIsLoading(true);
      const errors: string[] = [];

      try {
        for (const id of selectedPackages) {
          try {
            await deleteUCPackage(id);
          } catch (error) {
            errors.push(`Failed to delete package ${id}: ${error instanceof Error ? error.message : String(error)}`);
          }
        }

        // Update local state by removing deleted packages
        setPackages(packages.filter(pkg => !selectedPackages.has(pkg.id!)));
        setSelectedPackages(new Set());

        if (errors.length === 0) {
          toast({
            title: "نجاح",
            description: `تم حذف ${selectedPackages.size} باقة UC بنجاح`,
          });
        } else {
          toast({
            title: "تحذير",
            description: `تم حذف بعض الباقات مع وجود ${errors.length} أخطاء`,
            variant: "destructive",
          });
          console.error("Bulk delete errors:", errors);
        }
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في حذف الباقات المحددة",
          variant: "destructive",
        });
      }

      setIsLoading(false);
    }
  };

  // Add handler for UC Store settings changes
  const handleUCStoreSettingChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setUCStoreSettings(prev => ({ ...prev, [name]: value }));
  };

  // Add handler for saving UC Store settings
  const handleSaveUCStoreSettings = async () => {
    try {
      // Ensure heroImage is not empty, set default if it is
      if (!ucStoreSettings.heroImage || ucStoreSettings.heroImage.trim() === '') {
        setUCStoreSettings(prev => ({
          ...prev,
          heroImage: DEFAULT_UC_HERO_IMAGE
        }));
      }
      
      await updateUCStoreSettings(ucStoreSettings);
      toast({
        title: "تم بنجاح",
        description: "تم تحديث إعدادات متجر UC",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث إعدادات متجر UC: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    }
  };

  const handleBulkUCDataSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await handleProcessBulkUCData();
    setShowBulkAddDialog(false);
  };

  const resetFormAndClose = () => {
    setFormData({
      amount: "",
      priceUSD: "",
      priceLocal: "",
      localCurrencySymbol: "EGP",
      image: "",
      featured: false,
      originalPriceUSD: "",
      originalPriceLocal: "",
      discountUSD: "",
      discountLocal: "",
      highlights: ["instant_delivery", "support", "secure_payment"],
      category: "Gaming",
      description: "",
      description_en: "",
      notes: ["", "", ""],
      notes_en: ["", "", ""]
    });
    setTelegramPost("");
    setEditingPackage(null);
    setShowAddDialog(false);
  };

  if (isLoading && packages.length === 0) {
    return <div className="text-center py-8">Loading UC packages data...</div>;
  }

  return (
    <div>
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="packages">
            <List className="ml-2 h-4 w-4" />
            الباقات
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="ml-2 h-4 w-4" />
            إعدادات صفحة المتجر
          </TabsTrigger>
        </TabsList>

        <TabsContent value="packages">
          <div className="flex justify-between items-center mb-6 flex-col sm:flex-row gap-4">
            <h2 className="text-xl font-bold text-white">إدارة باقات UC</h2>
            <div className="flex flex-wrap gap-2 justify-end w-full sm:w-auto">
              <div className="flex flex-wrap items-center gap-2 ml-0 sm:ml-4 mr-auto">
                <div className="flex items-center">
                  <Label htmlFor="localCurrencyCode" className="text-sm whitespace-nowrap">العملة المحلية:</Label>
                  <Input
                    id="localCurrencyCode"
                    className="w-16 text-center h-8 mx-1"
                    value={localCurrencyCode}
                    onChange={handleLocalCurrencyChange}
                  />
                </div>
                <div className="flex items-center">
                  <Label htmlFor="exchangeRate" className="text-sm whitespace-nowrap mr-0 sm:mr-2">سعر الصرف (1$ =)</Label>
                  <Input
                    id="exchangeRate"
                    type="number"
                    className="w-16 text-center h-8"
                    value={exchangeRate}
                    onChange={handleExchangeRateChange}
                    step="0.01"
                    min="0.01"
                  />
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedPackages.size > 0 && (
                  <Button
                    onClick={handleBulkDelete}
                    className="bg-red-500 text-white hover:bg-red-600 h-8 px-2 py-1 text-xs sm:text-sm"
                    disabled={isLoading}
                  >
                    <Trash className="ml-1 h-3 w-3" />
                    حذف {selectedPackages.size}
                  </Button>
                )}
                <Button
                  onClick={() => setShowBulkAddDialog(true)}
                  className="bg-pubg-gray text-white hover:bg-pubg-gray/90 h-8 px-2 py-1 text-xs sm:text-sm"
                >
                  <List className="ml-1 h-3 w-3" />
                  إضافة متعددة
                </Button>
                <Button
                  onClick={() => setShowAddDialog(true)}
                  className="bg-pubg-gray text-white hover:bg-pubg-gray/90 h-8 px-2 py-1 text-xs sm:text-sm"
                >
                  <Plus className="ml-1 h-3 w-3" />
                  إضافة باقة
                </Button>
              </div>
            </div>
          </div>

          {/* UC Packages List */}
          <div className="space-y-4">
            {packages.length > 0 ? (
              <>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedPackages.size === packages.length}
                      onChange={handleSelectAll}
                      className="ml-2 w-4 h-4"
                    />
                    <span className="text-sm text-muted-foreground">
                      {selectedPackages.size > 0
                        ? `تم تحديد ${selectedPackages.size} باقة`
                        : "تحديد الكل"}
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4">
                  {packages.map((pkg) => (
                    <div
                      key={pkg.id}
                      className="glass-card rounded-lg p-3 flex flex-col"
                    >
                      <div className="flex items-center mb-2">
                        <input
                          type="checkbox"
                          checked={selectedPackages.has(pkg.id!)}
                          onChange={() => handleSelectPackage(pkg.id!)}
                          className="ml-2 w-4 h-4"
                        />
                        <div className="w-10 h-10 sm:w-12 sm:h-12 ml-2 sm:ml-4 shrink-0">
                          <img
                            src={pkg.image || DEFAULT_UC_IMAGE}
                            alt={`${pkg.amount} UC`}
                            className="w-full h-full object-cover rounded-md"
                          />
                        </div>
                        <div>
                          <div className="font-bold text-base sm:text-lg">{pkg.amount} UC</div>
                          <div className="text-muted-foreground text-xs sm:text-sm">
                            ${pkg.priceUSD} / {pkg.priceLocal} {pkg.localCurrencySymbol || "---"}
                            {pkg.originalPriceUSD && (
                              <span className="text-xs block opacity-75">
                                Original: ${pkg.originalPriceUSD} / {pkg.originalPriceLocal} {pkg.localCurrencySymbol || "---"}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="mt-auto pt-2 border-t border-border flex justify-end gap-1">
                        <Button
                          onClick={() => {
                            setEditingPackage(pkg);
                            setFormData({
                              ...pkg,
                              amount: pkg.amount || "",
                              priceUSD: pkg.priceUSD || "",
                              priceLocal: pkg.priceLocal || pkg.priceEGP || "", // Support for legacy field
                              localCurrencySymbol: pkg.localCurrencySymbol || pkg.localCurrencyCode || "EGP", // Support for legacy field
                              image: pkg.image || DEFAULT_UC_IMAGE,
                              featured: Boolean(pkg.featured),
                              originalPriceUSD: pkg.originalPriceUSD || "",
                              originalPriceLocal: pkg.originalPriceLocal || pkg.originalPrice || "", // Support for legacy field
                              discountUSD: pkg.discountUSD || "",
                              discountLocal: pkg.discountLocal || "",
                              highlights: pkg.highlights || ["Instant delivery", "24/7 support", "Secure payment"],
                              category: pkg.category || "Gaming"
                            });
                            setShowAddDialog(true);
                          }}
                          size="sm"
                          className="bg-pubg-gray text-white hover:bg-pubg-gray/90 h-7 px-2 text-xs"
                        >
                          <Pencil className="ml-1 h-3 w-3" />
                          تعديل
                        </Button>
                        <Button
                          onClick={() => handleDelete(pkg.id!)}
                          size="sm"
                          variant="destructive"
                          className="h-7 px-2 text-xs"
                        >
                          <Trash2 className="ml-1 h-3 w-3" />
                          حذف
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-10">
                <p className="text-lg text-muted-foreground">لا توجد باقات حتى الآن</p>
                <Button 
                  onClick={() => setShowAddDialog(true)} 
                  className="mt-4 bg-pubg-gray text-white hover:bg-pubg-gray/90"
                >
                  <Plus className="ml-2 h-4 w-4" />
                  إضافة باقة جديدة
                </Button>
              </div>
            )}
          </div>

          {/* Add/Edit UC Package Dialog */}
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-3 sm:p-6 w-[95vw] sm:w-auto">
              <DialogHeader className="pb-2">
                <DialogTitle>{editingPackage ? "تعديل باقة UC" : "إضافة باقة UC جديدة"}</DialogTitle>
              </DialogHeader>
              
              {/* AI-powered form filling from Telegram post */}
              {!editingPackage && (
                <div className="mb-4 p-3 sm:p-4 bg-pubg-black/30 rounded-lg">
                  <h4 className="text-sm sm:text-md font-semibold text-white mb-2 flex items-center">
                    <Sparkles className="ml-2 h-4 w-4 text-pubg-gray" />
                    الإدخال الذكي من منشور تيليجرام
                  </h4>
                  <div className="space-y-3">
                    <Textarea
                      placeholder="الصق محتوى منشور تيليجرام هنا للإدخال التلقائي..."
                      className="h-24 sm:h-32 text-sm"
                      value={telegramPost}
                      onChange={handleTelegramPostChange}
                    />
                    <div className="grid grid-cols-1 gap-2">
                      <Button
                        type="button"
                        onClick={handleProcessWithAI}
                        disabled={isAiProcessing || !telegramPost.trim()}
                        className="w-full bg-pubg-gray text-white hover:bg-pubg-gray/90 text-sm py-1 h-8"
                      >
                        {isAiProcessing ? "جاري المعالجة..." : "معالجة المنشور بالذكاء الاصطناعي"}
                      </Button>
                      <div>
                        <ModelSelector onModelChange={setSelectedModel} />
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <form onSubmit={handleSubmit} className="space-y-3">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div className="space-y-1 sm:space-y-2">
                    <Label htmlFor="amount" className="text-sm">Amount (UC) | كمية UC</Label>
                    <ClearableInput
                      id="amount"
                      name="amount"
                      type="number"
                      value={formData.amount}
                      onChange={handleInputChange}
                      onClear={() => setFormData({ ...formData, amount: "" })}
                      required
                    />
                  </div>
                  
                  <div className="space-y-1 sm:space-y-2">
                    <Label htmlFor="localCurrencySymbol" className="text-sm">Local Currency Symbol | رمز العملة المحلية</Label>
                    <ClearableInput
                      id="localCurrencySymbol"
                      name="localCurrencySymbol"
                      value={formData.localCurrencySymbol}
                      onChange={handleInputChange}
                      onClear={() => setFormData({ ...formData, localCurrencySymbol: "" })}
                      placeholder="EGP"
                    />
                  </div>
                  
                  <div className="space-y-1 sm:space-y-2">
                    <Label htmlFor="priceUSD" className="text-sm">Price (USD) | السعر (USD)</Label>
                    <ClearableInput
                      id="priceUSD"
                      name="priceUSD"
                      type="number"
                      value={formData.priceUSD}
                      onChange={(e) => {
                        handleInputChange(e);
                        // Calculate discount if original price exists
                        if (formData.originalPriceUSD) {
                          const price = Number(e.target.value) || 0;
                          const original = Number(formData.originalPriceUSD) || 0;
                          // Calculate discount percentage
                          const discountPercent = original > 0 ? Math.round(((original - price) / original) * 100) : 0;
                          setFormData(prev => ({ ...prev, discountUSD: discountPercent.toString() }));
                        }
                      }}
                      onClear={() => setFormData({ ...formData, priceUSD: "" })}
                      required
                    />
                  </div>
                  
                  <div className="space-y-1 sm:space-y-2">
                    <Label htmlFor="priceLocal" className="text-sm">Price in Local Currency | السعر بالعملة المحلية</Label>
                    <ClearableInput
                      id="priceLocal"
                      name="priceLocal"
                      type="number"
                      value={formData.priceLocal}
                      onChange={(e) => {
                        handleInputChange(e);
                        // Calculate discount if original price exists
                        if (formData.originalPriceLocal) {
                          const price = Number(e.target.value) || 0;
                          const original = Number(formData.originalPriceLocal) || 0;
                          // Calculate discount percentage
                          const discountPercent = original > 0 ? Math.round(((original - price) / original) * 100) : 0;
                          setFormData(prev => ({ ...prev, discountLocal: discountPercent.toString() }));
                        }
                      }}
                      onClear={() => setFormData({ ...formData, priceLocal: "" })}
                      required
                    />
                  </div>
                  
                  {/* Optional offer fields - only shown if admin fills any original price */}
                  <div className="space-y-1 sm:space-y-2">
                    <Label htmlFor="originalPriceUSD">Original Price (USD) | السعر الأصلي (USD)</Label>
                    <ClearableInput
                      id="originalPriceUSD"
                      name="originalPriceUSD"
                      type="number"
                      value={formData.originalPriceUSD}
                      onChange={(e) => {
                        handleInputChange(e);
                        // Calculate discount percentage
                        const original = Number(e.target.value) || 0;
                        const price = Number(formData.priceUSD) || 0;
                        const discountPercent = original > 0 ? Math.round(((original - price) / original) * 100) : 0;
                        setFormData(prev => ({ ...prev, discountUSD: discountPercent.toString() }));
                      }}
                      onClear={() => setFormData({ ...formData, originalPriceUSD: "", discountUSD: "" })}
                    />
                  </div>
                  
                  <div className="space-y-1 sm:space-y-2">
                    <Label htmlFor="originalPriceLocal">Original Price (Local) | السعر الأصلي ({formData.localCurrencySymbol})</Label>
                    <ClearableInput
                      id="originalPriceLocal"
                      name="originalPriceLocal"
                      type="number"
                      value={formData.originalPriceLocal}
                      onChange={(e) => {
                        handleInputChange(e);
                        // Calculate discount percentage
                        const original = Number(e.target.value) || 0;
                        const price = Number(formData.priceLocal) || 0;
                        const discountPercent = original > 0 ? Math.round(((original - price) / original) * 100) : 0;
                        setFormData(prev => ({ ...prev, discountLocal: discountPercent.toString() }));
                      }}
                      onClear={() => setFormData({ ...formData, originalPriceLocal: "", discountLocal: "" })}
                    />
                  </div>
                  
                  {formData.originalPriceUSD && (
                    <div className="space-y-1 sm:space-y-2">
                      <Label htmlFor="discountUSD">Discount % (USD) | نسبة الخصم % (USD)</Label>
                      <Input
                        id="discountUSD"
                        name="discountUSD"
                        type="number"
                        value={formData.discountUSD}
                        readOnly
                      />
                    </div>
                  )}
                  
                  {formData.originalPriceLocal && (
                    <div className="space-y-1 sm:space-y-2">
                      <Label htmlFor="discountLocal">Discount % (Local Currency) | نسبة الخصم % بالعملة المحلية</Label>
                      <Input
                        id="discountLocal"
                        name="discountLocal"
                        type="number"
                        value={formData.discountLocal}
                        readOnly
                      />
                    </div>
                  )}
                  
                  <div className="space-y-1 sm:space-y-2">
                    <Label htmlFor="category">التصنيف</Label>
                    <Input
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div className="md:col-span-2 space-y-2">
                    <Label htmlFor="image">رابط الصورة</Label>
                    <div className="flex space-x-2 space-x-reverse">
                      <Input
                        id="image"
                        name="image"
                        value={formData.image}
                        onChange={handleInputChange}
                        placeholder={DEFAULT_UC_IMAGE}
                        className="flex-1"
                      />
                      <ImageUploader
                        imageUrl={formData.image || ""}
                        onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                        fieldName="image"
                        placeholder={DEFAULT_UC_IMAGE}
                        aspectRatio={1}
                      />
                    </div>
                  </div>
                  
                  <div className="md:col-span-2 space-y-4">
                    <div className="flex items-center">
                      <input
                        id="featured"
                        name="featured"
                        type="checkbox"
                        checked={formData.featured}
                        onChange={(e) =>
                          setFormData({ ...formData, featured: e.target.checked })
                        }
                        className="ml-2 w-4 h-4"
                      />
                      <Label htmlFor="featured" className="cursor-pointer">
                        مميز (سيظهر في القسم المميز)
                      </Label>
                    </div>
                  </div>
                  
                  <div className="md:col-span-2 space-y-2">
                    <Label>نقاط بارزة (Highlights)</Label>
                    <p className="text-xs text-muted-foreground mb-1">
                      استخدم القيم الافتراضية: instant_delivery, support, secure_payment أو أضف قيمًا مخصصة
                    </p>
                    <div className="flex space-x-2 space-x-reverse">
                      <Input
                        value={highlightInput}
                        onChange={(e) => setHighlightInput(e.target.value)}
                        placeholder="أضف نقطة بارزة..."
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        onClick={handleHighlightAdd}
                        disabled={!highlightInput.trim()}
                        className="bg-pubg-gray text-white hover:bg-pubg-gray/90"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="mt-2 space-y-2">
                      {formData.highlights?.map((highlight, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-pubg-black/30 rounded">
                          <span>{highlight}</span>
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={() => handleHighlightRemove(index)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive/90"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="md:col-span-2 space-y-2">
                    <Label htmlFor="description" className="text-lg font-semibold text-white">وصف المنتج (Product Description)</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="description" className="text-sm mb-1 flex items-center">
                          <span className="ml-1 text-pubg-blue">عربي</span> وصف المنتج
                        </Label>
                        <p className="text-xs text-muted-foreground mb-1">
                          اترك فارغًا لاستخدام الترجمة التلقائية أو أضف وصفًا مخصصًا
                        </p>
                        <Textarea
                          id="description"
                          name="description"
                          value={formData.description}
                          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                          placeholder="شراء {amount} UC لحسابك في لعبة PUBG Mobile بأسعار منافسة وشحن فوري. يمكنك استخدام UC لشراء ملابس وأسلحة واكسسوارات مميزة داخل اللعبة."
                          className="min-h-[100px]"
                        />
                      </div>
                      <div>
                        <Label htmlFor="description_en" className="text-sm mb-1 flex items-center">
                          <span className="ml-1 text-pubg-blue">English</span> Product Description
                        </Label>
                        <p className="text-xs text-muted-foreground mb-1">
                          Leave empty to use automatic translation or add a custom description
                        </p>
                        <Textarea
                          id="description_en"
                          name="description_en"
                          value={formData.description_en}
                          onChange={(e) => setFormData({ ...formData, description_en: e.target.value })}
                          placeholder="Purchase {amount} UC for your PUBG Mobile account at competitive prices with instant delivery. You can use UC to buy exclusive outfits, weapons, and accessories in the game."
                          className="min-h-[100px]"
                          dir="ltr"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="md:col-span-2 space-y-2">
                    <Label className="text-lg font-semibold text-white">ملاحظات مهمة (Important Notes)</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm mb-1 flex items-center">
                          <span className="ml-1 text-pubg-blue">عربي</span> ملاحظات مهمة
                        </Label>
                        <p className="text-xs text-muted-foreground mb-1">
                          اترك فارغًا لاستخدام الترجمة التلقائية أو أضف ملاحظات مخصصة
                        </p>
                        {formData.notes?.map((note, index) => (
                          <div key={index} className="mb-2">
                            <Label htmlFor={`note-${index}`} className="text-xs mb-1">ملاحظة {index + 1}</Label>
                            <Textarea
                              id={`note-${index}`}
                              value={note}
                              onChange={(e) => {
                                const newNotes = [...(formData.notes || [])];
                                newNotes[index] = e.target.value;
                                setFormData({ ...formData, notes: newNotes });
                              }}
                              placeholder={
                                index === 0 ? "سيتم شحن الرصيد بعد استلام طلبك والتواصل معك عبر التلجرام." :
                                index === 1 ? "يرجى التأكد من صحة معرف حسابك في اللعبة عند التواصل معنا." :
                                "الشحن فوري في أغلب الأوقات وقد يستغرق حتى 24 ساعة في أوقات الضغط."
                              }
                              className="min-h-[60px]"
                            />
                          </div>
                        ))}
                      </div>
                      <div>
                        <Label className="text-sm mb-1 flex items-center">
                          <span className="ml-1 text-pubg-blue">English</span> Important Notes
                        </Label>
                        <p className="text-xs text-muted-foreground mb-1">
                          Leave empty to use automatic translation or add custom notes
                        </p>
                        {formData.notes_en?.map((note, index) => (
                          <div key={index} className="mb-2">
                            <Label htmlFor={`note-en-${index}`} className="text-xs mb-1">Note {index + 1}</Label>
                            <Textarea
                              id={`note-en-${index}`}
                              value={note}
                              onChange={(e) => {
                                const newNotes = [...(formData.notes_en || [])];
                                newNotes[index] = e.target.value;
                                setFormData({ ...formData, notes_en: newNotes });
                              }}
                              placeholder={
                                index === 0 ? "Your UC will be credited after we receive your order and contact you via Telegram." :
                                index === 1 ? "Please ensure your game ID is correct when communicating with us." :
                                "Delivery is instant in most cases but may take up to 24 hours during peak times."
                              }
                              className="min-h-[60px]"
                              dir="ltr"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        onClick={() => {
                          setFormData({ 
                            ...formData, 
                            notes: [...(formData.notes || []), ''],
                            notes_en: [...(formData.notes_en || []), '']
                          });
                        }}
                        className="bg-pubg-gray text-white hover:bg-pubg-gray/90"
                      >
                        <Plus className="ml-1 h-4 w-4" />
                        أضف ملاحظة أخرى
                      </Button>
                      {formData.notes && formData.notes.length > 3 && (
                        <Button
                          type="button"
                          onClick={() => {
                            const newNotes = [...formData.notes];
                            newNotes.pop();
                            const newNotesEn = [...(formData.notes_en || [])];
                            newNotesEn.pop();
                            setFormData({ ...formData, notes: newNotes, notes_en: newNotesEn });
                          }}
                          variant="destructive"
                          className="h-9"
                        >
                          <Trash className="ml-1 h-4 w-4" />
                          حذف آخر ملاحظة
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
                
                <DialogFooter className="mt-4 gap-2 flex-col sm:flex-row">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={resetFormAndClose}
                    className="w-full sm:w-auto"
                  >
                    إلغاء
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="bg-pubg-gray text-white hover:bg-pubg-gray/90 w-full sm:w-auto"
                  >
                    {isLoading ? "جاري الحفظ..." : (editingPackage ? "حفظ التغييرات" : "إضافة الباقة")}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

          {/* Bulk Add UC Packages Dialog */}
          <Dialog open={showBulkAddDialog} onOpenChange={setShowBulkAddDialog}>
            <DialogContent className="sm:max-w-[600px] w-[95vw] sm:w-auto p-3 sm:p-6">
              <DialogHeader className="pb-2">
                <DialogTitle>إضافة باقات UC متعددة</DialogTitle>
                <DialogDescription className="text-xs sm:text-sm">
                  أدخل كل باقة في سطر منفصل بتنسيق: "كمية UC = سعر" أو "كمية = سعر ج"
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={handleBulkUCDataSubmit} className="space-y-3">
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="bulkUCData" className="text-sm">قائمة باقات UC (كل سطر باقة)</Label>
                  <p className="text-xs text-muted-foreground mb-1">
                    مثال: "60UC=55ج" أو "120UC=110ج" أو "180UC=160ج"
                  </p>
                  <Textarea
                    id="bulkUCData"
                    placeholder="60UC=55ج
120UC=110ج
180UC=160ج
325UC=235ج
355UC=260ج
385UC=280ج
660UC=450ج"
                    className="h-32 sm:h-40 font-mono text-sm"
                    value={bulkUCData}
                    onChange={handleBulkUCDataChange}
                    required
                  />
                </div>
                
                <DialogFooter className="flex-col sm:flex-row gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowBulkAddDialog(false);
                      setBulkUCData("");
                    }}
                    className="w-full sm:w-auto ml-0 sm:ml-2"
                  >
                    إلغاء
                  </Button>
                  <Button
                    type="submit"
                    disabled={isBulkProcessing || !bulkUCData.trim()}
                    className="bg-pubg-gray text-white hover:bg-pubg-gray/90 w-full sm:w-auto"
                  >
                    {isBulkProcessing ? "جاري المعالجة..." : "إضافة كل الباقات مرة واحدة"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                <ImageIcon className="ml-2 h-5 w-5 text-pubg-gray" />
                إعدادات صفحة متجر UC
              </CardTitle>
              <CardDescription>
                قم بتخصيص مظهر صفحة متجر UC من هنا
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="heroTitle">عنوان القسم الرئيسي</Label>
                <Input
                  id="heroTitle"
                  name="heroTitle"
                  value={ucStoreSettings.heroTitle}
                  onChange={handleUCStoreSettingChange}
                  placeholder="عنوان القسم الرئيسي"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="heroSubtitle">وصف القسم الرئيسي</Label>
                <Textarea
                  id="heroSubtitle"
                  name="heroSubtitle"
                  value={ucStoreSettings.heroSubtitle}
                  onChange={handleUCStoreSettingChange}
                  placeholder="وصف القسم الرئيسي"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="heroImage">صورة القسم الرئيسي</Label>
                <div className="flex flex-col space-y-4">
                  <div className="relative w-full h-32 rounded-md overflow-hidden">
                    <img 
                      src={ucStoreSettings.heroImage} 
                      alt="Hero Preview" 
                      className="w-full h-full object-cover" 
                    />
                    <div className="absolute inset-0 bg-black/40"></div>
                  </div>
                  <ImageUploader 
                    imageUrl={ucStoreSettings.heroImage}
                    onChange={(e) => {
                      if (e.target.value) {
                        setUCStoreSettings(prev => ({ ...prev, heroImage: e.target.value }));
                      } else if (e.target.name === "image" && typeof e.target.value === "string") {
                        setUCStoreSettings(prev => ({ ...prev, heroImage: e.target.value }));
                      }
                    }}
                    label="صورة القسم الرئيسي"
                    fieldName="heroImage"
                    placeholder="رابط صورة القسم الرئيسي"
                    aspectRatio={16/5}
                  />
                  <p className="text-xs text-muted-foreground">
                    يمكنك تحميل صورة جديدة أو إدخال رابط لصورة موجودة على الإنترنت
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveUCStoreSettings} className="w-full">
                حفظ الإعدادات
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UCPackagesManager;
