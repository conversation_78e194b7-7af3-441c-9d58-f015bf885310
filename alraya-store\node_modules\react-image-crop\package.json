{"name": "react-image-crop", "version": "11.0.10", "description": "A responsive image cropping tool for React", "repository": "https://github.com/dominictobias/react-image-crop", "type": "module", "types": "./dist/index.d.ts", "main": "./dist/index.umd.cjs", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.umd.cjs"}, "./dist/ReactCrop.css": "./dist/ReactCrop.css", "./src/ReactCrop.scss": "./src/ReactCrop.scss", "./package.json": "./package.json"}, "files": ["dist", "src"], "browserslist": "last 3 versions, not IE > 0", "style": "dist/ReactCrop.css", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare": "npm run build"}, "author": "<PERSON> (https://github.com/dominictobias)", "keywords": ["react", "reactjs", "image", "crop", "react-component"], "license": "ISC", "devDependencies": {"@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.24.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.86.3", "typescript": "^5.8.3", "vite": "^6.2.6", "vite-plugin-dts": "^4.5.3"}, "peerDependencies": {"react": ">=16.13.1"}}