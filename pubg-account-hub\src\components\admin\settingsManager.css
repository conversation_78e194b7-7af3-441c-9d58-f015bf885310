/* Settings Manager Mobile Styles */
.mobile-scroll-container {
  display: block !important;
  width: 100%;
  overflow-y: visible;
  padding-bottom: 1rem;
  min-height: auto;
  max-height: none;
}

/* Tab content container to fix spacing */
.tab-content-container {
  width: 100%;
  display: block;
  position: relative;
}

/* Fix for tabbed interface on mobile */
[data-state="inactive"] {
  display: none;
}

[data-state="active"] {
  display: block;
}

/* Ensure proper spacing on mobile */
@media (max-width: 768px) {
  .admin-form-group {
    margin-bottom: 1rem;
  }
  
  .admin-card {
    margin-bottom: 2rem;
  }
}

/* Fix excessive spacing in card content */
.admin-card {
  background: var(--bg-card-dark) !important;
  border: 1px solid var(--border-gray) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(8px);
  margin-bottom: 1.5rem !important;
  overflow: hidden;
}

.admin-card:hover {
  border-color: var(--border-orange) !important;
}

/* Admin inputs with sharper styling */
.admin-input, .admin-textarea {
  background-color: var(--bg-input-dark) !important;
  border: 1px solid var(--border-gray) !important;
  color: #ffffff !important;
  border-radius: 6px !important;
  transition: all 0.2s ease-in-out;
}

.admin-input:focus, .admin-textarea:focus {
  border-color: var(--pubg-orange-color) !important;
  box-shadow: 0 0 0 1px rgba(242, 169, 0, 0.3) !important;
}

.admin-input:hover, .admin-textarea:hover {
  background-color: var(--bg-input-hover) !important;
  border-color: var(--border-orange) !important;
}

/* Admin button styling */
.admin-button {
  font-weight: 600 !important;
  letter-spacing: 0.02em !important;
  text-transform: uppercase !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.admin-button.bg-pubg-orange {
  background-color: var(--pubg-orange-color) !important;
  border: none !important;
  color: var(--pubg-black-color) !important;
}

.admin-button.bg-pubg-blue {
  background-color: var(--pubg-gray-color) !important;
  border: none !important;
  color: #ffffff !important;
}

.admin-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Admin labels */
.admin-label {
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-bottom: 0.25rem !important;
}

/* Admin section headers */
.admin-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #ffffff !important;
  margin-bottom: 1.5rem !important;
  border-bottom: 2px solid #F2A900 !important;
  padding-bottom: 0.5rem !important;
  display: inline-block !important;
}

.admin-subtitle {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  color: #ffffff !important;
}

/* Tab styling */
.tabs-trigger {
  background-color: var(--bg-input-dark) !important;
  border: 1px solid var(--border-light) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.tabs-trigger[data-state="active"] {
  background-color: rgba(242, 169, 0, 0.15) !important;
  border-color: var(--border-orange) !important;
  color: var(--pubg-orange-color) !important;
  font-weight: 600 !important;
}

.tabs-list {
  background-color: var(--bg-input-dark) !important;
  border-radius: 8px !important;
  padding: 4px !important;
  gap: 4px !important;
}

/* Draggable section items with sharper UI */
.section-item {
  position: relative;
  transition: all 0.2s ease;
  background: var(--bg-section-item);
  border: 1px solid var(--border-light);
  margin-bottom: 8px;
  border-radius: 6px;
  overflow: hidden;
}

.section-item:hover {
  border-color: var(--border-orange);
  background: var(--bg-section-item-hover);
}

.section-item:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--pubg-orange-color);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.section-item:hover:before {
  opacity: 1;
}

/* Drag handle styling */
.drag-handle {
  background-color: var(--bg-input-hover) !important;
  color: var(--pubg-orange-color) !important;
  cursor: grab;
  transition: all 0.2s ease;
}

.drag-handle:hover {
  background-color: rgba(242, 169, 0, 0.15) !important;
  transform: scale(1.05);
}

.drag-handle:active {
  cursor: grabbing;
}

/* Section actions styling */
.section-actions {
  background-color: var(--bg-input-hover) !important;
}

.section-actions button {
  color: rgba(255, 255, 255, 0.6) !important;
  transition: all 0.2s ease !important;
}

.section-actions button:not(:disabled):hover {
  color: var(--pubg-orange-color) !important;
  background-color: rgba(242, 169, 0, 0.15) !important;
}

/* Animation for moved items */
@keyframes itemMove {
  0% { background-color: rgba(242, 169, 0, 0.15); border-color: var(--border-orange); }
  50% { background-color: rgba(242, 169, 0, 0.1); border-color: rgba(242, 169, 0, 0.7); }
  100% { background-color: var(--bg-section-item); border-color: var(--border-light); }
}

.item-moved {
  animation: itemMove 1s ease-in-out;
}

/* Drag and drop states */
.section-item.dragging {
  background-color: var(--bg-section-item-hover) !important;
  border-color: var(--pubg-orange-color) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
  z-index: 10;
}

.section-item.drag-over {
  border-color: var(--pubg-gray-color) !important;
  background-color: rgba(136, 136, 136, 0.08) !important;
}

.section-item.drag-over::before {
  background-color: var(--pubg-gray-color) !important;
  opacity: 1;
}

/* Form grid for better layout */
.admin-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .admin-form-grid {
    grid-template-columns: 1fr;
  }
  
  .admin-form-group {
    margin-bottom: 1rem;
  }
}

/* Prevent text selection during drag */
.dragging-active {
  user-select: none !important;
  cursor: grabbing !important;
}

/* Complete overhaul of draggable item behavior */
.draggable-item {
  box-sizing: border-box !important;
  width: 100% !important;
  margin-bottom: 8px !important;
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 8px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #2c2c2c;
}

/* Override React Beautiful DND's transform to fix RTL issue */
[data-rbd-draggable-id] {
  position: relative !important;
  left: auto !important;
  right: auto !important;
  top: auto !important;
}

/* Only allow transform during drag */
.draggable-item-dragging {
  background-color: rgba(60, 60, 70, 0.9) !important;
  border: 1px solid #ff6600 !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
  z-index: 9999 !important;
}

/* Force the drag preview to stay in place */
.react-beautiful-dnd-dragging {
  transition: none !important;
}

/* Remove space-y-2 for droppable container and use flexbox instead */
.droppable-container {
  display: flex !important;
  flex-direction: column !important;
  padding: 4px !important;
  min-height: 50px !important;
}

/* Fix for transform during drag */
.draggable-item-dragging * {
  transition: none !important;
}

/* Set fixed heights for drag items */
.draggable-item > div {
  min-height: 40px;
}

/* Improve drag handle */
.drag-handle {
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff6600;
}

.drag-handle:active {
  cursor: grabbing;
}

/* Fix spacing when dragging over */
.droppable-container-dragging-over {
  background-color: rgba(255, 102, 0, 0.05);
  border-radius: 8px;
}

/* Animation for moved items */
@keyframes itemMove {
  0% { background-color: rgba(255, 102, 0, 0.1); }
  50% { background-color: rgba(255, 102, 0, 0.15); }
  100% { background-color: rgba(40, 40, 40, 0.5); }
}

.item-moved {
  animation: itemMove 0.8s ease-in-out;
}

/* Fix RTL specific issues */
[dir="rtl"] .droppable-container {
  width: 100% !important;
}

/* Animations for draggable items */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.draggable-item-appear {
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

/* Add a flip animation for the drag handle */
@keyframes flip {
  0% { transform: rotate(0deg); }
  25% { transform: rotate(5deg); }
  75% { transform: rotate(-5deg); }
  100% { transform: rotate(0deg); }
}

.drag-handle:hover {
  animation: flip 0.5s ease-in-out;
}

.draggable-item:before {
  content: '';
  position: absolute;
  left: -2px;
  top: 0;
  height: 100%;
  width: 4px;
  background: linear-gradient(to bottom, rgba(255, 102, 0, 0), rgba(255, 102, 0, 0.7), rgba(255, 102, 0, 0));
  opacity: 0;
  transition: opacity 0.2s ease;
}

.draggable-item:hover:before {
  opacity: 1;
}

/* Fix RTL alignment issues during drag */
[data-rbd-draggable-context-id] {
  position: relative;
  transform: translateX(0);
}

/* Add class for active dragging to help stabilize position */
.is-dragging {
  transform: translateX(0) !important;
  transition: none !important;
}

/* RTL-specific fixes for react-beautiful-dnd */
[dir="rtl"] .draggable-item, 
.draggable-item {
  transform-origin: center !important;
  float: none !important;
}

/* Force horizontal stability during drag */
[data-rbd-draggable-context-id] {
  transform: translate3d(0, 0, 0) !important;
  position: relative !important;
  width: 100% !important;
}

/* RTL specific fixes for drag and drop */
[dir="rtl"] .droppable-container {
  display: flex;
  flex-direction: column;
}

/* Reset all transforms for RTL mode */
[dir="rtl"] [data-rbd-draggable-context-id] {
  transform: none !important;
  position: relative !important;
}

/* Additional fixes for jumping issue */
[dir="rtl"] .draggable-item {
  width: 100% !important;
  box-sizing: border-box !important;
  margin: 0 0 8px 0 !important;
}

/* Ensure the drag handle is properly positioned in RTL */
[dir="rtl"] .drag-handle {
  right: auto;
  left: auto;
}

/* Section item styling for arrow-based navigation */
.section-item {
  position: relative;
  transition: all 0.2s ease;
  background: var(--bg-section-item);
  border: 1px solid var(--border-light);
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Prevent text selection during drag */
.dragging-active {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  cursor: grabbing !important;
}

.section-item:hover {
  border-color: rgba(242, 169, 0, 0.5);
  background: rgba(30, 30, 30, 0.8);
}

.section-item .section-actions {
  display: flex;
  flex-direction: column;
}

.section-item .section-actions button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.section-item .section-actions button:not(:disabled):hover {
  color: rgba(242, 169, 0, 1);
}

/* Item moved animation */
@keyframes highlight {
  0% {
    background: rgba(242, 169, 0, 0.1);
  }
  50% {
    background: rgba(242, 169, 0, 0.2);
  }
  100% {
    background: rgba(20, 20, 20, 0.6);
  }
}

.section-item.item-moved {
  animation: highlight 0.8s ease;
}

/* HTML5 Drag and Drop Styles */
.section-item.dragging {
  opacity: 0.6;
  border: 2px dashed #ff6600;
  box-shadow: 0 0 15px rgba(255, 102, 0, 0.2);
  background: rgba(30, 30, 30, 0.95);
  z-index: 1000;
}

.section-item.drag-over {
  border: 2px solid rgba(242, 169, 0, 0.7);
  background: rgba(242, 169, 0, 0.07);
  position: relative;
}

.section-item.drag-over::before {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px dashed rgba(242, 169, 0, 0.7);
  border-radius: 7px;
  animation: pulse 1.5s infinite;
  pointer-events: none;
}

@keyframes pulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.3;
  }
}

/* Enhanced animation for moved items */
@keyframes highlight-dnd {
  0% { 
    background: rgba(242, 169, 0, 0.15);
    border-color: rgba(242, 169, 0, 0.6);
  }
  50% { 
    background: rgba(242, 169, 0, 0.25);
    border-color: rgba(242, 169, 0, 0.8);
  }
  100% { 
    background: rgba(20, 20, 20, 0.6);
    border-color: rgba(50, 50, 50, 0.2);
  }
}

.section-item.item-moved {
  animation: highlight-dnd 0.8s ease;
}

/* Add a shadow hint when hovering over the drag handle */
.drag-handle:hover {
  box-shadow: 0 0 0 4px rgba(242, 169, 0, 0.15);
} 