"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Zap, Calculator, Star } from "lucide-react"
import { ProductTemplate, ProductFormData, ProductPackage, Currency, DynamicField } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"

interface SimpleProductFormProps {
  template: ProductTemplate
  onSubmit: (formData: ProductFormData) => void
  currency: Currency
  showPricing?: boolean
  disabled?: boolean
  className?: string
}

export function SimpleProductForm({
  template,
  onSubmit,
  currency,
  showPricing = true,
  disabled = false,
  className = ""
}: SimpleProductFormProps) {
  const [selectedPackage, setSelectedPackage] = useState<ProductPackage | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, any>>({})
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  
  const { convertPrice } = useCurrencyConverter()

  // Calculate total price
  const calculateTotalPrice = () => {
    if (!selectedPackage) return 0
    return selectedPackage.price * quantity
  }

  // Format price with currency
  const formatPrice = (price: number) => {
    const convertedPrice = convertPrice(price, "USD", currency)
    return formatCurrency(convertedPrice, currency)
  }

  // Handle package selection
  const handlePackageSelect = (pkg: ProductPackage) => {
    setSelectedPackage(pkg)
    setFieldErrors({}) // Clear errors when package changes
  }

  // Handle custom field changes
  const handleFieldChange = (fieldName: string, value: any) => {
    setCustomFieldValues(prev => ({
      ...prev,
      [fieldName]: value
    }))
    
    // Clear error for this field
    if (fieldErrors[fieldName]) {
      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: ""
      }))
    }
  }

  // Validate form fields
  const validateFields = () => {
    const errors: Record<string, string> = {}
    const activeFields = template.fields.filter(f => f.isActive)
    
    activeFields.forEach(field => {
      const value = customFieldValues[field.name]
      
      if (field.required && (!value || value.toString().trim() === "")) {
        errors[field.name] = `${field.label} مطلوب`
      }
      
      if (value && field.type === "email") {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          errors[field.name] = "يرجى إدخال بريد إلكتروني صحيح"
        }
      }
      
      if (value && field.type === "number") {
        if (isNaN(Number(value))) {
          errors[field.name] = "يرجى إدخال رقم صحيح"
        }
      }
    })
    
    return errors
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedPackage) {
      alert("يرجى اختيار حزمة")
      return
    }
    
    // Validate all custom fields
    const errors = validateFields()
    
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors)
      return
    }
    
    try {
      setIsLoading(true)
      
      const formData: ProductFormData = {
        templateId: template.id,
        selectedPackage,
        quantity,
        customFields: customFieldValues,
        totalPrice: calculateTotalPrice(),
        currency
      }
      
      await onSubmit(formData)
    } catch (error) {
      console.error("Error submitting form:", error)
      alert("حدث خطأ أثناء إرسال الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  // Render custom field
  const renderCustomField = (field: DynamicField) => {
    const value = customFieldValues[field.name] || ""
    const error = fieldErrors[field.name]
    
    return (
      <div key={field.id} className="space-y-2">
        <label className="block text-sm font-medium text-slate-300">
          {field.label}
          {field.required && <span className="text-red-400 ml-1">*</span>}
        </label>
        
        {field.type === "text" && (
          <input
            type="text"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          />
        )}
        
        {field.type === "email" && (
          <input
            type="email"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          />
        )}
        
        {field.type === "number" && (
          <input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          />
        )}
        
        {error && (
          <p className="text-red-400 text-sm">{error}</p>
        )}
      </div>
    )
  }

  const totalPrice = calculateTotalPrice()
  const visibleFields = template.fields.filter(f => f.isActive)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Package Selection */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="h-5 w-5" />
            اختر الحزمة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {template.packages.map((pkg) => (
              <div
                key={pkg.id}
                onClick={() => !disabled && handlePackageSelect(pkg)}
                className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedPackage?.id === pkg.id
                    ? "border-blue-500 bg-blue-500/10"
                    : "border-slate-600 bg-slate-700/30 hover:border-slate-500"
                } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-semibold text-white">{pkg.name}</h3>
                    {pkg.amount && (
                      <p className="text-slate-300 text-sm">{pkg.amount}</p>
                    )}
                    {pkg.description && (
                      <p className="text-slate-400 text-sm mt-1">{pkg.description}</p>
                    )}
                  </div>
                  
                  <div className="text-right">
                    {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                      <p className="text-slate-400 text-sm line-through">
                        {formatPrice(pkg.originalPrice)}
                      </p>
                    )}
                    <p className="text-white font-bold">
                      {formatPrice(pkg.price)}
                    </p>
                    {pkg.discount && (
                      <Badge variant="destructive" className="mt-1">
                        خصم {pkg.discount}%
                      </Badge>
                    )}
                  </div>
                </div>
                
                {pkg.popular && (
                  <Badge className="absolute top-2 left-2 bg-yellow-500 text-black">
                    <Star className="h-3 w-3 mr-1" />
                    الأكثر شعبية
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Custom Fields */}
      {visibleFields.length > 0 && selectedPackage && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">معلومات إضافية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {visibleFields.map(renderCustomField)}
          </CardContent>
        </Card>
      )}

      {/* Price Summary & Submit */}
      {selectedPackage && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-6">
            {showPricing && (
              <div className="flex justify-between items-center mb-4 text-white">
                <span className="text-lg">المجموع:</span>
                <span className="text-2xl font-bold text-yellow-400">
                  {formatPrice(totalPrice)}
                </span>
              </div>
            )}
            
            <Button
              onClick={handleSubmit}
              disabled={disabled || isLoading || !selectedPackage}
              className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold py-3 text-lg"
              size="lg"
            >
              {isLoading ? (
                "جاري المعالجة..."
              ) : (
                <>
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  {template.processingType === "instant" ? "اشتري الآن" : "أضف للسلة"}
                  {showPricing && ` - ${formatPrice(totalPrice)}`}
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
