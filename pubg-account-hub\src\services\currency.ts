// Function to fetch the current USD to local currency exchange rate
export async function getUSDtoLocalRate(currencyCode: string = 'EGP'): Promise<number> {
  try {
    const response = await fetch('https://open.er-api.com/v6/latest/USD');
    const data = await response.json();
    // Check if the requested currency exists in the API response
    if (data.rates && data.rates[currencyCode]) {
      return data.rates[currencyCode];
    } else {
      console.error(`Currency ${currencyCode} not found in exchange rates`);
      // Fallback to a default rate for EGP
      return currencyCode === 'EGP' ? 31.0 : 1.0;
    }
  } catch (error) {
    console.error('Error fetching exchange rate:', error);
    // Fallback to a default rate if API fails
    return 31.0; // Default fallback rate for EGP
  }
}

// Function to fetch the current USD to EGP exchange rate (for backward compatibility)
export async function getUSDtoEGPRate(): Promise<number> {
  return getUSDtoLocalRate('EGP');
}

// Convert USD to local currency
export function convertUSDtoLocal(usdAmount: number, rate: number): number {
  return Math.round(usdAmount * rate);
}

// Convert local currency to USD
export function convertLocalToUSD(localAmount: number, rate: number): number {
  return Number((localAmount / rate).toFixed(2));
}

// Convert USD to EGP (for backward compatibility)
export function convertUSDtoEGP(usdAmount: number, rate: number): number {
  return convertUSDtoLocal(usdAmount, rate);
}

// Convert EGP to USD (for backward compatibility)
export function convertEGPtoUSD(egpAmount: number, rate: number): number {
  return convertLocalToUSD(egpAmount, rate);
} 