import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate, useNavigate, useLocation } from "react-router-dom";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Users,
  CreditCard,
  Newspaper,
  Phone,
  ShoppingBag,
  Settings as SettingsIcon,
  Sparkles,
  Gamepad2,
  Info,
  ChevronDown,
  Settings,
  LayoutDashboard,
  MessageSquare,
  Tag,
  BadgeInfo
} from "lucide-react";
import AccountsManager from "@/components/admin/AccountsManager";
import UCPackagesManager from "@/components/admin/UCPackagesManager";
import BlogManager from "@/components/admin/BlogManager";
import ContactManager from "@/components/admin/ContactManager";
import SettingsManager from "@/components/admin/SettingsManager";
import ModManager from "@/components/admin/ModManager";
import AboutPageManager from "@/components/admin/AboutPageManager";
import CategoryManager from "@/components/admin/CategoryManager";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import UsersManager from "@/components/admin/UsersManager";

// Navigation items configuration
const navItems = [
  { id: "accounts", label: "الحسابات", icon: ShoppingBag },
  { id: "uc", label: "متجر UC", icon: CreditCard },
  { id: "mods", label: "المودات", icon: Gamepad2 },
  { id: "blog", label: "المدونة", icon: Newspaper },
  { id: "contact", label: "روابط التواصل", icon: Phone },
  { id: "about", label: "من نحن", icon: Info },
  { id: "settings", label: "الإعدادات", icon: SettingsIcon },
  { id: "users", label: "المستخدمين", icon: Users },
];

const Admin = () => {
  const { currentUser, isAdmin } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get the tab from URL query params or default to "accounts"
  const getTabFromUrl = () => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    // Validate that the tab exists in navItems
    return navItems.some(item => item.id === tab) ? tab : "accounts";
  };
  
  const [activeTab, setActiveTab] = useState(getTabFromUrl());
  
  // Update URL when tab changes
  const handleTabChange = (value) => {
    setActiveTab(value);
    const params = new URLSearchParams(location.search);
    params.set('tab', value);
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });
  };
  
  // Update active tab if URL changes
  useEffect(() => {
    setActiveTab(getTabFromUrl());
  }, [location.search]);

  // Redirect non-admin users
  if (!currentUser || !isAdmin) {
    return <Navigate to="/" />;
  }

  return (
    <div className="min-h-screen pt-16 md:pt-24 pb-12 px-2 md:px-4">
      <div className="container mx-auto">
        <div className="mb-4 md:mb-8">
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2 md:mb-4">لوحة التحكم</h1>
          <p className="text-sm md:text-base text-muted-foreground">
            إدارة الحسابات، متجر UC، المدونة، والمزيد
          </p>
        </div>

        <Alert className="mb-4 md:mb-6 bg-pubg-dark border-pubg-orange p-3 md:p-4">
          <Sparkles className="h-4 w-4 md:h-5 md:w-5 text-pubg-orange" />
          <AlertTitle className="text-sm md:text-base font-bold text-white">مساعد الذكاء الاصطناعي</AlertTitle>
          <AlertDescription className="text-xs md:text-sm text-muted-foreground">
            يمكنك استخدام الذكاء الاصطناعي لتحليل منشورات التليجرام وملء النماذج تلقائيًا.
          </AlertDescription>
        </Alert>

        <Tabs 
          defaultValue="accounts" 
          value={activeTab} 
          onValueChange={handleTabChange}
          className="w-full"
        >
          {/* Mobile Dropdown Navigation */}
          <div className="admin-sticky-header md:hidden">
            <div className="glass-card rounded-xl p-3 shadow-lg">
              <Select value={activeTab} onValueChange={handleTabChange}>
                <SelectTrigger className="flex items-center justify-between admin-input">
                  <div className="flex items-center gap-2">
                    {React.createElement(
                      navItems.find(item => item.id === activeTab)?.icon || ShoppingBag, 
                      { className: "w-4 h-4 ml-2" }
                    )}
                    <span className="text-sm">{navItems.find(item => item.id === activeTab)?.label}</span>
                  </div>
                  <ChevronDown className="w-4 h-4" />
                </SelectTrigger>
                <SelectContent>
                  {navItems.map((item) => (
                    <SelectItem key={item.id} value={item.id}>
                      <div className="flex items-center">
                        <item.icon className="ml-2 w-4 h-4" />
                        <span className="text-sm">{item.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Desktop Tabs */}
          <TabsList className="hidden md:grid grid-cols-3 lg:grid-cols-9 mb-8">
            {navItems.map((item) => (
              <TabsTrigger key={item.id} value={item.id} className="flex items-center">
                <item.icon className="ml-2 w-4 h-4" />
                <span>{item.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="accounts" className="admin-card">
            <AccountsManager />
          </TabsContent>

          <TabsContent value="uc" className="admin-card">
            <UCPackagesManager />
          </TabsContent>

          <TabsContent value="mods" className="admin-card">
            <ModManager />
          </TabsContent>

          <TabsContent value="blog" className="admin-card">
            <BlogManager />
          </TabsContent>

          <TabsContent value="about" className="admin-card">
            <AboutPageManager />
          </TabsContent>

          <TabsContent value="contact" className="admin-card">
            <ContactManager />
          </TabsContent>
          
          <TabsContent value="settings" className="admin-card">
            <SettingsManager />
          </TabsContent>

          <TabsContent value="users" className="admin-card">
            <UsersManager />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Admin;
