(()=>{var e={};e.id=970,e.ids=[970],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},88006:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>_,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>m});var n={};t.r(n),t.d(n,{GET:()=>u,GET_BREAKDOWN:()=>p,GET_SUMMARY:()=>y,POST:()=>i});var a=t(96559),o=t(48088),s=t(37719),c=t(32190);async function u(e){try{let{searchParams:r}=new URL(e.url),t={startDate:r.get("startDate")?new Date(r.get("startDate")):void 0,endDate:r.get("endDate")?new Date(r.get("endDate")):void 0,primaryCurrency:r.get("primaryCurrency")||"USD",includePending:"true"===r.get("includePending"),groupByCurrency:"true"===r.get("groupByCurrency"),includeExchangeRates:"true"===r.get("includeExchangeRates")},n=await d(t);return c.NextResponse.json({success:!0,report:n})}catch(e){return console.error("Error generating revenue report:",e),c.NextResponse.json({success:!1,error:"Failed to generate revenue report",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function i(e){try{let r=await e.json();if(r.startDate&&r.endDate&&r.startDate>r.endDate)return c.NextResponse.json({success:!1,error:"Start date must be before end date"},{status:400});let t=await d(r);return c.NextResponse.json({success:!0,report:t})}catch(e){return console.error("Error generating consolidated revenue report:",e),c.NextResponse.json({success:!1,error:"Failed to generate consolidated revenue report",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(e){try{let{data:r,error:t}=await supabase.rpc("calculate_revenue_consolidated",{p_start_date:e.startDate?.toISOString(),p_end_date:e.endDate?.toISOString(),p_primary_currency:e.primaryCurrency||"USD"});if(t)throw console.error("Error in revenue calculation RPC:",t),Error(`Revenue calculation failed: ${t.message}`);if(!r)throw Error("No revenue data returned from calculation");return{primaryCurrency:r.primary_currency,totalRevenue:r.total_revenue_primary,revenueByCurrency:r.revenue_by_currency||{},exchangeRatesUsed:await l(r.revenue_by_currency,r.primary_currency),calculatedAt:new Date(r.calculated_at),periodStart:r.period_start?new Date(r.period_start):void 0,periodEnd:r.period_end?new Date(r.period_end):void 0}}catch(e){throw console.error("Error in generateRevenueReport:",e),e}}async function l(e,r){let t={};for(let n of Object.keys(e))if(n!==r)try{let{data:e}=await supabase.rpc("get_exchange_rate",{p_from_currency:n,p_to_currency:r});e&&(t[`${n}_to_${r}`]=e)}catch(e){console.warn(`Failed to get exchange rate for ${n} to ${r}:`,e)}return t}async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("groupBy")||"month",n=r.get("currency"),a=r.get("startDate")?new Date(r.get("startDate")):void 0,o=r.get("endDate")?new Date(r.get("endDate")):void 0,s=supabase.from("product_orders").select(`
        total_price,
        currency_code,
        completed_at,
        created_at
      `).eq("status","completed");n&&(s=s.eq("currency_code",n)),a&&(s=s.gte("completed_at",a.toISOString())),o&&(s=s.lte("completed_at",o.toISOString()));let{data:u,error:i}=await s.order("completed_at",{ascending:!0});if(i)throw console.error("Error fetching revenue breakdown:",i),Error(`Failed to fetch revenue data: ${i.message}`);let d=function(e,r){let t={};return e.forEach(e=>{let n;let a=new Date(e.completed_at||e.created_at);switch(r){case"day":default:n=a.toISOString().split("T")[0];break;case"week":let o=new Date(a);o.setDate(a.getDate()-a.getDay()),n=o.toISOString().split("T")[0];break;case"month":n=`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}`;break;case"year":n=String(a.getFullYear())}t[n]||(t[n]={revenue:0,orders:0,currencies:{}}),t[n].revenue+=e.total_price,t[n].orders+=1;let s=e.currency_code;t[n].currencies[s]=(t[n].currencies[s]||0)+e.total_price}),Object.entries(t).map(([e,r])=>({period:e,...r})).sort((e,r)=>e.period.localeCompare(r.period))}(u||[],t);return c.NextResponse.json({success:!0,breakdown:d,groupBy:t,currency:n,periodStart:a,periodEnd:o})}catch(e){return console.error("Error getting revenue breakdown:",e),c.NextResponse.json({success:!1,error:"Failed to get revenue breakdown",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function y(e){try{let{data:e,error:r}=await supabase.from("product_orders").select(`
        total_price,
        currency_code,
        status,
        created_at,
        completed_at
      `);if(r)throw console.error("Error fetching revenue summary:",r),Error(`Failed to fetch revenue summary: ${r.message}`);let t=new Date,n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),a=new Date(t.getFullYear(),t.getMonth(),1),o=new Date(t.getFullYear(),0,1),s={total:{orders:e?.length||0,revenue:0,revenueByCurrency:{}},today:{orders:0,revenue:0,revenueByCurrency:{}},thisMonth:{orders:0,revenue:0,revenueByCurrency:{}},thisYear:{orders:0,revenue:0,revenueByCurrency:{}}};return e?.forEach(e=>{let r=new Date(e.completed_at||e.created_at),t=e.total_price,c=e.currency_code;"completed"===e.status&&(s.total.revenue+=t,s.total.revenueByCurrency[c]=(s.total.revenueByCurrency[c]||0)+t,r>=n&&(s.today.orders+=1,s.today.revenue+=t,s.today.revenueByCurrency[c]=(s.today.revenueByCurrency[c]||0)+t),r>=a&&(s.thisMonth.orders+=1,s.thisMonth.revenue+=t,s.thisMonth.revenueByCurrency[c]=(s.thisMonth.revenueByCurrency[c]||0)+t),r>=o&&(s.thisYear.orders+=1,s.thisYear.revenue+=t,s.thisYear.revenueByCurrency[c]=(s.thisYear.revenueByCurrency[c]||0)+t))}),c.NextResponse.json({success:!0,summary:s,generatedAt:new Date})}catch(e){return console.error("Error getting revenue summary:",e),c.NextResponse.json({success:!1,error:"Failed to get revenue summary",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/reports/revenue/route",pathname:"/api/reports/revenue",filename:"route",bundlePath:"app/api/reports/revenue/route"},resolvedPagePath:"D:\\VS-projects\\try\\alraya-store\\app\\api\\reports\\revenue\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:v,workUnitAsyncStorage:m,serverHooks:_}=g;function h(){return(0,s.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:m})}},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,580],()=>t(88006));module.exports=n})();