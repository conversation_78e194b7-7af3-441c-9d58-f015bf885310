# Multi-Currency Platform Integration Guide

This guide provides step-by-step instructions for implementing the scalable multi-currency system in your Al-Raya Store.

## 📋 Prerequisites

- Next.js 14+ application
- Supabase project with admin access
- Node.js 18+ and npm/yarn
- Basic understanding of TypeScript and React

## 🚀 Phase 1: Database Setup

### 1.1 Run Database Migrations

Execute the migration scripts in order:

```bash
# Connect to your Supabase project
psql "postgresql://postgres:[PASSWORD]@[PROJECT_REF].supabase.co:5432/postgres"

# Run migrations in order
\i migrations/001_multi_currency_schema.sql
\i migrations/002_data_migration.sql
\i migrations/003_rls_policies.sql
\i migrations/004_utility_functions.sql
\i migrations/005_table_cleanup.sql
```

### 1.2 Set USD as Default Currency

After running the main migrations, set USD as the default currency:

```bash
# Run the USD default setup script
\i scripts/set_user_currency_defaults.sql
```

### 1.3 Verify Migration Success

```sql
-- Check if all tables exist
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('currencies', 'exchange_rates', 'user_wallets', 'wallet_transactions');

-- Verify sample data and USD as default
SELECT * FROM currencies WHERE is_active = true ORDER BY sort_order;
SELECT * FROM exchange_rates WHERE is_active = true LIMIT 5;
SELECT primary_currency_code FROM client_currency_settings; -- Should show USD
```

### 1.3 Set Environment Variables

Update your `.env.local`:

```env
# Existing Supabase variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Multi-currency configuration (USD as default)
NEXT_PUBLIC_DEFAULT_CURRENCY=USD
NEXT_PUBLIC_ENABLE_MULTI_CURRENCY=true
NEXT_PUBLIC_ENABLE_CURRENCY_CONVERSION=true
```

## 🔧 Phase 2: Code Integration

### 2.1 Install Dependencies

```bash
npm install @supabase/supabase-js
# or
yarn add @supabase/supabase-js
```

### 2.2 Update TypeScript Configuration

Ensure your `tsconfig.json` includes the new types:

```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./lib/*", "./components/*", "./app/*"]
    }
  }
}
```

### 2.3 Replace Legacy Components

#### Update Currency Selector Usage

**Before:**
```tsx
import { CurrencySelector } from "@/components/wallet/CurrencySelector"

<CurrencySelector
  selectedCurrency={selectedCurrency}
  onCurrencyChange={handleCurrencyChange}
/>
```

**After:**
```tsx
import { CurrencySelector } from "@/components/wallet/CurrencySelector"

<CurrencySelector
  selectedCurrency={selectedCurrency}
  onCurrencyChange={handleCurrencyChange}
  availableCurrencies={availableCurrencies}
  showFullName={true}
/>
```

#### Update Wallet Hook Usage

**Before:**
```tsx
const [walletData, setWalletData] = useState(mockWalletData)
```

**After:**
```tsx
const {
  walletData,
  availableCurrencies,
  isLoading,
  convertCurrency,
  getConversionPreview,
  refreshData
} = useWalletData(userId)
```

### 2.4 Add New Components

#### Currency Converter
```tsx
import { CurrencyConverter } from "@/components/wallet/CurrencyConverter"

<CurrencyConverter
  availableCurrencies={availableCurrencies}
  walletBalances={walletData.balances}
  onConvert={handleCurrencyConversion}
  onPreview={getConversionPreview}
  isLoading={isConverting}
/>
```

#### Admin Currency Management
```tsx
import { CurrencySettingsPage } from "@/components/pages/CurrencySettingsPage"
import { ExchangeRateManager } from "@/components/admin/ExchangeRateManager"
import { RevenueReportDashboard } from "@/components/admin/RevenueReportDashboard"
```

## 🧪 Phase 3: Testing

### 3.1 Database Function Tests

Test core database functions:

```sql
-- Test exchange rate retrieval
SELECT get_exchange_rate('USD', 'SDG');
SELECT get_exchange_rate('SDG', 'EGP');

-- Test currency conversion
SELECT convert_currency(100, 'USD', 'SDG');

-- Test wallet operations
SELECT get_or_create_wallet('user-uuid', 'USD');
SELECT update_wallet_balance('user-uuid', 'USD', 50.00);

-- Test revenue calculation
SELECT calculate_revenue_consolidated(
  NOW() - INTERVAL '30 days',
  NOW(),
  'USD'
);
```

### 3.2 API Endpoint Tests

Test API endpoints using curl or Postman:

```bash
# Test currency management
curl -X GET "http://localhost:3000/api/currencies?active=true"

# Test exchange rates
curl -X GET "http://localhost:3000/api/exchange-rates?base=USD"

# Test currency conversion preview
curl -X GET "http://localhost:3000/api/wallet/convert/preview?from=USD&to=SDG&amount=100"

# Test revenue reporting
curl -X GET "http://localhost:3000/api/reports/revenue?primaryCurrency=USD"
```

### 3.3 Frontend Component Tests

#### Test Currency Selector
1. Navigate to wallet page
2. Verify all enabled currencies appear
3. Test currency switching
4. Verify Arabic RTL currencies display correctly

#### Test Currency Converter
1. Open currency converter
2. Select different from/to currencies
3. Enter amount and verify preview
4. Execute conversion and verify balance updates

#### Test Admin Interface
1. Navigate to `/admin/currency-settings`
2. Test adding new currency
3. Test updating exchange rates
4. Verify revenue reporting displays correctly

### 3.4 Integration Tests

#### Wallet Operations
```typescript
// Test wallet creation and balance updates
const userId = 'test-user-id'
const wallet = await getOrCreateWallet(userId, 'USD')
await updateWalletBalance(userId, 'USD', 100)

// Test currency conversion
const conversion = await convertWalletBalance(userId, 'USD', 'SDG', 50)
expect(conversion.success).toBe(true)
```

#### Order Processing
```typescript
// Test multi-currency order pricing
const pricing = await calculateOrderPricingWithExchangeRate(
  template,
  formData,
  { basePriceUSD: 10, targetCurrency: 'SDG' }
)
expect(pricing.targetPrice).toBeGreaterThan(0)
```

## 🔍 Phase 4: Validation & Monitoring

### 4.1 Data Integrity Checks

```sql
-- Verify no negative balances
SELECT * FROM user_wallets WHERE balance < 0;

-- Check for orphaned transactions
SELECT * FROM wallet_transactions wt
LEFT JOIN user_wallets uw ON wt.wallet_id = uw.id
WHERE uw.id IS NULL;

-- Validate exchange rates
SELECT * FROM exchange_rates 
WHERE rate <= 0 OR rate > 1000000;
```

### 4.2 Performance Monitoring

Monitor these metrics:
- Exchange rate API response times
- Currency conversion success rates
- Database query performance
- Revenue calculation times

### 4.3 Error Handling Verification

Test error scenarios:
- Invalid currency codes
- Missing exchange rates
- Insufficient wallet balances
- Network failures during conversion

## 📊 Phase 5: Production Deployment

### 5.1 Pre-Deployment Checklist

- [ ] All migrations executed successfully
- [ ] Environment variables configured
- [ ] API endpoints tested
- [ ] Admin interface accessible
- [ ] Exchange rates populated
- [ ] RLS policies active
- [ ] Backup procedures in place

### 5.2 Deployment Steps

1. **Backup existing data**
   ```bash
   pg_dump -h [host] -U postgres -d postgres > backup_pre_multicurrency.sql
   ```

2. **Deploy code changes**
   ```bash
   npm run build
   npm run deploy
   ```

3. **Run production migrations**
   ```bash
   # Use production database connection
   psql $DATABASE_URL -f migrations/001_multi_currency_schema.sql
   # ... continue with other migrations
   ```

4. **Verify deployment**
   - Test currency switching
   - Verify exchange rates loading
   - Test currency conversion
   - Check admin interface

### 5.3 Post-Deployment Monitoring

Monitor for 24-48 hours:
- Error rates in logs
- Database performance
- User experience issues
- Exchange rate updates

## 🔧 Troubleshooting

### Common Issues

#### 1. Exchange Rate Not Found
```
Error: Exchange rate not found for USD to XYZ
```
**Solution:** Add missing exchange rate in admin interface or database.

#### 2. RLS Policy Blocking Access
```
Error: new row violates row-level security policy
```
**Solution:** Check user authentication and RLS policies.

#### 3. Currency Conversion Failed
```
Error: Insufficient balance for conversion
```
**Solution:** Verify wallet balance and conversion amount.

### Debug Commands

```sql
-- Check user permissions
SELECT auth.uid(), auth.role();

-- View active exchange rates
SELECT * FROM exchange_rates WHERE is_active = true;

-- Check wallet balances
SELECT * FROM user_wallets WHERE user_id = auth.uid();
```

## 📈 Performance Optimization

### Database Optimization
- Index frequently queried columns
- Use connection pooling
- Monitor query performance
- Regular VACUUM and ANALYZE

### Caching Strategy
- Cache exchange rates for 5 minutes
- Cache currency list for 1 hour
- Use Redis for high-traffic scenarios

### API Optimization
- Implement rate limiting
- Use compression for large responses
- Batch currency operations where possible

## 🔒 Security Considerations

### Data Protection
- Encrypt sensitive financial data
- Use HTTPS for all API calls
- Implement proper authentication
- Regular security audits

### Access Control
- Restrict admin functions to authorized users
- Implement proper RLS policies
- Log all financial operations
- Monitor for suspicious activity

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Next.js 14 Guide](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Multi-Currency Best Practices](https://stripe.com/docs/currencies)

## 🆘 Support

For issues or questions:
1. Check the troubleshooting section
2. Review error logs
3. Test in development environment
4. Contact development team with specific error messages and steps to reproduce
