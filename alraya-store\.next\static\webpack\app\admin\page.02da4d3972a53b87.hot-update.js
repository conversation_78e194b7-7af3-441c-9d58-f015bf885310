"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/ProductDashboard.tsx":
/*!***********************************************!*\
  !*** ./components/admin/ProductDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductDashboard: () => (/* binding */ ProductDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_services_categoryService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/services/categoryService */ \"(app-pages-browser)/./lib/services/categoryService.ts\");\n/* harmony import */ var _SimpleProductForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./SimpleProductForm */ \"(app-pages-browser)/./components/admin/SimpleProductForm.tsx\");\n/* harmony import */ var _CategoryManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CategoryManager */ \"(app-pages-browser)/./components/admin/CategoryManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductDashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductDashboard() {\n    _s();\n    // ## TODO: Add user authentication check\n    // ## TODO: Implement real-time updates with Supabase subscriptions\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sub-navigation state\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"products\");\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load products and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            (0,_lib_services_categoryService__WEBPACK_IMPORTED_MODULE_8__.initializeCategories)() // Initialize default categories\n            ;\n            loadData();\n        }\n    }[\"ProductDashboard.useEffect\"], []);\n    // Apply filters when search or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"ProductDashboard.useEffect\"], [\n        products,\n        searchQuery,\n        filters\n    ]);\n    /**\n   * ## TODO: Replace with Supabase real-time subscription\n   * Load products and statistics\n   */ const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [productsData, statsData] = await Promise.all([\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_7__.getProducts)().catch((err)=>{\n                    console.error(\"Error loading products:\", err);\n                    return [];\n                }),\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_7__.getProductStats)().catch((err)=>{\n                    console.error(\"Error loading stats:\", err);\n                    return {\n                        totalProducts: 0,\n                        activeProducts: 0,\n                        digitalProducts: 0,\n                        physicalProducts: 0,\n                        totalPackages: 0,\n                        totalOrders: 0,\n                        popularCategories: []\n                    };\n                })\n            ]);\n            setProducts(Array.isArray(productsData) ? productsData : []);\n            setStats(statsData);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            // Set fallback data\n            setProducts([]);\n            setStats({\n                totalProducts: 0,\n                activeProducts: 0,\n                digitalProducts: 0,\n                physicalProducts: 0,\n                totalPackages: 0,\n                totalOrders: 0,\n                popularCategories: []\n            });\n        // ## TODO: Show error toast notification\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Apply search and filters to products\n   */ const applyFilters = ()=>{\n        // Ensure products is a valid array\n        const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.name && p.category) : [];\n        let filtered = [\n            ...validProducts\n        ];\n        // Apply search\n        if (searchQuery.trim()) {\n            const query = searchQuery.toLowerCase();\n            filtered = filtered.filter((product)=>{\n                var _product_name, _product_description, _product_category;\n                return ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.toLowerCase().includes(query)) || ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(query)) || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.toLowerCase().includes(query));\n            });\n        }\n        // Apply filters\n        if (filters.category) {\n            filtered = filtered.filter((p)=>p.category === filters.category);\n        }\n        if (filters.productType) {\n            filtered = filtered.filter((p)=>p.productType === filters.productType);\n        }\n        if (filters.processingType) {\n            filtered = filtered.filter((p)=>p.processingType === filters.processingType);\n        }\n        if (filters.isActive !== undefined) {\n            filtered = filtered.filter((p)=>p.isActive === filters.isActive);\n        }\n        setFilteredProducts(filtered);\n    };\n    /**\n   * Handle product creation\n   */ const handleProductCreate = async (product)=>{\n        setProducts((prev)=>[\n                ...prev,\n                product\n            ]);\n        setIsCreateDialogOpen(false);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product update\n   */ const handleProductUpdate = async (product)=>{\n        setProducts((prev)=>prev.map((p)=>p.id === product.id ? product : p));\n        setIsEditDialogOpen(false);\n        setSelectedProduct(null);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product deletion\n   */ const handleProductDelete = async (productId)=>{\n        if (!confirm(\"هل أنت متأكد من حذف هذا المنتج؟\")) return;\n        try {\n            await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_7__.deleteProduct)(productId);\n            setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n            await loadData() // Refresh stats\n            ;\n        } catch (error) {\n            console.error(\"Error deleting product:\", error);\n        // ## TODO: Show error toast notification\n        }\n    };\n    /**\n   * Get unique categories for filter dropdown\n   */ const getCategories = ()=>{\n        const categories = [\n            ...new Set(products.map((p)=>p.category))\n        ];\n        return categories.sort();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 p-4 lg:p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl lg:text-3xl font-bold text-white\",\n                                children: \"إدارة المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400 mt-1\",\n                                children: \"إنشاء وتعديل وإدارة منتجات المتجر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>setIsCreateDialogOpen(true),\n                        className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 min-h-[44px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            \"إضافة منتج جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            isCreateDialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                onClick: (e)=>{\n                    if (e.target === e.currentTarget) {\n                        setIsCreateDialogOpen(false);\n                    }\n                },\n                onKeyDown: (e)=>{\n                    if (e.key === 'Escape') {\n                        setIsCreateDialogOpen(false);\n                    }\n                },\n                tabIndex: -1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    onSave: handleProductCreate,\n                    onCancel: ()=>setIsCreateDialogOpen(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 bg-slate-800/50 rounded-lg p-1 border border-slate-700/50 overflow-x-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"products\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"products\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"منتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"categories\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"categories\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"الفئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"فئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            activeTab === \"categories\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CategoryManager__WEBPACK_IMPORTED_MODULE_10__.CategoryManager, {\n                onCategoryChange: loadData\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي المنتجات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: stats.totalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات النشطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: stats.activeProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات الرقمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-400\",\n                                                        children: stats.digitalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-8 w-8 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي الحزم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-400\",\n                                                        children: stats.totalPackages\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"البحث في المنتجات...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-slate-700 border-slate-600 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.category || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"الفئة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الفئات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            getCategories().map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.productType || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            productType: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الأنواع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"digital\",\n                                                                children: \"رقمي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"physical\",\n                                                                children: \"مادي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"service\",\n                                                                children: \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredProducts.map((product)=>{\n                            var _product_packages;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-white text-lg mb-2\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: product.isActive ? \"default\" : \"secondary\",\n                                                                children: product.isActive ? \"نشط\" : \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: product.productType === \"digital\" ? \"border-purple-500 text-purple-400\" : product.productType === \"physical\" ? \"border-blue-500 text-blue-400\" : \"border-green-500 text-green-400\",\n                                                                children: product.productType === \"digital\" ? \"رقمي\" : product.productType === \"physical\" ? \"مادي\" : \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            product.processingType === \"instant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-green-500/20 text-green-400 border-green-500/30\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"فوري\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm mb-3 line-clamp-2\",\n                                                children: product.description || \"لا يوجد وصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-sm text-slate-400 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"الفئة: \",\n                                                            product.category\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            ((_product_packages = product.packages) === null || _product_packages === void 0 ? void 0 : _product_packages.length) || 0,\n                                                            \" حزمة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>{\n                                                            setSelectedProduct(product);\n                                                            setIsEditDialogOpen(true);\n                                                        },\n                                                        className: \"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"تعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleProductDelete(product.id),\n                                                        className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 7\n                    }, this),\n                    filteredProducts.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-16 w-16 text-slate-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"لا توجد منتجات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-400 mb-4\",\n                                    children: searchQuery || Object.keys(filters).length > 0 ? \"لم يتم العثور على منتجات تطابق البحث أو الفلاتر\" : \"ابدأ بإنشاء منتجك الأول\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                !searchQuery && Object.keys(filters).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setIsCreateDialogOpen(true),\n                                    className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"إضافة منتج جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, this),\n                    isEditDialogOpen && selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            product: selectedProduct,\n                            onSave: handleProductUpdate,\n                            onCancel: ()=>{\n                                setIsEditDialogOpen(false);\n                                setSelectedProduct(null);\n                            },\n                            isEditing: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDashboard, \"w8YCEC8amsgFyatH9jatbW86Xe8=\");\n_c = ProductDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProductDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/ProductDashboard.tsx\n"));

/***/ })

});