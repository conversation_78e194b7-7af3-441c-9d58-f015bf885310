import React, { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import AccountCard from "@/components/AccountCard";
import UCPackage from "@/components/UCPackage";
import BlogPost from "@/components/BlogPost";
import SEO from "@/components/SEO";
import { OrganizationJsonLd } from "@/components/JsonLd";
import { motion, useScroll, useTransform } from "framer-motion";
import { Shield, Zap, Clock, Award, Users, Sparkles } from "lucide-react";
import { useConfig } from "@/contexts/ConfigContext";
import { Helmet } from "react-helmet-async";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const fadeInLeft = {
  hidden: { opacity: 0, x: -50 },
  visible: { opacity: 1, x: 0 },
};

const fadeInRight = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const popIn = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: { 
    scale: 1, 
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 260,
      damping: 20
    }
  },
};

// Static data types
interface StaticAccountModel {
  id: string;
  title: string;
  title_en?: string;
  description: string;
  description_en?: string;
  priceUSD: number;
  priceEGP: number;
  image: string;
  category: string;
  featured: boolean;
  localCurrencyCode?: string;
  type: string;
}

interface StaticUCPackageModel {
  id: string;
  amount: number;
  priceEGP: number;
  priceUSD: number;
  image: string;
  featured?: boolean;
  localCurrencyCode?: string;
  exchangeRate?: number;
  originalPrice?: number;
  discountPercent?: number;
  highlights?: string[];
  category?: string;
  type: string;
}

interface StaticBlogPostModel {
  id: string;
  title: string;
  title_en?: string;
  excerpt: string;
  excerpt_en?: string;
  content: string;
  content_en?: string;
  image: string;
  author: string;
  author_en?: string;
  date: string;
  slug?: string;
  slug_en?: string;
  featured?: boolean;
  special?: boolean;
  type: string;
}

interface StaticModModel {
  id: string;
  title: string;
  title_en?: string;
  description: string;
  description_en?: string;
  priceUSD: number;
  localCurrency?: number;
  localCurrencyCode?: string;
  exchangeRate?: number;
  image: string;
  features: string[];
  requirements: string[];
  includedSoftware: string[];
  platform?: "pc" | "mobile";
  mobileType?: "android" | "ios";
  rootRequired?: boolean;
  jailbreakRequired?: boolean;
  featured?: boolean;
  special?: boolean;
}

interface StaticSettingsModel {
  siteName: string;
  siteName_en?: string;
  heroTitle: string;
  heroTitle_en?: string;
  heroSubtitle: string;
  heroSubtitle_en?: string;
  heroImage: string;
  statsTitle?: string;
  statsTitle_en?: string;
  statCustomers?: string;
  statCustomersLabel?: string;
  statCustomersLabel_en?: string;
  statAccounts?: string;
  statAccountsLabel?: string;
  statAccountsLabel_en?: string;
  statSupport?: string;
  statSupportLabel?: string;
  statSupportLabel_en?: string;
  statSecurity?: string;
  statSecurityLabel?: string;
  statSecurityLabel_en?: string;
  featuredAccountsSectionTitle?: string;
  featuredAccountsSectionTitle_en?: string;
  featuredModsSectionTitle?: string;
  featuredModsSectionTitle_en?: string;
  whyChooseUsSectionTitle?: string;
  whyChooseUsSectionTitle_en?: string;
  homeSectionOrder?: string[];
  homeSectionVisibility?: {[key: string]: boolean};
}

// Static data objects with RNG VIP branding
const staticSettings: StaticSettingsModel = {
  siteName: "RNG VIP",
  siteName_en: "RNG VIP",
  heroTitle: "RNG VIP - أفضل متجر للهاكات والتطبيقات المتقدمة",
  heroTitle_en: "RNG VIP - Best Store for RNG Hacks & Advanced Tools",
  heroSubtitle: "احصل على أفضل هاكات RNG وأدوات PUBG المتقدمة مع ضمان الأمان والجودة",
  heroSubtitle_en: "Get the best RNG hacks and advanced PUBG tools with guaranteed safety and quality",
  heroImage: "https://i.ibb.co/N2Jb4PXP/cropped-image.jpg",
  statsTitle: "إحصائياتنا المميزة",
  statsTitle_en: "Our Outstanding Statistics",
  statCustomers: "15K+",
  statCustomersLabel: "عميل راضي",
  statCustomersLabel_en: "Satisfied Customers",
  statAccounts: "800+",
  statAccountsLabel: "حساب متاح",
  statAccountsLabel_en: "Available Accounts",
  statSupport: "24/7",
  statSupportLabel: "دعم فني",
  statSupportLabel_en: "Technical Support",
  statSecurity: "100%",
  statSecurityLabel: "أمان مضمون",
  statSecurityLabel_en: "Guaranteed Security",
  featuredAccountsSectionTitle: "الحسابات المميزة",
  featuredAccountsSectionTitle_en: "Featured Accounts",
  featuredModsSectionTitle: "هاكات RNG المميزة",
  featuredModsSectionTitle_en: "Featured RNG Hacks",
  whyChooseUsSectionTitle: "لماذا تختار RNG VIP؟",
  whyChooseUsSectionTitle_en: "Why Choose RNG VIP?",
  homeSectionOrder: [
    "stats",
    "featuredAccounts",
    "featuredMods",
    "gameFeatures",
    "ucStore",
    "testimonials",
    "whyChooseUs",
    "blogPosts",
    "newsletter"
  ],
  homeSectionVisibility: {
    stats: true,
    featuredAccounts: true,
    featuredMods: true,
    gameFeatures: true,
    ucStore: true,
    testimonials: true,
    whyChooseUs: true,
    blogPosts: true,
    newsletter: true
  }
};

const staticFeaturedAccounts: StaticAccountModel[] = [
  {
    id: "acc1",
    title: "حساب RNG VIP مميز - مستوى عالي",
    title_en: "RNG VIP Premium Account - High Level",
    description: "حساب PUBG مميز مع هاكات RNG متقدمة، بدلات نادرة، أسلحة ذهبية، ومستوى عالي مع إحصائيات ممتازة",
    description_en: "Premium PUBG account with advanced RNG hacks, rare outfits, golden weapons, and high level with excellent statistics",
    priceUSD: 45,
    priceEGP: 1400,
    image: "https://i.ibb.co/qBvZQyM/pubg-account-1.jpg",
    category: "Premium",
    featured: true,
    localCurrencyCode: "EGP",
    type: "account"
  },
  {
    id: "acc2",
    title: "حساب RNG Bypass متقدم",
    title_en: "Advanced RNG Bypass Account",
    description: "حساب مع تقنيات RNG bypass المتقدمة، حماية من البان، وأدوات تحكم كاملة في اللعبة",
    description_en: "Account with advanced RNG bypass techniques, ban protection, and full game control tools",
    priceUSD: 65,
    priceEGP: 2000,
    image: "https://i.ibb.co/8xQvKpM/pubg-account-2.jpg",
    category: "VIP",
    featured: true,
    localCurrencyCode: "EGP",
    type: "account"
  },
  {
    id: "acc3",
    title: "حساب PUBG RNG كامل المواصفات",
    title_en: "Full-Featured PUBG RNG Account",
    description: "حساب شامل مع جميع هاكات RNG، UC مجاني، بدلات حصرية، وضمان عدم الحظر",
    description_en: "Complete account with all RNG hacks, free UC, exclusive outfits, and ban protection guarantee",
    priceUSD: 85,
    priceEGP: 2650,
    image: "https://i.ibb.co/yNTqBcM/pubg-account-3.jpg",
    category: "Ultimate",
    featured: true,
    localCurrencyCode: "EGP",
    type: "account"
  },
  {
    id: "acc4",
    title: "حساب RNG Store مبتدئ",
    title_en: "RNG Store Beginner Account",
    description: "حساب مثالي للمبتدئين مع أساسيات RNG hack وأدوات بسيطة للتعلم",
    description_en: "Perfect account for beginners with RNG hack basics and simple learning tools",
    priceUSD: 25,
    priceEGP: 780,
    image: "https://i.ibb.co/2WzKpqL/pubg-account-4.jpg",
    category: "Starter",
    featured: true,
    localCurrencyCode: "EGP",
    type: "account"
  },
  {
    id: "acc5",
    title: "حساب PUBG Hacks متوسط",
    title_en: "Intermediate PUBG Hacks Account",
    description: "حساب متوسط المستوى مع مجموعة متنوعة من هاكات PUBG وأدوات RNG",
    description_en: "Intermediate level account with variety of PUBG hacks and RNG tools",
    priceUSD: 55,
    priceEGP: 1700,
    image: "https://i.ibb.co/5Bc8xQM/pubg-account-5.jpg",
    category: "Standard",
    featured: true,
    localCurrencyCode: "EGP",
    type: "account"
  },
  {
    id: "acc6",
    title: "حساب RNG VIP إكسكلوسيف",
    title_en: "RNG VIP Exclusive Account",
    description: "حساب حصري مع أحدث تقنيات RNG، هاكات متقدمة، وميزات VIP حصرية",
    description_en: "Exclusive account with latest RNG technology, advanced hacks, and exclusive VIP features",
    priceUSD: 120,
    priceEGP: 3750,
    image: "https://i.ibb.co/qNvBcXM/pubg-account-6.jpg",
    category: "Exclusive",
    featured: true,
    localCurrencyCode: "EGP",
    type: "account"
  }
];

const staticUCPackages: StaticUCPackageModel[] = [
  {
    id: "uc1",
    amount: 60,
    priceUSD: 1,
    priceEGP: 30,
    image: "https://i.ibb.co/qBvZQyM/uc-60.jpg",
    featured: true,
    localCurrencyCode: "EGP",
    category: "Basic",
    type: "uc"
  },
  {
    id: "uc2",
    amount: 325,
    priceUSD: 5,
    priceEGP: 155,
    image: "https://i.ibb.co/8xQvKpM/uc-325.jpg",
    featured: true,
    localCurrencyCode: "EGP",
    originalPrice: 180,
    discountPercent: 14,
    highlights: ["الأكثر شعبية", "Most Popular"],
    category: "Popular",
    type: "uc"
  },
  {
    id: "uc3",
    amount: 660,
    priceUSD: 10,
    priceEGP: 310,
    image: "https://i.ibb.co/yNTqBcM/uc-660.jpg",
    featured: true,
    localCurrencyCode: "EGP",
    category: "Standard",
    type: "uc"
  },
  {
    id: "uc4",
    amount: 1800,
    priceUSD: 25,
    priceEGP: 775,
    image: "https://i.ibb.co/2WzKpqL/uc-1800.jpg",
    featured: true,
    localCurrencyCode: "EGP",
    originalPrice: 850,
    discountPercent: 9,
    highlights: ["أفضل قيمة", "Best Value"],
    category: "Premium",
    type: "uc"
  },
  {
    id: "uc5",
    amount: 3850,
    priceUSD: 50,
    priceEGP: 1550,
    image: "https://i.ibb.co/5Bc8xQM/uc-3850.jpg",
    featured: true,
    localCurrencyCode: "EGP",
    category: "VIP",
    type: "uc"
  },
  {
    id: "uc6",
    amount: 8100,
    priceUSD: 100,
    priceEGP: 3100,
    image: "https://i.ibb.co/qNvBcXM/uc-8100.jpg",
    featured: true,
    localCurrencyCode: "EGP",
    originalPrice: 3400,
    discountPercent: 9,
    highlights: ["عرض خاص", "Special Offer"],
    category: "Ultimate",
    type: "uc"
  }
];

const staticFeaturedMods: StaticModModel[] = [
  {
    id: "mod1",
    title: "RNG Hack Pro - النسخة المتقدمة",
    title_en: "RNG Hack Pro - Advanced Version",
    description: "أقوى هاك RNG متاح مع جميع الميزات المتقدمة وحماية كاملة من البان",
    description_en: "Most powerful RNG hack available with all advanced features and full ban protection",
    priceUSD: 35,
    localCurrency: 1100,
    localCurrencyCode: "EGP",
    image: "https://i.ibb.co/qBvZQyM/rng-hack-pro.jpg",
    features: [
      "RNG Manipulation | تلاعب بالـ RNG",
      "Aimbot Advanced | إيمبوت متقدم",
      "ESP Wallhack | رؤية عبر الجدران",
      "Speed Hack | هاك السرعة",
      "No Recoil | بدون ارتداد"
    ],
    requirements: [
      "Windows 10/11 | ويندوز 10/11",
      "4GB RAM | 4 جيجا رام",
      "DirectX 11 | دايركت إكس 11"
    ],
    includedSoftware: [
      "RNG Injector | حاقن RNG",
      "Bypass Tool | أداة التجاوز",
      "Config Manager | مدير الإعدادات"
    ],
    platform: "pc",
    featured: true
  },
  {
    id: "mod2",
    title: "PUBG RNG Bypass Ultimate",
    title_en: "PUBG RNG Bypass Ultimate",
    description: "نظام تجاوز RNG الأكثر تقدماً مع حماية شاملة وتحديثات مستمرة",
    description_en: "Most advanced RNG bypass system with comprehensive protection and continuous updates",
    priceUSD: 55,
    localCurrency: 1700,
    localCurrencyCode: "EGP",
    image: "https://i.ibb.co/8xQvKpM/rng-bypass.jpg",
    features: [
      "Anti-Ban System | نظام مضاد للحظر",
      "RNG Control | تحكم في RNG",
      "Loot Hack | هاك الغنائم",
      "Vehicle Hack | هاك المركبات",
      "Radar Hack | هاك الرادار"
    ],
    requirements: [
      "Windows 10/11 | ويندوز 10/11",
      "8GB RAM | 8 جيجا رام",
      "NVIDIA GTX 1060+ | إنفيديا GTX 1060+"
    ],
    includedSoftware: [
      "Bypass Engine | محرك التجاوز",
      "Protection Suite | مجموعة الحماية",
      "Update Manager | مدير التحديثات"
    ],
    platform: "pc",
    featured: true,
    special: true
  },
  {
    id: "mod3",
    title: "RNG Store Mobile Hack",
    title_en: "RNG Store Mobile Hack",
    description: "هاك RNG مخصص للهواتف المحمولة مع ميزات متقدمة وسهولة في الاستخدام",
    description_en: "RNG hack designed for mobile phones with advanced features and ease of use",
    priceUSD: 25,
    localCurrency: 780,
    localCurrencyCode: "EGP",
    image: "https://i.ibb.co/yNTqBcM/rng-mobile.jpg",
    features: [
      "Mobile RNG Control | تحكم RNG للموبايل",
      "Touch Aimbot | إيمبوت اللمس",
      "ESP Mobile | رؤية للموبايل",
      "Speed Control | تحكم السرعة",
      "Auto Fire | إطلاق تلقائي"
    ],
    requirements: [
      "Android 8.0+ | أندرويد 8.0+",
      "4GB RAM | 4 جيجا رام",
      "Root Access | صلاحيات الروت"
    ],
    includedSoftware: [
      "Mobile Injector | حاقن الموبايل",
      "Root Manager | مدير الروت",
      "Config Tool | أداة الإعدادات"
    ],
    platform: "mobile",
    mobileType: "android",
    rootRequired: true,
    featured: true
  }
];

const staticBlogPosts: StaticBlogPostModel[] = [
  {
    id: "blog1",
    title: "أحدث تقنيات RNG Hack في 2024",
    title_en: "Latest RNG Hack Techniques in 2024",
    excerpt: "اكتشف أحدث التقنيات والطرق المتقدمة في عالم RNG hacking وكيفية استخدامها بأمان",
    excerpt_en: "Discover the latest techniques and advanced methods in RNG hacking world and how to use them safely",
    content: "محتوى المقال الكامل حول تقنيات RNG...",
    content_en: "Full article content about RNG techniques...",
    image: "https://i.ibb.co/qBvZQyM/blog-rng-2024.jpg",
    author: "فريق RNG VIP",
    author_en: "RNG VIP Team",
    date: "2024-01-15",
    slug: "rng-hack-techniques-2024",
    slug_en: "rng-hack-techniques-2024",
    featured: true,
    special: true,
    type: "post"
  },
  {
    id: "blog2",
    title: "دليل شامل لـ PUBG RNG Bypass",
    title_en: "Complete Guide to PUBG RNG Bypass",
    excerpt: "دليل مفصل وشامل لفهم وتطبيق تقنيات RNG bypass في لعبة PUBG بطريقة آمنة",
    excerpt_en: "Detailed and comprehensive guide to understand and apply RNG bypass techniques in PUBG safely",
    content: "محتوى الدليل الشامل...",
    content_en: "Complete guide content...",
    image: "https://i.ibb.co/8xQvKpM/blog-bypass-guide.jpg",
    author: "خبير RNG",
    author_en: "RNG Expert",
    date: "2024-01-10",
    slug: "pubg-rng-bypass-guide",
    slug_en: "pubg-rng-bypass-guide",
    featured: true,
    type: "post"
  },
  {
    id: "blog3",
    title: "نصائح الأمان عند استخدام RNG Store",
    title_en: "Safety Tips When Using RNG Store",
    excerpt: "نصائح مهمة وإرشادات الأمان للحفاظ على حسابك آمناً عند استخدام أدوات RNG",
    excerpt_en: "Important tips and safety guidelines to keep your account safe when using RNG tools",
    content: "نصائح الأمان المفصلة...",
    content_en: "Detailed safety tips...",
    image: "https://i.ibb.co/yNTqBcM/blog-safety-tips.jpg",
    author: "مختص الأمان",
    author_en: "Security Specialist",
    date: "2024-01-05",
    slug: "rng-safety-tips",
    slug_en: "rng-safety-tips",
    featured: true,
    type: "post"
  }
];

const Index = () => {
  const { t, i18n } = useTranslation('common');
  const currentLanguage = i18n.language;
  const isEnglish = currentLanguage === 'en';

  // Initialize with static data - no loading states needed
  const featuredAccounts = staticFeaturedAccounts;
  const ucPackages = staticUCPackages;
  const blogPosts = staticBlogPosts;
  const settings = staticSettings;
  const featuredMods = staticFeaturedMods;
  const sectionOrder = staticSettings.homeSectionOrder || [
    "stats",
    "featuredAccounts",
    "featuredMods",
    "gameFeatures",
    "ucStore",
    "testimonials",
    "whyChooseUs",
    "blogPosts",
    "newsletter"
  ];
  const sectionVisibility = staticSettings.homeSectionVisibility || {
    stats: true,
    featuredAccounts: true,
    featuredMods: true,
    gameFeatures: true,
    ucStore: true,
    testimonials: true,
    whyChooseUs: true,
    blogPosts: true,
    newsletter: true
  };
  
  // Helper function to get the text based on language
  const getLocalizedText = (arabicText: string | undefined, englishText: string | undefined): string => {
    if (isEnglish) {
      return englishText || arabicText || '';
    }
    return arabicText || englishText || '';
  };

  const heroRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  const heroOpacity = useTransform(scrollYProgress, [0, 1], [1, 0]);
  const heroScale = useTransform(scrollYProgress, [0, 1], [1, 1.1]);
  const heroY = useTransform(scrollYProgress, [0, 1], [0, 100]);

  const navigate = useNavigate();
  const { siteName, socialLinks } = useConfig();

  // Handle navigation with scroll reset
  const handleNavigation = (path: string) => {
    window.scrollTo(0, 0);
    navigate(path);
  };

  // Hero Section rendering with language support
  const renderHero = () => {
    if (!settings) return null;
    
    const heroTitle = getLocalizedText(settings.heroTitle, settings.heroTitle_en);
    const heroSubtitle = getLocalizedText(settings.heroSubtitle, settings.heroSubtitle_en);
    
    return (
      <motion.section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div 
          className="absolute inset-0 z-0"
          style={{ 
            scale: heroScale,
            y: heroY
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background z-10" />
          <div 
            className="h-full w-full bg-cover bg-center"
            style={{ 
              backgroundImage: `url(${settings.heroImage})` 
            }}
          />
          <div className="absolute inset-0 bg-black/50" />
        </motion.div>
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div 
            className="max-w-4xl mx-auto text-center"
            style={{ opacity: heroOpacity }}
          >
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6 leading-tight">
              {heroTitle}
            </h1>
            <p className="text-lg md:text-xl text-gray-200 mb-8 md:mb-10 max-w-2xl mx-auto">
              {heroSubtitle}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg"
                className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 text-lg px-8"
                onClick={() => handleNavigation('/accounts')}
              >
                {t('home.call_to_action.button')}
              </Button>
            </div>
          </motion.div>
        </div>
      </motion.section>
    );
  };

  // Stats Section with language support
  const renderStats = () => {
    if (!settings || !sectionVisibility.stats) return null;
    
    const statsTitle = getLocalizedText(settings.statsTitle, settings.statsTitle_en);
    const statCustomersLabel = getLocalizedText(settings.statCustomersLabel, settings.statCustomersLabel_en);
    const statAccountsLabel = getLocalizedText(settings.statAccountsLabel, settings.statAccountsLabel_en);
    const statSupportLabel = getLocalizedText(settings.statSupportLabel, settings.statSupportLabel_en);
    const statSecurityLabel = getLocalizedText(settings.statSecurityLabel, settings.statSecurityLabel_en);
    
    const stats = [
      { value: settings.statCustomers || "10K+", label: statCustomersLabel, icon: <Users className="w-6 h-6 text-pubg-orange" /> },
      { value: settings.statAccounts || "500+", label: statAccountsLabel, icon: <Award className="w-6 h-6 text-pubg-orange" /> },
      { value: settings.statSupport || "24/7", label: statSupportLabel, icon: <Clock className="w-6 h-6 text-pubg-orange" /> },
      { value: settings.statSecurity || "100%", label: statSecurityLabel, icon: <Shield className="w-6 h-6 text-pubg-orange" /> },
    ];
    
        return (
      <motion.section
                initial="hidden"
                whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
                variants={staggerContainer}
        className="py-12 md:py-16 relative z-10"
              >
        <div className="container mx-auto px-4">
          <motion.h2 
            variants={fadeInUp}
            className="text-2xl md:text-3xl font-bold text-white text-center mb-10"
          >
            {statsTitle || t('home.stats.title')}
          </motion.h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-10">
            {stats.map((stat, index) => (
                  <motion.div
                    key={index}
                    variants={popIn}
                className="flex flex-col items-center text-center"
                  >
                <div className="w-14 h-14 rounded-full bg-card/30 backdrop-blur-sm border border-border/50 flex items-center justify-center mb-4">
                  {stat.icon}
                    </div>
                <h3 className="text-2xl md:text-3xl font-bold text-pubg-orange mb-1">
                  {stat.value}
                </h3>
                <p className="text-sm md:text-base text-gray-300">
                  {stat.label}
                </p>
                  </motion.div>
                ))}
            </div>
        </div>
      </motion.section>
        );
  };

  // Featured Accounts Section with language support
  const renderFeaturedAccounts = () => {
    if (!settings || !sectionVisibility.featuredAccounts) return null;
    
    const sectionTitle = getLocalizedText(
      settings.featuredAccountsSectionTitle, 
      settings.featuredAccountsSectionTitle_en
    ) || t('home.featured_accounts');
    
    const viewAllText = t('home.view_all_accounts');
    
    return (
      <motion.section
                initial="hidden"
                whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
        className="py-16 relative z-10"
      >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <motion.h2 
              variants={fadeInLeft}
              className="text-3xl font-bold text-white"
            >
              {sectionTitle}
            </motion.h2>
            <motion.div variants={fadeInRight}>
              <Button 
                variant="outline" 
                className="text-white border-pubg-orange hover:bg-pubg-orange hover:text-pubg-dark"
                onClick={() => handleNavigation('/accounts')}
              >
                {viewAllText}
              </Button>
              </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredAccounts.map((account, index) => (
              <motion.div
                key={account.id || index}
                variants={popIn}
                className="h-full"
              >
                <AccountCard account={account} />
                  </motion.div>
                ))}
          </div>
        </div>
      </motion.section>
    );
  };

  // UC Store Section with language support
  const renderUCStore = () => {
    if (!settings || !sectionVisibility.ucStore) return null;
    
    return (
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
        className="py-16 relative z-10 bg-card/30"
                >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <motion.h2 
              variants={fadeInLeft}
              className="text-3xl font-bold text-white"
                  >
              {t('home.uc_packages')}
            </motion.h2>
            <motion.div variants={fadeInRight}>
              <Button 
                variant="outline" 
                className="text-white border-pubg-orange hover:bg-pubg-orange hover:text-pubg-dark"
                onClick={() => handleNavigation('/uc-store')}
              >
                {t('home.view_all_packages')}
              </Button>
                </motion.div>
            </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {ucPackages.slice(0, 6).map((ucPackage, index) => (
              <motion.div
                key={ucPackage.id || index}
                variants={popIn}
                className="h-full"
              >
                <UCPackage ucPackage={ucPackage} />
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>
    );
  };

  // Featured Mods Section with language support
  const renderFeaturedMods = () => {
    if (!settings || !sectionVisibility.featuredMods || featuredMods.length === 0) return null;
    
    const sectionTitle = getLocalizedText(
      settings.featuredModsSectionTitle, 
      settings.featuredModsSectionTitle_en
    ) || t('home.featured_mods');
    
    return (
      <motion.section
                initial="hidden"
                whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
                variants={staggerContainer}
        className="py-16 relative z-10"
              >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <motion.h2 
              variants={fadeInLeft}
              className="text-3xl font-bold text-white"
            >
              {sectionTitle}
            </motion.h2>
            <motion.div variants={fadeInRight}>
              <Button 
                variant="outline" 
                className="text-white border-pubg-orange hover:bg-pubg-orange hover:text-pubg-dark"
                onClick={() => handleNavigation('/mods')}
              >
                {t('home.view_all_mods')}
              </Button>
            </motion.div>
                            </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredMods.map((mod, index) => (
              <motion.div
                key={mod.id || index}
                variants={popIn}
                className="h-full"
              >
                <Card className="h-full border border-border/50 bg-card/50 backdrop-blur-sm hover:bg-card/70 transition-all duration-300 overflow-hidden">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg md:text-xl text-white">{mod.title}</CardTitle>
                    <CardDescription className="text-muted-foreground">
                      {mod.description?.substring(0, 100)}{mod.description?.length > 100 ? '...' : ''}
                        </CardDescription>
                      </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex flex-wrap gap-2 mb-4">
                      {mod.features?.slice(0, 3).map((feature, idx) => (
                        <span key={idx} className="px-2 py-1 bg-background/50 rounded-md text-xs text-muted-foreground">
                          {feature}
                        </span>
                      ))}
                        </div>
                      </CardContent>
                  <CardFooter>
                        <Button 
                      variant="default"
                      className="w-full bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                      onClick={() => handleNavigation(`/mods/${mod.id}`)}
                        >
                      {isEnglish ? "View Details" : "عرض التفاصيل"}
                        </Button>
                      </CardFooter>
                    </Card>
                  </motion.div>
                ))}
          </div>
        </div>
      </motion.section>
    );
  };
  
  // Blog Posts Section with language support
  const renderBlogPosts = () => {
    if (!settings || !sectionVisibility.blogPosts || blogPosts.length === 0) return null;
    
    return (
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
        className="py-16 relative z-10 bg-card/30"
      >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <motion.h2 
              variants={fadeInLeft}
              className="text-3xl font-bold text-white"
            >
              {t('home.latest_blog_posts')}
            </motion.h2>
            <motion.div variants={fadeInRight}>
              <Button 
                variant="outline" 
                className="text-white border-pubg-orange hover:bg-pubg-orange hover:text-pubg-dark"
                onClick={() => handleNavigation('/blog')}
              >
                {t('home.view_all_posts')}
              </Button>
              </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {blogPosts.slice(0, 3).map((post, index) => (
              <motion.div
                key={post.id || index}
                variants={popIn}
                className="h-full"
              >
                <BlogPost post={post} />
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>
    );
  };

  // Newsletter Section with language support
  const renderNewsletter = () => {
    if (!settings || !sectionVisibility.newsletter) return null;
    
    return (
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
        className="py-16 relative z-10"
      >
        <div className="container mx-auto px-4">
          <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-8 max-w-3xl mx-auto">
            <motion.h2 
              variants={fadeInUp}
              className="text-2xl font-bold text-white text-center mb-4"
              >
              {t('home.newsletter.title')}
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-muted-foreground text-center mb-6"
            >
              {t('home.newsletter.description')}
            </motion.p>
            <motion.div 
              variants={fadeInUp}
              className="flex flex-col sm:flex-row gap-3"
            >
              <input
                type="email"
                className="flex-1 px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-pubg-orange"
                placeholder={t('home.newsletter.email_placeholder')}
              />
              <Button className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90">
                {t('home.newsletter.subscribe')}
              </Button>
              </motion.div>
            </div>
        </div>
      </motion.section>
        );
  };

  // Why Choose Us Section with language support
  const renderWhyChooseUs = () => {
    if (!settings || !sectionVisibility.whyChooseUs) return null;
    
    const sectionTitle = getLocalizedText(
      settings.whyChooseUsSectionTitle, 
      settings.whyChooseUsSectionTitle_en
    ) || t('home.why_choose_us.title');
    
    const features = [
      { 
        icon: <Shield className="w-8 h-8 text-pubg-orange" />, 
        title: t('home.why_choose_us.secure'),
        description: t('home.why_choose_us.secure_desc')
      },
      { 
        icon: <Zap className="w-8 h-8 text-pubg-orange" />, 
        title: t('home.why_choose_us.fast'),
        description: t('home.why_choose_us.fast_desc')
      },
      { 
        icon: <Clock className="w-8 h-8 text-pubg-orange" />, 
        title: t('home.why_choose_us.support'),
        description: t('home.why_choose_us.support_desc')
      }
    ];
    
    return (
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
        className="py-16 relative z-10 bg-card/30"
      >
        <div className="container mx-auto px-4">
          <motion.h2 
            variants={fadeInUp}
            className="text-3xl font-bold text-white text-center mb-12"
          >
            {sectionTitle}
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={popIn}
                className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 text-center flex flex-col items-center"
              >
                <div className="mb-4 p-3 bg-background/50 rounded-full">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>
    );
  };

  // Render a section based on its ID
  const renderSection = (sectionId: string) => {
    // Skip rendering if section is not visible
    if (!sectionVisibility[sectionId]) {
      return null;
    }

    switch (sectionId) {
      case "stats":
        return renderStats();

      case "featuredAccounts":
        return renderFeaturedAccounts();

      case "featuredMods":
        return renderFeaturedMods();

      case "gameFeatures":
        return (
          <section key="gameFeatures" className="py-20 px-4 bg-card/30 relative overflow-hidden">
            <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-**********-adc38448a05e?q=80&w=2070')] bg-cover bg-center opacity-10"></div>
            <div className="container mx-auto relative z-10">
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, amount: 0.1 }}
                variants={fadeInUp}
                className="text-center mb-16"
              >
                <h2 className="text-3xl font-bold text-white mb-4">{t('home.game_features.title')}</h2>
                <p className="text-muted-foreground max-w-3xl mx-auto">
                  {t('home.game_features.subtitle')}
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
                <motion.div
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  variants={fadeInLeft}
                >
                  <div className="relative rounded-xl overflow-hidden">
                    <div className="aspect-video relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-pubg-orange/30 to-pubg-blue/30 mix-blend-overlay z-10"></div>
                      <img 
                        src="/images/uc_coin_fallback.png" 
                        alt="PUBG Game Features"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        className="w-20 h-20 rounded-full bg-pubg-orange/80 flex items-center justify-center cursor-pointer shadow-lg"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  variants={staggerContainer}
                >
                  <motion.div variants={fadeInRight} className="mb-6">
                    <h3 className="text-2xl font-bold text-white mb-2">{t('home.game_features.outfits_title')}</h3>
                    <p className="text-muted-foreground">
                      {t('home.game_features.outfits_desc')}
                    </p>
                  </motion.div>

                  <div className="space-y-6">
                    {[
                      {
                        icon: <Sparkles className="w-6 h-6" />,
                        title: t('home.game_features.outfits_title'),
                        description: t('home.game_features.outfits_desc')
                      },
                      {
                        icon: <Zap className="w-6 h-6" />,
                        title: t('home.game_features.weapons_title'),
                        description: t('home.game_features.weapons_desc')
                      },
                      {
                        icon: <Award className="w-6 h-6" />,
                        title: t('home.game_features.stats_title'),
                        description: t('home.game_features.stats_desc')
                      }
                    ].map((feature, index) => (
                      <motion.div key={index} variants={fadeInUp} className="flex gap-4">
                        <div className="shrink-0 w-12 h-12 bg-pubg-orange/20 rounded-full flex items-center justify-center">
                          {React.cloneElement(feature.icon, { className: "text-pubg-orange w-6 h-6" })}
                        </div>
                        <div>
                          <h4 className="text-lg font-bold text-white mb-1">{feature.title}</h4>
                          <p className="text-muted-foreground">{feature.description}</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </div>
          </section>
        );

      case "ucStore":
        return renderUCStore();

      case "testimonials":
        return (
          <section key="testimonials" className="py-20 px-4 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-b from-card/30 to-background/90 z-0"></div>
            <div className="container mx-auto relative z-10">
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, amount: 0.1 }}
                variants={fadeInUp}
                className="text-center mb-16"
              >
                <h2 className="text-3xl font-bold text-white mb-4">{t('home.testimonials.title')}</h2>
                <p className="text-muted-foreground max-w-3xl mx-auto">
                  {t('home.testimonials.subtitle')}
                </p>
              </motion.div>

              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, amount: 0.1 }}
                variants={staggerContainer}
                className="grid grid-cols-1 md:grid-cols-3 gap-8"
              >
                {[
                  {
                    name: isEnglish ? "Mohammed Abdullah" : "محمد عبدالله",
                    avatar: "https://randomuser.me/api/portraits/men/1.jpg",
                    rating: 5,
                    text: isEnglish ?
                      "RNG VIP is the best rng store I've ever used. Their rng hack tools are amazing and work perfectly. The bypass system is undetectable and very safe." :
                      "RNG VIP أفضل rng store استخدمته على الإطلاق. أدوات rng hack رائعة وتعمل بشكل مثالي. نظام bypass غير قابل للكشف وآمن جداً."
                  },
                  {
                    name: isEnglish ? "Sarah Ahmed" : "سارة أحمد",
                    avatar: "https://randomuser.me/api/portraits/women/1.jpg",
                    rating: 5,
                    text: isEnglish ?
                      "I've been using RNG VIP's pubg hacks for months now. The rng bypass feature is incredible and I've never been banned. Highly recommend this rng store!" :
                      "أستخدم pubg hacks من RNG VIP منذ شهور. ميزة rng bypass رائعة ولم أتعرض للحظر أبداً. أنصح بشدة بهذا rng store!"
                  },
                  {
                    name: isEnglish ? "Ali Mahmoud" : "علي محمود",
                    avatar: "https://randomuser.me/api/portraits/men/3.jpg",
                    rating: 5,
                    text: isEnglish ?
                      "Outstanding service from RNG VIP! Their rng hack tools are top quality and the customer support is excellent. Best rng store in the market." :
                      "خدمة متميزة من RNG VIP! أدوات rng hack عالية الجودة والدعم الفني ممتاز. أفضل rng store في السوق."
                  }
                ].map((testimonial, index) => (
                  <motion.div
                    key={index}
                    variants={fadeInUp}
                    className="glass-card p-6 rounded-xl relative"
                  >
                    <div className="mb-6">
                      <div className="flex items-center mb-4">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="w-12 h-12 rounded-full mr-4 border-2 border-pubg-orange"
                        />
                        <div>
                          <h4 className="text-white font-bold">{testimonial.name}</h4>
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <svg
                                key={i}
                                xmlns="http://www.w3.org/2000/svg"
                                className={`h-4 w-4 ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-500'}`}
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="relative">
                        <svg
                          className="absolute top-0 left-0 transform -translate-x-6 -translate-y-8 text-pubg-orange/30 w-16 h-16"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                        </svg>
                        <p className="text-muted-foreground relative z-10">{testimonial.text}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </section>
        );

      case "whyChooseUs":
        return renderWhyChooseUs();

      case "blogPosts":
        return renderBlogPosts();

      case "newsletter":
        return renderNewsletter();

      default:
        return null;
    }
  };

  return (
    <>
      <Helmet>
        <title>RNG VIP | أفضل متجر RNG Store وهاكات PUBG RNG</title>
        <meta name="description" content="RNG VIP - أفضل متجر rng store لهاكات PUBG RNG وأدوات RNG bypass المتقدمة. احصل على rng hack وpubg hacks بأعلى جودة وأمان مضمون" />
        <meta name="keywords" content="rng store, rng hack, rng bypass, pubg rng, pubg hacks, RNG VIP, هاكات ببجي, متجر RNG" />
        <meta property="og:title" content="RNG VIP - أفضل متجر RNG Store وهاكات PUBG" />
        <meta property="og:description" content="أفضل rng store لهاكات PUBG RNG وأدوات RNG bypass المتقدمة مع ضمان الأمان والجودة" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://rng-vip.netlify.app" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="RNG VIP - أفضل متجر RNG Store" />
        <meta name="twitter:description" content="أفضل rng store لهاكات PUBG RNG وأدوات RNG bypass" />
      </Helmet>
      <div className="min-h-screen">
        <SEO
          title="RNG VIP - أفضل متجر RNG Store وهاكات PUBG RNG"
          description="RNG VIP أفضل rng store لهاكات PUBG RNG وأدوات rng bypass المتقدمة. احصل على rng hack وpubg hacks بأعلى جودة مع ضمان الأمان والحماية من البان. متجر RNG موثوق مع دعم فني 24/7"
          ogImage="/images/rng-vip-banner.jpg"
        />
        <OrganizationJsonLd
          name="RNG VIP"
          url="https://rng-vip.netlify.app"
          logo="https://rng-vip.netlify.app/favicon.png"
          sameAs={socialLinks || []}
        />
        
        {/* Hero Section */}
        {renderHero()}

        {/* Render sections in the specified order */}
        {sectionOrder.map(sectionId => renderSection(sectionId))}
      </div>
    </>
  );
};

export default Index;
