# نظام إدارة العملات - دليل التكامل

## 🎯 نظرة عامة

تم تطوير نظام إدارة العملات المبسط للوحة الإدارة بحيث يتكامل مع جميع أجزاء النظام الحالي. هذا الدليل يوضح كيفية عمل النظام ونقاط التكامل المختلفة.

## 🏗️ هيكل النظام الحالي

### العملة الأساسية
- **USD (الدولار الأمريكي)** هو العملة الأساسية للنظام
- جميع الأسعار في قاعدة البيانات مخزنة بالدولار الأمريكي
- أسعار الصرف محسوبة نسبة إلى الدولار الأمريكي

### العملات المدعومة حالياً
1. **USD** - الدولار الأمريكي (العملة الأساسية)
2. **SDG** - الجنيه السوداني (سعر الصرف: 450.00)
3. **EGP** - الجنيه المصري (سعر الصرف: 30.80)

## 🔧 مكونات النظام

### 1. واجهة إدارة العملات (`CurrencyManagement.tsx`)

**الميزات الحالية:**
- عرض العملات الموجودة مع أسعار الصرف
- إضافة عملة جديدة مع تحديد سعر الصرف
- تفعيل/تعطيل العملات
- واجهة عربية كاملة

**سير العمل لإضافة عملة جديدة:**
1. المدير يدخل اسم العملة (بالإنجليزية والعربية)
2. يدخل رمز العملة (مثل: ر.س)
3. النظام يطلب: "1 USD = ? [العملة الجديدة]"
4. المدير يدخل سعر الصرف
5. النظام يحفظ العملة ويفعلها تلقائياً

### 2. نقاط التكامل مع النظام

#### أ) نظام المنتجات
**الملف:** `components/admin/ProductDashboard.tsx`

```typescript
// الأسعار مخزنة كأرقام في المنتجات
packages: [
  {
    id: "pkg_1",
    name: "100 جوهرة",
    price: 35, // بالدولار الأمريكي
    // ...
  }
]
```

**التكامل:**
- عند إضافة عملة جديدة، ستظهر في محدد العملات في لوحة المنتجات
- الأسعار ستحول تلقائياً باستخدام أسعار الصرف الجديدة
- المدير يمكنه عرض أسعار المنتجات بأي عملة

#### ب) نظام المحافظ
**الملف:** `lib/data/mockWalletData.ts`

```typescript
balances: [
  {
    currency: "USD",
    amount: 100.00,
    lastUpdated: new Date()
  },
  {
    currency: "SDG", 
    amount: 15000,
    lastUpdated: new Date()
  }
  // العملة الجديدة ستضاف هنا تلقائياً
]
```

**التكامل:**
- كل مستخدم سيحصل على رصيد منفصل للعملة الجديدة
- المعاملات ستسجل بالعملة المحددة
- تحويل العملات متاح بين جميع العملات المفعلة

#### ج) نظام أسعار الصرف
**الملف:** `lib/utils/exchangeRates.ts`

```typescript
// أسعار الصرف مخزنة نسبة للدولار الأمريكي
const exchangeRates = {
  "USD": 1,           // العملة الأساسية
  "SDG": 450.00,      // 1 USD = 450 SDG
  "EGP": 30.80,       // 1 USD = 30.80 EGP
  "SAR": 3.75         // العملة الجديدة: 1 USD = 3.75 SAR
}
```

**التكامل:**
- عند إضافة عملة جديدة، يتم إنشاء سعر صرف في الاتجاهين
- جميع حسابات الأسعار تستخدم هذه الأسعار
- تحديث أسعار الصرف يؤثر على جميع العمليات الحسابية

## 📊 قاعدة البيانات (التكامل المستقبلي)

### الجداول المطلوبة

#### 1. جدول العملات (`currencies`)
```sql
CREATE TABLE currencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(3) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  arabic_name VARCHAR(100),
  decimal_places INTEGER DEFAULT 2,
  is_rtl BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. جدول أسعار الصرف (`exchange_rates`)
```sql
CREATE TABLE exchange_rates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  from_currency_code VARCHAR(3) REFERENCES currencies(code),
  to_currency_code VARCHAR(3) REFERENCES currencies(code),
  rate DECIMAL(18,8) NOT NULL,
  effective_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  source VARCHAR(50) DEFAULT 'manual',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. جدول محافظ المستخدمين (`user_wallets`)
```sql
CREATE TABLE user_wallets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  currency_code VARCHAR(3) REFERENCES currencies(code),
  balance DECIMAL(18,8) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, currency_code)
);
```

## 🔄 سير العمل لإضافة عملة جديدة

### 1. من واجهة الإدارة
```typescript
// المدير يملأ النموذج
const newCurrency = {
  name: "Saudi Riyal",
  arabicName: "الريال السعودي", 
  symbol: "ر.س",
  exchangeRate: 3.75 // 1 USD = 3.75 SAR
}
```

### 2. العمليات في قاعدة البيانات
```sql
-- 1. إضافة العملة
INSERT INTO currencies (code, name, symbol, arabic_name, is_active)
VALUES ('SAR', 'Saudi Riyal', 'ر.س', 'الريال السعودي', true);

-- 2. إضافة سعر الصرف (USD إلى SAR)
INSERT INTO exchange_rates (from_currency_code, to_currency_code, rate)
VALUES ('USD', 'SAR', 3.75);

-- 3. إضافة سعر الصرف العكسي (SAR إلى USD)
INSERT INTO exchange_rates (from_currency_code, to_currency_code, rate)
VALUES ('SAR', 'USD', 0.2667); -- 1/3.75

-- 4. إنشاء محافظ للمستخدمين الموجودين
INSERT INTO user_wallets (user_id, currency_code, balance)
SELECT id, 'SAR', 0 FROM auth.users;
```

### 3. التأثير على النظام
- **المنتجات:** الأسعار ستظهر بالريال السعودي
- **المحافظ:** كل مستخدم سيحصل على رصيد ريال سعودي
- **المعاملات:** يمكن إجراء معاملات بالريال السعودي
- **التحويل:** يمكن تحويل الأموال من/إلى الريال السعودي

## 🎨 واجهة المستخدم

### عرض العملات
```typescript
// في محدد العملات
const currencies = [
  { code: "USD", symbol: "$", arabicName: "الدولار الأمريكي" },
  { code: "SDG", symbol: "ج.س", arabicName: "الجنيه السوداني" },
  { code: "EGP", symbol: "ج.م", arabicName: "الجنيه المصري" },
  { code: "SAR", symbol: "ر.س", arabicName: "الريال السعودي" } // جديد
]
```

### تحويل الأسعار
```typescript
// حساب السعر بالعملة المختارة
function convertPrice(priceUSD: number, targetCurrency: string): number {
  const exchangeRate = getExchangeRate("USD", targetCurrency)
  return priceUSD * exchangeRate
}

// مثال: منتج بسعر 25 دولار
const priceUSD = 25
const priceSAR = convertPrice(priceUSD, "SAR") // 25 * 3.75 = 93.75 ريال
```

## 🔒 الأمان والتحقق

### التحقق من صحة البيانات
```typescript
// التحقق من رمز العملة
function validateCurrencyCode(code: string): boolean {
  return /^[A-Z]{3}$/.test(code) // 3 أحرف كبيرة
}

// التحقق من سعر الصرف
function validateExchangeRate(rate: number): boolean {
  return rate > 0 && rate < 1000000 // بين 0 و مليون
}
```

### صلاحيات الوصول
- فقط المديرين يمكنهم إضافة/تعديل العملات
- المستخدمين يمكنهم عرض العملات المفعلة فقط
- تسجيل جميع التغييرات في سجل المراجعة

## 📈 المراقبة والتحليل

### مؤشرات الأداء
- عدد المعاملات لكل عملة
- إجمالي الأرصدة لكل عملة
- معدل استخدام العملات الجديدة
- أخطاء تحويل العملات

### التقارير
- تقرير الإيرادات موحد بالدولار الأمريكي
- تفصيل الإيرادات حسب العملة
- تقرير استخدام العملات
- تقرير أسعار الصرف التاريخية

## 🚀 الخطوات التالية

### 1. التطوير الفوري
- [x] واجهة إدارة العملات (مكتملة)
- [ ] تكامل قاعدة البيانات
- [ ] API endpoints للعملات
- [ ] تحديث واجهات المستخدم

### 2. التحسينات المستقبلية
- [ ] تحديث أسعار الصرف التلقائي
- [ ] دعم العملات المشفرة
- [ ] تحليلات متقدمة للعملات
- [ ] إشعارات تغيير أسعار الصرف

## 📞 الدعم الفني

للأسئلة حول نظام العملات:
1. مراجعة هذا الدليل
2. فحص ملفات التكامل في المجلدات المذكورة
3. اختبار الواجهة في بيئة التطوير
4. التواصل مع فريق التطوير للمساعدة التقنية
