import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  MessageCircle, 
  Phone, 
  Mail, 
  Facebook, 
  Instagram, 
  Twitter, 
  Loader2,
  Globe,
  Clock,
  MapPin,
  Shield,
  Award,
  Link as LinkIcon,
  ArrowRight
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useConfig } from "@/contexts/ConfigContext";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { shouldShowLoader } from "@/lib/utils";
import GlobalLoader from "@/components/ui/GlobalLoader";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const fadeInLeft = {
  hidden: { opacity: 0, x: -50 },
  visible: { opacity: 1, x: 0 },
};

const fadeInRight = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Map icon names to their components
const iconMap: Record<string, JSX.Element> = {
  telegram: <MessageCircle className="text-[#0088cc] w-6 h-6" />,
  whatsapp: <Phone className="text-[#25D366] w-6 h-6" />,
  discord: <MessageCircle className="text-[#5865F2] w-6 h-6" />,
  email: <Mail className="text-[#EA4335] w-6 h-6" />,
  facebook: <Facebook className="text-[#1877F2] w-6 h-6" />,
  instagram: <Instagram className="text-[#E4405F] w-6 h-6" />,
  twitter: <Twitter className="text-[#1DA1F2] w-6 h-6" />,
  website: <Globe className="text-[#4285F4] w-6 h-6" />,
  location: <MapPin className="text-[#FF5722] w-6 h-6" />,
  hours: <Clock className="text-[#FFCA28] w-6 h-6" />,
  award: <Award className="text-[#8E24AA] w-6 h-6" />,
  link: <LinkIcon className="text-[#00BCD4] w-6 h-6" />,
  shield: <Shield className="text-[#4CAF50] w-6 h-6" />,
};

const Contact = () => {
  const { contactInfo, isLoading, siteName } = useConfig();
  const { toast } = useToast();
  const { t } = useTranslation('common');
  const { language } = useLanguage();
  
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });
  const [submitting, setSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    
    // Simulate form submission delay
    setTimeout(() => {
      toast({
        title: t('contact.success_title'),
        description: t('contact.success_description'),
      });
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      setSubmitting(false);
    }, 1500);
  };

  // Create dynamic social links from contact info
  const getSocialLinks = () => {
    const links = [];
    
    if (contactInfo?.telegramLink) {
      links.push({
        name: "Telegram",
        icon: "telegram",
        url: contactInfo.telegramLink,
        color: "#0088cc",
        backgroundColor: "bg-[#0088cc]/10",
        textColor: "text-[#0088cc]",
        hoverColor: "hover:bg-[#0088cc]/20",
        borderColor: "border-[#0088cc]/30",
        description: t('contact.social_telegram')
      });
    }
    
    if (contactInfo?.whatsappNumber) {
      const whatsappUrl = contactInfo.whatsappNumber.startsWith('http') 
        ? contactInfo.whatsappNumber 
        : `https://wa.me/${contactInfo.whatsappNumber.replace(/\D/g, '')}`;
      
      links.push({
        name: "WhatsApp",
        icon: "whatsapp",
        url: whatsappUrl,
        color: "#25D366",
        backgroundColor: "bg-[#25D366]/10",
        textColor: "text-[#25D366]",
        hoverColor: "hover:bg-[#25D366]/20",
        borderColor: "border-[#25D366]/30",
        description: t('contact.social_whatsapp')
      });
    }
    
    if (contactInfo?.discord) {
      links.push({
        name: "Discord",
        icon: "discord",
        url: contactInfo.discord,
        color: "#5865F2",
        backgroundColor: "bg-[#5865F2]/10",
        textColor: "text-[#5865F2]",
        hoverColor: "hover:bg-[#5865F2]/20",
        borderColor: "border-[#5865F2]/30",
        description: t('contact.social_discord')
      });
    }
    
    if (contactInfo?.facebook) {
      links.push({
        name: "Facebook",
        icon: "facebook",
        url: contactInfo.facebook,
        color: "#1877F2",
        backgroundColor: "bg-[#1877F2]/10",
        textColor: "text-[#1877F2]",
        hoverColor: "hover:bg-[#1877F2]/20",
        borderColor: "border-[#1877F2]/30",
        description: t('contact.social_facebook')
      });
    }
    
    if (contactInfo?.instagram) {
      links.push({
        name: "Instagram",
        icon: "instagram",
        url: contactInfo.instagram,
        color: "#E4405F",
        backgroundColor: "bg-[#E4405F]/10",
        textColor: "text-[#E4405F]",
        hoverColor: "hover:bg-[#E4405F]/20", 
        borderColor: "border-[#E4405F]/30",
        description: t('contact.social_instagram')
      });
    }
    
    if (contactInfo?.twitter) {
      links.push({
        name: "Twitter",
        icon: "twitter",
        url: contactInfo.twitter,
        color: "#1DA1F2",
        backgroundColor: "bg-[#1DA1F2]/10",
        textColor: "text-[#1DA1F2]",
        hoverColor: "hover:bg-[#1DA1F2]/20",
        borderColor: "border-[#1DA1F2]/30",
        description: t('contact.social_twitter')
      });
    }
    
    return links;
  };

  const socialLinks = getSocialLinks();

  if (shouldShowLoader(isLoading)) {
    return (
      <div className="min-h-screen pt-24 pb-16">
        <GlobalLoader fullPage />
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16 md:pt-24 pb-12 md:pb-16 px-3 md:px-4">
      <div className="container mx-auto">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          className="mb-8 md:mb-12 text-center"
        >
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 md:mb-4">{t('contact.title')}</h1>
          <p className="text-sm md:text-base text-muted-foreground max-w-2xl mx-auto px-2">
            {t('contact.description')}
          </p>
        </motion.div>

        {/* Enhanced Social Media Cards Section */}
        {socialLinks.length > 0 && (
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.1 }}
            variants={fadeInUp}
            className="mb-8 md:mb-16"
          >
            <div className="glass-card rounded-lg md:rounded-xl p-4 md:p-8 backdrop-blur-sm">
              <h2 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 text-center">{t('contact.social_links')}</h2>
              
              <div className="grid grid-cols-1 gap-4 md:gap-6">
                {socialLinks.map((link, index) => (
                  <motion.div
                    key={index}
                    variants={fadeInUp}
                    className={`flex items-stretch border ${link.borderColor} rounded-lg md:rounded-xl overflow-hidden transition-all duration-300 hover:scale-[1.01] group`}
                  >
                    <div className={`${link.backgroundColor} w-16 md:w-20 flex-shrink-0 flex items-center justify-center`}>
                      <div className={`w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center ${link.backgroundColor} group-hover:scale-110 transition-transform duration-300`}>
                        {iconMap[link.icon] || <LinkIcon className={`${link.textColor} w-5 h-5 md:w-6 md:h-6`} />}
                      </div>
                    </div>
                    
                    <div className="flex-grow p-3 md:p-4 flex items-center">
                      <div className="flex-grow">
                        <h3 className="text-white font-medium text-base md:text-lg">{link.name}</h3>
                        <p className="text-muted-foreground text-xs md:text-sm">{link.description}</p>
                      </div>
                    </div>
                    
                    <div className={`w-10 md:w-12 flex items-center justify-center ${link.hoverColor} opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer`}
                      onClick={() => window.open(link.url, "_blank")}>
                      <ArrowRight className={`${link.textColor} w-4 h-4 md:w-5 md:h-5`} />
                    </div>
                  </motion.div>
                ))}
              </div>
              
              {/* Modern Social Icons Row */}
              <div className="mt-8 md:mt-10 flex flex-wrap justify-center gap-2 md:gap-4">
                {socialLinks.map((link, index) => (
                  <motion.div
                    key={`icon-${index}`}
                    variants={fadeInUp}
                    whileHover={{ scale: 1.1 }}
                    className="cursor-pointer"
                    onClick={() => window.open(link.url, "_blank")}
                  >
                    <div className={`w-10 h-10 md:w-12 md:h-12 ${link.backgroundColor} ${link.borderColor} border rounded-full flex items-center justify-center relative group overflow-hidden`}>
                      <div className="absolute inset-0 bg-gradient-to-tr from-black/10 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                      {iconMap[link.icon] || <LinkIcon className={`${link.textColor} w-5 h-5 md:w-6 md:h-6`} />}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        <div className="grid grid-cols-1 gap-6 md:gap-8 mb-8 md:mb-16">
          {/* Contact Information */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInLeft}
          >
            <div className="glass-card rounded-lg md:rounded-xl p-4 md:p-8 h-full">
              <h2 className="text-xl md:text-2xl font-bold text-white mb-4 md:mb-6">{t('contact.contact_info')}</h2>
              <div className="space-y-4 md:space-y-6">
                {contactInfo?.whatsappNumber && (
                  <div className="flex items-center">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-[#25D366]/20 rounded-full flex items-center justify-center shrink-0 ml-3 md:ml-4">
                      <Phone className="text-[#25D366] w-5 h-5 md:w-6 md:h-6" />
                    </div>
                    <div>
                      <h3 className="text-white text-sm md:text-base font-medium mb-0.5 md:mb-1">{t('contact.phone_whatsapp')}</h3>
                      <p className="text-muted-foreground text-xs md:text-sm">{contactInfo.whatsappNumber}</p>
                    </div>
                  </div>
                )}
                
                {contactInfo?.email && (
                  <div className="flex items-center">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-[#EA4335]/20 rounded-full flex items-center justify-center shrink-0 ml-3 md:ml-4">
                      <Mail className="text-[#EA4335] w-5 h-5 md:w-6 md:h-6" />
                    </div>
                    <div>
                      <h3 className="text-white text-sm md:text-base font-medium mb-0.5 md:mb-1">{t('contact.email')}</h3>
                      <p className="text-muted-foreground text-xs md:text-sm">{contactInfo.email}</p>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-[#FFCA28]/20 rounded-full flex items-center justify-center shrink-0 ml-3 md:ml-4">
                    <Clock className="text-[#FFCA28] w-5 h-5 md:w-6 md:h-6" />
                  </div>
                  <div>
                    <h3 className="text-white text-sm md:text-base font-medium mb-0.5 md:mb-1">{t('contact.working_hours')}</h3>
                    <p className="text-muted-foreground text-xs md:text-sm">{t('contact.working_hours_value')}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-pubg-orange/20 rounded-full flex items-center justify-center shrink-0 ml-3 md:ml-4">
                    <Award className="text-pubg-orange w-5 h-5 md:w-6 md:h-6" />
                  </div>
                  <div>
                    <h3 className="text-white text-sm md:text-base font-medium mb-0.5 md:mb-1">{t('contact.customer_service')}</h3>
                    <p className="text-muted-foreground text-xs md:text-sm">{t('contact.customer_service_value')}</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInRight}
          >
            <div className="glass-card rounded-lg md:rounded-xl p-4 md:p-8">
              <h2 className="text-xl md:text-2xl font-bold text-white mb-4 md:mb-6">{t('contact.send_message')}</h2>
              <form className="space-y-3 md:space-y-4" onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 gap-3 md:gap-4">
                  <div>
                    <label htmlFor="name" className="block text-white text-sm md:text-base font-medium mb-1 md:mb-2">
                      {t('contact.name')}
                    </label>
                    <Input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full bg-muted border border-border rounded-lg px-3 py-1.5 md:px-4 md:py-2 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-pubg-orange"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-white text-sm md:text-base font-medium mb-1 md:mb-2">
                      {t('contact.email')}
                    </label>
                    <Input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full bg-muted border border-border rounded-lg px-3 py-1.5 md:px-4 md:py-2 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-pubg-orange"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="subject" className="block text-white text-sm md:text-base font-medium mb-1 md:mb-2">
                      {t('contact.subject')}
                    </label>
                    <Input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="w-full bg-muted border border-border rounded-lg px-3 py-1.5 md:px-4 md:py-2 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-pubg-orange"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-white text-sm md:text-base font-medium mb-1 md:mb-2">
                      {t('contact.message')}
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      className="w-full bg-muted border border-border rounded-lg px-3 py-1.5 md:px-4 md:py-2 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-pubg-orange min-h-[100px] md:min-h-[150px]"
                      required
                    />
                  </div>
                </div>
                <Button
                  type="submit"
                  disabled={submitting}
                  className="w-full mt-2 md:mt-4 bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 text-sm md:text-base py-1.5 md:py-2"
                >
                  {submitting ? (
                    <>
                      <Loader2 className="ml-2 h-3 w-3 md:h-4 md:w-4 animate-spin" />
                      {t('contact.sending')}
                    </>
                  ) : (
                    t('contact.send_button')
                  )}
                </Button>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
