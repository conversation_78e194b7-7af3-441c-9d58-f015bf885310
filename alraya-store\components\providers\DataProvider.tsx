"use client"

import React, { useEffect, ReactNode } from 'react'
import { useAppLoading, useAppError, useAppInitialized, useLoadBulkData, useRefreshData } from '@/lib/stores/appStore'
import { ClientOnlyProvider } from './ClientOnlyProvider'

interface DataProviderProps {
  children: ReactNode
}

/**
 * DataProvider Component
 * 
 * This component handles the initial bulk data loading for the entire application.
 * It loads all necessary data on app startup and provides loading/error states.
 * 
 * Features:
 * - Loads all app data in a single API call on startup
 * - Shows loading screen while data is being fetched
 * - Handles offline mode with cached data
 * - Provides error handling with retry functionality
 * - Enables true offline functionality after initial load
 */
export function DataProvider({ children }: DataProviderProps) {
  return (
    <ClientOnlyProvider fallback={<LoadingScreen />}>
      <DataProviderInner>{children}</DataProviderInner>
    </ClientOnlyProvider>
  )
}

function DataProviderInner({ children }: DataProviderProps) {
  const isLoading = useAppLoading()
  const error = useAppError()
  const isInitialized = useAppInitialized()
  const loadBulkData = useLoadBulkData()
  const refreshData = useRefreshData()

  // Load data on component mount
  useEffect(() => {
    if (!isInitialized) {
      loadBulkData()
    }
  }, [isInitialized, loadBulkData])

  // Show loading screen while initial data is being fetched
  if (isLoading && !isInitialized) {
    return <LoadingScreen />
  }

  // Show error screen if data loading failed and no cached data available
  if (error && !isInitialized) {
    return <ErrorScreen error={error} onRetry={refreshData} />
  }

  // Render children once data is loaded or cached data is available
  return (
    <>
      {children}
      {/* Show offline indicator if in offline mode */}
      <OfflineIndicator />
    </>
  )
}

/**
 * Loading Screen Component
 * Shows while initial data is being loaded
 */
function LoadingScreen() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center">
      <div className="text-center space-y-6">
        {/* Logo */}
        <div className="mb-8">
          <div className="w-20 h-20 mx-auto bg-yellow-400 rounded-full flex items-center justify-center">
            <span className="text-2xl font-bold text-slate-900">رايه</span>
          </div>
        </div>

        {/* Loading Animation */}
        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
          </div>
          <h2 className="text-xl font-semibold text-yellow-400">جاري تحميل البيانات...</h2>
          <p className="text-slate-300 text-sm">يتم تحضير جميع البيانات للاستخدام بدون اتصال</p>
        </div>

        {/* Progress Dots */}
        <div className="flex justify-center space-x-2 rtl:space-x-reverse">
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-100"></div>
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-200"></div>
        </div>
      </div>
    </div>
  )
}

/**
 * Error Screen Component
 * Shows when data loading fails and no cached data is available
 */
function ErrorScreen({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        {/* Error Icon */}
        <div className="mb-8">
          <div className="w-20 h-20 mx-auto bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-2xl">⚠️</span>
          </div>
        </div>

        {/* Error Message */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-red-400">فشل في تحميل البيانات</h2>
          <p className="text-slate-300 text-sm">{error}</p>
        </div>

        {/* Retry Button */}
        <button
          onClick={onRetry}
          className="bg-yellow-400 hover:bg-yellow-500 text-slate-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
        >
          إعادة المحاولة
        </button>

        {/* Help Text */}
        <p className="text-slate-400 text-xs">
          تأكد من اتصالك بالإنترنت وحاول مرة أخرى
        </p>
      </div>
    </div>
  )
}

/**
 * Offline Indicator Component
 * Shows when app is in offline mode
 */
function OfflineIndicator() {
  const error = useAppError()
  const isOffline = error?.includes('offline') || error?.includes('cached')

  if (!isOffline) return null

  return (
    <div className="fixed top-0 left-0 right-0 bg-orange-500 text-white text-center py-2 text-sm z-50">
      <span className="flex items-center justify-center gap-2">
        <span>📡</span>
        <span>وضع عدم الاتصال - يتم استخدام البيانات المحفوظة</span>
      </span>
    </div>
  )
}
