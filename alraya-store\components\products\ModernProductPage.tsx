"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Star,
  Clock,
  Zap,
  ShoppingCart,
  Plus,
  Minus,
  Shield,
  MessageCircle,
  ArrowLeft
} from "lucide-react"
import { ProductTemplate, ProductFormData, ProductPackage, Currency } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"
import { ModernPackageGrid } from "./ModernPackageGrid"
import { ModernCustomerForm, validateCustomerForm } from "./ModernCustomerForm"
import { ModernCartControls, StickyCartControls } from "./ModernCartControls"

interface ModernProductPageProps {
  product: ProductTemplate
  onPurchase: (formData: ProductFormData) => void
  onBack?: () => void
  currency?: Currency
}

export function ModernProductPage({ 
  product, 
  onPurchase, 
  onBack,
  currency = "USD" 
}: ModernProductPageProps) {
  const [selectedPackage, setSelectedPackage] = useState<ProductPackage | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  
  const { convertPrice } = useCurrencyConverter()

  // Auto-select first package if available
  useEffect(() => {
    if (product.packages.length > 0 && !selectedPackage) {
      const firstAvailable = product.packages.find(pkg => pkg.isActive)
      if (firstAvailable) {
        setSelectedPackage(firstAvailable)
      }
    }
  }, [product.packages, selectedPackage])

  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = convertPrice(priceUSD, "USD", currency)
    return formatCurrency(convertedPrice, currency)
  }

  const handlePackageSelect = (pkg: ProductPackage) => {
    setSelectedPackage(pkg)
  }

  const handleQuantityChange = (change: number) => {
    const newQuantity = Math.max(1, quantity + change)
    setQuantity(newQuantity)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const calculateTotal = () => {
    if (!selectedPackage) return 0
    return selectedPackage.price * quantity
  }

  const handlePurchase = async () => {
    if (!selectedPackage) {
      alert("يرجى اختيار حزمة")
      return
    }

    // Validate form
    const errors = validateCustomerForm(product.fields, formData)
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsLoading(true)
    try {
      const purchaseData: ProductFormData = {
        templateId: product.id,
        selectedPackage,
        quantity,
        customFields: formData,
        totalPrice: calculateTotal(),
        currency
      }

      await onPurchase(purchaseData)
    } catch (error) {
      console.error("Purchase error:", error)
      alert("حدث خطأ أثناء معالجة الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  const totalPrice = calculateTotal()
  const originalTotal = selectedPackage?.originalPrice ? selectedPackage.originalPrice * quantity : totalPrice
  const savings = originalTotal - totalPrice

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" dir="rtl">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-slate-900/95 backdrop-blur-sm border-b border-slate-700">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="text-slate-300 hover:text-white"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            )}
            <h1 className="text-lg font-bold text-white truncate">{product.name}</h1>
          </div>
          <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
            {product.processingType === "instant" ? (
              <>
                <Zap className="h-3 w-3 ml-1" />
                فوري
              </>
            ) : (
              <>
                <Clock className="h-3 w-3 ml-1" />
                {product.estimatedTime}
              </>
            )}
          </Badge>
        </div>
      </div>

      <div className="p-4 space-y-6 pb-32">
        {/* Product Info Card */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm overflow-hidden">
          <CardContent className="p-0">
            <div className="flex">
              {/* Product Image */}
              <div className="w-24 h-24 bg-gradient-to-br from-slate-700 to-slate-600 flex items-center justify-center">
                <div className="text-2xl">🎮</div>
              </div>

              {/* Product Details */}
              <div className="flex-1 p-4">
                <h2 className="text-white font-bold text-lg mb-1">{product.name}</h2>
                <p className="text-slate-400 text-sm mb-2 line-clamp-2">
                  {product.description || "شحن سريع وآمن"}
                </p>
                <div className="flex items-center gap-4 text-xs">
                  <div className="flex items-center gap-1 text-yellow-400">
                    <Star className="h-3 w-3 fill-current" />
                    <span>4.8</span>
                  </div>
                  <span className="text-slate-500">+1000 عملية شحن</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Package Selection */}
        <div className="space-y-3">
          <h3 className="text-white font-bold text-lg">اختر الحزمة</h3>
          <ModernPackageGrid
            packages={product.packages}
            selectedPackage={selectedPackage}
            onPackageSelect={handlePackageSelect}
            currency={currency}
            disabled={isLoading}
          />
        </div>

        {/* Customer Information Form */}
        {selectedPackage && (
          <ModernCustomerForm
            fields={product.fields}
            values={formData}
            errors={formErrors}
            onChange={handleInputChange}
            disabled={isLoading}
          />
        )}

        {/* Cart Controls */}
        {selectedPackage && (
          <ModernCartControls
            selectedPackage={selectedPackage}
            quantity={quantity}
            currency={currency}
            onQuantityChange={handleQuantityChange}
            onPurchase={handlePurchase}
            isLoading={isLoading}
            processingType={product.processingType}
            estimatedTime={product.estimatedTime}
            showQuantity={true}
          />
        )}
      </div>

      {/* Sticky Bottom Actions */}
      <StickyCartControls
        selectedPackage={selectedPackage}
        quantity={quantity}
        currency={currency}
        onPurchase={handlePurchase}
        isLoading={isLoading}
        processingType={product.processingType}
      />
    </div>
  )
}
