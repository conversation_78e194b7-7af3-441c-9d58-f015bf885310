-- =====================================================
-- Al-Raya Store Multi-Currency Platform Rollback
-- EMERGENCY ROLLBACK SCRIPT - USE WITH CAUTION
-- =====================================================

-- WARNING: This script will remove all multi-currency functionality
-- and restore the original dual-currency system (SDG/EGP only).
-- 
-- BEFORE RUNNING:
-- 1. Create a full database backup
-- 2. Ensure you have the original table structures
-- 3. Verify all users are notified of the rollback
-- 4. Test in staging environment first

-- =====================================================
-- 1. BACKUP VERIFICATION
-- =====================================================
DO $$
BEGIN
  -- Check if backup tables exist
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name LIKE '%_backup_%'
  ) THEN
    RAISE EXCEPTION 'No backup tables found. Create backups before rollback!';
  END IF;
  
  RAISE NOTICE 'Backup tables found. Proceeding with rollback...';
END $$;

-- =====================================================
-- 2. DISABLE RLS TEMPORARILY
-- =====================================================
ALTER TABLE currencies DISABLE ROW LEVEL SECURITY;
ALTER TABLE exchange_rates DISABLE ROW LEVEL SECURITY;
ALTER TABLE client_currency_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE currency_audit_log DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_wallets DISABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE product_orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 3. EXTRACT CRITICAL DATA FOR RESTORATION
-- =====================================================

-- Create temporary tables to preserve essential data
CREATE TEMP TABLE temp_user_balances AS
SELECT 
  user_id,
  currency_code,
  balance,
  created_at,
  updated_at
FROM user_wallets
WHERE currency_code IN ('SDG', 'EGP');

CREATE TEMP TABLE temp_transactions AS
SELECT 
  id,
  user_id,
  transaction_type as type,
  amount,
  currency_code as currency,
  description,
  reference_number as reference,
  status,
  created_at
FROM wallet_transactions
WHERE currency_code IN ('SDG', 'EGP')
ORDER BY created_at DESC
LIMIT 10000; -- Preserve last 10k transactions

CREATE TEMP TABLE temp_orders AS
SELECT 
  id,
  user_id,
  template_id,
  template_name,
  template_category,
  product_data,
  pricing_data,
  customer_name as user_details_name,
  customer_email as user_details_email,
  customer_phone as user_details_phone,
  status,
  processing_type,
  timeline_events as timeline,
  metadata,
  created_at,
  updated_at,
  completed_at
FROM product_orders;

-- =====================================================
-- 4. DROP NEW MULTI-CURRENCY TABLES
-- =====================================================

-- Drop views first
DROP VIEW IF EXISTS user_wallets_legacy CASCADE;
DROP VIEW IF EXISTS wallet_transactions_legacy CASCADE;
DROP VIEW IF EXISTS product_orders_legacy CASCADE;
DROP VIEW IF EXISTS wallet_balances_with_currency CASCADE;
DROP VIEW IF EXISTS transactions_with_currency CASCADE;
DROP VIEW IF EXISTS orders_with_currency CASCADE;

-- Drop triggers
DROP TRIGGER IF EXISTS audit_currencies_trigger ON currencies;
DROP TRIGGER IF EXISTS audit_exchange_rates_trigger ON exchange_rates;
DROP TRIGGER IF EXISTS audit_client_currency_settings_trigger ON client_currency_settings;
DROP TRIGGER IF EXISTS update_currencies_updated_at ON currencies;
DROP TRIGGER IF EXISTS update_user_wallets_updated_at ON user_wallets;
DROP TRIGGER IF EXISTS update_product_orders_updated_at ON product_orders;
DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
DROP TRIGGER IF EXISTS update_client_currency_settings_updated_at ON client_currency_settings;

-- Drop functions
DROP FUNCTION IF EXISTS get_exchange_rate(VARCHAR(3), VARCHAR(3));
DROP FUNCTION IF EXISTS convert_currency(DECIMAL(18,8), VARCHAR(3), VARCHAR(3));
DROP FUNCTION IF EXISTS get_or_create_wallet(UUID, VARCHAR(3));
DROP FUNCTION IF EXISTS update_wallet_balance(UUID, VARCHAR(3), DECIMAL(18,8));
DROP FUNCTION IF EXISTS convert_wallet_balance(UUID, VARCHAR(3), VARCHAR(3), DECIMAL(18,8));
DROP FUNCTION IF EXISTS calculate_order_price(DECIMAL(18,8), VARCHAR(3));
DROP FUNCTION IF EXISTS calculate_revenue_consolidated(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, VARCHAR(3));
DROP FUNCTION IF EXISTS audit_trigger_function();
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS is_admin();
DROP FUNCTION IF EXISTS is_super_admin();
DROP FUNCTION IF EXISTS get_user_role();

-- Drop tables in dependency order
DROP TABLE IF EXISTS currency_audit_log CASCADE;
DROP TABLE IF EXISTS user_preferences CASCADE;
DROP TABLE IF EXISTS wallet_transactions CASCADE;
DROP TABLE IF EXISTS user_wallets CASCADE;
DROP TABLE IF EXISTS product_orders CASCADE;
DROP TABLE IF EXISTS client_currency_settings CASCADE;
DROP TABLE IF EXISTS exchange_rates CASCADE;
DROP TABLE IF EXISTS currencies CASCADE;

-- =====================================================
-- 5. RESTORE ORIGINAL TABLES
-- =====================================================

-- Recreate original user_wallets table
CREATE TABLE user_wallets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  currency VARCHAR(3) NOT NULL CHECK (currency IN ('SDG', 'EGP')),
  balance DECIMAL(15,2) DEFAULT 0.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, currency)
);

-- Recreate original wallet_transactions table
CREATE TABLE wallet_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'purchase')),
  amount DECIMAL(15,2) NOT NULL,
  currency VARCHAR(3) NOT NULL CHECK (currency IN ('SDG', 'EGP')),
  description TEXT NOT NULL,
  reference VARCHAR(100),
  status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recreate original product_orders table
CREATE TABLE product_orders (
  id VARCHAR(50) PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  template_id VARCHAR(100) NOT NULL,
  template_name VARCHAR(200) NOT NULL,
  template_category VARCHAR(100),
  product_data JSONB NOT NULL DEFAULT '{}',
  pricing JSONB NOT NULL DEFAULT '{}',
  user_details JSONB NOT NULL DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'pending' CHECK (
    status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')
  ),
  processing_type VARCHAR(20) DEFAULT 'manual' CHECK (
    processing_type IN ('auto', 'manual', 'review')
  ),
  timeline JSONB DEFAULT '[]',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- 6. RESTORE DATA
-- =====================================================

-- Restore user wallets (SDG and EGP only)
INSERT INTO user_wallets (user_id, currency, balance, created_at, updated_at)
SELECT 
  user_id,
  currency_code,
  balance::DECIMAL(15,2),
  created_at,
  updated_at
FROM temp_user_balances;

-- Restore wallet transactions
INSERT INTO wallet_transactions (id, user_id, type, amount, currency, description, reference, status, created_at)
SELECT 
  id,
  user_id,
  type,
  amount::DECIMAL(15,2),
  currency,
  description,
  reference,
  status,
  created_at
FROM temp_transactions;

-- Restore product orders
INSERT INTO product_orders (
  id, user_id, template_id, template_name, template_category,
  product_data, pricing, user_details, status, processing_type,
  timeline, metadata, created_at, updated_at, completed_at
)
SELECT 
  id,
  user_id,
  template_id,
  template_name,
  template_category,
  product_data,
  pricing_data,
  jsonb_build_object(
    'fullName', user_details_name,
    'email', user_details_email,
    'phone', user_details_phone
  ),
  status,
  processing_type,
  timeline,
  metadata,
  created_at,
  updated_at,
  completed_at
FROM temp_orders;

-- =====================================================
-- 7. RECREATE ORIGINAL INDEXES
-- =====================================================

-- User wallets indexes
CREATE INDEX idx_user_wallets_user_id ON user_wallets(user_id);
CREATE INDEX idx_user_wallets_currency ON user_wallets(currency);

-- Wallet transactions indexes
CREATE INDEX idx_wallet_transactions_user_id ON wallet_transactions(user_id);
CREATE INDEX idx_wallet_transactions_type ON wallet_transactions(type);
CREATE INDEX idx_wallet_transactions_created_at ON wallet_transactions(created_at DESC);

-- Product orders indexes
CREATE INDEX idx_product_orders_user_id ON product_orders(user_id);
CREATE INDEX idx_product_orders_status ON product_orders(status);
CREATE INDEX idx_product_orders_created_at ON product_orders(created_at DESC);

-- =====================================================
-- 8. RESTORE ORIGINAL RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_orders ENABLE ROW LEVEL SECURITY;

-- User wallets policies
CREATE POLICY "Users can access own wallets" ON user_wallets
  FOR ALL USING (auth.uid() = user_id);

-- Wallet transactions policies
CREATE POLICY "Users can access own transactions" ON wallet_transactions
  FOR ALL USING (auth.uid() = user_id);

-- Product orders policies
CREATE POLICY "Users can access own orders" ON product_orders
  FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- 9. CLEANUP AND VALIDATION
-- =====================================================

-- Drop temporary tables
DROP TABLE temp_user_balances;
DROP TABLE temp_transactions;
DROP TABLE temp_orders;

-- Validation checks
DO $$
DECLARE
  wallet_count INTEGER;
  transaction_count INTEGER;
  order_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO wallet_count FROM user_wallets;
  SELECT COUNT(*) INTO transaction_count FROM wallet_transactions;
  SELECT COUNT(*) INTO order_count FROM product_orders;
  
  RAISE NOTICE 'Rollback completed successfully!';
  RAISE NOTICE 'Restored records:';
  RAISE NOTICE '- User wallets: %', wallet_count;
  RAISE NOTICE '- Wallet transactions: %', transaction_count;
  RAISE NOTICE '- Product orders: %', order_count;
  
  IF wallet_count = 0 THEN
    RAISE WARNING 'No wallet records restored - check backup data';
  END IF;
END $$;

-- =====================================================
-- 10. FINAL NOTES
-- =====================================================

-- Log the rollback
INSERT INTO auth.audit_log_entries (
  instance_id,
  id,
  payload,
  created_at
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  jsonb_build_object(
    'action', 'multi_currency_rollback',
    'timestamp', NOW(),
    'reason', 'Emergency rollback to dual-currency system'
  ),
  NOW()
);

RAISE NOTICE '=== ROLLBACK COMPLETED ===';
RAISE NOTICE 'The system has been restored to the original dual-currency setup (SDG/EGP).';
RAISE NOTICE 'Next steps:';
RAISE NOTICE '1. Update application code to use original components';
RAISE NOTICE '2. Remove multi-currency API endpoints';
RAISE NOTICE '3. Test all wallet and order functionality';
RAISE NOTICE '4. Notify users of the change';
RAISE NOTICE '5. Monitor for any issues';
RAISE NOTICE '';
RAISE NOTICE 'Backup tables with "_backup_" prefix are preserved for reference.';
