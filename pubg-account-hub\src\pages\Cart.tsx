import React, { useState } from "react";
import { useCart } from "@/contexts/CartContext";
import { motion } from "framer-motion";
import { ShoppingCart, Trash2, Minus, Plus, ExternalLink, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useTelegramUsername } from "@/contexts/ConfigContext";
import { shouldShowLoader } from "@/lib/utils";
import GlobalLoader from "@/components/ui/GlobalLoader";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const Cart = () => {
  const { items, totalEGP, totalUSD, clearCart, removeItem, updateQuantity, isLoading } = useCart();
  const { toast } = useToast();
  const telegramUsername = useTelegramUsername();
  const [checkoutLinkClicked, setCheckoutLinkClicked] = useState(false);

  const handleRemoveItem = (id: string, title: string) => {
    removeItem(id);
    toast({
      title: "تمت إزالة المنتج",
      description: `تم إزالة ${title} من سلة التسوق`,
    });
  };

  const handleUpdateQuantity = (id: string, quantity: number) => {
    updateQuantity(id, quantity);
  };
  
  const handleCheckoutClick = () => {
    setCheckoutLinkClicked(true);
    
    // Create a detailed message for the Telegram order
    let message = "🛒 *طلب جديد من PUBG STORE*\n\n";
    message += "📋 *تفاصيل الطلب:*\n";
    
    items.forEach((item, index) => {
      message += `${index + 1}. ${item.title} - ${item.quantity} × ${item.priceEGP} EGP\n`;
    });
    
    message += "\n💰 *المجموع:* " + totalEGP + " EGP / $" + totalUSD;
    message += "\n\nأرجو التواصل معي لإتمام عملية الشراء.";

    // Encode the message for URL inclusion
    const encodedMessage = encodeURIComponent(message);
    
    // Open Telegram in a new tab with prefilled message
    // Using proper Telegram URL format
    window.open(`https://t.me/${telegramUsername}?text=${encodedMessage}`, '_blank');
    
    // When the link is clicked, show a success toast
    toast({
      title: "تم إرسال الطلب",
      description: "تم تحضير الطلب وإرساله إلى تلجرام، يرجى التواصل مع فريق المبيعات لإتمام الشراء",
    });
  };

  if (shouldShowLoader(isLoading)) {
    return (
      <div className="min-h-screen pt-24 pb-16">
        <GlobalLoader fullPage />
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <div className="container mx-auto">
        <motion.div 
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">سلة التسوق</h1>
          <p className="text-muted-foreground">
            مراجعة المنتجات المختارة قبل إتمام عملية الشراء
          </p>
        </motion.div>

        {items.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className="lg:col-span-2 space-y-4"
            >
              {items.map((item) => (
                <motion.div key={item.id} variants={fadeInUp}>
                  <div className="glass-card rounded-xl overflow-hidden">
                    <div className="flex flex-col sm:flex-row">
                      <div className="w-full sm:w-1/4 p-4">
                        <img
                          src={item.image}
                          alt={item.title}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                      </div>
                      <div className="flex-1 p-4 flex flex-col justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-white">{item.title}</h3>
                          <p className="text-muted-foreground mb-2">
                            ${item.priceUSD} / {item.priceEGP} EGP
                          </p>
                          <div className="flex items-center gap-3 mt-2">
                            <span className="text-white">الكمية:</span>
                            <div className="flex items-center border border-border rounded-md">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-r-none"
                                onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                                disabled={item.quantity <= 1}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="w-10 text-center">{item.quantity}</span>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-l-none"
                                onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-between items-center mt-4">
                          <span className="text-white font-semibold">
                            المجموع: {item.priceEGP * item.quantity} EGP
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-500 hover:bg-red-500/10"
                            onClick={() => handleRemoveItem(item.id, item.title)}
                          >
                            <Trash2 className="h-4 w-4 ml-2" />
                            إزالة
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
              <div className="flex justify-end mt-4">
                <Button
                  variant="outline"
                  className="border-red-500 text-red-500 hover:bg-red-500/10 hover:text-red-500"
                  onClick={() => {
                    clearCart();
                    toast({
                      title: "تم إفراغ السلة",
                      description: "تم حذف جميع المنتجات من سلة التسوق",
                    });
                  }}
                >
                  <Trash2 className="ml-2 h-4 w-4" />
                  إفراغ السلة
                </Button>
              </div>
            </motion.div>

            {/* Order Summary */}
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
              className="glass-card rounded-xl p-6"
            >
              <h2 className="text-xl font-bold text-white mb-6">ملخص الطلب</h2>
              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">عدد المنتجات</span>
                  <span className="text-white font-medium">{items.reduce((acc, item) => acc + item.quantity, 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">المجموع (EGP)</span>
                  <span className="text-white font-medium">{totalEGP} EGP</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">المجموع (USD)</span>
                  <span className="text-white font-medium">${totalUSD}</span>
                </div>
                <hr className="border-border" />
                <div className="flex justify-between">
                  <span className="text-white font-bold">المجموع الكلي</span>
                  <span className="text-pubg-orange font-bold text-xl">
                    {totalEGP} EGP
                  </span>
                </div>
              </div>
              
              {/* Telegram Checkout - Updated */}
              <Button 
                className="w-full bg-pubg-blue hover:bg-pubg-blue/90"
                onClick={handleCheckoutClick}
              >
                <MessageCircle className="ml-2 h-5 w-5" />
                إتمام الطلب عبر تلجرام
              </Button>
              
              <p className="text-xs text-muted-foreground text-center mt-4">
                سيتم تحويلك إلى تطبيق تلجرام لإتمام عملية الشراء والتواصل مع فريق المبيعات
              </p>
            </motion.div>
          </div>
        ) : (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="glass-card rounded-xl p-8 text-center"
          >
            <div className="flex justify-center mb-4">
              <ShoppingCart className="w-16 h-16 text-muted-foreground" />
            </div>
            <h2 className="text-xl font-bold text-white mb-2">
              سلة التسوق فارغة
            </h2>
            <p className="text-muted-foreground mb-6">
              لم تقم بإضافة أي منتجات إلى سلة التسوق بعد
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button asChild className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90">
                <Link to="/mods">تصفح المودات المتوفرة</Link>
              </Button>
              <Button asChild variant="outline">
                <Link to="/accounts">تصفح الحسابات</Link>
              </Button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Cart;
