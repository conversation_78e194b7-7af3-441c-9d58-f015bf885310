"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { BankAccount } from "@/lib/types"
import { Building2, Copy, Check } from "lucide-react"
import { useState } from "react"
import Image from "next/image"

interface BankOptionCardProps {
  bank: BankAccount
  isSelected: boolean
  onSelect: (bank: BankAccount) => void
  className?: string
}

export function BankOptionCard({ 
  bank, 
  isSelected, 
  onSelect, 
  className 
}: BankOptionCardProps) {
  const [copied, setCopied] = useState(false)

  const handleCopyAccountNumber = async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    try {
      await navigator.clipboard.writeText(bank.accountNumber)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Failed to copy account number:", error)
    }
  }

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-300 hover:scale-105",
        "bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-lg",
        isSelected
          ? "border-yellow-400 bg-yellow-400/10 shadow-yellow-400/20"
          : "hover:border-slate-600 hover:shadow-xl",
        className
      )}
      onClick={() => onSelect(bank)}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          {/* Bank Logo and Name */}
          <div className="flex items-center gap-4">
            <div className="relative w-12 h-12 rounded-xl overflow-hidden bg-slate-700/50 flex items-center justify-center">
              {bank.logoUrl ? (
                <Image
                  src={bank.logoUrl}
                  alt={bank.name}
                  width={48}
                  height={48}
                  className="object-contain"
                  onError={(e) => {
                    // Fallback to icon if image fails to load
                    const target = e.target as HTMLImageElement
                    target.style.display = "none"
                    target.nextElementSibling?.classList.remove("hidden")
                  }}
                />
              ) : null}
              <Building2 
                className={cn(
                  "h-6 w-6 text-slate-400",
                  bank.logoUrl ? "hidden" : "block"
                )} 
              />
            </div>
            <div>
              <h3 className="font-semibold text-white text-lg">
                {bank.name}
              </h3>
              <p className="text-slate-400 text-sm">
                حساب بنكي
              </p>
            </div>
          </div>

          {/* Selection Indicator */}
          <div
            className={cn(
              "w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300",
              isSelected
                ? "border-yellow-400 bg-yellow-400"
                : "border-slate-500"
            )}
          >
            {isSelected && (
              <Check className="h-4 w-4 text-slate-900" />
            )}
          </div>
        </div>

        {/* Account Number */}
        <div className="space-y-2">
          <p className="text-slate-400 text-sm font-medium">
            رقم الحساب
          </p>
          <div className="flex items-center justify-between bg-slate-700/50 rounded-lg p-3">
            <span className="font-mono text-white text-lg tracking-wider">
              {bank.accountNumber}
            </span>
            <button
              onClick={handleCopyAccountNumber}
              className={cn(
                "p-2 rounded-lg transition-all duration-300",
                "hover:bg-slate-600/50 active:scale-95",
                copied
                  ? "text-green-400 bg-green-400/10"
                  : "text-slate-400 hover:text-white"
              )}
              title="نسخ رقم الحساب"
            >
              {copied ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>

        {/* Selection Hint */}
        {isSelected && (
          <div className="mt-4 p-3 bg-yellow-400/10 border border-yellow-400/20 rounded-lg">
            <p className="text-yellow-400 text-sm text-center">
              تم اختيار هذا البنك للتحويل
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
