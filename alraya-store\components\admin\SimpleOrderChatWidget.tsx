"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  MessageSquare,
  User,
  Package,
  Circle
} from "lucide-react"
import { ProductOrder } from "@/lib/types"

interface SimpleOrderChatWidgetProps {
  order: ProductOrder
}

export function SimpleOrderChatWidget({ order }: SimpleOrderChatWidgetProps) {
  const [unreadCount, setUnreadCount] = useState(0)
  const [isOnline, setIsOnline] = useState(false)

  // Load basic chat info
  useEffect(() => {
    // Mock data - replace with real API call
    setUnreadCount(Math.floor(Math.random() * 5))
    setIsOnline(Math.random() > 0.5)
  }, [order.id])

  const handleOpenChat = () => {
    // Get customer info
    const customerName = order.userDetails.name || "عميل"
    const customerEmail = order.userDetails.email

    // Use customer email as the ID (this opens the USER's chat, not product-specific)
    const customerId = customerEmail

    // Trigger the floating chat to open with this user
    const chatEvent = new CustomEvent('open-admin-chat', {
      detail: {
        customerId: customerId,
        customerName: customerName, // Just the customer name, not product-specific
        customerEmail: customerEmail,
        orderId: order.id, // Pass order ID for context only
        packageName: order.templateName, // Pass package for context only
        unreadCount: unreadCount,
        isOnline: isOnline
      }
    })

    // Open chat with user context

    window.dispatchEvent(chatEvent)
  }

  return (
    <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-4">
      <div className="flex items-center justify-between">
        {/* Customer Info */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-10 h-10 bg-slate-600 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-slate-300" />
            </div>
            {/* Online indicator */}
            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-slate-800 ${
              isOnline ? 'bg-green-400' : 'bg-slate-400'
            }`}></div>
          </div>
          
          <div>
            <div className="flex items-center gap-2">
              <span className="text-white font-medium">
                {order.userDetails.name || "عميل"}
              </span>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="bg-red-500 text-xs">
                  {unreadCount}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-slate-400">
              <span>💬 محادثة العميل</span>
            </div>
          </div>
        </div>

        {/* Chat Button */}
        <Button
          onClick={handleOpenChat}
          className="bg-blue-500 hover:bg-blue-600 flex items-center gap-2"
        >
          <MessageSquare className="h-4 w-4" />
          <span className="hidden sm:inline">فتح المحادثة</span>
          {unreadCount > 0 && (
            <Circle className="h-2 w-2 text-red-300 fill-current" />
          )}
        </Button>
      </div>

      {/* Quick Status */}
      <div className="mt-3 flex items-center gap-4 text-xs text-slate-400">
        <span>📧 {order.userDetails.email}</span>
        <span className={isOnline ? 'text-green-400' : 'text-slate-400'}>
          {isOnline ? '🟢 متصل الآن' : '⚫ غير متصل'}
        </span>
        <span>🔗 اختصار للمحادثة</span>
      </div>
    </div>
  )
}
