"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Crop,Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Image upload and cropping state\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedImageFile, setSelectedImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [croppedImage, setCroppedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Initialize cropped image if editing existing product\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            if (product === null || product === void 0 ? void 0 : product.image) {\n                setCroppedImage(product.image);\n            }\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        product === null || product === void 0 ? void 0 : product.image\n    ]);\n    // Package dialog form state\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"text\",\n        placeholder: \"\",\n        required: false\n    });\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setCroppedImage(null);\n        setImagePreview(null);\n        setSelectedImageFile(null);\n    };\n    // Image upload functions\n    const handleImageSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            if (file.type.startsWith('image/')) {\n                setSelectedImageFile(file);\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    setImagePreview((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result);\n                    setIsImageCropDialogOpen(true);\n                };\n                reader.readAsDataURL(file);\n            } else {\n                alert('يرجى اختيار ملف صورة صحيح');\n            }\n        }\n    };\n    const handleImageCrop = (croppedImageData)=>{\n        setCroppedImage(croppedImageData);\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageData\n            }));\n        setIsImageCropDialogOpen(false);\n    };\n    const removeImage = ()=>{\n        setCroppedImage(null);\n        setImagePreview(null);\n        setSelectedImageFile(null);\n        setFormData((prev)=>({\n                ...prev,\n                image: \"\"\n            }));\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const openImageSelector = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleSave = async ()=>{\n        var _formData_name, _formData_category;\n        setIsLoading(true);\n        // Basic validation\n        if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n            alert(\"يرجى إدخال اسم المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!((_formData_category = formData.category) === null || _formData_category === void 0 ? void 0 : _formData_category.trim())) {\n            alert(\"يرجى إدخال فئة المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!formData.packages || formData.packages.length === 0) {\n            alert(\"يرجى إضافة حزمة واحدة على الأقل\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const productData = {\n                name: formData.name,\n                description: formData.description,\n                category: formData.category,\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: formData.fields,\n                packages: formData.packages,\n                features: formData.features,\n                tags: formData.tags,\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.updateProduct)(product.id, productData);\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.createProduct)(productData);\n            }\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            alert(\"حدث خطأ أثناء حفظ المنتج\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"text\",\n            placeholder: \"\",\n            required: false\n        });\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            discount: pkg.discount || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: fileInputRef,\n                                                type: \"file\",\n                                                accept: \"image/*\",\n                                                onChange: handleImageSelect,\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 15\n                                            }, this),\n                                            croppedImage || formData.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: croppedImage || formData.image,\n                                                            alt: \"صورة المنتج\",\n                                                            className: \"w-full h-48 object-cover rounded-xl border border-gray-600/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-xl flex items-center justify-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    onClick: openImageSelector,\n                                                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"تغيير\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    variant: \"destructive\",\n                                                                    onClick: removeImage,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"حذف\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: openImageSelector,\n                                                className: \"border-2 border-dashed border-gray-600 rounded-xl p-8 text-center cursor-pointer hover:border-purple-500 hover:bg-purple-500/5 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-700/50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium mb-1\",\n                                                                    children: \"اختر صورة الغلاف\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: \"PNG, JPG, GIF حتى 10MB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"رفع صورة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 758,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 831,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 882,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 757,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 756,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 904,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 903,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 933,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 910,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 973,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 965,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 902,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 901,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 987,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 994,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 985,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 984,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"p+1fJbjHMZN/XZ0f5mmj73Gv5t8=\");\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 300,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleMouseDown = (e)=>{\n        var _canvasRef_current;\n        setIsDragging(true);\n        const rect = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getBoundingClientRect();\n        if (rect) {\n            setDragStart({\n                x: e.clientX - rect.left - cropArea.x,\n                y: e.clientY - rect.top - cropArea.y\n            });\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging || !canvasRef.current) return;\n        const rect = canvasRef.current.getBoundingClientRect();\n        const newX = Math.max(0, Math.min(e.clientX - rect.left - dragStart.x, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(e.clientY - rect.top - dragStart.y, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Set canvas size to crop area\n        canvas.width = cropArea.width;\n        canvas.height = cropArea.height;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.width;\n        const scaleY = image.naturalHeight / image.height;\n        // Draw cropped image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, cropArea.width, cropArea.height);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.8);\n        onCrop(croppedImageData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اسحب المربع الأخضر لتحديد منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1084,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: containerRef,\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        onMouseMove: handleMouseMove,\n                        onMouseUp: handleMouseUp,\n                        onMouseLeave: handleMouseUp,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { width, height } = imageRef.current;\n                                        setCropArea({\n                                            x: Math.max(0, (width - 300) / 2),\n                                            y: Math.max(0, (height - 200) / 2),\n                                            width: Math.min(300, width),\n                                            height: Math.min(200, height)\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1093,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/50 pointer-events-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bg-transparent\",\n                                    style: {\n                                        left: cropArea.x,\n                                        top: cropArea.y,\n                                        width: cropArea.width,\n                                        height: cropArea.height,\n                                        boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1116,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1114,\n                                columnNumber: 13\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-2 border-green-400 cursor-move select-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height\n                                },\n                                onMouseDown: handleMouseDown,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border border-dashed border-green-300/70\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-1 -left-1 w-3 h-3 bg-green-400 border border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-1 -right-1 w-3 h-3 bg-green-400 border border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-green-400 border border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-green-400 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1086,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1083,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 إرشادات القص:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-blue-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اسحب المربع الأخضر لتحديد منطقة القص\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يمكنك تحريك منطقة القص بالسحب\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleCrop,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Crop_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1179,\n                                columnNumber: 11\n                            }, this),\n                            \"قص الصورة\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1167,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1082,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"3jbjpb8IDvEt11Cl+op1yNhRB+Q=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});