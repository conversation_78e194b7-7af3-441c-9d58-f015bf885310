/**
 * Helper functions to programmatically update theme colors
 * This allows you to change colors in one place and have them reflected across the entire application
 */

interface ThemeColors {
  orange: string;
  black: string;
  darkGray: string;
  gray: string;
  lightGray: string;
}

/**
 * Helper function to convert hex color to RGB
 */
function hexToRgb(hex: string): { r: number, g: number, b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Updates CSS variables with new color values
 * @param colors Object containing new color values
 */
export function updateThemeColors(colors: Partial<ThemeColors>): void {
  const root = document.documentElement;
  
  if (colors.orange) {
    // Set the main color
    root.style.setProperty('--pubg-orange-color', colors.orange);
    
    // Update opacity variants
    const rgb = hexToRgb(colors.orange);
    if (rgb) {
      root.style.setProperty('--pubg-orange-10', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`);
      root.style.setProperty('--pubg-orange-20', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.2)`);
      root.style.setProperty('--pubg-orange-30', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.3)`);
      root.style.setProperty('--pubg-orange-50', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.5)`);
      root.style.setProperty('--pubg-orange-80', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.8)`);
      root.style.setProperty('--border-orange', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.5)`);
    }
  }
  
  if (colors.gray) {
    // Set the main color
    root.style.setProperty('--pubg-gray-color', colors.gray);
    
    // Update opacity variants
    const rgb = hexToRgb(colors.gray);
    if (rgb) {
      root.style.setProperty('--pubg-gray-10', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`);
      root.style.setProperty('--pubg-gray-20', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.2)`);
      root.style.setProperty('--pubg-gray-30', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.3)`);
      root.style.setProperty('--pubg-gray-50', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.5)`);
      root.style.setProperty('--border-gray', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.7)`);
      root.style.setProperty('--border-light', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.4)`);
    }
  }
  
  if (colors.black) {
    root.style.setProperty('--pubg-black-color', colors.black);
  }
  
  if (colors.darkGray) {
    root.style.setProperty('--pubg-darkGray-color', colors.darkGray);
  }
  
  if (colors.lightGray) {
    root.style.setProperty('--pubg-lightGray-color', colors.lightGray);
  }
}

/**
 * Gets the current theme colors from CSS variables
 * @returns Current theme colors
 */
export function getCurrentThemeColors(): ThemeColors {
  const root = document.documentElement;
  const computedStyle = getComputedStyle(root);
  
  return {
    orange: computedStyle.getPropertyValue('--pubg-orange-color').trim(),
    black: computedStyle.getPropertyValue('--pubg-black-color').trim(),
    darkGray: computedStyle.getPropertyValue('--pubg-darkGray-color').trim(),
    gray: computedStyle.getPropertyValue('--pubg-gray-color').trim(),
    lightGray: computedStyle.getPropertyValue('--pubg-lightGray-color').trim(),
  };
}

/**
 * Resets theme colors to their default values
 */
export function resetThemeColors(): void {
  updateThemeColors({
    orange: '#F2A900',
    black: '#000000',
    darkGray: '#222222',
    gray: '#888888',
    lightGray: '#E5E5E5',
  });
}

/**
 * Example of how to use this in a component:
 * 
 * import { updateThemeColors } from '@/lib/updateTheme';
 * 
 * // In a component:
 * function changeTheme() {
 *   updateThemeColors({
 *     orange: '#FF5500', // Change just the orange color
 *   });
 * }
 */ 