import React, { useState } from "react";
import { useCart } from "@/contexts/CartContext";
import { ShoppingCart, Check, Star, X, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { motion, AnimatePresence } from "framer-motion";
import { ProductJsonLd } from "@/components/JsonLd";
import { 
  Dialog, 
  DialogContent, 
  DialogClose,
  DialogTitle,
  DialogDescription 
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useLanguage } from "@/contexts/LanguageContext";
import { getCategoryById } from "@/services/firestore";

interface AccountCardProps {
  account: {
    id?: string;
    title: string;
    title_en?: string;
    description: string;
    description_en?: string;
    priceUSD: number;
    priceEGP: number;
    image: string;
    category: string;
    featured?: boolean;
    localCurrencyCode?: string;
  };
  categoryData?: {
    [key: string]: {
      name: string;
      name_en?: string;
    }
  };
}

const AccountCard = ({ account, categoryData }: AccountCardProps) => {
  const { addItem, items } = useCart();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [showDetails, setShowDetails] = useState(false);
  const { language, setLanguage } = useLanguage();
  const [showEnglish, setShowEnglish] = useState(language === "en");
  const [categoryName, setCategoryName] = useState<{ name: string; name_en?: string } | null>(null);

  const isInCart = account.id ? items.some(item => item.id === account.id) : false;

  // Fetch category data if not provided
  React.useEffect(() => {
    const loadCategory = async () => {
      if (categoryData && categoryData[account.category]) {
        setCategoryName(categoryData[account.category]);
      } else if (account.category) {
        try {
          const catData = await getCategoryById(account.category);
          if (catData) {
            setCategoryName(catData);
          }
        } catch (error) {
          console.error("Error fetching category:", error);
        }
      }
    };
    
    loadCategory();
  }, [account.category, categoryData]);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent modal from opening when clicking add to cart
    
    addItem(account, 'account');
    
    toast({
      title: showEnglish ? "Added to Cart" : "تمت الإضافة إلى السلة",
      description: (
        <div className="flex flex-col">
          <span>{showEnglish 
            ? `${account.title_en || account.title} has been added to your cart` 
            : `تمت إضافة ${account.title} إلى سلة التسوق`}</span>
          <a 
            href="/cart" 
            className="text-pubg-orange hover:underline mt-2 text-sm flex items-center"
          >
            <ShoppingCart className={`h-3.5 w-3.5 ${showEnglish ? 'mr-1.5' : 'ml-1.5'}`} />
            {showEnglish ? "Go to Cart" : "الذهاب إلى السلة"}
          </a>
        </div>
      ),
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatLocalPrice = (price: number) => {
    const currency = account.localCurrencyCode || "EGP";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Toggle language in the modal
  const handleLanguageToggle = () => {
    const newLanguage = showEnglish ? 'ar' : 'en';
    setShowEnglish(!showEnglish);
    setLanguage(newLanguage);
  };

  const getTitle = () => showEnglish && account.title_en ? account.title_en : account.title;
  const getDescription = () => showEnglish && account.description_en ? account.description_en : account.description;
  const getCategoryDisplay = () => {
    if (!categoryName) return account.category;
    return showEnglish && categoryName.name_en ? categoryName.name_en : categoryName.name;
  };

  return (
    <>
      <ProductJsonLd
        name={getTitle()}
        description={getDescription()}
        image={account.image}
        price={account.priceEGP}
        priceCurrency={account.localCurrencyCode || "EGP"}
        sku={account.id || ''}
        brand="PUBG"
        url={window.location.origin + '/accounts'}
      />
      
      <motion.div 
        className="glass-card rounded-xl overflow-hidden transition-all duration-300 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-pubg-orange/10 group cursor-pointer"
        onClick={() => setShowDetails(true)}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="relative overflow-hidden h-36 sm:h-48 md:h-56">
          <img
            src={account.image}
            alt={getTitle()}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          <Badge className="absolute top-2 right-2 bg-pubg-orange text-pubg-dark text-[10px] sm:text-xs">
            {getCategoryDisplay()}
          </Badge>
          {account.featured && (
            <Badge className="absolute top-2 left-2 bg-purple-500 text-white text-[10px] sm:text-xs flex items-center">
              <Star className="h-3 w-3 mr-1" /> {showEnglish ? "Featured" : "مميز"}
            </Badge>
          )}
          {isInCart && (
            <div className="absolute top-10 right-2 bg-pubg-blue text-white text-[10px] sm:text-xs px-2 py-1 rounded-full flex items-center">
              <Check className="h-3 w-3 mr-1" />
              {showEnglish ? "In Cart" : "في السلة"}
            </div>
          )}
        </div>
        <div className="p-3 sm:p-4 md:p-5">
          <h3 className="text-base sm:text-lg md:text-xl font-bold text-white mb-1 sm:mb-2 line-clamp-1">{getTitle()}</h3>
          <p className="text-muted-foreground text-xs sm:text-sm mb-2 sm:mb-4 line-clamp-2" dir={showEnglish ? "ltr" : "rtl"}>
            {getDescription()}
          </p>
          <div className="flex justify-between items-center">
            <div className="flex flex-col gap-0 sm:gap-1">
              <div className="text-sm sm:text-base md:text-xl font-bold text-primary">
                {formatPrice(account.priceUSD)}
              </div>
              <div className="text-xs sm:text-sm md:text-lg text-muted-foreground">
                {formatLocalPrice(account.priceEGP)}
              </div>
            </div>
            <Button 
              onClick={handleAddToCart} 
              className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 h-8 sm:h-9 md:h-10 text-xs sm:text-sm px-2 sm:px-3 md:px-4 relative overflow-hidden"
              disabled={isInCart}
            >
              <AnimatePresence>
                {isInCart ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="flex items-center"
                  >
                    <Check className="mr-1 sm:mr-2 h-3 sm:h-4 w-3 sm:w-4" />
                    <span>{showEnglish ? "In Cart" : "في السلة"}</span>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    className="flex items-center"
                  >
                    <ShoppingCart className="mr-1 sm:mr-2 h-3 sm:h-4 w-3 sm:w-4" />
                    <span className="hidden xs:inline">{showEnglish ? "Add to Cart" : "إضافة للسلة"}</span>
                    <span className="inline xs:hidden">{showEnglish ? "Add" : "إضافة"}</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Enhanced Product Modal with Language Toggle */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-[95vw] sm:max-w-[600px] p-0 overflow-hidden bg-background border-none rounded-xl shadow-xl max-h-[90vh] flex flex-col">
          <DialogClose className="absolute top-3 right-3 z-50 h-8 w-8 rounded-full bg-black/50 backdrop-blur-sm text-white hover:bg-pubg-orange transition-all hover:scale-110 flex items-center justify-center">
            <X className="h-4 w-4" />
          </DialogClose>
          
          <div className="relative flex-shrink-0">
            <motion.div
              className="relative w-full h-56 sm:h-72"
              initial={{ scale: 1.1, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <img
                src={account.image}
                alt={getTitle()}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-background via-background/20 to-transparent" />
            </motion.div>

            <div className="absolute top-4 left-14 flex gap-2">
              <Badge className="bg-pubg-orange text-pubg-dark">
                {getCategoryDisplay()}
              </Badge>
              {account.featured && (
                <Badge className="bg-purple-500 text-white flex items-center">
                  <Star className="h-3 w-3 mr-1" /> {showEnglish ? "Featured" : "مميز"}
                </Badge>
              )}
              {isInCart && (
                <Badge className="bg-pubg-blue text-white flex items-center">
                  <Check className="h-3 w-3 mr-1" /> {showEnglish ? "In Cart" : "في السلة"}
                </Badge>
              )}
            </div>
          </div>

          <div className="relative p-4 sm:p-6 overflow-y-auto flex-grow">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              dir={showEnglish ? "ltr" : "rtl"}
            >
              <DialogTitle className="text-xl sm:text-2xl md:text-3xl font-bold">
                {getTitle()}
              </DialogTitle>
              
              <DialogDescription className="text-muted-foreground text-sm">
                {showEnglish 
                  ? "Exclusive PUBG account with special features and rare weapons" 
                  : "حساب PUBG متميز مع مواصفات خاصة وأسلحة نادرة"}
              </DialogDescription>
              
              <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-8 py-4 my-4 border-y border-border">
                <div className="text-center sm:text-right">
                  <div className="text-sm text-muted-foreground mb-1">
                    {showEnglish ? "USD Price" : "السعر بالدولار"}
                  </div>
                  <div className="text-2xl font-bold text-primary">{formatPrice(account.priceUSD)}</div>
                </div>
                <div className="text-center sm:text-right">
                  <div className="text-sm text-muted-foreground mb-1">
                    {showEnglish ? "Local Price" : "السعر المحلي"}
                  </div>
                  <div className="text-2xl font-bold text-primary">{formatLocalPrice(account.priceEGP)}</div>
                </div>
              </div>

              <div className="mb-4">
                <h3 className="text-lg font-semibold mb-2 flex items-center">
                  <span className={`${showEnglish ? "mr-2" : "ml-2"} inline-flex h-5 w-5 items-center justify-center rounded-full bg-pubg-orange/20 text-pubg-orange`}>
                    <span className="h-2.5 w-2.5 rounded-full bg-pubg-orange"></span>
                  </span>
                  {showEnglish ? "Account Details" : "تفاصيل الحساب"}
                </h3>
                <div 
                  className="text-muted-foreground whitespace-pre-line overflow-y-auto pr-2 custom-scrollbar rounded-md bg-muted/20 p-3 border border-border"
                  style={{ 
                    maxHeight: "25vh",
                    overflowY: "auto",
                    direction: showEnglish ? "ltr" : "rtl",
                    textAlign: showEnglish ? "left" : "right"
                  }}
                >
                  {getDescription()}
                </div>
              </div>
            </motion.div>
          </div>

          <div className="p-4 sm:p-6 pt-0 border-t border-border mt-2 flex-shrink-0">
            <Button 
              onClick={handleAddToCart}
              disabled={isInCart}
              className="w-full bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 h-10 sm:h-12"
            >
              {isInCart ? (
                <div className="flex items-center justify-center">
                  <Check className={`${showEnglish ? "mr-2" : "ml-2"} h-4 w-4`} />
                  <span>{showEnglish ? "Item in Cart" : "العنصر في السلة"}</span>
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <ShoppingCart className={`${showEnglish ? "mr-2" : "ml-2"} h-5 w-5`} />
                  <span>{showEnglish ? "Add to Cart" : "إضافة إلى السلة"}</span>
                </div>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AccountCard;
