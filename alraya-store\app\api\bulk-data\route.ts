import { NextRequest, NextResponse } from 'next/server'
import { BulkDataResponse, BulkAppData } from '@/lib/types'

// Import existing data sources
import { gameCards } from '@/lib/data/gameCards'
import { slides } from '@/lib/data/slides'
import { getProducts } from '@/lib/services/productService'
import { CURRENCIES } from '@/lib/data/currencies'

/**
 * GET /api/bulk-data
 * 
 * Single endpoint that fetches ALL application data in one request.
 * This eliminates the need for multiple API calls and enables true offline functionality.
 * 
 * Returns:
 * - Products (all active products with packages and fields)
 * - Currencies (available currencies with display info)
 * - Exchange rates (current rates for all currency pairs)
 * - Game cards (static game card data)
 * - Slides (promotional slides)
 * - User preferences (if user is authenticated)
 * - Wallet data (if user is authenticated)
 * - Contact info and other static content
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🚀 Loading bulk application data...')
    
    // Get user ID from request if authenticated (optional)
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    // Parallel data fetching for optimal performance
    const [
      products,
      currencies,
      exchangeRates,
      walletData,
      userPreferences,
    ] = await Promise.all([
      // Load products
      loadProducts(),
      
      // Load currencies
      loadCurrencies(),
      
      // Load exchange rates
      loadExchangeRates(),
      
      // Load wallet data (if user authenticated)
      userId ? loadWalletData(userId) : Promise.resolve(undefined),
      
      // Load user preferences (if user authenticated)
      userId ? loadUserPreferences(userId) : Promise.resolve(undefined),
    ])

    // Combine all data
    const bulkData: BulkAppData = {
      products,
      currencies,
      exchangeRates,
      gameCards, // Static data
      slides, // Static data
      walletData,
      userPreferences,
      // Add other static content as needed
    }

    const response: BulkDataResponse = {
      success: true,
      data: bulkData,
      timestamp: new Date(),
      version: '1.0.0', // For cache invalidation if needed
    }

    console.log('✅ Bulk data loaded successfully:', {
      products: products.length,
      currencies: currencies.length,
      gameCards: gameCards.length,
      slides: slides.length,
      hasWalletData: !!walletData,
      hasUserPreferences: !!userPreferences,
    })

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Failed to load bulk data:', error)
    
    const errorResponse: BulkDataResponse = {
      success: false,
      data: {} as BulkAppData,
      timestamp: new Date(),
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }

    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * Load all active products with their packages and fields
 */
async function loadProducts() {
  try {
    const products = await getProducts({ isActive: true })
    return products
  } catch (error) {
    console.error('Failed to load products:', error)
    return []
  }
}

/**
 * Load available currencies
 */
async function loadCurrencies() {
  try {
    // Convert CURRENCIES object to array format expected by the app
    const currencyArray = Object.entries(CURRENCIES).map(([code, info]) => ({
      code,
      name: info.name,
      symbol: info.symbol,
      isRTL: info.isRTL,
    }))
    
    return currencyArray
  } catch (error) {
    console.error('Failed to load currencies:', error)
    return []
  }
}

/**
 * Load current exchange rates
 */
async function loadExchangeRates() {
  try {
    // Mock exchange rates - in production this would fetch from your exchange rate service
    const mockRates = {
      'USD': 1,
      'SDG': 450.00,
      'EGP': 30.80,
    }
    
    return mockRates
  } catch (error) {
    console.error('Failed to load exchange rates:', error)
    return { 'USD': 1 } // Fallback to USD base
  }
}

/**
 * Load wallet data for authenticated user
 */
async function loadWalletData(userId: string) {
  try {
    // TODO: Implement actual wallet data loading from Supabase
    // For now, return undefined to use existing wallet hook behavior
    return undefined
  } catch (error) {
    console.error('Failed to load wallet data:', error)
    return undefined
  }
}

/**
 * Load user preferences for authenticated user
 */
async function loadUserPreferences(userId: string) {
  try {
    // TODO: Implement actual user preferences loading from Supabase
    // Return default preferences for now
    return {
      preferredCurrency: 'USD' as const,
      theme: 'dark' as const,
      language: 'ar' as const,
      notifications: true,
      autoRefresh: false,
    }
  } catch (error) {
    console.error('Failed to load user preferences:', error)
    return undefined
  }
}
