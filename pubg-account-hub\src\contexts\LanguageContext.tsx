import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'ar' | 'en' | 'fr' | 'es' | 'de';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  // Get language from localStorage or default to Arabic
  const [language, setLanguageState] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem('language');
    const i18nextLanguage = localStorage.getItem('i18nextLng');
    // First try to use 'language' key, then fall back to 'i18nextLng', then default to 'ar'
    return (savedLanguage as Language) || (i18nextLanguage as Language) || 'ar';
  });

  // Determine if the language is RTL
  const isRTL = language === 'ar';

  // Update localStorage when language changes
  useEffect(() => {
    localStorage.setItem('language', language);
    
    // Set the dir attribute on the html element
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    
    // You can also set a data attribute for styling purposes
    document.documentElement.setAttribute('data-language', language);
  }, [language, isRTL]);

  // Listen for changes to i18nextLng
  useEffect(() => {
    const handleStorageChange = () => {
      const i18nextLanguage = localStorage.getItem('i18nextLng') as Language;
      if (i18nextLanguage && i18nextLanguage !== language) {
        setLanguageState(i18nextLanguage);
      }
    };

    // Check for changes when the component mounts
    handleStorageChange();

    // Add event listener for storage changes (helps with multiple tabs)
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [language]);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageProvider;
