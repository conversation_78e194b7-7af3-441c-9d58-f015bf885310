/**
 * Theme configuration for the entire application
 * This makes it easy to change colors in one place and have them applied everywhere
 */

export const siteColors = {
  // Main brand colors
  orange: 'var(--pubg-orange-color)',
  black: 'var(--pubg-black-color)',
  darkGray: 'var(--pubg-darkGray-color)',
  gray: 'var(--pubg-gray-color)',
  lightGray: 'var(--pubg-lightGray-color)',
  
  // Colors with opacity
  orange10: 'var(--pubg-orange-10)',
  orange20: 'var(--pubg-orange-20)',
  orange30: 'var(--pubg-orange-30)',
  orange50: 'var(--pubg-orange-50)',
  orange80: 'var(--pubg-orange-80)',
  
  gray10: 'var(--pubg-gray-10)',
  gray20: 'var(--pubg-gray-20)',
  gray30: 'var(--pubg-gray-30)',
  gray50: 'var(--pubg-gray-50)',
  
  // Background colors
  cardBg: 'var(--bg-card-dark)',
  inputBg: 'var(--bg-input-dark)',
  inputHoverBg: 'var(--bg-input-hover)',
  sectionBg: 'var(--bg-section-item)',
  sectionHoverBg: 'var(--bg-section-item-hover)',
  
  // Border colors
  border: 'var(--border-gray)',
  borderLight: 'var(--border-light)',
  borderOrange: 'var(--border-orange)',
}

// Button variants that can be used consistently
export const buttonVariants = {
  primary: 'bg-pubg-orange text-pubg-black hover:bg-pubg-orange/90',
  secondary: 'bg-pubg-gray text-white hover:bg-pubg-gray/90',
  outline: 'border border-border bg-transparent hover:bg-pubg-orange/10 hover:text-pubg-orange',
  destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  ghost: 'hover:bg-accent hover:text-accent-foreground',
}

// For dynamic class generation based on state
export function getStatusColor(status: string) {
  switch(status.toLowerCase()) {
    case 'active':
    case 'online':
    case 'completed':
    case 'approved':
      return 'text-green-500';
    case 'pending':
    case 'in progress':
    case 'processing':
      return 'text-pubg-orange';
    case 'inactive':
    case 'offline':
    case 'rejected':
      return 'text-red-500';
    case 'paused':
    case 'suspended':
      return 'text-yellow-500';
    default:
      return 'text-pubg-gray';
  }
} 