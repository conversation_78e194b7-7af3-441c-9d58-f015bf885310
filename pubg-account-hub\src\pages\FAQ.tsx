import React from "react";
import { useTranslation } from "react-i18next";
import SEO from "@/components/SEO";
import { FAQJsonLd } from "@/components/JsonLd";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { HelpCircle, Shield, CreditCard, Truck, Users, MessageCircle } from "lucide-react";

const FAQ = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const faqData = [
    {
      id: "account-safety",
      question: "هل حسابات ببجي آمنة ومضمونة؟",
      questionEn: "Are PUBG accounts safe and guaranteed?",
      answer: "نعم، جميع حساباتنا آمنة ومضمونة 100%. نحن نقدم ضمان كامل على جميع الحسابات المباعة، ونضمن عدم استرداد الحساب من قبل المالك الأصلي. كما نقدم دعم فني 24/7 لحل أي مشاكل قد تواجهها.",
      answerEn: "Yes, all our accounts are 100% safe and guaranteed. We provide full warranty on all sold accounts and guarantee that the account won't be recovered by the original owner. We also offer 24/7 technical support to solve any issues you might face.",
      category: "security"
    },
    {
      id: "account-delivery",
      question: "كم يستغرق تسليم الحساب بعد الشراء؟",
      questionEn: "How long does account delivery take after purchase?",
      answer: "يتم تسليم الحسابات فوراً بعد تأكيد الدفع. ستحصل على بيانات الحساب (الإيميل وكلمة المرور) خلال 5-10 دقائق كحد أقصى. في حالة التأخير، يمكنك التواصل مع خدمة العملاء للمساعدة الفورية.",
      answerEn: "Accounts are delivered instantly after payment confirmation. You'll receive account credentials (email and password) within 5-10 minutes maximum. In case of delay, you can contact customer service for immediate assistance.",
      category: "delivery"
    },
    {
      id: "uc-recharge-time",
      question: "كم يستغرق شحن UC في ببجي؟",
      questionEn: "How long does PUBG UC recharge take?",
      answer: "شحن UC يتم خلال 1-5 دقائق بعد تأكيد الدفع. نحن نستخدم أسرع طرق الشحن المتاحة لضمان وصول UC إلى حسابك في أقل وقت ممكن. في حالة التأخير، نقدم تعويض إضافي.",
      answerEn: "UC recharge takes 1-5 minutes after payment confirmation. We use the fastest available recharge methods to ensure UC reaches your account in minimum time. In case of delay, we provide additional compensation.",
      category: "uc"
    },
    {
      id: "payment-methods",
      question: "ما هي طرق الدفع المتاحة؟",
      questionEn: "What payment methods are available?",
      answer: "نقبل جميع طرق الدفع الشائعة: فيزا، ماستركارد، فودافون كاش، أورانج موني، إتصالات كاش، CIB، والتحويل البنكي. جميع المعاملات آمنة ومشفرة بأعلى معايير الأمان.",
      answerEn: "We accept all common payment methods: Visa, Mastercard, Vodafone Cash, Orange Money, Etisalat Cash, CIB, and bank transfer. All transactions are secure and encrypted with highest security standards.",
      category: "payment"
    },
    {
      id: "account-types",
      question: "ما أنواع حسابات ببجي المتوفرة؟",
      questionEn: "What types of PUBG accounts are available?",
      answer: "نوفر جميع أنواع الحسابات: حسابات كونكر، أيس، كراون، دايموند، وحسابات مع بدلات نادرة مثل Glacier M416، Dragon Hunter، Pharaoh، وغيرها من البدلات الأسطورية والنادرة.",
      answerEn: "We offer all account types: Conqueror, Ace, Crown, Diamond accounts, and accounts with rare outfits like Glacier M416, Dragon Hunter, Pharaoh, and other legendary and rare outfits.",
      category: "accounts"
    },
    {
      id: "account-change-info",
      question: "هل يمكنني تغيير بيانات الحساب بعد الشراء؟",
      questionEn: "Can I change account information after purchase?",
      answer: "نعم، يمكنك تغيير جميع بيانات الحساب بعد الشراء. ننصح بتغيير كلمة المرور والإيميل فوراً بعد استلام الحساب لضمان الأمان الكامل. نقدم دليل مفصل لتغيير البيانات.",
      answerEn: "Yes, you can change all account information after purchase. We recommend changing password and email immediately after receiving the account to ensure complete security. We provide detailed guide for changing information.",
      category: "accounts"
    },
    {
      id: "refund-policy",
      question: "ما هي سياسة الاسترداد؟",
      questionEn: "What is the refund policy?",
      answer: "نقدم ضمان استرداد كامل خلال 24 ساعة في حالة وجود مشكلة في الحساب أو عدم مطابقة الوصف. بعد 24 ساعة، نقدم دعم فني مجاني لحل أي مشاكل بدلاً من الاسترداد.",
      answerEn: "We offer full refund guarantee within 24 hours in case of account issues or description mismatch. After 24 hours, we provide free technical support to solve any issues instead of refund.",
      category: "policy"
    },
    {
      id: "customer-support",
      question: "كيف يمكنني التواصل مع خدمة العملاء؟",
      questionEn: "How can I contact customer service?",
      answer: "خدمة العملاء متاحة 24/7 عبر: واتساب، تليجرام، ديسكورد، والدردشة المباشرة على الموقع. فريقنا جاهز لمساعدتك في أي وقت وحل جميع استفساراتك بسرعة ومهنية.",
      answerEn: "Customer service is available 24/7 via: WhatsApp, Telegram, Discord, and live chat on website. Our team is ready to help you anytime and solve all your inquiries quickly and professionally.",
      category: "support"
    }
  ];

  const categories = [
    { id: "security", name: "الأمان والضمان", nameEn: "Security & Guarantee", icon: Shield },
    { id: "delivery", name: "التسليم", nameEn: "Delivery", icon: Truck },
    { id: "uc", name: "شحن UC", nameEn: "UC Recharge", icon: CreditCard },
    { id: "accounts", name: "الحسابات", nameEn: "Accounts", icon: Users },
    { id: "payment", name: "الدفع", nameEn: "Payment", icon: CreditCard },
    { id: "support", name: "الدعم الفني", nameEn: "Support", icon: MessageCircle }
  ];

  const faqKeywords = [
    "أسئلة شائعة ببجي",
    "حسابات ببجي آمنة",
    "شحن UC ببجي",
    "شراء حسابات ببجي",
    "ضمان حسابات ببجي",
    "دعم فني ببجي",
    "PUBG accounts FAQ",
    "PUBG UC recharge help"
  ];

  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <SEO 
        title="الأسئلة الشائعة - دليل شامل لشراء حسابات ببجي وشحن UC"
        description="إجابات شاملة على جميع أسئلتك حول شراء حسابات ببجي الآمنة وشحن UC. دليل كامل للمبتدئين مع ضمان الأمان والدعم الفني 24/7"
        keywords={faqKeywords}
        ogImage="/images/faq-banner.jpg"
      />
      
      <FAQJsonLd 
        faqs={faqData.map(faq => ({
          question: isRTL ? faq.question : faq.questionEn,
          answer: isRTL ? faq.answer : faq.answerEn
        }))}
      />

      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <HelpCircle className="h-16 w-16 text-primary" />
          </div>
          <h1 className="text-4xl font-bold mb-4">
            {isRTL ? "الأسئلة الشائعة" : "Frequently Asked Questions"}
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            {isRTL 
              ? "إجابات شاملة على جميع أسئلتك حول شراء حسابات ببجي الآمنة وشحن UC بأفضل الأسعار"
              : "Comprehensive answers to all your questions about buying safe PUBG accounts and UC recharge at best prices"
            }
          </p>
        </div>

        {/* Categories */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-12">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <Card key={category.id} className="text-center hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-4">
                  <Icon className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <p className="text-sm font-medium">
                    {isRTL ? category.name : category.nameEn}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* FAQ Accordion */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">
              {isRTL ? "الأسئلة والأجوبة" : "Questions & Answers"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              {faqData.map((faq, index) => (
                <AccordionItem key={faq.id} value={`item-${index}`}>
                  <AccordionTrigger className="text-right">
                    <span className="font-semibold">
                      {isRTL ? faq.question : faq.questionEn}
                    </span>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="text-muted-foreground leading-relaxed">
                      {isRTL ? faq.answer : faq.answerEn}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>

        {/* Contact CTA */}
        <Card className="mt-12 bg-primary/5">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">
              {isRTL ? "لم تجد إجابة لسؤالك؟" : "Didn't find your answer?"}
            </h3>
            <p className="text-muted-foreground mb-6">
              {isRTL 
                ? "فريق الدعم الفني متاح 24/7 لمساعدتك في أي استفسار"
                : "Our support team is available 24/7 to help with any inquiry"
              }
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <a 
                href="https://wa.me/201234567890" 
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                {isRTL ? "واتساب" : "WhatsApp"}
              </a>
              <a 
                href="/contact" 
                className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                {isRTL ? "تواصل معنا" : "Contact Us"}
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FAQ;
