import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { isChangingLanguageGlobal } from "@/components/LanguageSwitcher";

/**
 * Combines class names using clsx and tailwind-merge
 * This ensures that Tailwind classes are applied correctly and conflicting classes are handled properly
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a date string to a localized format
 * @param dateStr - Date string in ISO format (YYYY-MM-DD)
 * @param locale - Locale for formatting (default: 'ar-EG')
 * @returns Formatted date string
 */
export function formatDate(dateStr: string, locale: string = 'ar-EG'): string {
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (e) {
    return dateStr; // Return original if parsing fails
  }
}

/**
 * Determines if a page loader should be shown
 * Prevents duplicate loaders when changing language
 */
export function shouldShowLoader(isPageLoading: boolean): boolean {
  return isPageLoading && !isChangingLanguageGlobal.value;
}
