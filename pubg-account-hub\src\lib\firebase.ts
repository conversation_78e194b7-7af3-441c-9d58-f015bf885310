import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getAuth } from "firebase/auth";
import { getStorage } from "firebase/storage";
import directConfig from "./firebaseConfig.js";

// Firebase configuration based on environment
let firebaseConfig;

// In production, use the direct config to avoid environment variable issues
if (import.meta.env.PROD) {
  console.log("Using direct Firebase configuration for production");
  firebaseConfig = directConfig;
} else {
  // In development, use environment variables
  firebaseConfig = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: import.meta.env.VITE_FIREBASE_APP_ID,
    measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
  };
}

// Validate the Firebase configuration
const requiredConfigFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'appId'];
const missingFields = requiredConfigFields.filter(field => !firebaseConfig[field]);

if (missingFields.length > 0) {
  console.error(`Firebase configuration is incomplete. Missing: ${missingFields.join(', ')}`);
  console.error('Make sure your .env file is properly set up with all Firebase configuration values.');
  
  // In development, provide clear error message
  if (import.meta.env.DEV) {
    console.warn('For development, copy .env.example to .env and fill in your Firebase credentials');
  }
} else {
  console.log('Firebase configuration is valid.');
}

// Initialize Firebase only if we have the required configuration
let app, db, auth, storage;

try {
  // Only initialize if we have the minimum required fields
  if (missingFields.length === 0) {
    app = initializeApp(firebaseConfig);
    db = getFirestore(app);
    auth = getAuth(app);
    storage = getStorage(app);
    console.log(`Firebase initialized with project: ${firebaseConfig.projectId}`);
  }
} catch (error) {
  console.error('Failed to initialize Firebase:', error);
}

export { app, db, auth, storage };
