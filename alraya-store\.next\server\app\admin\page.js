(()=>{var e={};e.id=698,e.ids=[698],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4595:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>t8});var a=t(60687),l=t(43210),r=t(16189),i=t(55192),n=t(24934),c=t(19080),d=t(62688);let o=(0,d.A)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]);var x=t(28561),m=t(23928),h=t(48340);let u=(0,d.A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var p=t(68988),g=t(59821),j=t(63974),f=t(37826),b=t(96474),N=t(45583),v=t(64398),w=t(99270),y=t(63143),k=t(88233),C=t(48196);let A="product_categories";function R(){try{let e=localStorage.getItem(A);if(!e)return S();return JSON.parse(e).map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt)}))}catch(e){return console.error("Error loading categories:",e),S()}}function D(e){try{localStorage.setItem(A,JSON.stringify(e))}catch(e){throw console.error("Error saving categories:",e),Error("فشل في حفظ الفئات")}}function S(){let e=new Date;return[{id:"cat_pubg_mobile",name:"PUBG Mobile",image:"https://via.placeholder.com/100x100/1e293b/f1f5f9?text=PUBG",createdAt:e,updatedAt:e},{id:"cat_free_fire",name:"Free Fire",image:"https://via.placeholder.com/100x100/dc2626/ffffff?text=FF",createdAt:e,updatedAt:e},{id:"cat_gift_cards",name:"بطاقات الهدايا",image:"https://via.placeholder.com/100x100/059669/ffffff?text=GIFT",createdAt:e,updatedAt:e},{id:"cat_other_games",name:"ألعاب أخرى",image:"https://via.placeholder.com/100x100/7c3aed/ffffff?text=GAMES",createdAt:e,updatedAt:e}]}var M=t(11860),_=t(16023),I=t(19959);function T({product:e,onSave:s,onCancel:t,isEditing:r=!1}){let[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)({name:e?.name||"",description:e?.description||"",category:e?.category||"",tags:e?.tags||[],image:e?.image||"",packages:e?.packages||[],fields:e?.fields||[],features:e?.features||[],isActive:e?.isActive??!0,isFeatured:e?.isFeatured||!1,deliveryType:e?.deliveryType||"code_based",productType:e?.productType||"digital",processingType:e?.processingType||"instant"}),o=async()=>{if(n(!0),!c.name?.trim()){alert("يرجى إدخال اسم المنتج"),n(!1);return}if(!c.category?.trim()){alert("يرجى إدخال فئة المنتج"),n(!1);return}if(!c.packages||0===c.packages.length){alert("يرجى إضافة حزمة واحدة على الأقل"),n(!1);return}try{let t;let a={name:c.name,description:c.description,category:c.category,image:c.image,deliveryType:c.deliveryType,productType:c.productType,processingType:c.processingType,fields:c.fields,packages:c.packages,features:c.features,tags:c.tags,isActive:c.isActive,isFeatured:c.isFeatured,createdBy:void 0};t=r&&e?await (0,C.vc)(e.id,a):await (0,C.WY)(a),s(t)}catch(e){console.error("Error saving product:",e),alert("حدث خطأ أثناء حفظ المنتج")}finally{n(!1)}},x=(e,s,t)=>{d(a=>({...a,packages:a.packages?.map((a,l)=>l===e?{...a,[s]:t}:a)||[]}))},m=e=>{d(s=>({...s,packages:s.packages?.filter((s,t)=>t!==e)||[]}))},h=e=>e.digitalCodes?.map(e=>e.key).join("\n")||"",u=(e,s)=>{x(e,"digitalCodes",s.split("\n").map(e=>e.trim()).filter(Boolean).map((e,s)=>({id:`${Date.now()}-${s}`,key:e,used:!1,assignedToOrderId:null})))},p=(e,s,t)=>{d(a=>({...a,fields:a.fields?.map((a,l)=>l===e?{...a,[s]:t}:a)||[]}))},g=e=>{d(s=>({...s,fields:s.fields?.filter((s,t)=>t!==e)||[]}))};return(0,a.jsxs)("div",{className:"bg-gray-800/90 backdrop-blur-md rounded-xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl",children:[(0,a.jsx)("div",{className:"p-4 md:p-6 border-b border-gray-700/50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg md:text-2xl font-bold text-white",children:r?"تعديل المنتج":"إضافة منتج جديد"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors",children:(0,a.jsx)(M.A,{className:"w-5 h-5 md:w-6 md:h-6"})})]})}),(0,a.jsxs)("div",{className:"p-4 md:p-6 space-y-4 md:space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"اسم المنتج"}),(0,a.jsx)("input",{type:"text",value:c.name||"",onChange:e=>d(s=>({...s,name:e.target.value})),className:"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white",placeholder:"أدخل اسم المنتج"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"الفئة"}),(0,a.jsx)("input",{type:"text",value:c.category||"",onChange:e=>d(s=>({...s,category:e.target.value})),className:"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white",placeholder:"مثل: MOBA, RPG, باتل رويال"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"الوصف"}),(0,a.jsx)("textarea",{value:c.description||"",onChange:e=>d(s=>({...s,description:e.target.value})),rows:3,className:"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white",placeholder:"وصف المنتج"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"العلامات (مفصولة بفاصلة)"}),(0,a.jsx)("input",{type:"text",value:c.tags?.join(", ")||"",onChange:e=>d(s=>({...s,tags:e.target.value.split(",").map(e=>e.trim()).filter(Boolean)})),className:"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white",placeholder:"شائع, مميز, جديد"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"صورة الغلاف"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("input",{type:"text",value:c.image||"",onChange:e=>d(s=>({...s,image:e.target.value})),className:"flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white",placeholder:"رابط الصورة"}),(0,a.jsxs)("button",{className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors",children:[(0,a.jsx)(_.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"رفع"})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-6 space-x-reverse",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2 space-x-reverse text-white",children:[(0,a.jsx)("input",{type:"checkbox",checked:c.isFeatured||!1,onChange:e=>d(s=>({...s,isFeatured:e.target.checked})),className:"rounded"}),(0,a.jsx)("span",{children:"منتج مميز"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2 space-x-reverse text-white",children:[(0,a.jsx)("input",{type:"checkbox",checked:c.isActive??!0,onChange:e=>d(s=>({...s,isActive:e.target.checked})),className:"rounded"}),(0,a.jsx)("span",{children:"منتج نشط"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white",children:"الحزم"}),(0,a.jsxs)("button",{onClick:()=>{let e={id:Date.now().toString(),name:"",amount:"",price:0,originalPrice:0,discount:0,description:"",popular:!1,isActive:!0,digitalCodes:[]};d(s=>({...s,packages:[...s.packages||[],e]}))},className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"إضافة حزمة"})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:c.packages?.map((e,s)=>a.jsxs("div",{className:"bg-gray-700/30 backdrop-blur-sm rounded-lg p-6 border border-gray-600/50",children:[a.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-4",children:[a.jsx("input",{type:"text",value:e.name,onChange:e=>x(s,"name",e.target.value),placeholder:"اسم الحزمة",className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"}),a.jsx("input",{type:"number",step:"0.01",value:e.price,onChange:e=>x(s,"price",Number(e.target.value)),placeholder:"السعر",className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"}),a.jsx("input",{type:"number",step:"0.01",value:e.originalPrice||"",onChange:e=>x(s,"originalPrice",Number(e.target.value)||void 0),placeholder:"السعر الأصلي",className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"}),a.jsx("input",{type:"number",value:e.discount||"",onChange:e=>x(s,"discount",Number(e.target.value)||void 0),placeholder:"نسبة الخصم (%)",className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"})]}),a.jsxs("div",{className:"grid grid-cols-1 gap-4 mb-4",children:[a.jsx("input",{type:"text",value:e.amount,onChange:e=>x(s,"amount",e.target.value),placeholder:"الكمية (مثل: 60 يوسي)",className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"}),a.jsx("textarea",{value:e.description||"",onChange:e=>x(s,"description",e.target.value),placeholder:"وصف الحزمة (اختياري)",rows:2,className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none text-white"})]}),a.jsxs("div",{className:"border-t border-gray-600/50 pt-4",children:[a.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse mb-3",children:[a.jsx(I.A,{className:"w-5 h-5 text-blue-400"}),a.jsx("h5",{className:"font-semibold text-blue-400",children:"الأكواد الرقمية"}),a.jsx("span",{className:"text-sm text-gray-400",children:"(اختياري)"})]}),a.jsxs("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3",children:[a.jsx("p",{className:"text-sm text-blue-300 mb-2",children:"\uD83D\uDCA1 إرشادات الأكواد الرقمية:"}),a.jsxs("ul",{className:"text-xs text-blue-200 space-y-1",children:[a.jsx("li",{children:"• أدخل كود واحد في كل سطر"}),a.jsx("li",{children:"• سيتم تخصيص كود واحد فقط لكل طلب"}),a.jsx("li",{children:"• الأكواد المستخدمة لن تظهر للمشترين الآخرين"}),a.jsx("li",{children:"• إذا نفدت الأكواد، ستصبح الحزمة غير متاحة"})]})]}),a.jsx("textarea",{value:h(e),onChange:e=>u(s,e.target.value),placeholder:"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12 9GHT-LMK3-992Z",rows:4,className:"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 font-mono text-sm resize-none text-white"}),e.digitalCodes&&e.digitalCodes.length>0&&a.jsxs("div",{className:"mt-2 text-sm text-green-400",children:["✅ تم إضافة ",e.digitalCodes.length," كود رقمي"]})]}),a.jsx("div",{className:"flex items-center justify-end mt-4",children:a.jsx("button",{onClick:()=>m(s),className:"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-all duration-300",children:a.jsx(k.A,{className:"w-4 h-4"})})})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white",children:"الحقول المخصصة"}),(0,a.jsxs)("button",{onClick:()=>{let e={id:Date.now().toString(),type:"text",name:`field_${Date.now()}`,label:"",placeholder:"",required:!1,isActive:!0,validation:{}};d(s=>({...s,fields:[...s.fields||[],e]}))},className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"إضافة حقل"})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:c.fields?.map((e,s)=>a.jsxs("div",{className:"bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50",children:[a.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-4",children:[a.jsx("input",{type:"text",value:e.label,onChange:e=>p(s,"label",e.target.value),placeholder:"تسمية الحقل",className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"}),a.jsxs("select",{value:e.type,onChange:e=>p(s,"type",e.target.value),className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white",children:[a.jsx("option",{value:"text",children:"نص"}),a.jsx("option",{value:"email",children:"بريد إلكتروني"}),a.jsx("option",{value:"number",children:"رقم"})]}),a.jsx("input",{type:"text",value:e.placeholder,onChange:e=>p(s,"placeholder",e.target.value),placeholder:"النص التوضيحي",className:"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("label",{className:"flex items-center space-x-2 space-x-reverse text-white",children:[a.jsx("input",{type:"checkbox",checked:e.required,onChange:e=>p(s,"required",e.target.checked),className:"rounded"}),a.jsx("span",{children:"مطلوب"})]}),a.jsx("button",{onClick:()=>g(s),className:"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-all duration-300",children:a.jsx(k.A,{className:"w-4 h-4"})})]})]},s))})]}),(0,a.jsxs)("div",{className:"flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50",children:[(0,a.jsx)("button",{onClick:o,disabled:i,className:"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors",children:i?(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):(0,a.jsx)("span",{children:r?"تحديث المنتج":"إضافة المنتج"})}),(0,a.jsx)("button",{onClick:t,disabled:i,className:"flex-1 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors",children:"إلغاء"})]})]})]})}var P=t(10403),E=t(11273),Z=t(98599),$=t(26134),O=t(70569),q=t(8730),z="AlertDialog",[F,L]=(0,E.A)(z,[$.Hs]),U=(0,$.Hs)(),W=e=>{let{__scopeAlertDialog:s,...t}=e,l=U(s);return(0,a.jsx)($.bL,{...l,...t,modal:!0})};W.displayName=z;var J=l.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...l}=e,r=U(t);return(0,a.jsx)($.l9,{...r,...l,ref:s})});J.displayName="AlertDialogTrigger";var B=e=>{let{__scopeAlertDialog:s,...t}=e,l=U(s);return(0,a.jsx)($.ZL,{...l,...t})};B.displayName="AlertDialogPortal";var G=l.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...l}=e,r=U(t);return(0,a.jsx)($.hJ,{...r,...l,ref:s})});G.displayName="AlertDialogOverlay";var V="AlertDialogContent",[H,X]=F(V),K=(0,q.Dc)("AlertDialogContent"),Y=l.forwardRef((e,s)=>{let{__scopeAlertDialog:t,children:r,...i}=e,n=U(t),c=l.useRef(null),d=(0,Z.s)(s,c),o=l.useRef(null);return(0,a.jsx)($.G$,{contentName:V,titleName:Q,docsSlug:"alert-dialog",children:(0,a.jsx)(H,{scope:t,cancelRef:o,children:(0,a.jsxs)($.UC,{role:"alertdialog",...n,...i,ref:d,onOpenAutoFocus:(0,O.m)(i.onOpenAutoFocus,e=>{e.preventDefault(),o.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsx)(K,{children:r}),(0,a.jsx)(ei,{contentRef:c})]})})})});Y.displayName=V;var Q="AlertDialogTitle",ee=l.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...l}=e,r=U(t);return(0,a.jsx)($.hE,{...r,...l,ref:s})});ee.displayName=Q;var es="AlertDialogDescription",et=l.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...l}=e,r=U(t);return(0,a.jsx)($.VY,{...r,...l,ref:s})});et.displayName=es;var ea=l.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...l}=e,r=U(t);return(0,a.jsx)($.bm,{...r,...l,ref:s})});ea.displayName="AlertDialogAction";var el="AlertDialogCancel",er=l.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...l}=e,{cancelRef:r}=X(el,t),i=U(t),n=(0,Z.s)(s,r);return(0,a.jsx)($.bm,{...i,...l,ref:n})});er.displayName=el;var ei=({contentRef:e})=>{let s=`\`${V}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${V}\` by passing a \`${es}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${V}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return l.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(s)},[s,e]),null},en=t(96241);let ec=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(G,{className:(0,en.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:t}));ec.displayName=G.displayName;let ed=l.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(B,{children:[(0,a.jsx)(ec,{}),(0,a.jsx)(Y,{ref:t,className:(0,en.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s})]}));ed.displayName=Y.displayName;let eo=({className:e,...s})=>(0,a.jsx)("div",{className:(0,en.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});eo.displayName="AlertDialogHeader";let ex=({className:e,...s})=>(0,a.jsx)("div",{className:(0,en.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});ex.displayName="AlertDialogFooter";let em=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(ee,{ref:t,className:(0,en.cn)("text-lg font-semibold",e),...s}));em.displayName=ee.displayName;let eh=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(et,{ref:t,className:(0,en.cn)("text-sm text-muted-foreground",e),...s}));eh.displayName=et.displayName;let eu=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(ea,{ref:t,className:(0,en.cn)((0,n.r)(),e),...s}));eu.displayName=ea.displayName;let ep=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(er,{ref:t,className:(0,en.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...s}));ep.displayName=er.displayName;var eg=t(26269);let ej=(0,d.A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);function ef({value:e,onChange:s,label:t="الصورة",placeholder:r="اختر صورة...",className:i=""}){let[c,d]=(0,l.useState)("upload"),[o,x]=(0,l.useState)(""),m=(0,l.useRef)(null);return(0,a.jsxs)("div",{className:i,children:[(0,a.jsxs)(P.J,{className:"text-slate-300 mb-2 block",children:[t," *"]}),e&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center overflow-hidden",children:(0,a.jsx)("img",{src:e,alt:"Preview",className:"w-full h-full object-cover"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"الصورة الحالية"}),(0,a.jsx)("p",{className:"text-slate-400 text-xs",children:e.startsWith("data:")?"صورة مرفوعة":"رابط خارجي"})]})]}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{s(""),x(""),m.current&&(m.current.value="")},className:"border-red-600 text-red-400 hover:bg-red-600/10",children:(0,a.jsx)(M.A,{className:"h-4 w-4"})})]})}),(0,a.jsxs)(eg.tU,{value:c,onValueChange:e=>d(e),className:"w-full",children:[(0,a.jsxs)(eg.j7,{className:"grid w-full grid-cols-2 bg-slate-700/50",children:[(0,a.jsxs)(eg.Xi,{value:"upload",className:"text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-1"}),"رفع صورة"]}),(0,a.jsxs)(eg.Xi,{value:"url",className:"text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white",children:[(0,a.jsx)(ej,{className:"h-4 w-4 mr-1"}),"رابط صورة"]})]}),(0,a.jsxs)(eg.av,{value:"upload",className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"ارفع صورة من جهازك (أقل من 5 ميجابايت)"}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(p.p,{ref:m,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files?.[0];if(t){if(t.size>5242880){alert("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");return}if(!t.type.startsWith("image/")){alert("يرجى اختيار ملف صورة صالح");return}let e=new FileReader;e.onload=e=>{s(e.target?.result)},e.readAsDataURL(t)}},className:"bg-slate-600 border-slate-500 text-white file:bg-slate-700 file:text-white file:border-0 file:mr-2"})})]}),(0,a.jsxs)(eg.av,{value:"url",className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"أدخل رابط صورة من الإنترنت"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(p.p,{value:o,onChange:e=>x(e.target.value),placeholder:"https://example.com/image.jpg",className:"bg-slate-600 border-slate-500 text-white"}),(0,a.jsx)(n.$,{onClick:()=>{if(!o.trim()){alert("يرجى إدخال رابط الصورة");return}try{new URL(o),s(o.trim()),x("")}catch{alert("رابط غير صالح")}},className:"bg-blue-600 hover:bg-blue-700",children:"إضافة"})]})]})]})]})}var eb=t(13964);let eN=(0,d.A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),ev=(0,d.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function ew({type:e,title:s,message:t,duration:r=4e3,onClose:i}){let[n,c]=(0,l.useState)(!0);return n?(0,a.jsx)("div",{className:`
      fixed top-4 right-4 z-50 max-w-sm w-full
      transform transition-all duration-300 ease-in-out
      ${n?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
    `,children:(0,a.jsx)("div",{className:`
        rounded-lg border p-4 shadow-lg backdrop-blur-sm
        ${(()=>{switch(e){case"success":return"bg-green-500/20 border-green-500/30 text-green-400";case"error":return"bg-red-500/20 border-red-500/30 text-red-400";case"warning":return"bg-yellow-500/20 border-yellow-500/30 text-yellow-400";case"info":return"bg-blue-500/20 border-blue-500/30 text-blue-400"}})()}
      `,children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(e){case"success":return(0,a.jsx)(eb.A,{className:"h-5 w-5"});case"error":return(0,a.jsx)(M.A,{className:"h-5 w-5"});case"warning":return(0,a.jsx)(eN,{className:"h-5 w-5"});case"info":return(0,a.jsx)(ev,{className:"h-5 w-5"})}})()}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:s}),t&&(0,a.jsx)("p",{className:"text-xs opacity-90 mt-1",children:t})]}),(0,a.jsx)("button",{onClick:()=>{c(!1),setTimeout(()=>i?.(),300)},className:"flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity",children:(0,a.jsx)(M.A,{className:"h-4 w-4"})})]})})}):null}let ey=0,ek=[],eC=[],eA=(e,s,t,a)=>{let l=`notification-${++ey}`;return eC=[...eC,{id:l,type:e,title:s,message:t,duration:a}],ek.forEach(e=>e(eC)),0!==a&&setTimeout(()=>{eR(l)},a||4e3),l},eR=e=>{eC=eC.filter(s=>s.id!==e),ek.forEach(e=>e(eC))};function eD(){let[e,s]=(0,l.useState)([]);return(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(e=>(0,a.jsx)(ew,{type:e.type,title:e.title,message:e.message,duration:0,onClose:()=>eR(e.id)},e.id))})}let eS=(e,s)=>eA("success",e,s),eM=(e,s)=>eA("error",e,s),e_=(e,s)=>eA("warning",e,s);function eI({onCategoryChange:e}){let[s,t]=(0,l.useState)([]),[r,c]=(0,l.useState)(!1),[d,x]=(0,l.useState)(null),[m,h]=(0,l.useState)({name:"",image:""}),u=()=>{try{let e=R();t(e)}catch(e){eM("خطأ في التحميل","فشل في تحميل الفئات")}},g=()=>{h({name:"",image:""}),x(null)},j=()=>{g(),c(!0)},N=e=>{h({name:e.name,image:e.image}),x(e),c(!0)},v=s=>{try{(function(e){let s=R(),t=s.filter(s=>s.id!==e);if(t.length===s.length)throw Error("الفئة غير موجودة");D(t)})(s.id),eS("تم الحذف","تم حذف الفئة بنجاح"),u(),e?.()}catch(e){eM("خطأ في الحذف",e instanceof Error?e.message:"فشل في حذف الفئة")}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o,{className:"h-5 w-5 text-blue-400"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"إدارة الفئات"})]}),(0,a.jsxs)(f.lG,{open:r,onOpenChange:c,children:[(0,a.jsx)(f.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{onClick:j,className:"bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة"]})}),(0,a.jsxs)(f.Cf,{className:"bg-slate-800 border-slate-700 text-white max-w-md",children:[(0,a.jsx)(f.c7,{children:(0,a.jsx)(f.L3,{children:d?"تعديل الفئة":"إضافة فئة جديدة"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"اسم الفئة *"}),(0,a.jsx)(p.p,{value:m.name,onChange:e=>h(s=>({...s,name:e.target.value})),placeholder:"مثال: PUBG Mobile",className:"bg-slate-600 border-slate-500 text-white"})]}),(0,a.jsx)(ef,{value:m.image,onChange:e=>h(s=>({...s,image:e})),label:"صورة الفئة",placeholder:"اختر صورة للفئة..."}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{onClick:()=>{if(!m.name.trim()){e_("اسم مطلوب","يرجى إدخال اسم الفئة");return}if(!m.image.trim()){e_("صورة مطلوبة","يرجى اختيار صورة للفئة");return}try{d?(function(e,s){let t=R(),a=t.findIndex(s=>s.id===e);if(-1===a)throw Error("الفئة غير موجودة");if(s.name&&t.some(t=>t.id!==e&&t.name.toLowerCase()===s.name.toLowerCase()))throw Error("اسم الفئة موجود بالفعل");if(void 0!==s.image&&!s.image.trim())throw Error("صورة الفئة مطلوبة");let l={...t[a],...s,updatedAt:new Date};t[a]=l,D(t)}(d.id,{name:m.name.trim(),image:m.image.trim()}),eS("تم التحديث","تم تحديث الفئة بنجاح")):(function(e,s){let t=R();if(t.some(s=>s.name.toLowerCase()===e.toLowerCase()))throw Error("اسم الفئة موجود بالفعل");if(!s.trim())throw Error("صورة الفئة مطلوبة");let a={id:`cat_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,name:e.trim(),image:s.trim(),createdAt:new Date,updatedAt:new Date};D([...t,a])}(m.name.trim(),m.image.trim()),eS("تم الإنشاء","تم إنشاء الفئة بنجاح")),u(),c(!1),g(),e?.()}catch(e){eM("خطأ في الحفظ",e instanceof Error?e.message:"فشل في حفظ الفئة")}},className:"flex-1",children:d?"تحديث":"إضافة"}),(0,a.jsx)(n.$,{variant:"outline",onClick:()=>c(!1),children:"إلغاء"})]})]})]})]})]}),0===s.length?(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsxs)(i.Wu,{className:"p-8 text-center",children:[(0,a.jsx)(o,{className:"h-16 w-16 text-slate-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"لا توجد فئات"}),(0,a.jsx)("p",{className:"text-slate-400 mb-4",children:"ابدأ بإضافة فئات لتنظيم منتجاتك"}),(0,a.jsxs)(n.$,{onClick:j,className:"bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})]})}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:s.map(e=>(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors",children:(0,a.jsxs)(i.Wu,{className:"p-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center overflow-hidden",children:(0,a.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-slate-400 text-xs",children:e.createdAt.toLocaleDateString("ar")})]})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{size:"sm",variant:"outline",onClick:()=>N(e),className:"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"تعديل"]}),(0,a.jsxs)(W,{children:[(0,a.jsx)(J,{asChild:!0,children:(0,a.jsx)(n.$,{size:"sm",variant:"outline",className:"border-red-600 text-red-400 hover:bg-red-600/10",children:(0,a.jsx)(k.A,{className:"h-3 w-3"})})}),(0,a.jsxs)(ed,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsxs)(eo,{children:[(0,a.jsx)(em,{className:"text-white",children:"حذف الفئة"}),(0,a.jsxs)(eh,{className:"text-slate-400",children:['هل أنت متأكد من حذف فئة "',e.name,'"؟',(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-yellow-400 text-sm",children:"تحذير: المنتجات المرتبطة بهذه الفئة ستحتاج إلى إعادة تصنيف."})]})]}),(0,a.jsxs)(ex,{children:[(0,a.jsx)(ep,{className:"bg-slate-700 text-white border-slate-600",children:"إلغاء"}),(0,a.jsx)(eu,{onClick:()=>v(e),className:"bg-red-600 hover:bg-red-700",children:"حذف"})]})]})]})]})]})},e.id))})]})}function eT(){let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)([]),[d,x]=(0,l.useState)(null),[m,h]=(0,l.useState)(!0),[A,R]=(0,l.useState)(""),[D,S]=(0,l.useState)({}),[M,_]=(0,l.useState)(null),[I,P]=(0,l.useState)(!1),[E,Z]=(0,l.useState)("products"),[$,O]=(0,l.useState)(!1),q=async()=>{try{h(!0);let[e,t]=await Promise.all([(0,C.d$)().catch(e=>(console.error("Error loading products:",e),[])),(0,C.do)().catch(e=>(console.error("Error loading stats:",e),{totalProducts:0,activeProducts:0,digitalProducts:0,physicalProducts:0,totalPackages:0,totalOrders:0,popularCategories:[]}))]);s(Array.isArray(e)?e:[]),x(t)}catch(e){console.error("Error loading data:",e),s([]),x({totalProducts:0,activeProducts:0,digitalProducts:0,physicalProducts:0,totalPackages:0,totalOrders:0,popularCategories:[]})}finally{h(!1)}},z=async e=>{s(s=>[...s,e]),P(!1),await q()},F=async e=>{s(s=>s.map(s=>s.id===e.id?e:s)),O(!1),_(null),await q()},L=async e=>{if(confirm("هل أنت متأكد من حذف هذا المنتج؟"))try{await (0,C.DD)(e),s(s=>s.filter(s=>s.id!==e)),await q()}catch(e){console.error("Error deleting product:",e)}};return m?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-white",children:"جاري التحميل..."})}):(0,a.jsxs)("div",{className:"space-y-6 p-4 lg:p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold text-white",children:"إدارة المنتجات"}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"إنشاء وتعديل وإدارة منتجات المتجر"})]}),(0,a.jsxs)(f.lG,{open:I,onOpenChange:P,children:[(0,a.jsx)(f.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 min-h-[44px]",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة منتج جديد"]})}),(0,a.jsxs)(f.Cf,{className:"bg-slate-800 border-slate-700 text-white max-w-[95vw] sm:max-w-[90vw] lg:max-w-4xl max-h-[90vh] overflow-y-auto p-4 sm:p-6",children:[(0,a.jsx)(f.c7,{className:"pb-4",children:(0,a.jsx)(f.L3,{className:"text-lg sm:text-xl",children:"إنشاء منتج جديد"})}),(0,a.jsx)(T,{onSave:z,onCancel:()=>P(!1)})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-slate-800/50 rounded-lg p-1 border border-slate-700/50 overflow-x-auto",children:[(0,a.jsxs)("button",{onClick:()=>Z("products"),className:`flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap ${"products"===E?"bg-blue-600 text-white shadow-sm":"text-slate-400 hover:text-white hover:bg-slate-700/50"}`,children:[(0,a.jsx)(c.A,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"hidden xs:inline",children:"المنتجات"}),(0,a.jsx)("span",{className:"xs:hidden",children:"منتجات"})]}),(0,a.jsxs)("button",{onClick:()=>Z("categories"),className:`flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap ${"categories"===E?"bg-blue-600 text-white shadow-sm":"text-slate-400 hover:text-white hover:bg-slate-700/50"}`,children:[(0,a.jsx)(o,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"hidden xs:inline",children:"الفئات"}),(0,a.jsx)("span",{className:"xs:hidden",children:"فئات"})]})]}),"categories"===E?(0,a.jsx)(eI,{onCategoryChange:q}):(0,a.jsxs)(a.Fragment,{children:[d&&(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"إجمالي المنتجات"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:d.totalProducts})]}),(0,a.jsx)(c.A,{className:"h-8 w-8 text-blue-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"المنتجات النشطة"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:d.activeProducts})]}),(0,a.jsx)(N.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"المنتجات الرقمية"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-400",children:d.digitalProducts})]}),(0,a.jsx)(v.A,{className:"h-8 w-8 text-purple-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"إجمالي الحزم"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:d.totalPackages})]}),(0,a.jsx)(u,{className:"h-8 w-8 text-yellow-400"})]})})})]}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)(p.p,{placeholder:"البحث في المنتجات...",value:A,onChange:e=>R(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(j.l6,{value:D.category||"all",onValueChange:e=>S(s=>({...s,category:"all"===e?void 0:e})),children:[(0,a.jsx)(j.bq,{className:"w-40 bg-slate-700 border-slate-600 text-white",children:(0,a.jsx)(j.yv,{placeholder:"الفئة"})}),(0,a.jsxs)(j.gC,{className:"bg-slate-700 border-slate-600",children:[(0,a.jsx)(j.eb,{value:"all",children:"جميع الفئات"}),[...new Set(e.map(e=>e.category))].sort().map(e=>(0,a.jsx)(j.eb,{value:e,children:e},e))]})]}),(0,a.jsxs)(j.l6,{value:D.productType||"all",onValueChange:e=>S(s=>({...s,productType:"all"===e?void 0:e})),children:[(0,a.jsx)(j.bq,{className:"w-40 bg-slate-700 border-slate-600 text-white",children:(0,a.jsx)(j.yv,{placeholder:"النوع"})}),(0,a.jsxs)(j.gC,{className:"bg-slate-700 border-slate-600",children:[(0,a.jsx)(j.eb,{value:"all",children:"جميع الأنواع"}),(0,a.jsx)(j.eb,{value:"digital",children:"رقمي"}),(0,a.jsx)(j.eb,{value:"physical",children:"مادي"}),(0,a.jsx)(j.eb,{value:"service",children:"خدمة"})]})]})]})]})})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors",children:[(0,a.jsx)(i.aR,{className:"pb-3",children:(0,a.jsx)("div",{className:"flex justify-between items-start",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(i.ZB,{className:"text-white text-lg mb-2",children:e.name}),(0,a.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,a.jsx)(g.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"نشط":"غير نشط"}),(0,a.jsx)(g.E,{variant:"outline",className:"digital"===e.productType?"border-purple-500 text-purple-400":"physical"===e.productType?"border-blue-500 text-blue-400":"border-green-500 text-green-400",children:"digital"===e.productType?"رقمي":"physical"===e.productType?"مادي":"خدمة"}),"instant"===e.processingType&&(0,a.jsxs)(g.E,{className:"bg-green-500/20 text-green-400 border-green-500/30",children:[(0,a.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"فوري"]})]})]})})}),(0,a.jsxs)(i.Wu,{className:"pt-0",children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm mb-3 line-clamp-2",children:e.description||"لا يوجد وصف"}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm text-slate-400 mb-4",children:[(0,a.jsxs)("span",{children:["الفئة: ",e.category]}),(0,a.jsxs)("span",{children:[e.packages?.length||0," حزمة"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{size:"sm",variant:"outline",onClick:()=>{_(e),O(!0)},className:"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-1"}),"تعديل"]}),(0,a.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>L(e.id),className:"border-red-600 text-red-400 hover:bg-red-600/10",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})})]})]})]},e.id))}),0===t.length&&!m&&(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsxs)(i.Wu,{className:"p-8 text-center",children:[(0,a.jsx)(c.A,{className:"h-16 w-16 text-slate-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد منتجات"}),(0,a.jsx)("p",{className:"text-slate-400 mb-4",children:A||Object.keys(D).length>0?"لم يتم العثور على منتجات تطابق البحث أو الفلاتر":"ابدأ بإنشاء منتجك الأول"}),!A&&0===Object.keys(D).length&&(0,a.jsxs)(n.$,{onClick:()=>P(!0),className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة منتج جديد"]})]})}),(0,a.jsx)(f.lG,{open:$,onOpenChange:O,children:(0,a.jsxs)(f.Cf,{className:"bg-slate-800 border-slate-700 text-white max-w-[95vw] sm:max-w-[90vw] lg:max-w-4xl max-h-[90vh] overflow-y-auto p-4 sm:p-6",children:[(0,a.jsx)(f.c7,{className:"pb-4",children:(0,a.jsx)(f.L3,{className:"text-lg sm:text-xl",children:"تعديل المنتج"})}),M&&(0,a.jsx)(T,{product:M,onSave:F,onCancel:()=>{O(!1),_(null)},isEditing:!0})]})})]})]})}var eP=t(15616),eE=t(40214),eZ=t(7965),e$=t(33872),eO=t(65668),eq=t(10022),ez=t(99891),eF=t(75034),eL=t(19169),eU=t(48730),eW=t(97992);let eJ=(0,d.A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),eB=(0,d.A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var eG=t(41312);let eV={id:"contact_1",header:{id:"header_1",title:"اتصل بنا",description:"نحن هنا لمساعدتك! تواصل معنا في أي وقت وسنكون سعداء للرد على استفساراتك",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},contactInfo:{id:"info_1",whatsapp:"+249 123 456 789",email:"<EMAIL>",workingHours:"8:00 ص - 12:00 م (يومياً)",location:"الخرطوم، السودان",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},formFields:[{id:"field_1",name:"name",label:"الاسم",type:"text",required:!0,placeholder:"اسمك الكامل",order:1,isActive:!0},{id:"field_2",name:"phone",label:"رقم الهاتف",type:"tel",required:!0,placeholder:"+249xxxxxxxxx",order:2,isActive:!0},{id:"field_3",name:"email",label:"البريد الإلكتروني",type:"email",required:!1,placeholder:"<EMAIL>",order:3,isActive:!0},{id:"field_4",name:"subject",label:"الموضوع",type:"text",required:!0,placeholder:"موضوع رسالتك",order:4,isActive:!0},{id:"field_5",name:"message",label:"الرسالة",type:"textarea",required:!0,placeholder:"اكتب رسالتك هنا...",order:5,isActive:!0}],aboutUs:{id:"about_1",title:"من نحن",subtitle:"رايه شوب - رائدون في عالم الألعاب الرقمية",vision:{title:"رؤيتنا",description:"أن نكون المتجر الرقمي الأول في السودان والمنطقة لشحن الألعاب والخدمات الرقمية، نقدم أفضل تجربة للاعبين مع أسرع خدمة وأفضل الأسعار.",icon:"Target"},mission:{title:"مهمتنا",description:"تمكين اللاعبين من الاستمتاع بألعابهم المفضلة من خلال توفير خدمات شحن سريعة وآمنة وموثوقة، مع دعم فني متميز على مدار الساعة.",icon:"Heart"},values:{title:"قيمنا",items:["الثقة والشفافية في جميع التعاملات","السرعة والكفاءة في تقديم الخدمات","الابتكار المستمر لتحسين التجربة","دعم المجتمع المحلي للألعاب"],icon:"Award"},team:{title:"فريقنا",description:"فريق من المتخصصين والمتحمسين للألعاب، نعمل بجد لضمان حصولك على أفضل تجربة شحن ممكنة.",icon:"Users"},createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},stats:{id:"stats_1",customers:{value:"+10K",label:"عميل راضي",color:"text-yellow-400"},successRate:{value:"99%",label:"نجاح العمليات",color:"text-green-400"},averageTime:{value:"<1 دقيقة",label:"متوسط الشحن",color:"text-blue-400"},support:{value:"24/7",label:"دعم فني",color:"text-purple-400"},createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},faqs:[{id:"faq_1",question:"كم يستغرق وقت الشحن؟",answer:"معظم عمليات الشحن تتم خلال أقل من دقيقة واحدة للمنتجات الفورية، و 5-15 دقيقة للمنتجات اليدوية.",order:1,isActive:!0,category:"general",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"faq_2",question:"ما هي طرق الدفع المتاحة؟",answer:"نقبل الدفع عبر البنك الأهلي، بنك فيصل الإسلامي، بنك الخرطوم، والمحافظ الإلكترونية مثل بنكك.",order:2,isActive:!0,category:"payment",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}],trustIndicators:[{id:"trust_1",title:"أمان مضمون",description:"جميع المعاملات محمية بأعلى معايير الأمان",icon:"Shield",color:"text-green-400",order:1,isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"trust_2",title:"شحن فوري",description:"معظم عمليات الشحن تتم خلال أقل من دقيقة",icon:"Zap",color:"text-yellow-400",order:2,isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"trust_3",title:"دعم متميز",description:"فريق دعم متخصص متاح على مدار الساعة",icon:"Users",color:"text-blue-400",order:3,isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}],createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"};function eH(){let[e,s]=(0,l.useState)(eV),[t,r]=(0,l.useState)("overview"),[c,d]=(0,l.useState)(!1),[o,x]=(0,l.useState)(null),[m,N]=(0,l.useState)(!1),[v,w]=(0,l.useState)("new"),[y,C]=(0,l.useState)({}),A=async(e,t)=>{d(!0);try{await new Promise(e=>setTimeout(e,1e3)),s(s=>({...s,[e]:t,updatedAt:new Date().toISOString()})),N(!1),x(null)}catch(e){console.error("Save error:",e)}finally{d(!1)}},R=e=>{x(e),w("field"),C({name:e.name,label:e.label,type:e.type,required:e.required,placeholder:e.placeholder}),N(!0)},D=e=>{x(e),w("faq"),C({question:e.question,answer:e.answer,category:e.category||"general",isActive:e.isActive}),N(!0)},S=e=>{x(e),w("trust"),C({title:e.title,description:e.description,icon:e.icon,color:e.color,isActive:e.isActive}),N(!0)},M=async(e,t)=>{d(!0);try{await new Promise(e=>setTimeout(e,500)),"field"===e?s(e=>({...e,formFields:e.formFields.filter(e=>e.id!==t)})):"faq"===e?s(e=>({...e,faqs:e.faqs.filter(e=>e.id!==t)})):"trust"===e&&s(e=>({...e,trustIndicators:e.trustIndicators.filter(e=>e.id!==t)}))}catch(e){console.error("Delete error:",e)}finally{d(!1)}},_=async()=>{d(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"field"===v&&o){let t=e.formFields.map(e=>e.id===o.id?{...e,...y,updatedAt:new Date().toISOString()}:e);s(e=>({...e,formFields:t}))}else if("faq"===v&&o){let t=e.faqs.map(e=>e.id===o.id?{...e,...y,updatedAt:new Date().toISOString()}:e);s(e=>({...e,faqs:t}))}else if("trust"===v&&o){let t=e.trustIndicators.map(e=>e.id===o.id?{...e,...y,updatedAt:new Date().toISOString()}:e);s(e=>({...e,trustIndicators:t}))}N(!1),x(null),C({})}catch(e){console.error("Save item error:",e)}finally{d(!1)}};return(0,a.jsx)("div",{className:"text-white p-4 lg:p-8 min-h-screen",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2",children:"إدارة صفحة الاتصال"}),(0,a.jsx)("p",{className:"text-slate-400 text-lg",children:"إدارة شاملة لجميع محتويات صفحة الاتصال والدعم الفني"})]}),(0,a.jsxs)(eg.tU,{value:t,onValueChange:r,className:"w-full",children:[(0,a.jsxs)(eg.j7,{className:"grid w-full grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 bg-slate-800/50 border-slate-700/50 mb-6",children:[(0,a.jsx)(eg.Xi,{value:"overview",className:"text-xs lg:text-sm",children:"نظرة عامة"}),(0,a.jsx)(eg.Xi,{value:"header",className:"text-xs lg:text-sm",children:"العنوان"}),(0,a.jsx)(eg.Xi,{value:"contact",className:"text-xs lg:text-sm",children:"الاتصال"}),(0,a.jsx)(eg.Xi,{value:"form",className:"text-xs lg:text-sm",children:"النموذج"}),(0,a.jsx)(eg.Xi,{value:"about",className:"text-xs lg:text-sm",children:"من نحن"}),(0,a.jsx)(eg.Xi,{value:"stats",className:"text-xs lg:text-sm",children:"الإحصائيات"}),(0,a.jsx)(eg.Xi,{value:"faqs",className:"text-xs lg:text-sm",children:"الأسئلة"}),(0,a.jsx)(eg.Xi,{value:"trust",className:"text-xs lg:text-sm",children:"الثقة"})]}),(0,a.jsx)(eg.av,{value:"overview",children:(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"الرسائل"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:"247"})]}),(0,a.jsx)(e$.A,{className:"h-8 w-8 text-blue-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"الأسئلة الشائعة"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.faqs.length})]}),(0,a.jsx)(eO.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"حقول النموذج"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.formFields.length})]}),(0,a.jsx)(eq.A,{className:"h-8 w-8 text-yellow-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"مؤشرات الثقة"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.trustIndicators.length})]}),(0,a.jsx)(ez.A,{className:"h-8 w-8 text-purple-400"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(eF.A,{className:"h-5 w-5"}),"تحرير العنوان"]})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm mb-4",children:"تحرير عنوان الصفحة والوصف"}),(0,a.jsx)(n.$,{onClick:()=>r("header"),className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",children:"تحرير العنوان"})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"معلومات الاتصال"]})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm mb-4",children:"تحديث أرقام الهواتف والإيميلات"}),(0,a.jsx)(n.$,{onClick:()=>r("contact"),className:"w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",children:"تحرير الاتصال"})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(eO.A,{className:"h-5 w-5"}),"إدارة الأسئلة الشائعة"]})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm mb-4",children:"إضافة وتعديل الأسئلة الشائعة"}),(0,a.jsx)(n.$,{onClick:()=>r("faqs"),className:"w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700",children:"إدارة الأسئلة"})]})]})]})]}),{})}),(0,a.jsx)(eg.av,{value:"header",children:(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(eF.A,{className:"h-5 w-5"}),"تحرير عنوان الصفحة"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"العنوان الرئيسي"}),(0,a.jsx)(p.p,{value:e.header.title,onChange:e=>s(s=>({...s,header:{...s.header,title:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"الوصف"}),(0,a.jsx)(eP.T,{value:e.header.description,onChange:e=>s(s=>({...s,header:{...s.header,description:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white",rows:3})]}),(0,a.jsx)(n.$,{onClick:()=>A("header",e.header),disabled:c,className:"w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold",children:c?"جاري الحفظ...":"حفظ التغييرات"})]})]})}),(0,a.jsx)(eg.av,{value:"contact",children:(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"معلومات الاتصال"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(P.J,{className:"text-slate-300 flex items-center gap-2",children:[(0,a.jsx)(e$.A,{className:"h-4 w-4"}),"رقم الواتساب"]}),(0,a.jsx)(p.p,{value:e.contactInfo.whatsapp,onChange:e=>s(s=>({...s,contactInfo:{...s.contactInfo,whatsapp:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white",placeholder:"+249 123 456 789"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(P.J,{className:"text-slate-300 flex items-center gap-2",children:[(0,a.jsx)(eL.A,{className:"h-4 w-4"}),"البريد الإلكتروني"]}),(0,a.jsx)(p.p,{value:e.contactInfo.email,onChange:e=>s(s=>({...s,contactInfo:{...s.contactInfo,email:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(P.J,{className:"text-slate-300 flex items-center gap-2",children:[(0,a.jsx)(eU.A,{className:"h-4 w-4"}),"ساعات العمل"]}),(0,a.jsx)(p.p,{value:e.contactInfo.workingHours,onChange:e=>s(s=>({...s,contactInfo:{...s.contactInfo,workingHours:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white",placeholder:"8:00 ص - 12:00 م (يومياً)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(P.J,{className:"text-slate-300 flex items-center gap-2",children:[(0,a.jsx)(eW.A,{className:"h-4 w-4"}),"الموقع"]}),(0,a.jsx)(p.p,{value:e.contactInfo.location,onChange:e=>s(s=>({...s,contactInfo:{...s.contactInfo,location:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white",placeholder:"الخرطوم، السودان"})]})]}),(0,a.jsx)(n.$,{onClick:()=>A("contactInfo",e.contactInfo),disabled:c,className:"w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold",children:c?"جاري الحفظ...":"حفظ معلومات الاتصال"})]})]})}),(0,a.jsx)(eg.av,{value:"form",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"إدارة حقول النموذج"}),(0,a.jsxs)(f.lG,{open:m,onOpenChange:N,children:[(0,a.jsx)(f.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{onClick:()=>{x(null),w("field"),C({})},className:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة حقل جديد"]})}),(0,a.jsxs)(f.Cf,{className:"bg-slate-800 border-slate-700 text-white max-w-md",children:[(0,a.jsxs)(f.c7,{children:[(0,a.jsxs)(f.L3,{children:[o?"تحرير":"إضافة","field"===v?" الحقل":"faq"===v?" السؤال":" مؤشر الثقة"]}),(0,a.jsxs)(f.rr,{className:"text-slate-400",children:[o?"تحرير":"إضافة","field"===v?" حقل النموذج":"faq"===v?" السؤال الشائع":" مؤشر الثقة"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:["field"===v&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"اسم الحقل"}),(0,a.jsx)(p.p,{value:y.name||"",onChange:e=>C(s=>({...s,name:e.target.value})),placeholder:"مثال: name, phone, email",className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"تسمية الحقل"}),(0,a.jsx)(p.p,{value:y.label||"",onChange:e=>C(s=>({...s,label:e.target.value})),placeholder:"مثال: الاسم، الهاتف، الإيميل",className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"نوع الحقل"}),(0,a.jsxs)(j.l6,{value:y.type||"",onValueChange:e=>C(s=>({...s,type:e})),children:[(0,a.jsx)(j.bq,{className:"bg-slate-700/50 border-slate-600 text-white",children:(0,a.jsx)(j.yv,{placeholder:"اختر نوع الحقل"})}),(0,a.jsxs)(j.gC,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(j.eb,{value:"text",children:"نص"}),(0,a.jsx)(j.eb,{value:"email",children:"إيميل"}),(0,a.jsx)(j.eb,{value:"tel",children:"هاتف"}),(0,a.jsx)(j.eb,{value:"textarea",children:"نص طويل"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"النص التوضيحي"}),(0,a.jsx)(p.p,{value:y.placeholder||"",onChange:e=>C(s=>({...s,placeholder:e.target.value})),placeholder:"النص الذي يظهر داخل الحقل",className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"حقل مطلوب"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"يجب على المستخدم ملء هذا الحقل"})]}),(0,a.jsx)(eE.d,{checked:y.required||!1,onCheckedChange:e=>C(s=>({...s,required:e}))})]})]}),"faq"===v&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"السؤال"}),(0,a.jsx)(p.p,{value:y.question||"",onChange:e=>C(s=>({...s,question:e.target.value})),placeholder:"اكتب السؤال هنا",className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"الإجابة"}),(0,a.jsx)(eP.T,{value:y.answer||"",onChange:e=>C(s=>({...s,answer:e.target.value})),placeholder:"اكتب الإجابة هنا",className:"bg-slate-700/50 border-slate-600 text-white",rows:4})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"الفئة"}),(0,a.jsxs)(j.l6,{value:y.category||"general",onValueChange:e=>C(s=>({...s,category:e})),children:[(0,a.jsx)(j.bq,{className:"bg-slate-700/50 border-slate-600 text-white",children:(0,a.jsx)(j.yv,{placeholder:"اختر الفئة"})}),(0,a.jsxs)(j.gC,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(j.eb,{value:"general",children:"عام"}),(0,a.jsx)(j.eb,{value:"payment",children:"الدفع"}),(0,a.jsx)(j.eb,{value:"delivery",children:"التسليم"}),(0,a.jsx)(j.eb,{value:"support",children:"الدعم"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"نشط"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"إظهار هذا السؤال للمستخدمين"})]}),(0,a.jsx)(eE.d,{checked:y.isActive||!1,onCheckedChange:e=>C(s=>({...s,isActive:e}))})]})]}),"trust"===v&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"العنوان"}),(0,a.jsx)(p.p,{value:y.title||"",onChange:e=>C(s=>({...s,title:e.target.value})),placeholder:"عنوان مؤشر الثقة",className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"الوصف"}),(0,a.jsx)(eP.T,{value:y.description||"",onChange:e=>C(s=>({...s,description:e.target.value})),placeholder:"وصف مؤشر الثقة",className:"bg-slate-700/50 border-slate-600 text-white",rows:3})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"اللون"}),(0,a.jsxs)(j.l6,{value:y.color||"",onValueChange:e=>C(s=>({...s,color:e})),children:[(0,a.jsx)(j.bq,{className:"bg-slate-700/50 border-slate-600 text-white",children:(0,a.jsx)(j.yv,{placeholder:"اختر اللون"})}),(0,a.jsxs)(j.gC,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(j.eb,{value:"text-green-400",children:"أخضر"}),(0,a.jsx)(j.eb,{value:"text-blue-400",children:"أزرق"}),(0,a.jsx)(j.eb,{value:"text-yellow-400",children:"أصفر"}),(0,a.jsx)(j.eb,{value:"text-purple-400",children:"بنفسجي"}),(0,a.jsx)(j.eb,{value:"text-red-400",children:"أحمر"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"نشط"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"إظهار هذا المؤشر للمستخدمين"})]}),(0,a.jsx)(eE.d,{checked:y.isActive||!1,onCheckedChange:e=>C(s=>({...s,isActive:e}))})]})]}),(0,a.jsx)(n.$,{onClick:_,disabled:c,className:"w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",children:c?"جاري الحفظ...":o?"حفظ التغييرات":"إضافة"})]})]})]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:e.formFields.map((t,l)=>(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:t.label}),t.required&&(0,a.jsx)(g.E,{variant:"destructive",className:"text-xs",children:"مطلوب"}),(0,a.jsx)(g.E,{variant:"outline",className:"text-xs border-slate-600 text-slate-300",children:t.type})]}),(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:t.placeholder})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{let t=[...e.formFields];l>0&&([t[l],t[l-1]]=[t[l-1],t[l]],s(e=>({...e,formFields:t})))},disabled:0===l,children:(0,a.jsx)(eJ,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{let t=[...e.formFields];l<t.length-1&&([t[l],t[l+1]]=[t[l+1],t[l]],s(e=>({...e,formFields:t})))},disabled:l===e.formFields.length-1,children:(0,a.jsx)(eB,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>R(t),children:(0,a.jsx)(eF.A,{className:"h-4 w-4"})}),(0,a.jsxs)(W,{children:[(0,a.jsx)(J,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"text-red-400 hover:text-red-300",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(ed,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsxs)(eo,{children:[(0,a.jsx)(em,{className:"text-white",children:"حذف الحقل"}),(0,a.jsxs)(eh,{className:"text-slate-400",children:['هل أنت متأكد من حذف حقل "',t.label,'"؟ لا يمكن التراجع عن هذا الإجراء.']})]}),(0,a.jsxs)(ex,{children:[(0,a.jsx)(ep,{className:"bg-slate-700 border-slate-600 text-white hover:bg-slate-600",children:"إلغاء"}),(0,a.jsx)(eu,{onClick:()=>M("field",t.id),className:"bg-red-600 hover:bg-red-700",children:"حذف"})]})]})]})]})]})})},t.id))})]})}),(0,a.jsx)(eg.av,{value:"about",children:(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(eG.A,{className:"h-5 w-5"}),'تحرير قسم "من نحن"']})}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"العنوان الرئيسي"}),(0,a.jsx)(p.p,{value:e.aboutUs.title,onChange:e=>s(s=>({...s,aboutUs:{...s.aboutUs,title:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"العنوان الفرعي"}),(0,a.jsx)(p.p,{value:e.aboutUs.subtitle,onChange:e=>s(s=>({...s,aboutUs:{...s.aboutUs,subtitle:e.target.value}})),className:"bg-slate-700/50 border-slate-600 text-white"})]})]}),(0,a.jsx)(eZ.w,{className:"bg-slate-700"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"الرؤية"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"عنوان الرؤية"}),(0,a.jsx)(p.p,{value:e.aboutUs.vision.title,onChange:e=>s(s=>({...s,aboutUs:{...s.aboutUs,vision:{...s.aboutUs.vision,title:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"وصف الرؤية"}),(0,a.jsx)(eP.T,{value:e.aboutUs.vision.description,onChange:e=>s(s=>({...s,aboutUs:{...s.aboutUs,vision:{...s.aboutUs.vision,description:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white",rows:3})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"المهمة"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"عنوان المهمة"}),(0,a.jsx)(p.p,{value:e.aboutUs.mission.title,onChange:e=>s(s=>({...s,aboutUs:{...s.aboutUs,mission:{...s.aboutUs.mission,title:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"وصف المهمة"}),(0,a.jsx)(eP.T,{value:e.aboutUs.mission.description,onChange:e=>s(s=>({...s,aboutUs:{...s.aboutUs,mission:{...s.aboutUs.mission,description:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white",rows:3})]})]})]}),(0,a.jsx)(n.$,{onClick:()=>A("aboutUs",e.aboutUs),disabled:c,className:"w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-bold",children:c?"جاري الحفظ...":"حفظ معلومات الشركة"})]})]})}),(0,a.jsx)(eg.av,{value:"stats",children:(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(u,{className:"h-5 w-5"}),"إحصائيات الشركة"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"العملاء الراضين"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"القيمة"}),(0,a.jsx)(p.p,{value:e.stats.customers.value,onChange:e=>s(s=>({...s,stats:{...s.stats,customers:{...s.stats.customers,value:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"التسمية"}),(0,a.jsx)(p.p,{value:e.stats.customers.label,onChange:e=>s(s=>({...s,stats:{...s.stats,customers:{...s.stats.customers,label:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"نسبة النجاح"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"القيمة"}),(0,a.jsx)(p.p,{value:e.stats.successRate.value,onChange:e=>s(s=>({...s,stats:{...s.stats,successRate:{...s.stats.successRate,value:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"التسمية"}),(0,a.jsx)(p.p,{value:e.stats.successRate.label,onChange:e=>s(s=>({...s,stats:{...s.stats,successRate:{...s.stats.successRate,label:e.target.value}}})),className:"bg-slate-700/50 border-slate-600 text-white"})]})]})]})]}),(0,a.jsx)(n.$,{onClick:()=>A("stats",e.stats),disabled:c,className:"w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-bold",children:c?"جاري الحفظ...":"حفظ الإحصائيات"})]})]})}),(0,a.jsx)(eg.av,{value:"faqs",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"إدارة الأسئلة الشائعة"}),(0,a.jsxs)(n.$,{onClick:()=>{x(null),w("faq"),C({}),N(!0)},className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة سؤال جديد"]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:e.faqs.map((t,l)=>(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-white mb-1",children:t.question}),(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:t.answer})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.E,{variant:"outline",className:"border-slate-600 text-slate-300",children:t.category}),(0,a.jsxs)("div",{className:"flex items-center justify-between min-w-[140px]",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-300 text-sm",children:"نشط"}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"إظهار للمستخدمين"})]}),(0,a.jsx)(eE.d,{checked:t.isActive,onCheckedChange:a=>{let l=e.faqs.map(e=>e.id===t.id?{...e,isActive:a}:e);s(e=>({...e,faqs:l}))}})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eJ,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eB,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>D(t),children:(0,a.jsx)(eF.A,{className:"h-4 w-4"})}),(0,a.jsxs)(W,{children:[(0,a.jsx)(J,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"text-red-400 hover:text-red-300",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(ed,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsxs)(eo,{children:[(0,a.jsx)(em,{className:"text-white",children:"حذف السؤال"}),(0,a.jsxs)(eh,{className:"text-slate-400",children:['هل أنت متأكد من حذف السؤال "',t.question,'"؟ لا يمكن التراجع عن هذا الإجراء.']})]}),(0,a.jsxs)(ex,{children:[(0,a.jsx)(ep,{className:"bg-slate-700 border-slate-600 text-white hover:bg-slate-600",children:"إلغاء"}),(0,a.jsx)(eu,{onClick:()=>M("faq",t.id),className:"bg-red-600 hover:bg-red-700",children:"حذف"})]})]})]})]})]})]})})},t.id))})]})}),(0,a.jsx)(eg.av,{value:"trust",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"مؤشرات الثقة"}),(0,a.jsxs)(n.$,{onClick:()=>{x(null),w("trust"),C({}),N(!0)},className:"bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة مؤشر جديد"]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:e.trustIndicators.map((t,l)=>(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-slate-700/30",children:(0,a.jsx)(ez.A,{className:`h-5 w-5 ${t.color}`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:t.title}),(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:t.description})]})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between min-w-[140px]",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-300 text-sm",children:"نشط"}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"إظهار للمستخدمين"})]}),(0,a.jsx)(eE.d,{checked:t.isActive,onCheckedChange:a=>{let l=e.trustIndicators.map(e=>e.id===t.id?{...e,isActive:a}:e);s(e=>({...e,trustIndicators:l}))}})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>S(t),children:(0,a.jsx)(eF.A,{className:"h-4 w-4"})}),(0,a.jsxs)(W,{children:[(0,a.jsx)(J,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"text-red-400 hover:text-red-300",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(ed,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsxs)(eo,{children:[(0,a.jsx)(em,{className:"text-white",children:"حذف مؤشر الثقة"}),(0,a.jsxs)(eh,{className:"text-slate-400",children:['هل أنت متأكد من حذف مؤشر "',t.title,'"؟ لا يمكن التراجع عن هذا الإجراء.']})]}),(0,a.jsxs)(ex,{children:[(0,a.jsx)(ep,{className:"bg-slate-700 border-slate-600 text-white hover:bg-slate-600",children:"إلغاء"}),(0,a.jsx)(eu,{onClick:()=>M("trust",t.id),className:"bg-red-600 hover:bg-red-700",children:"حذف"})]})]})]})]})]})})},t.id))})]})})]})]})})}var eX=t(70373),eK=t(65551),eY=t(14163),eQ=t(9510),e0=t(43),e4=t(31355),e2=t(1359),e1=t(32547),e5=t(96963),e3=t(55509),e6=t(25028),e7=t(46059),e8=t(72942),e9=t(13495),se=t(63376),ss=t(42247),st=["Enter"," "],sa=["ArrowUp","PageDown","End"],sl=["ArrowDown","PageUp","Home",...sa],sr={ltr:[...st,"ArrowRight"],rtl:[...st,"ArrowLeft"]},si={ltr:["ArrowLeft"],rtl:["ArrowRight"]},sn="Menu",[sc,sd,so]=(0,eQ.N)(sn),[sx,sm]=(0,E.A)(sn,[so,e3.Bk,e8.RG]),sh=(0,e3.Bk)(),su=(0,e8.RG)(),[sp,sg]=sx(sn),[sj,sf]=sx(sn),sb=e=>{let{__scopeMenu:s,open:t=!1,children:r,dir:i,onOpenChange:n,modal:c=!0}=e,d=sh(s),[o,x]=l.useState(null),m=l.useRef(!1),h=(0,e9.c)(n),u=(0,e0.jH)(i);return l.useEffect(()=>{let e=()=>{m.current=!0,document.addEventListener("pointerdown",s,{capture:!0,once:!0}),document.addEventListener("pointermove",s,{capture:!0,once:!0})},s=()=>m.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",s,{capture:!0}),document.removeEventListener("pointermove",s,{capture:!0})}},[]),(0,a.jsx)(e3.bL,{...d,children:(0,a.jsx)(sp,{scope:s,open:t,onOpenChange:h,content:o,onContentChange:x,children:(0,a.jsx)(sj,{scope:s,onClose:l.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:m,dir:u,modal:c,children:r})})})};sb.displayName=sn;var sN=l.forwardRef((e,s)=>{let{__scopeMenu:t,...l}=e,r=sh(t);return(0,a.jsx)(e3.Mz,{...r,...l,ref:s})});sN.displayName="MenuAnchor";var sv="MenuPortal",[sw,sy]=sx(sv,{forceMount:void 0}),sk=e=>{let{__scopeMenu:s,forceMount:t,children:l,container:r}=e,i=sg(sv,s);return(0,a.jsx)(sw,{scope:s,forceMount:t,children:(0,a.jsx)(e7.C,{present:t||i.open,children:(0,a.jsx)(e6.Z,{asChild:!0,container:r,children:l})})})};sk.displayName=sv;var sC="MenuContent",[sA,sR]=sx(sC),sD=l.forwardRef((e,s)=>{let t=sy(sC,e.__scopeMenu),{forceMount:l=t.forceMount,...r}=e,i=sg(sC,e.__scopeMenu),n=sf(sC,e.__scopeMenu);return(0,a.jsx)(sc.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(e7.C,{present:l||i.open,children:(0,a.jsx)(sc.Slot,{scope:e.__scopeMenu,children:n.modal?(0,a.jsx)(sS,{...r,ref:s}):(0,a.jsx)(sM,{...r,ref:s})})})})}),sS=l.forwardRef((e,s)=>{let t=sg(sC,e.__scopeMenu),r=l.useRef(null),i=(0,Z.s)(s,r);return l.useEffect(()=>{let e=r.current;if(e)return(0,se.Eq)(e)},[]),(0,a.jsx)(sI,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,O.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),sM=l.forwardRef((e,s)=>{let t=sg(sC,e.__scopeMenu);return(0,a.jsx)(sI,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),s_=(0,q.TL)("MenuContent.ScrollLock"),sI=l.forwardRef((e,s)=>{let{__scopeMenu:t,loop:r=!1,trapFocus:i,onOpenAutoFocus:n,onCloseAutoFocus:c,disableOutsidePointerEvents:d,onEntryFocus:o,onEscapeKeyDown:x,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:u,onDismiss:p,disableOutsideScroll:g,...j}=e,f=sg(sC,t),b=sf(sC,t),N=sh(t),v=su(t),w=sd(t),[y,k]=l.useState(null),C=l.useRef(null),A=(0,Z.s)(s,C,f.onContentChange),R=l.useRef(0),D=l.useRef(""),S=l.useRef(0),M=l.useRef(null),_=l.useRef("right"),I=l.useRef(0),T=g?ss.A:l.Fragment,P=e=>{let s=D.current+e,t=w().filter(e=>!e.disabled),a=document.activeElement,l=t.find(e=>e.ref.current===a)?.textValue,r=function(e,s,t){var a;let l=s.length>1&&Array.from(s).every(e=>e===s[0])?s[0]:s,r=(a=Math.max(t?e.indexOf(t):-1,0),e.map((s,t)=>e[(a+t)%e.length]));1===l.length&&(r=r.filter(e=>e!==t));let i=r.find(e=>e.toLowerCase().startsWith(l.toLowerCase()));return i!==t?i:void 0}(t.map(e=>e.textValue),s,l),i=t.find(e=>e.textValue===r)?.ref.current;(function e(s){D.current=s,window.clearTimeout(R.current),""!==s&&(R.current=window.setTimeout(()=>e(""),1e3))})(s),i&&setTimeout(()=>i.focus())};l.useEffect(()=>()=>window.clearTimeout(R.current),[]),(0,e2.Oh)();let E=l.useCallback(e=>_.current===M.current?.side&&function(e,s){return!!s&&function(e,s){let{x:t,y:a}=e,l=!1;for(let e=0,r=s.length-1;e<s.length;r=e++){let i=s[e],n=s[r],c=i.x,d=i.y,o=n.x,x=n.y;d>a!=x>a&&t<(o-c)*(a-d)/(x-d)+c&&(l=!l)}return l}({x:e.clientX,y:e.clientY},s)}(e,M.current?.area),[]);return(0,a.jsx)(sA,{scope:t,searchRef:D,onItemEnter:l.useCallback(e=>{E(e)&&e.preventDefault()},[E]),onItemLeave:l.useCallback(e=>{E(e)||(C.current?.focus(),k(null))},[E]),onTriggerLeave:l.useCallback(e=>{E(e)&&e.preventDefault()},[E]),pointerGraceTimerRef:S,onPointerGraceIntentChange:l.useCallback(e=>{M.current=e},[]),children:(0,a.jsx)(T,{...g?{as:s_,allowPinchZoom:!0}:void 0,children:(0,a.jsx)(e1.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,O.m)(n,e=>{e.preventDefault(),C.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,a.jsx)(e4.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:x,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:u,onDismiss:p,children:(0,a.jsx)(e8.bL,{asChild:!0,...v,dir:b.dir,orientation:"vertical",loop:r,currentTabStopId:y,onCurrentTabStopIdChange:k,onEntryFocus:(0,O.m)(o,e=>{b.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,a.jsx)(e3.UC,{role:"menu","aria-orientation":"vertical","data-state":s3(f.open),"data-radix-menu-content":"",dir:b.dir,...N,...j,ref:A,style:{outline:"none",...j.style},onKeyDown:(0,O.m)(j.onKeyDown,e=>{let s=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,a=1===e.key.length;s&&("Tab"===e.key&&e.preventDefault(),!t&&a&&P(e.key));let l=C.current;if(e.target!==l||!sl.includes(e.key))return;e.preventDefault();let r=w().filter(e=>!e.disabled).map(e=>e.ref.current);sa.includes(e.key)&&r.reverse(),function(e){let s=document.activeElement;for(let t of e)if(t===s||(t.focus(),document.activeElement!==s))return}(r)}),onBlur:(0,O.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(R.current),D.current="")}),onPointerMove:(0,O.m)(e.onPointerMove,s8(e=>{let s=e.target,t=I.current!==e.clientX;e.currentTarget.contains(s)&&t&&(_.current=e.clientX>I.current?"right":"left",I.current=e.clientX)}))})})})})})})});sD.displayName=sC;var sT=l.forwardRef((e,s)=>{let{__scopeMenu:t,...l}=e;return(0,a.jsx)(eY.sG.div,{role:"group",...l,ref:s})});sT.displayName="MenuGroup";var sP=l.forwardRef((e,s)=>{let{__scopeMenu:t,...l}=e;return(0,a.jsx)(eY.sG.div,{...l,ref:s})});sP.displayName="MenuLabel";var sE="MenuItem",sZ="menu.itemSelect",s$=l.forwardRef((e,s)=>{let{disabled:t=!1,onSelect:r,...i}=e,n=l.useRef(null),c=sf(sE,e.__scopeMenu),d=sR(sE,e.__scopeMenu),o=(0,Z.s)(s,n),x=l.useRef(!1);return(0,a.jsx)(sO,{...i,ref:o,disabled:t,onClick:(0,O.m)(e.onClick,()=>{let e=n.current;if(!t&&e){let s=new CustomEvent(sZ,{bubbles:!0,cancelable:!0});e.addEventListener(sZ,e=>r?.(e),{once:!0}),(0,eY.hO)(e,s),s.defaultPrevented?x.current=!1:c.onClose()}}),onPointerDown:s=>{e.onPointerDown?.(s),x.current=!0},onPointerUp:(0,O.m)(e.onPointerUp,e=>{x.current||e.currentTarget?.click()}),onKeyDown:(0,O.m)(e.onKeyDown,e=>{let s=""!==d.searchRef.current;!t&&(!s||" "!==e.key)&&st.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});s$.displayName=sE;var sO=l.forwardRef((e,s)=>{let{__scopeMenu:t,disabled:r=!1,textValue:i,...n}=e,c=sR(sE,t),d=su(t),o=l.useRef(null),x=(0,Z.s)(s,o),[m,h]=l.useState(!1),[u,p]=l.useState("");return l.useEffect(()=>{let e=o.current;e&&p((e.textContent??"").trim())},[n.children]),(0,a.jsx)(sc.ItemSlot,{scope:t,disabled:r,textValue:i??u,children:(0,a.jsx)(e8.q7,{asChild:!0,...d,focusable:!r,children:(0,a.jsx)(eY.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...n,ref:x,onPointerMove:(0,O.m)(e.onPointerMove,s8(e=>{r?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,O.m)(e.onPointerLeave,s8(e=>c.onItemLeave(e))),onFocus:(0,O.m)(e.onFocus,()=>h(!0)),onBlur:(0,O.m)(e.onBlur,()=>h(!1))})})})}),sq=l.forwardRef((e,s)=>{let{checked:t=!1,onCheckedChange:l,...r}=e;return(0,a.jsx)(sG,{scope:e.__scopeMenu,checked:t,children:(0,a.jsx)(s$,{role:"menuitemcheckbox","aria-checked":s6(t)?"mixed":t,...r,ref:s,"data-state":s7(t),onSelect:(0,O.m)(r.onSelect,()=>l?.(!!s6(t)||!t),{checkForDefaultPrevented:!1})})})});sq.displayName="MenuCheckboxItem";var sz="MenuRadioGroup",[sF,sL]=sx(sz,{value:void 0,onValueChange:()=>{}}),sU=l.forwardRef((e,s)=>{let{value:t,onValueChange:l,...r}=e,i=(0,e9.c)(l);return(0,a.jsx)(sF,{scope:e.__scopeMenu,value:t,onValueChange:i,children:(0,a.jsx)(sT,{...r,ref:s})})});sU.displayName=sz;var sW="MenuRadioItem",sJ=l.forwardRef((e,s)=>{let{value:t,...l}=e,r=sL(sW,e.__scopeMenu),i=t===r.value;return(0,a.jsx)(sG,{scope:e.__scopeMenu,checked:i,children:(0,a.jsx)(s$,{role:"menuitemradio","aria-checked":i,...l,ref:s,"data-state":s7(i),onSelect:(0,O.m)(l.onSelect,()=>r.onValueChange?.(t),{checkForDefaultPrevented:!1})})})});sJ.displayName=sW;var sB="MenuItemIndicator",[sG,sV]=sx(sB,{checked:!1}),sH=l.forwardRef((e,s)=>{let{__scopeMenu:t,forceMount:l,...r}=e,i=sV(sB,t);return(0,a.jsx)(e7.C,{present:l||s6(i.checked)||!0===i.checked,children:(0,a.jsx)(eY.sG.span,{...r,ref:s,"data-state":s7(i.checked)})})});sH.displayName=sB;var sX=l.forwardRef((e,s)=>{let{__scopeMenu:t,...l}=e;return(0,a.jsx)(eY.sG.div,{role:"separator","aria-orientation":"horizontal",...l,ref:s})});sX.displayName="MenuSeparator";var sK=l.forwardRef((e,s)=>{let{__scopeMenu:t,...l}=e,r=sh(t);return(0,a.jsx)(e3.i3,{...r,...l,ref:s})});sK.displayName="MenuArrow";var sY="MenuSub",[sQ,s0]=sx(sY),s4="MenuSubTrigger",s2=l.forwardRef((e,s)=>{let t=sg(s4,e.__scopeMenu),r=sf(s4,e.__scopeMenu),i=s0(s4,e.__scopeMenu),n=sR(s4,e.__scopeMenu),c=l.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:o}=n,x={__scopeMenu:e.__scopeMenu},m=l.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return l.useEffect(()=>m,[m]),l.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),o(null)}},[d,o]),(0,a.jsx)(sN,{asChild:!0,...x,children:(0,a.jsx)(sO,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":i.contentId,"data-state":s3(t.open),...e,ref:(0,Z.t)(s,i.onTriggerChange),onClick:s=>{e.onClick?.(s),e.disabled||s.defaultPrevented||(s.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,O.m)(e.onPointerMove,s8(s=>{n.onItemEnter(s),s.defaultPrevented||e.disabled||t.open||c.current||(n.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{t.onOpenChange(!0),m()},100))})),onPointerLeave:(0,O.m)(e.onPointerLeave,s8(e=>{m();let s=t.content?.getBoundingClientRect();if(s){let a=t.content?.dataset.side,l="right"===a,r=s[l?"left":"right"],i=s[l?"right":"left"];n.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:r,y:s.top},{x:i,y:s.top},{x:i,y:s.bottom},{x:r,y:s.bottom}],side:a}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>n.onPointerGraceIntentChange(null),300)}else{if(n.onTriggerLeave(e),e.defaultPrevented)return;n.onPointerGraceIntentChange(null)}})),onKeyDown:(0,O.m)(e.onKeyDown,s=>{let a=""!==n.searchRef.current;!e.disabled&&(!a||" "!==s.key)&&sr[r.dir].includes(s.key)&&(t.onOpenChange(!0),t.content?.focus(),s.preventDefault())})})})});s2.displayName=s4;var s1="MenuSubContent",s5=l.forwardRef((e,s)=>{let t=sy(sC,e.__scopeMenu),{forceMount:r=t.forceMount,...i}=e,n=sg(sC,e.__scopeMenu),c=sf(sC,e.__scopeMenu),d=s0(s1,e.__scopeMenu),o=l.useRef(null),x=(0,Z.s)(s,o);return(0,a.jsx)(sc.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(e7.C,{present:r||n.open,children:(0,a.jsx)(sc.Slot,{scope:e.__scopeMenu,children:(0,a.jsx)(sI,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:x,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&o.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,O.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&n.onOpenChange(!1)}),onEscapeKeyDown:(0,O.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,O.m)(e.onKeyDown,e=>{let s=e.currentTarget.contains(e.target),t=si[c.dir].includes(e.key);s&&t&&(n.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function s3(e){return e?"open":"closed"}function s6(e){return"indeterminate"===e}function s7(e){return s6(e)?"indeterminate":e?"checked":"unchecked"}function s8(e){return s=>"mouse"===s.pointerType?e(s):void 0}s5.displayName=s1;var s9="DropdownMenu",[te,ts]=(0,E.A)(s9,[sm]),tt=sm(),[ta,tl]=te(s9),tr=e=>{let{__scopeDropdownMenu:s,children:t,dir:r,open:i,defaultOpen:n,onOpenChange:c,modal:d=!0}=e,o=tt(s),x=l.useRef(null),[m,h]=(0,eK.i)({prop:i,defaultProp:n??!1,onChange:c,caller:s9});return(0,a.jsx)(ta,{scope:s,triggerId:(0,e5.B)(),triggerRef:x,contentId:(0,e5.B)(),open:m,onOpenChange:h,onOpenToggle:l.useCallback(()=>h(e=>!e),[h]),modal:d,children:(0,a.jsx)(sb,{...o,open:m,onOpenChange:h,dir:r,modal:d,children:t})})};tr.displayName=s9;var ti="DropdownMenuTrigger",tn=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,disabled:l=!1,...r}=e,i=tl(ti,t),n=tt(t);return(0,a.jsx)(sN,{asChild:!0,...n,children:(0,a.jsx)(eY.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":l?"":void 0,disabled:l,...r,ref:(0,Z.t)(s,i.triggerRef),onPointerDown:(0,O.m)(e.onPointerDown,e=>{l||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,O.m)(e.onKeyDown,e=>{!l&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});tn.displayName=ti;var tc=e=>{let{__scopeDropdownMenu:s,...t}=e,l=tt(s);return(0,a.jsx)(sk,{...l,...t})};tc.displayName="DropdownMenuPortal";var td="DropdownMenuContent",to=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...r}=e,i=tl(td,t),n=tt(t),c=l.useRef(!1);return(0,a.jsx)(sD,{id:i.contentId,"aria-labelledby":i.triggerId,...n,...r,ref:s,onCloseAutoFocus:(0,O.m)(e.onCloseAutoFocus,e=>{c.current||i.triggerRef.current?.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:(0,O.m)(e.onInteractOutside,e=>{let s=e.detail.originalEvent,t=0===s.button&&!0===s.ctrlKey,a=2===s.button||t;(!i.modal||a)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});to.displayName=td,l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sT,{...r,...l,ref:s})}).displayName="DropdownMenuGroup";var tx=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sP,{...r,...l,ref:s})});tx.displayName="DropdownMenuLabel";var tm=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(s$,{...r,...l,ref:s})});tm.displayName="DropdownMenuItem";var th=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sq,{...r,...l,ref:s})});th.displayName="DropdownMenuCheckboxItem",l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sU,{...r,...l,ref:s})}).displayName="DropdownMenuRadioGroup";var tu=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sJ,{...r,...l,ref:s})});tu.displayName="DropdownMenuRadioItem";var tp=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sH,{...r,...l,ref:s})});tp.displayName="DropdownMenuItemIndicator";var tg=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sX,{...r,...l,ref:s})});tg.displayName="DropdownMenuSeparator",l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(sK,{...r,...l,ref:s})}).displayName="DropdownMenuArrow";var tj=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(s2,{...r,...l,ref:s})});tj.displayName="DropdownMenuSubTrigger";var tf=l.forwardRef((e,s)=>{let{__scopeDropdownMenu:t,...l}=e,r=tt(t);return(0,a.jsx)(s5,{...r,...l,ref:s,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tf.displayName="DropdownMenuSubContent";var tb=t(14952);let tN=(0,d.A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);l.forwardRef(({className:e,inset:s,children:t,...l},r)=>(0,a.jsxs)(tj,{ref:r,className:(0,en.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",e),...l,children:[t,(0,a.jsx)(tb.A,{className:"ml-auto"})]})).displayName=tj.displayName,l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(tf,{ref:t,className:(0,en.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})).displayName=tf.displayName;let tv=l.forwardRef(({className:e,sideOffset:s=4,...t},l)=>(0,a.jsx)(tc,{children:(0,a.jsx)(to,{ref:l,sideOffset:s,className:(0,en.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));tv.displayName=to.displayName;let tw=l.forwardRef(({className:e,inset:s,...t},l)=>(0,a.jsx)(tm,{ref:l,className:(0,en.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",e),...t}));tw.displayName=tm.displayName,l.forwardRef(({className:e,children:s,checked:t,...l},r)=>(0,a.jsxs)(th,{ref:r,className:(0,en.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(tp,{children:(0,a.jsx)(eb.A,{className:"h-4 w-4"})})}),s]})).displayName=th.displayName,l.forwardRef(({className:e,children:s,...t},l)=>(0,a.jsxs)(tu,{ref:l,className:(0,en.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(tp,{children:(0,a.jsx)(tN,{className:"h-2 w-2 fill-current"})})}),s]})).displayName=tu.displayName,l.forwardRef(({className:e,inset:s,...t},l)=>(0,a.jsx)(tx,{ref:l,className:(0,en.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",e),...t})).displayName=tx.displayName;let ty=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(tg,{ref:t,className:(0,en.cn)("-mx-1 my-1 h-px bg-muted",e),...s}));ty.displayName=tg.displayName;var tk=t(30474);let tC=(0,d.A)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var tA=t(84027),tR=t(97051),tD=t(32192),tS=t(58869),tM=t(40083);let t_=(0,d.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);function tI({activeTab:e,onTabChange:s}){let[t,i]=(0,l.useState)(!1),d=(0,r.useRouter)(),o=()=>{d.push("/")},m=[{id:"overview",label:"نظرة عامة",icon:tC,shortLabel:"عام"},{id:"products",label:"المنتجات",icon:c.A,shortLabel:"منتجات"},{id:"orders",label:"الطلبات",icon:x.A,shortLabel:"طلبات"},{id:"contact",label:"صفحة الاتصال",icon:h.A,shortLabel:"الاتصال"},{id:"settings",label:"الإعدادات",icon:tA.A,shortLabel:"إعدادات"}];return(0,a.jsx)("header",{className:"bg-slate-800/95 border-b border-slate-700/50 backdrop-blur-xl sticky top-0 z-50 shadow-2xl",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 lg:px-6 py-4 lg:py-5",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 lg:gap-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-3 cursor-pointer hover:opacity-80 transition-all duration-300 group",onClick:o,children:(0,a.jsx)(tk.default,{src:"/logo-without-background.png",alt:"رايه شوب",width:140,height:56,className:"h-10 lg:h-12 w-auto object-contain group-hover:scale-105 transition-transform duration-300",priority:!0})}),(0,a.jsx)("div",{className:"h-8 w-px bg-slate-600/50 hidden lg:block"}),(0,a.jsxs)("div",{className:"hidden lg:block",children:[(0,a.jsx)("h1",{className:"text-xl lg:text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent",children:"لوحة التحكم الإدارية"}),(0,a.jsx)("p",{className:"text-slate-400 text-sm mt-1",children:"إدارة شاملة للمنتجات والطلبات"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 lg:gap-4",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"hidden lg:flex text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-300",children:(0,a.jsx)(w.A,{className:"h-5 w-5"})}),(0,a.jsxs)(n.$,{variant:"ghost",size:"icon",className:"relative text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-300",children:[(0,a.jsx)(tR.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs"})]}),(0,a.jsxs)(n.$,{onClick:o,variant:"outline",size:"sm",className:"hidden lg:flex bg-slate-700/50 border-slate-600/50 text-slate-300 hover:text-white hover:bg-slate-600/50 hover:border-slate-500/50 transition-all duration-300",children:[(0,a.jsx)(tD.A,{className:"h-4 w-4 mr-2"}),"الرئيسية"]}),(0,a.jsx)(g.E,{className:"hidden lg:flex bg-green-500/20 text-green-400 border-green-500/30 px-3 py-1",children:"متصل"}),(0,a.jsxs)(tr,{children:[(0,a.jsx)(tn,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",className:"relative h-10 w-10 rounded-full",children:(0,a.jsxs)(eX.eu,{className:"h-10 w-10 border-2 border-yellow-400/20 hover:border-yellow-400/40 transition-all duration-300",children:[(0,a.jsx)(eX.BK,{src:"",alt:"Admin"}),(0,a.jsx)(eX.q5,{className:"bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-sm font-bold",children:"أ"})]})})}),(0,a.jsxs)(tv,{className:"w-56 bg-slate-800 border-slate-700",align:"end",forceMount:!0,children:[(0,a.jsxs)(tw,{onClick:()=>{d.push("/profile")},className:"text-slate-300 hover:text-white hover:bg-slate-700",children:[(0,a.jsx)(tS.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"الملف الشخصي"})]}),(0,a.jsxs)(tw,{onClick:o,className:"text-slate-300 hover:text-white hover:bg-slate-700",children:[(0,a.jsx)(tD.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"الصفحة الرئيسية"})]}),(0,a.jsx)(ty,{className:"bg-slate-700"}),(0,a.jsxs)(tw,{className:"text-red-400 hover:text-red-300 hover:bg-slate-700",children:[(0,a.jsx)(tM.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"تسجيل الخروج"})]})]})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"lg:hidden text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-300",onClick:()=>i(!t),children:t?(0,a.jsx)(M.A,{className:"h-6 w-6"}):(0,a.jsx)(t_,{className:"h-6 w-6"})})]})]}),(0,a.jsx)("div",{className:"hidden lg:block border-t border-slate-700/30 bg-slate-800/30",children:(0,a.jsx)("div",{className:"flex items-center px-4 lg:px-6 py-4",children:(0,a.jsx)("nav",{className:"flex items-center gap-3",children:m.map(t=>(0,a.jsxs)(n.$,{variant:e===t.id?"default":"ghost",size:"sm",onClick:()=>s(t.id),className:`${e===t.id?"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold shadow-lg border-0":"text-slate-300 hover:text-white hover:bg-slate-700/50 border-0"} px-5 py-3 h-auto transition-all duration-300 hover:scale-105 rounded-lg font-medium relative`,children:[(0,a.jsx)(t.icon,{className:"h-4 w-4 mr-2"}),t.label]},t.id))})})}),t&&(0,a.jsx)("div",{className:"lg:hidden border-t border-slate-700/30 bg-slate-800/95 backdrop-blur-xl",children:(0,a.jsxs)("div",{className:"px-4 py-4 space-y-2",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-bold text-white",children:"لوحة التحكم"}),(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"إدارة المنتجات والطلبات"})]}),(0,a.jsx)("nav",{className:"space-y-1",children:m.map(t=>(0,a.jsxs)(n.$,{variant:e===t.id?"default":"ghost",size:"sm",onClick:()=>{s(t.id),i(!1)},className:`${e===t.id?"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold":"text-slate-300 hover:text-white hover:bg-slate-700/50"} w-full justify-start px-4 py-3 h-auto relative`,children:[(0,a.jsx)(t.icon,{className:"h-4 w-4 mr-3"}),t.label]},t.id))}),(0,a.jsxs)("div",{className:"pt-4 border-t border-slate-700/30 space-y-2",children:[(0,a.jsxs)(n.$,{onClick:()=>{o(),i(!1)},variant:"outline",size:"sm",className:"w-full justify-start bg-slate-700/50 border-slate-600/50 text-slate-300 hover:text-white hover:bg-slate-600/50",children:[(0,a.jsx)(tD.A,{className:"h-4 w-4 mr-3"}),"الصفحة الرئيسية"]}),(0,a.jsx)("div",{className:"flex items-center justify-between pt-2",children:(0,a.jsx)(g.E,{className:"bg-green-500/20 text-green-400 border-green-500/30",children:"متصل"})})]})]})})]})})}var tT=t(5336),tP=t(35071),tE=t(78122),tZ=t(40228),t$=t(13861),tO=t(59885),tq=t(5525),tz=t(54278),tF=t(94511),tL=t(93613),tU=t(31158);let tW=(0,d.A)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);var tJ=t(58887),tB=t(70615),tG=t(8819);function tV({order:e}){let[s,t]=(0,l.useState)(0),[r,i]=(0,l.useState)(!1);return(0,a.jsxs)("div",{className:"bg-slate-800/50 border border-slate-700 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-slate-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(tS.A,{className:"h-5 w-5 text-slate-300"})}),(0,a.jsx)("div",{className:`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-slate-800 ${r?"bg-green-400":"bg-slate-400"}`})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:e.userDetails.name||"عميل"}),s>0&&(0,a.jsx)(g.E,{variant:"destructive",className:"bg-red-500 text-xs",children:s})]}),(0,a.jsx)("div",{className:"flex items-center gap-2 text-sm text-slate-400",children:(0,a.jsx)("span",{children:"\uD83D\uDCAC محادثة العميل"})})]})]}),(0,a.jsxs)(n.$,{onClick:()=>{let t=e.userDetails.name||"عميل",a=e.userDetails.email,l=new CustomEvent("open-admin-chat",{detail:{customerId:a,customerName:t,customerEmail:a,orderId:e.id,packageName:e.templateName,unreadCount:s,isOnline:r}});window.dispatchEvent(l)},className:"bg-blue-500 hover:bg-blue-600 flex items-center gap-2",children:[(0,a.jsx)(tJ.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"فتح المحادثة"}),s>0&&(0,a.jsx)(tN,{className:"h-2 w-2 text-red-300 fill-current"})]})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center gap-4 text-xs text-slate-400",children:[(0,a.jsxs)("span",{children:["\uD83D\uDCE7 ",e.userDetails.email]}),(0,a.jsx)("span",{className:r?"text-green-400":"text-slate-400",children:r?"\uD83D\uDFE2 متصل الآن":"⚫ غير متصل"}),(0,a.jsx)("span",{children:"\uD83D\uDD17 اختصار للمحادثة"})]})]})}function tH({order:e,onClose:s,onOrderUpdate:t,userRole:r="admin"}){let[d,o]=(0,l.useState)("details"),[x,h]=(0,l.useState)(!1),[u,f]=(0,l.useState)(e.adminNotes||""),[b,N]=(0,l.useState)(e.internalNotes||""),[v,w]=(0,l.useState)(e.status),[C,A]=(0,l.useState)(e.priority||"normal"),[R,D]=(0,l.useState)(e.assignedAdminId||""),[S,_]=(0,l.useState)(!1),[I,T]=(0,l.useState)(!1),E=async(s,a)=>{if("viewer"!==r){_(!0);try{(0,tO.ND)(e.id,s,a,"admin_current"),t(),alert(`تم تحديث حالة الطلب إلى "${O(s)}" بنجاح`)}catch(e){console.error("Error updating order status:",e),alert("حدث خطأ أثناء تحديث حالة الطلب")}finally{_(!1)}}},Z=async()=>{if("admin"===r){_(!0);try{(0,tO.Ls)(e.id),t(),s(),alert("تم حذف الطلب بنجاح")}catch(e){console.error("Error deleting order:",e),alert("حدث خطأ أثناء حذف الطلب")}finally{_(!1)}}},$=async()=>{if("viewer"!==r){_(!0);try{let s=JSON.parse(localStorage.getItem("product_orders")||"[]"),a=s.findIndex(s=>s.id===e.id);-1!==a&&(s[a]={...s[a],adminNotes:u,internalNotes:b,priority:C,assignedAdminId:R,updatedAt:new Date},localStorage.setItem("product_orders",JSON.stringify(s))),t(),h(!1),alert("تم حفظ التحديثات بنجاح")}catch(e){console.error("Error saving updates:",e),alert("حدث خطأ أثناء حفظ التحديثات")}finally{_(!1)}}},O=e=>({pending:"في الانتظار",processing:"قيد المعالجة",completed:"مكتمل",failed:"فشل",cancelled:"ملغي"})[e],q=(s,t)=>t?"object"==typeof t?t.name&&t.price?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-white font-medium",children:t.name}),(0,a.jsx)("p",{className:"text-green-400 text-sm",children:(0,tz.vv)(t.price,e.pricing.currency)}),t.description&&(0,a.jsx)("p",{className:"text-slate-400 text-xs",children:t.description})]}):(0,a.jsx)("div",{className:"bg-slate-800/50 rounded p-2",children:(0,a.jsx)("pre",{className:"text-xs text-slate-300 whitespace-pre-wrap break-words",children:JSON.stringify(t,null,2)})}):"boolean"==typeof t?(0,a.jsx)("div",{className:"flex items-center gap-2",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tT.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("span",{className:"text-green-400",children:"نعم"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tP.A,{className:"h-4 w-4 text-red-400"}),(0,a.jsx)("span",{className:"text-red-400",children:"لا"})]})}):s.includes("image")||s.includes("file")?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-500/20 rounded",children:(0,a.jsx)(tU.A,{className:"h-4 w-4 text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-blue-400 text-sm",children:"ملف مرفق"}),(0,a.jsx)("p",{className:"text-slate-400 text-xs",children:"انقر للتحميل"})]})]}):"string"==typeof t&&(t.startsWith("http")||t.startsWith("www"))?(0,a.jsx)("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 underline break-all",children:t}):(0,a.jsx)("span",{className:"text-white break-words",children:String(t)}):(0,a.jsx)("span",{className:"text-slate-500 italic",children:"لا توجد بيانات"});return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-2 sm:p-4 z-50",children:(0,a.jsxs)(i.Zp,{className:"bg-slate-800 border-slate-700 w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden",children:[(0,a.jsx)(i.aR,{className:"border-b border-slate-700 p-3 sm:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-3",children:[(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2 text-lg sm:text-xl",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0"}),(0,a.jsxs)("span",{className:"truncate",children:["تفاصيل الطلب: ",e.id]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mt-2",children:[(0,a.jsxs)(g.E,{className:`${(e=>{switch(e){case"pending":return"bg-yellow-500/20 text-yellow-400 border-yellow-500/30";case"processing":return"bg-blue-500/20 text-blue-400 border-blue-500/30";case"completed":return"bg-green-500/20 text-green-400 border-green-500/30";case"failed":return"bg-red-500/20 text-red-400 border-red-500/30";case"cancelled":return"bg-gray-500/20 text-gray-400 border-gray-500/30";default:return"bg-slate-500/20 text-slate-400 border-slate-500/30"}})(e.status)} text-xs sm:text-sm`,children:[(e=>{switch(e){case"pending":return(0,a.jsx)(eU.A,{className:"h-4 w-4"});case"processing":return(0,a.jsx)(tL.A,{className:"h-4 w-4"});case"completed":return(0,a.jsx)(tT.A,{className:"h-4 w-4"});case"failed":case"cancelled":return(0,a.jsx)(tP.A,{className:"h-4 w-4"});default:return(0,a.jsx)(c.A,{className:"h-4 w-4"})}})(e.status),(0,a.jsx)("span",{className:"mr-1",children:O(e.status)})]}),(0,a.jsxs)(g.E,{className:`${(e=>{switch(e){case"urgent":return"bg-red-500/20 text-red-400 border-red-500/30";case"high":return"bg-orange-500/20 text-orange-400 border-orange-500/30";case"normal":return"bg-blue-500/20 text-blue-400 border-blue-500/30";case"low":return"bg-gray-500/20 text-gray-400 border-gray-500/30";default:return"bg-slate-500/20 text-slate-400 border-slate-500/30"}})(e.priority||"normal")} text-xs sm:text-sm`,children:[(0,a.jsx)(tW,{className:"h-3 w-3 mr-1"}),{urgent:"عاجل",high:"عالي",normal:"عادي",low:"منخفض"}[e.priority||"normal"]||"عادي"]}),(0,a.jsx)(g.E,{variant:"outline",className:"text-slate-300 text-xs sm:text-sm",children:"instant"===e.processingType?"معالجة فورية":"معالجة يدوية"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:["viewer"!==r&&(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>h(!x),className:"border-slate-600 text-slate-300 text-xs sm:text-sm",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:x?"إلغاء التعديل":"تعديل"}),(0,a.jsx)("span",{className:"sm:hidden",children:x?"إلغاء":"تعديل"})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:s,className:"text-slate-400 hover:text-white",children:(0,a.jsx)(M.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsx)(i.Wu,{className:"p-0 overflow-y-auto max-h-[calc(95vh-120px)] sm:max-h-[calc(90vh-120px)]",children:(0,a.jsxs)(eg.tU,{value:d,onValueChange:e=>o(e),children:[(0,a.jsxs)(eg.j7,{className:"grid w-full grid-cols-4 bg-slate-800/50 m-2 sm:m-4 mb-0",children:[(0,a.jsxs)(eg.Xi,{value:"details",className:"data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"تفاصيل الطلب"}),(0,a.jsx)("span",{className:"sm:hidden",children:"التفاصيل"})]}),(0,a.jsxs)(eg.Xi,{value:"chat",className:"data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3",children:[(0,a.jsx)(tJ.A,{className:"h-3 w-3 sm:h-4 sm:w-4 ml-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"المحادثة"}),(0,a.jsx)("span",{className:"sm:hidden",children:"دردشة"})]}),(0,a.jsxs)(eg.Xi,{value:"timeline",className:"data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"سجل الأحداث"}),(0,a.jsx)("span",{className:"sm:hidden",children:"الأحداث"})]}),(0,a.jsxs)(eg.Xi,{value:"actions",className:"data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"إجراءات الإدارة"}),(0,a.jsx)("span",{className:"sm:hidden",children:"الإجراءات"})]})]}),(0,a.jsxs)(eg.av,{value:"details",className:"p-2 sm:p-4 space-y-4 sm:space-y-6",children:[(0,a.jsxs)(i.Zp,{className:"bg-slate-700/50 border-slate-600",children:[(0,a.jsx)(i.aR,{className:"p-3 sm:p-6",children:(0,a.jsxs)(i.ZB,{className:"text-white text-base sm:text-lg flex items-center gap-2",children:[(0,a.jsx)(tS.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}),"معلومات العميل"]})}),(0,a.jsxs)(i.Wu,{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 p-3 sm:p-6",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm",children:"الاسم الكامل"}),(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base break-words",children:e.userDetails.fullName})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm",children:"البريد الإلكتروني"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base break-all flex-1 min-w-0",children:e.userDetails.email}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{navigator.clipboard.writeText(e.userDetails.email),alert("تم نسخ البريد الإلكتروني")},className:"h-6 w-6 p-0 text-slate-400 hover:text-white flex-shrink-0",children:(0,a.jsx)(tB.A,{className:"h-3 w-3"})})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm",children:"رقم الهاتف"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base break-all flex-1 min-w-0",children:e.userDetails.phone}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{navigator.clipboard.writeText(e.userDetails.phone),alert("تم نسخ رقم الهاتف")},className:"h-6 w-6 p-0 text-slate-400 hover:text-white flex-shrink-0",children:(0,a.jsx)(tB.A,{className:"h-3 w-3"})})]})]})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-700/50 border-slate-600",children:[(0,a.jsx)(i.aR,{className:"p-3 sm:p-6",children:(0,a.jsxs)(i.ZB,{className:"text-white text-base sm:text-lg flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}),"معلومات المنتج"]})}),(0,a.jsxs)(i.Wu,{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 p-3 sm:p-6",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm",children:"اسم المنتج"}),(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base break-words",children:e.templateName})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm",children:"الفئة"}),(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base",children:e.templateCategory})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm",children:"تاريخ الإنشاء"}),(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base",children:(0,tF.r6)(e.createdAt)})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm",children:"آخر تحديث"}),(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base",children:(0,tF.r6)(e.updatedAt)})]})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg",children:[(0,a.jsx)(i.aR,{className:"p-3 sm:p-6",children:(0,a.jsxs)(i.ZB,{className:"text-white text-base sm:text-lg flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-green-500/20 rounded-lg",children:(0,a.jsx)(m.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-400"})}),"تفاصيل السعر"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-3 sm:space-y-4 p-3 sm:p-6",children:[e.pricing.basePrice>0&&(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-slate-800/50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-slate-400 text-sm sm:text-base",children:"السعر الأساسي:"}),(0,a.jsx)("span",{className:"text-white font-medium text-sm sm:text-base",children:(0,tz.vv)(e.pricing.basePrice,e.pricing.currency)})]}),e.pricing.modifiers.map((s,t)=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-slate-800/30 rounded-lg",children:[(0,a.jsxs)("span",{className:"text-slate-400 text-sm sm:text-base",children:[s.fieldLabel,":"]}),(0,a.jsxs)("span",{className:`font-medium text-sm sm:text-base ${"add"===s.type?"text-green-400":"text-blue-400"}`,children:["add"===s.type?"+":"",(0,tz.vv)(s.modifier,e.pricing.currency)]})]},t)),e.pricing.quantity>1&&(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-slate-800/30 rounded-lg",children:[(0,a.jsx)("span",{className:"text-slate-400 text-sm sm:text-base",children:"الكمية:"}),(0,a.jsxs)("span",{className:"text-blue-400 font-medium text-sm sm:text-base",children:["\xd7 ",e.pricing.quantity]})]}),(0,a.jsx)("div",{className:"border-t border-slate-600 pt-4 mt-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-lg border border-yellow-500/30",children:[(0,a.jsx)("span",{className:"text-yellow-400 font-bold text-base sm:text-lg",children:"الإجمالي:"}),(0,a.jsx)("span",{className:"text-yellow-400 font-bold text-lg sm:text-xl",children:(0,tz.vv)(e.pricing.totalPrice,e.pricing.currency)})]})})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-700/50 border-slate-600",children:[(0,a.jsx)(i.aR,{className:"p-3 sm:p-6",children:(0,a.jsxs)(i.ZB,{className:"text-white text-base sm:text-lg flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}),"بيانات النموذج"]})}),(0,a.jsxs)(i.Wu,{className:"p-3 sm:p-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6",children:Object.entries(e.productData).map(([e,s])=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-slate-400 text-xs sm:text-sm font-medium",children:e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())}),(0,a.jsx)("div",{className:"bg-slate-800/50 rounded-lg p-3 min-h-[2.5rem] flex items-start",children:(0,a.jsx)("div",{className:"w-full",children:q(e,s)})})]},e))}),(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-slate-600",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{let s=new Blob([JSON.stringify(e.productData,null,2)],{type:"application/json"}),t=URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download=`order-${e.id}-data.json`,a.click(),URL.revokeObjectURL(t),alert("تم تحميل ملف البيانات")},className:"border-slate-600 text-slate-300 hover:bg-slate-600",children:[(0,a.jsx)(tU.A,{className:"h-4 w-4 mr-2"}),"تصدير البيانات"]})})]})]})]}),(0,a.jsx)(eg.av,{value:"timeline",className:"p-2 sm:p-4",children:(0,a.jsxs)(i.Zp,{className:"bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg",children:[(0,a.jsx)(i.aR,{className:"p-3 sm:p-6",children:(0,a.jsxs)(i.ZB,{className:"text-white text-base sm:text-lg flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-500/20 rounded-lg",children:(0,a.jsx)(eU.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-blue-400"})}),"سجل الأحداث"]})}),(0,a.jsx)(i.Wu,{className:"p-3 sm:p-6",children:(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[e.timeline.map((s,t)=>(0,a.jsxs)("div",{className:"flex gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center flex-shrink-0",children:[(0,a.jsx)("div",{className:`w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 border-slate-800 shadow-lg ${"created"===s.type?"bg-blue-500":"status_change"===s.type?"bg-yellow-500":"admin_note"===s.type?"bg-purple-500":"bg-gray-500"}`}),t<e.timeline.length-1&&(0,a.jsx)("div",{className:"w-0.5 h-8 sm:h-12 bg-gradient-to-b from-slate-600 to-slate-700 mt-2"})]}),(0,a.jsx)("div",{className:"flex-1 pb-4 min-w-0",children:(0,a.jsxs)("div",{className:"bg-slate-800/50 rounded-lg p-3 sm:p-4 border border-slate-700/50 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-2",children:[(0,a.jsx)("p",{className:"text-white font-medium text-sm sm:text-base break-words",children:s.description}),(0,a.jsx)(g.E,{variant:"outline",className:`text-xs flex-shrink-0 ${"created"===s.type?"text-blue-400 border-blue-400/30":"status_change"===s.type?"text-yellow-400 border-yellow-400/30":"admin_note"===s.type?"text-purple-400 border-purple-400/30":"text-gray-400 border-gray-400/30"}`,children:"created"===s.type?"إنشاء":"status_change"===s.type?"تغيير حالة":"admin_note"===s.type?"ملاحظة إدارية":"حدث"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 text-slate-400 text-xs sm:text-sm",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(tZ.A,{className:"h-3 w-3"}),(0,tF.r6)(s.createdAt)]}),s.createdBy&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(tS.A,{className:"h-3 w-3"}),s.createdBy]})]}),s.details&&Object.keys(s.details).length>0&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50",children:(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:Object.entries(s.details).map(([e,s])=>(0,a.jsxs)("div",{className:"text-slate-300 text-xs sm:text-sm",children:[(0,a.jsxs)("span",{className:"font-medium text-slate-200",children:[e,":"]}),(0,a.jsx)("span",{className:"mr-2 break-words",children:String(s)})]},e))})})]})})]},s.id)),0===e.timeline.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(eU.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-slate-400",children:"لا توجد أحداث في السجل"})]})]})})]})}),(0,a.jsxs)(eg.av,{value:"actions",className:"p-2 sm:p-4 space-y-4 sm:space-y-6",children:[(0,a.jsxs)(i.Zp,{className:"bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg",children:[(0,a.jsx)(i.aR,{className:"p-3 sm:p-6",children:(0,a.jsxs)(i.ZB,{className:"text-white text-base sm:text-lg flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-green-500/20 rounded-lg",children:(0,a.jsx)(tT.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-400"})}),"إجراءات سريعة"]})}),(0,a.jsx)(i.Wu,{className:"p-3 sm:p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3",children:["pending"===e.status&&"viewer"!==r&&(0,a.jsxs)(n.$,{onClick:()=>E("processing","تم قبول الطلب وبدء المعالجة"),disabled:S,className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 text-sm sm:text-base",children:[(0,a.jsx)(tT.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"قبول الطلب"}),(0,a.jsx)("span",{className:"sm:hidden",children:"قبول"})]}),"processing"===e.status&&"viewer"!==r&&(0,a.jsxs)(n.$,{onClick:()=>E("completed","تم إنجاز الطلب بنجاح"),disabled:S,className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 text-sm sm:text-base",children:[(0,a.jsx)(tT.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"إنجاز الطلب"}),(0,a.jsx)("span",{className:"sm:hidden",children:"إنجاز"})]}),("pending"===e.status||"processing"===e.status)&&"viewer"!==r&&(0,a.jsxs)(W,{children:[(0,a.jsx)(J,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"destructive",disabled:S,children:[(0,a.jsx)(tP.A,{className:"h-4 w-4 mr-2"}),"رفض الطلب"]})}),(0,a.jsxs)(ed,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsxs)(eo,{children:[(0,a.jsx)(em,{className:"text-white",children:"تأكيد رفض الطلب"}),(0,a.jsx)(eh,{className:"text-slate-400",children:"هل أنت متأكد من رفض هذا الطلب؟ سيتم إشعار العميل برفض الطلب."})]}),(0,a.jsxs)(ex,{children:[(0,a.jsx)(ep,{className:"bg-slate-700 text-white border-slate-600",children:"إلغاء"}),(0,a.jsx)(eu,{onClick:()=>E("failed","تم رفض الطلب من قبل الإدارة"),className:"bg-red-600 hover:bg-red-700",children:"تأكيد الرفض"})]})]})]}),"admin"===r&&(0,a.jsxs)(W,{children:[(0,a.jsx)(J,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"destructive",disabled:S,children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"حذف الطلب"]})}),(0,a.jsxs)(ed,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsxs)(eo,{children:[(0,a.jsx)(em,{className:"text-white",children:"تأكيد حذف الطلب"}),(0,a.jsx)(eh,{className:"text-slate-400",children:"هل أنت متأكد من حذف هذا الطلب؟ هذا الإجراء لا يمكن التراجع عنه."})]}),(0,a.jsxs)(ex,{children:[(0,a.jsx)(ep,{className:"bg-slate-700 text-white border-slate-600",children:"إلغاء"}),(0,a.jsx)(eu,{onClick:Z,className:"bg-red-600 hover:bg-red-700",children:"تأكيد الحذف"})]})]})]})]})})]}),(0,a.jsxs)(i.Zp,{className:"bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg",children:[(0,a.jsx)(i.aR,{className:"p-3 sm:p-6",children:(0,a.jsxs)(i.ZB,{className:"text-white text-base sm:text-lg flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-500/20 rounded-lg",children:(0,a.jsx)(tL.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-yellow-400"})}),"إدارة الحالة والأولوية"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4 sm:space-y-6 p-3 sm:p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-slate-300 text-sm sm:text-base font-medium",children:"تغيير الحالة"}),(0,a.jsxs)(j.l6,{value:v,onValueChange:e=>w(e),disabled:"viewer"===r,children:[(0,a.jsx)(j.bq,{className:"bg-slate-600 border-slate-500 text-white hover:bg-slate-500 transition-colors",children:(0,a.jsx)(j.yv,{})}),(0,a.jsxs)(j.gC,{className:"bg-slate-700 border-slate-600",children:[(0,a.jsx)(j.eb,{value:"pending",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eU.A,{className:"h-4 w-4 text-yellow-400"}),"في الانتظار"]})}),(0,a.jsx)(j.eb,{value:"processing",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tL.A,{className:"h-4 w-4 text-blue-400"}),"قيد المعالجة"]})}),(0,a.jsx)(j.eb,{value:"completed",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tT.A,{className:"h-4 w-4 text-green-400"}),"مكتمل"]})}),(0,a.jsx)(j.eb,{value:"failed",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tP.A,{className:"h-4 w-4 text-red-400"}),"فشل"]})}),(0,a.jsx)(j.eb,{value:"cancelled",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tP.A,{className:"h-4 w-4 text-gray-400"}),"ملغي"]})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P.J,{className:"text-slate-300 text-sm sm:text-base font-medium",children:"الأولوية"}),(0,a.jsxs)(j.l6,{value:C,onValueChange:A,disabled:"viewer"===r,children:[(0,a.jsx)(j.bq,{className:"bg-slate-600 border-slate-500 text-white hover:bg-slate-500 transition-colors",children:(0,a.jsx)(j.yv,{})}),(0,a.jsxs)(j.gC,{className:"bg-slate-700 border-slate-600",children:[(0,a.jsx)(j.eb,{value:"low",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tW,{className:"h-4 w-4 text-gray-400"}),"منخفض"]})}),(0,a.jsx)(j.eb,{value:"normal",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tW,{className:"h-4 w-4 text-blue-400"}),"عادي"]})}),(0,a.jsx)(j.eb,{value:"high",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tW,{className:"h-4 w-4 text-orange-400"}),"عالي"]})}),(0,a.jsx)(j.eb,{value:"urgent",className:"hover:bg-slate-600",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(tW,{className:"h-4 w-4 text-red-400"}),"عاجل"]})})]})]})]})]}),(v!==e.status||C!==(e.priority||"normal"))&&"viewer"!==r&&(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-4 border-t border-slate-600",children:[v!==e.status&&(0,a.jsxs)(n.$,{onClick:()=>E(v),disabled:S,className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,a.jsx)(tG.A,{className:"h-4 w-4 mr-2"}),"تحديث الحالة"]}),C!==(e.priority||"normal")&&(0,a.jsxs)(n.$,{onClick:$,disabled:S,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-600",children:[(0,a.jsx)(tW,{className:"h-4 w-4 mr-2"}),"تحديث الأولوية"]})]})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-700/50 border-slate-600",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white text-lg flex items-center gap-2",children:[(0,a.jsx)(tJ.A,{className:"h-5 w-5"}),"إدارة الملاحظات"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"ملاحظات للعميل"}),(0,a.jsx)(eP.T,{value:u,onChange:e=>f(e.target.value),placeholder:"ملاحظات ستكون مرئية للعميل...",className:"bg-slate-600 border-slate-500 text-white",rows:3,disabled:"viewer"===r})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"ملاحظات داخلية"}),(0,a.jsx)(eP.T,{value:b,onChange:e=>N(e.target.value),placeholder:"ملاحظات داخلية للإدارة فقط...",className:"bg-slate-600 border-slate-500 text-white",rows:3,disabled:"viewer"===r})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"تعيين للمشرف"}),(0,a.jsx)(p.p,{value:R,onChange:e=>D(e.target.value),placeholder:"معرف المشرف المسؤول...",className:"bg-slate-600 border-slate-500 text-white",disabled:"viewer"===r})]}),"viewer"!==r&&(0,a.jsxs)(n.$,{onClick:$,disabled:S,className:"bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(tG.A,{className:"h-4 w-4 mr-2"}),"حفظ التحديثات"]})]})]})]}),(0,a.jsx)(eg.av,{value:"chat",className:"p-2 sm:p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"محادثة العميل"}),(0,a.jsx)(tV,{order:e}),(0,a.jsxs)("div",{className:"bg-slate-700/30 rounded-lg p-4 border border-slate-600",children:[(0,a.jsx)("h4",{className:"text-white font-medium mb-2",children:"معلومات سريعة"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm",children:[(0,a.jsxs)("div",{className:"text-slate-300",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"العميل:"})," ",e.userDetails.name||"غير محدد"]}),(0,a.jsxs)("div",{className:"text-slate-300",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"المنتج:"})," ",e.templateName]}),(0,a.jsxs)("div",{className:"text-slate-300",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"البريد:"})," ",e.userDetails.email]}),(0,a.jsxs)("div",{className:"text-slate-300",children:[(0,a.jsx)("span",{className:"text-slate-400",children:"رقم الطلب:"})," #",e.id]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(tJ.A,{className:"h-4 w-4 text-blue-400"}),(0,a.jsx)("span",{className:"text-blue-300 font-medium",children:"اختصار المحادثة"})]}),(0,a.jsx)("p",{className:"text-blue-200 text-sm",children:"هذا اختصار سريع لفتح محادثة العميل. ستفتح نافذة المحادثة الرئيسية مع تحديد العميل تلقائياً، ويمكنك التنقل بين جميع المحادثات الأخرى بشكل طبيعي."})]})]})})]})})]})})}function tX({userRole:e="admin"}){let[s,t]=(0,l.useState)([]),[r,d]=(0,l.useState)(null),[o,x]=(0,l.useState)(null),[h,u]=(0,l.useState)({}),[j,f]=(0,l.useState)(""),[b,N]=(0,l.useState)(!0),[v,y]=(0,l.useState)(!1),[k,C]=(0,l.useState)("all"),A=(0,l.useCallback)(async()=>{N(!0);try{let e;(0,tq.F)(),"pending"===k?e=["pending","processing"]:"approved"===k?e=["completed"]:"declined"===k&&(e=["failed","cancelled"]);let s={...h,status:e,search:j||void 0},a=(0,tO.zt)(s);t(a);let l=(0,tO.NC)(),r={...l,pending:l.pending+l.processing,approved:l.completed,declined:l.failed+l.cancelled};d(r)}catch(e){console.error("Error loading orders:",e)}finally{N(!1)}},[h,j,k]),R=e=>{x((0,tO.Nn)().find(s=>s.id===e)||null)},D=e=>{f(e)},S=e=>{switch(e){case"pending":case"processing":return"bg-yellow-500/20 text-yellow-400 border-yellow-500/30";case"completed":return"bg-green-500/20 text-green-400 border-green-500/30";case"failed":case"cancelled":return"bg-red-500/20 text-red-400 border-red-500/30";default:return"bg-slate-500/20 text-slate-400 border-slate-500/30"}},M=e=>{switch(e){case"pending":case"processing":return(0,a.jsx)(eU.A,{className:"h-4 w-4"});case"completed":return(0,a.jsx)(tT.A,{className:"h-4 w-4"});case"failed":case"cancelled":return(0,a.jsx)(tP.A,{className:"h-4 w-4"});default:return(0,a.jsx)(c.A,{className:"h-4 w-4"})}},_=e=>({pending:"قيد الانتظار",processing:"قيد الانتظار",completed:"موافق عليها",failed:"مرفوضة",cancelled:"مرفوضة"})[e];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:"إدارة الطلبات"}),(0,a.jsx)("p",{className:"text-slate-400",children:"إدارة ومتابعة طلبات المنتجات"})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:A,className:"border-slate-600 text-slate-300",children:[(0,a.jsx)(tE.A,{className:"h-4 w-4 ml-2"}),"تحديث"]})})]}),r&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:r.total}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"إجمالي الطلبات"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:r.pending}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"قيد الانتظار"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-400",children:r.approved}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"موافق عليها"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-400",children:r.declined}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"مرفوضة"})]})})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex flex-col sm:flex-row gap-4",children:(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(w.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(p.p,{placeholder:"البحث برقم الطلب، اسم العميل، أو البريد الإلكتروني...",value:j,onChange:e=>D(e.target.value),className:"bg-slate-700/50 border-slate-600 text-white pr-10"})]})})}),v&&(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50",children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsx)("p",{className:"text-slate-400",children:"فلاتر متقدمة - قيد التطوير"})})})]}),(0,a.jsxs)(eg.tU,{value:k,onValueChange:e=>C(e),children:[(0,a.jsxs)(eg.j7,{className:"grid w-full grid-cols-4 bg-slate-800/50",children:[(0,a.jsxs)(eg.Xi,{value:"all",className:"data-[state=active]:bg-slate-700",children:["الكل (",r?.total||0,")"]}),(0,a.jsxs)(eg.Xi,{value:"pending",className:"data-[state=active]:bg-slate-700",children:["قيد الانتظار (",r?.pending||0,")"]}),(0,a.jsxs)(eg.Xi,{value:"approved",className:"data-[state=active]:bg-slate-700",children:["موافق عليها (",r?.approved||0,")"]}),(0,a.jsxs)(eg.Xi,{value:"declined",className:"data-[state=active]:bg-slate-700",children:["مرفوضة (",r?.declined||0,")"]})]}),(0,a.jsx)(eg.av,{value:k,className:"mt-6",children:b?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(tE.A,{className:"h-8 w-8 animate-spin text-slate-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-slate-400",children:"جاري تحميل الطلبات..."})]}):0===s.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-slate-400 text-lg",children:"لا توجد طلبات"}),(0,a.jsx)("p",{className:"text-slate-500",children:"لم يتم العثور على طلبات تطابق المعايير المحددة"})]}):(0,a.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-colors cursor-pointer",onClick:()=>R(e.id),children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 flex-1",children:[(0,a.jsx)("div",{className:"p-2 bg-slate-700/50 rounded-lg",children:M(e.status)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h3",{className:"font-medium text-white truncate",children:e.templateName}),(0,a.jsx)(g.E,{className:S(e.status),children:_(e.status)})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-slate-400",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(tS.A,{className:"h-3 w-3"}),e.customerName]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(tZ.A,{className:"h-3 w-3"}),(0,tF.Yq)(e.createdAt)]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),(0,tz.vv)(e.totalPrice,e.currency)]})]})]})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:s=>{s.stopPropagation(),R(e.id)},className:"text-slate-400 hover:text-white",children:(0,a.jsx)(t$.A,{className:"h-4 w-4"})})})]})})},e.id))})})]}),o&&(0,a.jsx)(tH,{order:o,onClose:()=>x(null),onOrderUpdate:A,userRole:e})]})}var tK=t(45468),tY=t(8870);function tQ({userId:e,userName:s,userEmail:t,userRole:r,position:i="bottom-right",className:c=""}){if(!r||!["admin","customer"].includes(r))return console.error("ChatButton: Invalid or missing userRole"),null;if(!e)return console.error("ChatButton: Missing required userId"),null;let[d,o]=(0,l.useState)(!1),[x,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)(!1),{unreadCount:p}=(0,tY.Y)({userId:e,userType:r});return(0,a.jsxs)(a.Fragment,{children:[!d&&(0,a.jsxs)("div",{className:`fixed ${(()=>{switch(i){case"bottom-left":return"bottom-6 left-6";case"top-right":return"top-6 right-6";case"top-left":return"top-6 left-6";default:return"bottom-6 right-6"}})()} z-40 ${c}`,children:[(0,a.jsxs)(n.$,{onClick:()=>o(!0),className:`
              w-14 h-14 rounded-full shadow-lg
              bg-gradient-to-r from-green-500 to-green-600
              hover:from-green-600 hover:to-green-700
              text-white border-0
              transition-all duration-300
              ${h?"animate-bounce":"hover:scale-110"}
              focus:outline-none focus:ring-4 focus:ring-green-500/30
            `,"aria-label":"customer"===r?"فتح الدعم الفني":"فتح المحادثات",children:[(0,a.jsx)(tJ.A,{className:"w-6 h-6"}),p>0&&(0,a.jsx)(g.E,{className:"absolute -top-2 -right-2 bg-red-500 text-white text-xs px-1.5 py-0.5 min-w-[20px] h-5 flex items-center justify-center animate-pulse",children:p>99?"99+":p}),h&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-green-400 animate-ping opacity-75"})]}),(0,a.jsx)("div",{className:"absolute bottom-full right-0 mb-2 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none",children:(0,a.jsxs)("div",{className:"bg-slate-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap",children:["customer"===r?"الدعم الفني":"المحادثات"," ",p>0&&`(${p} غير مقروءة)`]})})]}),(0,a.jsx)(tK.k,{isOpen:d,onClose:()=>{o(!1),m(!1)},userId:e,userName:s,userEmail:t,userRole:r,position:"bottom-left"===i||"top-left"===i?"bottom-left":"bottom-right",isMinimized:x,onToggleMinimize:()=>m(!x)})]})}function t0({userRole:e,userId:s="",userName:t="",userEmail:r=""}){let[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)({});return"admin"!==e?null:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{id:"admin-chat-modal-trigger",className:"hidden",onClick:()=>n(!0)}),i&&(0,a.jsx)(tK.k,{isOpen:!0,onClose:()=>n(!1),userId:s||"admin_current",userName:t||"مدير النظام",userEmail:r||"<EMAIL>",userRole:"admin",position:"center",initialCustomerId:c.customerId,initialOrderId:c.orderId,initialChatRoomId:c.customerId})]})}let t4=(0,t(24224).F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),t2=l.forwardRef(({className:e,variant:s,...t},l)=>(0,a.jsx)("div",{ref:l,role:"alert",className:(0,en.cn)(t4({variant:s}),e),...t}));t2.displayName="Alert",l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("h5",{ref:t,className:(0,en.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let t1=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,en.cn)("text-sm [&_p]:leading-relaxed",e),...s}));t1.displayName="AlertDescription";var t5=t(41862);let t3=(0,d.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var t6=t(25541);function t7(){let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[c,d]=(0,l.useState)(!1),[o,x]=(0,l.useState)({symbol:"",arabicName:"",exchangeRate:1}),[h,u]=(0,l.useState)(!1),[j,f]=(0,l.useState)(null),N=async()=>{if(!o.arabicName||!o.symbol){f({type:"error",text:"يرجى ملء جميع الحقول المطلوبة"});return}if(o.exchangeRate<=0){f({type:"error",text:"سعر الصرف يجب أن يكون أكبر من صفر"});return}u(!0);try{let e={code:o.symbol.substring(0,3).toUpperCase(),name:o.arabicName,symbol:o.symbol,arabicName:o.arabicName,exchangeRate:o.exchangeRate,isActive:!0,isDefault:!1};s(s=>[...s,e]),x({symbol:"",arabicName:"",exchangeRate:1}),d(!1),f({type:"success",text:`تم إضافة العملة ${o.arabicName} بنجاح`})}catch(e){console.error("Error adding currency:",e),f({type:"error",text:"فشل في إضافة العملة"})}finally{u(!1)}},v=async e=>{s(s=>s.map(s=>s.code===e?{...s,isActive:!s.isActive}:s)),f({type:"success",text:"تم تحديث حالة العملة"})};return t?(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50",children:(0,a.jsxs)(i.Wu,{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(t5.A,{className:"h-8 w-8 animate-spin text-blue-400"}),(0,a.jsx)("span",{className:"mr-2 text-slate-400",children:"جاري تحميل العملات..."})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-6 w-6 text-yellow-400"}),"إدارة العملات"]}),(0,a.jsx)("p",{className:"text-slate-400 mt-1",children:"إضافة وإدارة العملات المدعومة في النظام"})]}),(0,a.jsxs)(n.$,{onClick:()=>d(!c),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 ml-2"}),"إضافة عملة جديدة"]})]}),j&&(0,a.jsxs)(t2,{className:"success"===j.type?"bg-green-900/20 border-green-700/50":"bg-red-900/20 border-red-700/50",children:["success"===j.type?(0,a.jsx)(tT.A,{className:"h-4 w-4 text-green-400"}):(0,a.jsx)(eN,{className:"h-4 w-4 text-red-400"}),(0,a.jsx)(t1,{className:"success"===j.type?"text-green-100":"text-red-100",children:j.text})]}),c&&(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-white",children:"إضافة عملة جديدة"})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"اسم العملة"}),(0,a.jsx)(p.p,{value:o.name,onChange:e=>x(s=>({...s,name:e.target.value})),placeholder:"مثال: الريال السعودي",className:"bg-slate-700/50 border-slate-600 text-white",dir:"rtl"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(P.J,{className:"text-slate-300",children:"رمز العملة"}),(0,a.jsx)(p.p,{value:o.symbol,onChange:e=>x(s=>({...s,symbol:e.target.value})),placeholder:"مثال: ر.س",className:"bg-slate-700/50 border-slate-600 text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(P.J,{className:"text-slate-300",children:["سعر الصرف (1 USD = ? ",o.arabicName||"العملة الجديدة",")"]}),(0,a.jsx)(p.p,{type:"number",step:"0.01",value:o.exchangeRate,onChange:e=>x(s=>({...s,exchangeRate:parseFloat(e.target.value)||1})),placeholder:"مثال: 3.75",className:"bg-slate-700/50 border-slate-600 text-white"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,a.jsx)(n.$,{onClick:N,disabled:h,className:"bg-green-600 hover:bg-green-700",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(t5.A,{className:"h-4 w-4 animate-spin ml-2"}),"جاري الإضافة..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tT.A,{className:"h-4 w-4 ml-2"}),"إضافة العملة"]})}),(0,a.jsx)(n.$,{onClick:()=>d(!1),variant:"outline",className:"border-slate-600 text-slate-300",children:"إلغاء"})]})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(t3,{className:"h-5 w-5 text-blue-400"}),"العملات الحالية"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600/30",children:[(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:e.symbol}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium text-white",children:e.code}),e.isDefault&&(0,a.jsx)(g.E,{className:"bg-yellow-600 text-white text-xs",children:"العملة الأساسية"}),(0,a.jsx)(g.E,{className:e.isActive?"bg-green-600":"bg-red-600",children:e.isActive?"نشطة":"معطلة"})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:[e.arabicName," • ",e.name]})]})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[!e.isDefault&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(t6.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsxs)("span",{className:"text-sm text-slate-300",children:["1 USD = ",e.exchangeRate.toLocaleString()," ",e.symbol]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:!e.isDefault&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>v(e.code),className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:e.isActive?"تعطيل":"تفعيل"}),(0,a.jsx)(n.$,{size:"sm",variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:(0,a.jsx)(eF.A,{className:"h-4 w-4"})})]})})]})]},e.code))})})]}),(0,a.jsxs)(i.Zp,{className:"bg-blue-900/20 backdrop-blur-xl border-blue-700/50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-blue-100 text-lg",children:"نقاط التكامل مع النظام"})}),(0,a.jsxs)(i.Wu,{className:"text-blue-200 text-sm space-y-2",children:[(0,a.jsxs)("div",{children:["• ",(0,a.jsx)("strong",{children:"نظام المنتجات:"})," العملات الجديدة ستظهر في حقول تسعير المنتجات"]}),(0,a.jsxs)("div",{children:["• ",(0,a.jsx)("strong",{children:"نظام المحافظ:"})," سيتم إنشاء أقسام رصيد منفصلة للمستخدمين لكل عملة جديدة"]}),(0,a.jsxs)("div",{children:["• ",(0,a.jsx)("strong",{children:"أسعار الصرف:"})," الأسعار الجديدة ستؤثر على حسابات الأسعار في جميع أنحاء النظام"]}),(0,a.jsxs)("div",{children:["• ",(0,a.jsx)("strong",{children:"لوحة الإدارة:"})," العملات ستظهر في جميع محددات العملات في لوحة الإدارة"]})]})]})]})}function t8(){let[e,s]=(0,l.useState)("overview");(0,r.useRouter)();let t={totalProducts:15,totalCategories:4,totalOrders:128,totalRevenue:45600,activeProducts:12,pendingOrders:8};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900",children:[(0,a.jsx)(tI,{activeTab:e,onTabChange:s}),(()=>{switch(e){case"products":return(0,a.jsx)(eT,{});case"orders":return(0,a.jsx)(tX,{});case"contact":return(0,a.jsx)(eH,{});case"settings":return(0,a.jsx)("div",{className:"text-white p-6 lg:p-8",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2",children:"الإعدادات"}),(0,a.jsx)("p",{className:"text-slate-400 text-lg",children:"تخصيص وإعدادات النظام"})]}),(0,a.jsx)(t7,{})]})});default:return(0,a.jsx)("div",{className:"text-white p-6 lg:p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8 lg:mb-10",children:[(0,a.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2",children:"نظرة عامة"}),(0,a.jsx)("p",{className:"text-slate-400 text-lg",children:"مرحباً بك في لوحة التحكم - إدارة شاملة للمنتجات والطلبات"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8",children:[(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:(0,a.jsx)(i.Wu,{className:"p-3 sm:p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-xs sm:text-sm",children:"إجمالي المنتجات"}),(0,a.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-white",children:t.totalProducts}),(0,a.jsxs)("p",{className:"text-green-400 text-xs sm:text-sm",children:[t.activeProducts," نشط"]})]}),(0,a.jsx)(c.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:(0,a.jsx)(i.Wu,{className:"p-3 sm:p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-xs sm:text-sm",children:"الفئات"}),(0,a.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-white",children:t.totalCategories}),(0,a.jsx)("p",{className:"text-blue-400 text-xs sm:text-sm",children:"جميعها نشطة"})]}),(0,a.jsx)(o,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:(0,a.jsx)(i.Wu,{className:"p-3 sm:p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-xs sm:text-sm",children:"إجمالي الطلبات"}),(0,a.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-white",children:t.totalOrders}),(0,a.jsxs)("p",{className:"text-yellow-400 text-xs sm:text-sm",children:[t.pendingOrders," في الانتظار"]})]}),(0,a.jsx)(x.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-yellow-400"})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:(0,a.jsx)(i.Wu,{className:"p-3 sm:p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-xs sm:text-sm",children:"إجمالي الإيرادات"}),(0,a.jsx)("p",{className:"text-xl sm:text-2xl font-bold text-white",children:(0,tz.vv)(t.totalRevenue,"SDG")})]}),(0,a.jsx)(m.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-400"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"إدارة المنتجات"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm",children:"إضافة وتعديل وحذف المنتجات مع الحقول والخيارات الديناميكية"}),(0,a.jsx)(n.$,{onClick:()=>s("products"),className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white",children:"إدارة المنتجات"})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"إدارة صفحة الاتصال"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm",children:"إدارة محتوى صفحة الاتصال والأسئلة الشائعة والنموذج"}),(0,a.jsx)(n.$,{onClick:()=>s("contact"),className:"w-full bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white",children:"إدارة صفحة الاتصال"})]})]}),(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(u,{className:"h-5 w-5"}),"التقارير والإحصائيات"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm",children:"عرض تقارير مفصلة عن المبيعات والطلبات والأداء العام"}),(0,a.jsx)(n.$,{disabled:!0,className:"w-full bg-slate-600 text-slate-400 cursor-not-allowed",children:"قريباً"})]})]})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-white",children:"معلومات النظام"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400",children:"إصدار النظام"}),(0,a.jsx)("p",{className:"text-white font-medium",children:"v1.0.0 Beta"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400",children:"قاعدة البيانات"}),(0,a.jsx)("p",{className:"text-white font-medium",children:"Supabase (قيد الإعداد)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400",children:"آخر تحديث"}),(0,a.jsx)("p",{className:"text-white font-medium",children:(()=>{let e=new Date,s=e.getFullYear(),t=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return`${a}/${t}/${s}`})()})]})]})})]})})]})})}})(),(0,a.jsx)(tQ,{userId:"admin_current",userName:"مدير النظام",userEmail:"<EMAIL>",userRole:"admin",position:"bottom-left"}),(0,a.jsx)(t0,{userRole:"admin",userId:"admin_current",userName:"مدير النظام",userEmail:"<EMAIL>"}),(0,a.jsx)(eD,{})]})}},7965:(e,s,t)=>{"use strict";t.d(s,{w:()=>o});var a=t(60687),l=t(43210),r=t(14163),i="horizontal",n=["horizontal","vertical"],c=l.forwardRef((e,s)=>{var t;let{decorative:l,orientation:c=i,...d}=e,o=(t=c,n.includes(t))?c:i;return(0,a.jsx)(r.sG.div,{"data-orientation":o,...l?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...d,ref:s})});c.displayName="Separator";var d=t(96241);let o=l.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...l},r)=>(0,a.jsx)(c,{ref:r,decorative:t,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...l}));o.displayName=c.displayName},8819:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10022:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10403:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var a=t(60687),l=t(43210),r=t(14163),i=l.forwardRef((e,s)=>(0,a.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=t(24224),c=t(96241);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(i,{ref:t,className:(0,c.cn)(d(),e),...s}));o.displayName=i.displayName},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15616:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var a=t(60687),l=t(43210),r=t(96241);let i=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...s}));i.displayName="Textarea"},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},18374:(e,s,t)=>{Promise.resolve().then(t.bind(t,4595))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},25541:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},26134:(e,s,t)=>{"use strict";t.d(s,{G$:()=>V,Hs:()=>v,UC:()=>et,VY:()=>el,ZL:()=>ee,bL:()=>Y,bm:()=>er,hE:()=>ea,hJ:()=>es,l9:()=>Q});var a=t(43210),l=t(70569),r=t(98599),i=t(11273),n=t(96963),c=t(65551),d=t(31355),o=t(32547),x=t(25028),m=t(46059),h=t(14163),u=t(1359),p=t(42247),g=t(63376),j=t(8730),f=t(60687),b="Dialog",[N,v]=(0,i.A)(b),[w,y]=N(b),k=e=>{let{__scopeDialog:s,children:t,open:l,defaultOpen:r,onOpenChange:i,modal:d=!0}=e,o=a.useRef(null),x=a.useRef(null),[m,h]=(0,c.i)({prop:l,defaultProp:r??!1,onChange:i,caller:b});return(0,f.jsx)(w,{scope:s,triggerRef:o,contentRef:x,contentId:(0,n.B)(),titleId:(0,n.B)(),descriptionId:(0,n.B)(),open:m,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),modal:d,children:t})};k.displayName=b;var C="DialogTrigger",A=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,i=y(C,t),n=(0,r.s)(s,i.triggerRef);return(0,f.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":B(i.open),...a,ref:n,onClick:(0,l.m)(e.onClick,i.onOpenToggle)})});A.displayName=C;var R="DialogPortal",[D,S]=N(R,{forceMount:void 0}),M=e=>{let{__scopeDialog:s,forceMount:t,children:l,container:r}=e,i=y(R,s);return(0,f.jsx)(D,{scope:s,forceMount:t,children:a.Children.map(l,e=>(0,f.jsx)(m.C,{present:t||i.open,children:(0,f.jsx)(x.Z,{asChild:!0,container:r,children:e})}))})};M.displayName=R;var _="DialogOverlay",I=a.forwardRef((e,s)=>{let t=S(_,e.__scopeDialog),{forceMount:a=t.forceMount,...l}=e,r=y(_,e.__scopeDialog);return r.modal?(0,f.jsx)(m.C,{present:a||r.open,children:(0,f.jsx)(P,{...l,ref:s})}):null});I.displayName=_;var T=(0,j.TL)("DialogOverlay.RemoveScroll"),P=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,l=y(_,t);return(0,f.jsx)(p.A,{as:T,allowPinchZoom:!0,shards:[l.contentRef],children:(0,f.jsx)(h.sG.div,{"data-state":B(l.open),...a,ref:s,style:{pointerEvents:"auto",...a.style}})})}),E="DialogContent",Z=a.forwardRef((e,s)=>{let t=S(E,e.__scopeDialog),{forceMount:a=t.forceMount,...l}=e,r=y(E,e.__scopeDialog);return(0,f.jsx)(m.C,{present:a||r.open,children:r.modal?(0,f.jsx)($,{...l,ref:s}):(0,f.jsx)(O,{...l,ref:s})})});Z.displayName=E;var $=a.forwardRef((e,s)=>{let t=y(E,e.__scopeDialog),i=a.useRef(null),n=(0,r.s)(s,t.contentRef,i);return a.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,f.jsx)(q,{...e,ref:n,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,t=0===s.button&&!0===s.ctrlKey;(2===s.button||t)&&e.preventDefault()}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault())})}),O=a.forwardRef((e,s)=>{let t=y(E,e.__scopeDialog),l=a.useRef(!1),r=a.useRef(!1);return(0,f.jsx)(q,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(l.current||t.triggerRef.current?.focus(),s.preventDefault()),l.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(l.current=!0,"pointerdown"!==s.detail.originalEvent.type||(r.current=!0));let a=s.target;t.triggerRef.current?.contains(a)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&r.current&&s.preventDefault()}})}),q=a.forwardRef((e,s)=>{let{__scopeDialog:t,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:n,...c}=e,x=y(E,t),m=a.useRef(null),h=(0,r.s)(s,m);return(0,u.Oh)(),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(o.n,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:i,onUnmountAutoFocus:n,children:(0,f.jsx)(d.qW,{role:"dialog",id:x.contentId,"aria-describedby":x.descriptionId,"aria-labelledby":x.titleId,"data-state":B(x.open),...c,ref:h,onDismiss:()=>x.onOpenChange(!1)})}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(X,{titleId:x.titleId}),(0,f.jsx)(K,{contentRef:m,descriptionId:x.descriptionId})]})]})}),z="DialogTitle",F=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,l=y(z,t);return(0,f.jsx)(h.sG.h2,{id:l.titleId,...a,ref:s})});F.displayName=z;var L="DialogDescription",U=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,l=y(L,t);return(0,f.jsx)(h.sG.p,{id:l.descriptionId,...a,ref:s})});U.displayName=L;var W="DialogClose",J=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=y(W,t);return(0,f.jsx)(h.sG.button,{type:"button",...a,ref:s,onClick:(0,l.m)(e.onClick,()=>r.onOpenChange(!1))})});function B(e){return e?"open":"closed"}J.displayName=W;var G="DialogTitleWarning",[V,H]=(0,i.q)(G,{contentName:E,titleName:z,docsSlug:"dialog"}),X=({titleId:e})=>{let s=H(G),t=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return a.useEffect(()=>{e&&!document.getElementById(e)&&console.error(t)},[t,e]),null},K=({contentRef:e,descriptionId:s})=>{let t=H("DialogDescriptionWarning"),l=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return a.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");s&&t&&!document.getElementById(s)&&console.warn(l)},[l,e,s]),null},Y=k,Q=A,ee=M,es=I,et=Z,ea=F,el=U,er=J},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37826:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>m,L3:()=>u,c7:()=>h,lG:()=>c,rr:()=>p,zM:()=>d});var a=t(60687),l=t(43210),r=t(26134),i=t(11860),n=t(96241);let c=r.bL,d=r.l9,o=r.ZL;r.bm;let x=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.hJ,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));x.displayName=r.hJ.displayName;let m=l.forwardRef(({className:e,children:s,...t},l)=>(0,a.jsxs)(o,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(r.UC,{ref:l,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=r.UC.displayName;let h=({className:e,...s})=>(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});h.displayName="DialogHeader";let u=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.hE,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));u.displayName=r.hE.displayName;let p=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.VY,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));p.displayName=r.VY.displayName},40083:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},40214:(e,s,t)=>{"use strict";t.d(s,{d:()=>y});var a=t(60687),l=t(43210),r=t(70569),i=t(98599),n=t(11273),c=t(65551),d=t(83721),o=t(18853),x=t(14163),m="Switch",[h,u]=(0,n.A)(m),[p,g]=h(m),j=l.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:d,defaultChecked:o,required:h,disabled:u,value:g="on",onCheckedChange:j,form:f,...b}=e,[w,y]=l.useState(null),k=(0,i.s)(s,e=>y(e)),C=l.useRef(!1),A=!w||f||!!w.closest("form"),[R,D]=(0,c.i)({prop:d,defaultProp:o??!1,onChange:j,caller:m});return(0,a.jsxs)(p,{scope:t,checked:R,disabled:u,children:[(0,a.jsx)(x.sG.button,{type:"button",role:"switch","aria-checked":R,"aria-required":h,"data-state":v(R),"data-disabled":u?"":void 0,disabled:u,value:g,...b,ref:k,onClick:(0,r.m)(e.onClick,e=>{D(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,a.jsx)(N,{control:w,bubbles:!C.current,name:n,value:g,checked:R,required:h,disabled:u,form:f,style:{transform:"translateX(-100%)"}})]})});j.displayName=m;var f="SwitchThumb",b=l.forwardRef((e,s)=>{let{__scopeSwitch:t,...l}=e,r=g(f,t);return(0,a.jsx)(x.sG.span,{"data-state":v(r.checked),"data-disabled":r.disabled?"":void 0,...l,ref:s})});b.displayName=f;var N=l.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:r=!0,...n},c)=>{let x=l.useRef(null),m=(0,i.s)(x,c),h=(0,d.Z)(t),u=(0,o.X)(s);return l.useEffect(()=>{let e=x.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==t&&s){let a=new Event("click",{bubbles:r});s.call(e,t),e.dispatchEvent(a)}},[h,t,r]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:m,style:{...n.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}N.displayName="SwitchBubbleInput";var w=t(96241);let y=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(j,{className:(0,w.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:(0,a.jsx)(b,{className:(0,w.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));y.displayName=j.displayName},49441:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\app\\admin\\page.tsx","default")},55192:(e,s,t)=>{"use strict";t.d(s,{Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>n});var a=t(60687),l=t(43210),r=t(96241);let i=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle",l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription";let d=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",e),...s}));d.displayName="CardContent",l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63974:(e,s,t)=>{"use strict";t.d(s,{bq:()=>m,eb:()=>g,gC:()=>p,l6:()=>o,yv:()=>x});var a=t(60687),l=t(43210),r=t(25911),i=t(78272),n=t(3589),c=t(13964),d=t(96241);let o=r.bL;r.YJ;let x=r.WT,m=l.forwardRef(({className:e,children:s,...t},l)=>(0,a.jsxs)(r.l9,{ref:l,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=r.l9.displayName;let h=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=r.PP.displayName;let u=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=r.wn.displayName;let p=l.forwardRef(({className:e,children:s,position:t="popper",...l},i)=>(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...l,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(u,{})]})}));p.displayName=r.UC.displayName,l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=r.JU.displayName;let g=l.forwardRef(({className:e,children:s,...t},l)=>(0,a.jsxs)(r.q7,{ref:l,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsx)(r.p4,{children:s})]}));g.displayName=r.q7.displayName,l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=r.wv.displayName},65668:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},68080:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(65239),l=t(48088),r=t(88170),i=t.n(r),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,49441)),"D:\\VS-projects\\try\\alraya-store\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"D:\\VS-projects\\try\\alraya-store\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["D:\\VS-projects\\try\\alraya-store\\app\\admin\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70373:(e,s,t)=>{"use strict";t.d(s,{BK:()=>c,eu:()=>n,q5:()=>d});var a=t(60687),l=t(43210),r=t(11096),i=t(96241);let n=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.bL,{ref:t,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...s}));n.displayName=r.bL.displayName;let c=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r._V,{ref:t,className:(0,i.cn)("aspect-square h-full w-full",e),...s}));c.displayName=r._V.displayName;let d=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.H4,{ref:t,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...s}));d.displayName=r.H4.displayName},70615:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},75034:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},78206:(e,s,t)=>{Promise.resolve().then(t.bind(t,49441))},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},97051:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,228,935,984,510,485,50,840,540],()=>t(68080));module.exports=a})();