"use client"

import { CheckoutConfig, BankAccount, RechargeOption, CheckoutOrder } from "@/lib/types"

// ## LocalStorage keys for checkout system
const CHECKOUT_CONFIG_KEY = "checkout_config"
const CHECKOUT_ORDERS_KEY = "checkout_orders"

// ## Default checkout configuration
const defaultCheckoutConfig: CheckoutConfig = {
  bankAccounts: [
    {
      id: "bank_001",
      name: "بنك الخرطوم",
      accountNumber: "****************",
      logoUrl: "/banks/khartoum-bank.png",
      isActive: true
    },
    {
      id: "bank_002", 
      name: "بنك فيصل الإسلامي",
      accountNumber: "****************",
      logoUrl: "/banks/faisal-bank.png",
      isActive: true
    },
    {
      id: "bank_003",
      name: "بنك السودان المركزي",
      accountNumber: "****************",
      logoUrl: "/banks/central-bank.png",
      isActive: true
    }
  ],
  rechargeOptions: [
    { id: "amount_001", amount: 10000, currency: "SDG", isActive: true },
    { id: "amount_002", amount: 25000, currency: "SDG", isActive: true },
    { id: "amount_003", amount: 50000, currency: "SDG", isActive: true },
    { id: "amount_004", amount: 100000, currency: "SDG", isActive: true },
    { id: "amount_005", amount: 50, currency: "EGP", isActive: true },
    { id: "amount_006", amount: 100, currency: "EGP", isActive: true },
    { id: "amount_007", amount: 250, currency: "EGP", isActive: true },
    { id: "amount_008", amount: 500, currency: "EGP", isActive: true }
  ],
  notes: [
    "يرجى التأكد من صحة رقم المرجع قبل الإرسال",
    "سيتم مراجعة طلبك خلال 24 ساعة",
    "احتفظ بإيصال التحويل للمراجعة"
  ],
  lastUpdated: new Date()
}

// ## Get checkout configuration from localStorage
export function getCheckoutConfig(): CheckoutConfig {
  if (typeof window === "undefined") return defaultCheckoutConfig
  
  try {
    const stored = localStorage.getItem(CHECKOUT_CONFIG_KEY)
    if (!stored) {
      // Initialize with default config
      setCheckoutConfig(defaultCheckoutConfig)
      return defaultCheckoutConfig
    }
    
    const parsed = JSON.parse(stored)
    // Convert date strings back to Date objects
    parsed.lastUpdated = new Date(parsed.lastUpdated)
    return parsed
  } catch (error) {
    console.error("Error loading checkout config:", error)
    return defaultCheckoutConfig
  }
}

// ## Save checkout configuration to localStorage
export function setCheckoutConfig(config: CheckoutConfig): void {
  if (typeof window === "undefined") return
  
  try {
    const configToStore = {
      ...config,
      lastUpdated: new Date()
    }
    localStorage.setItem(CHECKOUT_CONFIG_KEY, JSON.stringify(configToStore))
  } catch (error) {
    console.error("Error saving checkout config:", error)
  }
}

// ## Add new bank account
export function addBankAccount(bank: Omit<BankAccount, "id">): BankAccount {
  const config = getCheckoutConfig()
  const newBank: BankAccount = {
    ...bank,
    id: `bank_${Date.now()}`
  }
  
  config.bankAccounts.push(newBank)
  setCheckoutConfig(config)
  return newBank
}

// ## Update bank account
export function updateBankAccount(bankId: string, updates: Partial<BankAccount>): void {
  const config = getCheckoutConfig()
  const bankIndex = config.bankAccounts.findIndex(bank => bank.id === bankId)
  
  if (bankIndex !== -1) {
    config.bankAccounts[bankIndex] = { ...config.bankAccounts[bankIndex], ...updates }
    setCheckoutConfig(config)
  }
}

// ## Delete bank account
export function deleteBankAccount(bankId: string): void {
  const config = getCheckoutConfig()
  config.bankAccounts = config.bankAccounts.filter(bank => bank.id !== bankId)
  setCheckoutConfig(config)
}

// ## Add recharge option
export function addRechargeOption(option: Omit<RechargeOption, "id">): RechargeOption {
  const config = getCheckoutConfig()
  const newOption: RechargeOption = {
    ...option,
    id: `amount_${Date.now()}`
  }
  
  config.rechargeOptions.push(newOption)
  setCheckoutConfig(config)
  return newOption
}

// ## Update recharge option
export function updateRechargeOption(optionId: string, updates: Partial<RechargeOption>): void {
  const config = getCheckoutConfig()
  const optionIndex = config.rechargeOptions.findIndex(option => option.id === optionId)
  
  if (optionIndex !== -1) {
    config.rechargeOptions[optionIndex] = { ...config.rechargeOptions[optionIndex], ...updates }
    setCheckoutConfig(config)
  }
}

// ## Delete recharge option
export function deleteRechargeOption(optionId: string): void {
  const config = getCheckoutConfig()
  config.rechargeOptions = config.rechargeOptions.filter(option => option.id !== optionId)
  setCheckoutConfig(config)
}

// ## Get active bank accounts
export function getActiveBankAccounts(): BankAccount[] {
  const config = getCheckoutConfig()
  return config.bankAccounts.filter(bank => bank.isActive)
}

// ## Get active recharge options for currency
export function getActiveRechargeOptions(currency: "SDG" | "EGP"): RechargeOption[] {
  const config = getCheckoutConfig()
  return config.rechargeOptions.filter(option =>
    option.isActive && option.currency === currency
  )
}

// ## Checkout Orders Management

// ## Get all checkout orders from localStorage
export function getCheckoutOrders(): CheckoutOrder[] {
  if (typeof window === "undefined") return []

  try {
    const stored = localStorage.getItem(CHECKOUT_ORDERS_KEY)
    if (!stored) return []

    const parsed = JSON.parse(stored)
    // Convert date strings back to Date objects
    return parsed.map((order: any) => ({
      ...order,
      createdAt: new Date(order.createdAt)
    }))
  } catch (error) {
    console.error("Error loading checkout orders:", error)
    return []
  }
}

// ## Save checkout order to localStorage
export function saveCheckoutOrder(order: CheckoutOrder): void {
  if (typeof window === "undefined") return

  try {
    const orders = getCheckoutOrders()
    orders.push(order)
    localStorage.setItem(CHECKOUT_ORDERS_KEY, JSON.stringify(orders))
  } catch (error) {
    console.error("Error saving checkout order:", error)
  }
}

// ## Get checkout order by ID
export function getCheckoutOrderById(orderId: string): CheckoutOrder | null {
  const orders = getCheckoutOrders()
  return orders.find(order => order.id === orderId) || null
}

// ## Update checkout order status
export function updateCheckoutOrderStatus(orderId: string, status: CheckoutOrder["status"]): void {
  if (typeof window === "undefined") return

  try {
    const orders = getCheckoutOrders()
    const orderIndex = orders.findIndex(order => order.id === orderId)

    if (orderIndex !== -1) {
      orders[orderIndex].status = status
      localStorage.setItem(CHECKOUT_ORDERS_KEY, JSON.stringify(orders))
    }
  } catch (error) {
    console.error("Error updating checkout order status:", error)
  }
}
