import { useCart, CartItem as CartItemType } from "@/contexts/CartContext";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";

interface CartItemProps {
  item: CartItemType;
}

const CartItem = ({ item }: CartItemProps) => {
  const { removeItem } = useCart();
  const isMobile = useIsMobile();

  const handleRemove = () => {
    removeItem(item.id);
  };

  // Check if the item is an account or UC package based on its properties
  const isAccount = item.type === 'account';
  
  return (
    <div className="glass-card rounded-lg p-3 sm:p-4 flex items-center">
      <div className="w-12 h-12 sm:w-16 sm:h-16 ml-2 sm:ml-4 shrink-0">
        <img
          src={item.image}
          alt={item.title}
          className="w-full h-full object-cover rounded-md"
        />
      </div>
      <div className="flex-grow overflow-hidden">
        <h3 className="text-sm sm:text-base text-white font-medium mb-0 sm:mb-1 truncate">
          {item.title}
        </h3>
        {isAccount && !isMobile && (
          <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1 hidden sm:block">
            {/* Account description if available */}
          </p>
        )}
      </div>
      <div className="flex items-center ml-2 sm:ml-4">
        <span className="text-pubg-orange font-bold text-sm sm:text-base ml-2 sm:ml-6 whitespace-nowrap">
          {item.priceEGP} EGP
        </span>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleRemove}
          className="text-muted-foreground hover:text-red-500 hover:bg-red-500/10 h-8 w-8 sm:h-9 sm:w-9 p-0"
        >
          <Trash2 size={isMobile ? 16 : 18} />
        </Button>
      </div>
    </div>
  );
};

export default CartItem;
