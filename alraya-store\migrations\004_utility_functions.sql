-- =====================================================
-- Al-Raya Store Multi-Currency Platform Migration
-- Phase 4: Utility Functions and Triggers
-- =====================================================

-- =====================================================
-- 1. CURRENCY CONVERSION FUNCTIONS
-- =====================================================

-- Get current exchange rate between two currencies
CREATE OR REPLACE FUNCTION get_exchange_rate(
  p_from_currency VARCHAR(3),
  p_to_currency VARCHAR(3)
)
RETURNS DECIMAL(18,8) AS $$
DECLARE
  v_rate DECIMAL(18,8);
  v_usd_from_rate DECIMAL(18,8);
  v_usd_to_rate DECIMAL(18,8);
BEGIN
  -- If same currency, return 1
  IF p_from_currency = p_to_currency THEN
    RETURN 1.0;
  END IF;
  
  -- Try direct rate first
  SELECT rate INTO v_rate
  FROM exchange_rates
  WHERE from_currency_code = p_from_currency
    AND to_currency_code = p_to_currency
    AND is_active = true
  ORDER BY effective_date DESC
  LIMIT 1;
  
  IF v_rate IS NOT NULL THEN
    RETURN v_rate;
  END IF;
  
  -- Calculate via USD if direct rate not available
  SELECT rate INTO v_usd_from_rate
  FROM exchange_rates
  WHERE from_currency_code = 'USD'
    AND to_currency_code = p_from_currency
    AND is_active = true
  ORDER BY effective_date DESC
  LIMIT 1;
  
  SELECT rate INTO v_usd_to_rate
  FROM exchange_rates
  WHERE from_currency_code = 'USD'
    AND to_currency_code = p_to_currency
    AND is_active = true
  ORDER BY effective_date DESC
  LIMIT 1;
  
  IF v_usd_from_rate IS NOT NULL AND v_usd_to_rate IS NOT NULL THEN
    RETURN v_usd_to_rate / v_usd_from_rate;
  END IF;
  
  -- If no rate found, raise exception
  RAISE EXCEPTION 'Exchange rate not found for % to %', p_from_currency, p_to_currency;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Convert amount between currencies
CREATE OR REPLACE FUNCTION convert_currency(
  p_amount DECIMAL(18,8),
  p_from_currency VARCHAR(3),
  p_to_currency VARCHAR(3)
)
RETURNS DECIMAL(18,8) AS $$
DECLARE
  v_rate DECIMAL(18,8);
BEGIN
  v_rate := get_exchange_rate(p_from_currency, p_to_currency);
  RETURN p_amount * v_rate;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 2. WALLET MANAGEMENT FUNCTIONS
-- =====================================================

-- Get or create user wallet for specific currency
CREATE OR REPLACE FUNCTION get_or_create_wallet(
  p_user_id UUID,
  p_currency_code VARCHAR(3)
)
RETURNS UUID AS $$
DECLARE
  v_wallet_id UUID;
BEGIN
  -- Try to get existing wallet
  SELECT id INTO v_wallet_id
  FROM user_wallets_new
  WHERE user_id = p_user_id AND currency_code = p_currency_code;
  
  -- Create wallet if it doesn't exist
  IF v_wallet_id IS NULL THEN
    INSERT INTO user_wallets_new (user_id, currency_code)
    VALUES (p_user_id, p_currency_code)
    RETURNING id INTO v_wallet_id;
  END IF;
  
  RETURN v_wallet_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update wallet balance
CREATE OR REPLACE FUNCTION update_wallet_balance(
  p_user_id UUID,
  p_currency_code VARCHAR(3),
  p_amount DECIMAL(18,8)
)
RETURNS BOOLEAN AS $$
DECLARE
  v_wallet_id UUID;
  v_new_balance DECIMAL(18,8);
BEGIN
  -- Get or create wallet
  v_wallet_id := get_or_create_wallet(p_user_id, p_currency_code);
  
  -- Update balance
  UPDATE user_wallets_new
  SET 
    balance = balance + p_amount,
    updated_at = NOW(),
    last_transaction_at = NOW()
  WHERE id = v_wallet_id
  RETURNING balance INTO v_new_balance;
  
  -- Check for negative balance
  IF v_new_balance < 0 THEN
    RAISE EXCEPTION 'Insufficient balance. Current balance: %, Attempted change: %', 
      v_new_balance - p_amount, p_amount;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Convert wallet balance between currencies
CREATE OR REPLACE FUNCTION convert_wallet_balance(
  p_user_id UUID,
  p_from_currency VARCHAR(3),
  p_to_currency VARCHAR(3),
  p_amount DECIMAL(18,8)
)
RETURNS JSONB AS $$
DECLARE
  v_from_wallet_id UUID;
  v_to_wallet_id UUID;
  v_exchange_rate DECIMAL(18,8);
  v_converted_amount DECIMAL(18,8);
  v_conversion_fee DECIMAL(18,8) := 0.00; -- Can be configured
  v_transaction_id UUID;
  v_result JSONB;
BEGIN
  -- Validate amount
  IF p_amount <= 0 THEN
    RAISE EXCEPTION 'Amount must be positive';
  END IF;
  
  -- Get exchange rate
  v_exchange_rate := get_exchange_rate(p_from_currency, p_to_currency);
  v_converted_amount := p_amount * v_exchange_rate;
  
  -- Get wallets
  v_from_wallet_id := get_or_create_wallet(p_user_id, p_from_currency);
  v_to_wallet_id := get_or_create_wallet(p_user_id, p_to_currency);
  
  -- Check sufficient balance
  IF (SELECT balance FROM user_wallets_new WHERE id = v_from_wallet_id) < p_amount THEN
    RAISE EXCEPTION 'Insufficient balance for conversion';
  END IF;
  
  -- Start transaction
  BEGIN
    -- Deduct from source wallet
    PERFORM update_wallet_balance(p_user_id, p_from_currency, -p_amount);
    
    -- Add to target wallet (minus fee)
    PERFORM update_wallet_balance(p_user_id, p_to_currency, v_converted_amount - v_conversion_fee);
    
    -- Record conversion transaction
    INSERT INTO wallet_transactions_new (
      user_id,
      wallet_id,
      transaction_type,
      amount,
      currency_code,
      original_amount,
      original_currency_code,
      exchange_rate,
      conversion_fee,
      description,
      status
    ) VALUES (
      p_user_id,
      v_to_wallet_id,
      'currency_conversion',
      v_converted_amount - v_conversion_fee,
      p_to_currency,
      p_amount,
      p_from_currency,
      v_exchange_rate,
      v_conversion_fee,
      FORMAT('Converted %s %s to %s %s', p_amount, p_from_currency, v_converted_amount, p_to_currency),
      'completed'
    ) RETURNING id INTO v_transaction_id;
    
    -- Build result
    v_result := jsonb_build_object(
      'success', true,
      'transaction_id', v_transaction_id,
      'original_amount', p_amount,
      'original_currency', p_from_currency,
      'converted_amount', v_converted_amount - v_conversion_fee,
      'target_currency', p_to_currency,
      'exchange_rate', v_exchange_rate,
      'conversion_fee', v_conversion_fee
    );
    
    RETURN v_result;
    
  EXCEPTION WHEN OTHERS THEN
    -- Rollback and re-raise
    RAISE;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. ORDER PRICING FUNCTIONS
-- =====================================================

-- Calculate order price in target currency
CREATE OR REPLACE FUNCTION calculate_order_price(
  p_base_price_usd DECIMAL(18,8),
  p_target_currency VARCHAR(3)
)
RETURNS JSONB AS $$
DECLARE
  v_exchange_rate DECIMAL(18,8);
  v_target_price DECIMAL(18,8);
  v_result JSONB;
BEGIN
  -- Get exchange rate from USD to target currency
  v_exchange_rate := get_exchange_rate('USD', p_target_currency);
  v_target_price := p_base_price_usd * v_exchange_rate;
  
  v_result := jsonb_build_object(
    'base_price_usd', p_base_price_usd,
    'target_currency', p_target_currency,
    'exchange_rate', v_exchange_rate,
    'target_price', v_target_price,
    'rate_timestamp', NOW()
  );
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. REVENUE CALCULATION FUNCTIONS
-- =====================================================

-- Calculate revenue in primary currency for date range
CREATE OR REPLACE FUNCTION calculate_revenue_consolidated(
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_primary_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS JSONB AS $$
DECLARE
  v_start_date TIMESTAMP WITH TIME ZONE;
  v_end_date TIMESTAMP WITH TIME ZONE;
  v_revenue_by_currency JSONB := '{}';
  v_total_revenue DECIMAL(18,8) := 0;
  v_order_record RECORD;
  v_converted_amount DECIMAL(18,8);
  v_result JSONB;
BEGIN
  -- Set default date range if not provided
  v_start_date := COALESCE(p_start_date, DATE_TRUNC('month', NOW()));
  v_end_date := COALESCE(p_end_date, NOW());
  
  -- Calculate revenue by original currency and convert to primary currency
  FOR v_order_record IN
    SELECT 
      currency_code,
      SUM(total_price) as revenue
    FROM product_orders_new
    WHERE status = 'completed'
      AND completed_at >= v_start_date
      AND completed_at <= v_end_date
    GROUP BY currency_code
  LOOP
    -- Store original currency revenue
    v_revenue_by_currency := v_revenue_by_currency || 
      jsonb_build_object(v_order_record.currency_code, v_order_record.revenue);
    
    -- Convert to primary currency and add to total
    v_converted_amount := convert_currency(
      v_order_record.revenue,
      v_order_record.currency_code,
      p_primary_currency
    );
    v_total_revenue := v_total_revenue + v_converted_amount;
  END LOOP;
  
  -- Build result
  v_result := jsonb_build_object(
    'period_start', v_start_date,
    'period_end', v_end_date,
    'primary_currency', p_primary_currency,
    'revenue_by_currency', v_revenue_by_currency,
    'total_revenue_primary', v_total_revenue,
    'calculated_at', NOW()
  );
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. AUDIT TRIGGER FUNCTIONS
-- =====================================================

-- Generic audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'DELETE' THEN
    INSERT INTO currency_audit_log (
      table_name,
      record_id,
      action,
      old_values,
      changed_by,
      changed_at
    ) VALUES (
      TG_TABLE_NAME,
      OLD.id,
      TG_OP,
      to_jsonb(OLD),
      auth.uid(),
      NOW()
    );
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO currency_audit_log (
      table_name,
      record_id,
      action,
      old_values,
      new_values,
      changed_by,
      changed_at
    ) VALUES (
      TG_TABLE_NAME,
      NEW.id,
      TG_OP,
      to_jsonb(OLD),
      to_jsonb(NEW),
      auth.uid(),
      NOW()
    );
    RETURN NEW;
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO currency_audit_log (
      table_name,
      record_id,
      action,
      new_values,
      changed_by,
      changed_at
    ) VALUES (
      TG_TABLE_NAME,
      NEW.id,
      TG_OP,
      to_jsonb(NEW),
      auth.uid(),
      NOW()
    );
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. CREATE AUDIT TRIGGERS
-- =====================================================

-- Audit triggers for critical tables
CREATE TRIGGER audit_currencies_trigger
  AFTER INSERT OR UPDATE OR DELETE ON currencies
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_exchange_rates_trigger
  AFTER INSERT OR UPDATE OR DELETE ON exchange_rates
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_client_currency_settings_trigger
  AFTER INSERT OR UPDATE OR DELETE ON client_currency_settings
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- =====================================================
-- 7. UPDATE TIMESTAMP TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create update triggers for tables with updated_at columns
CREATE TRIGGER update_currencies_updated_at
  BEFORE UPDATE ON currencies
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_wallets_updated_at
  BEFORE UPDATE ON user_wallets_new
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_orders_updated_at
  BEFORE UPDATE ON product_orders_new
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_currency_settings_updated_at
  BEFORE UPDATE ON client_currency_settings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 8. GRANT EXECUTE PERMISSIONS
-- =====================================================

GRANT EXECUTE ON FUNCTION get_exchange_rate(VARCHAR(3), VARCHAR(3)) TO authenticated;
GRANT EXECUTE ON FUNCTION convert_currency(DECIMAL(18,8), VARCHAR(3), VARCHAR(3)) TO authenticated;
GRANT EXECUTE ON FUNCTION get_or_create_wallet(UUID, VARCHAR(3)) TO authenticated;
GRANT EXECUTE ON FUNCTION update_wallet_balance(UUID, VARCHAR(3), DECIMAL(18,8)) TO authenticated;
GRANT EXECUTE ON FUNCTION convert_wallet_balance(UUID, VARCHAR(3), VARCHAR(3), DECIMAL(18,8)) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_order_price(DECIMAL(18,8), VARCHAR(3)) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_revenue_consolidated(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, VARCHAR(3)) TO authenticated;
