import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Plus, Pencil, Trash2, Save, X, Sparkles, Upload, RefreshCw, Globe as GlobeIcon, ImageIcon, Bot } from "lucide-react";
import { BlogPostModel, getBlogPosts, addBlogPost, updateBlogPost, deleteBlogPost, updateAllBlogPostSlugs } from "@/services/firestore";
import { parseBlogPostDetails, getSavedModel } from "@/services/gemini";
import { uploadImage } from "@/services/imgbb";
import ModelSelector from "./ModelSelector";
import ImageUploader from "./ImageUploader";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  <PERSON>ert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { siteColors, buttonVariants } from "@/lib/theme";

// Create a custom clearable input component
const ClearableInput = ({ 
  value, 
  onChange, 
  onClear, 
  ...props 
}: { 
  value: string | number; 
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; 
  onClear: () => void; 
} & React.InputHTMLAttributes<HTMLInputElement>) => {
  return (
    <div className="relative">
      <Input
        value={value}
        onChange={onChange}
        className="pr-8"
        {...props}
      />
      {value && (
        <button
          type="button"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-5 w-5 rounded-full bg-gray-500/20 flex items-center justify-center text-gray-400 hover:bg-gray-500/30 hover:text-gray-100"
          onClick={onClear}
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </div>
  );
};

const BlogManager = () => {
  const { toast } = useToast();
  const [posts, setPosts] = useState<BlogPostModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingPost, setEditingPost] = useState<BlogPostModel | null>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [telegramPost, setTelegramPost] = useState("");
  const [isAiProcessing, setIsAiProcessing] = useState(false);
  const [selectedModel, setSelectedModel] = useState(getSavedModel());
  const [formData, setFormData] = useState<Partial<BlogPostModel>>({
    title: "",
    title_en: "",
    excerpt: "",
    excerpt_en: "",
    content: "",
    content_en: "",
    image: "",
    author: "",
    author_en: "",
    date: new Date().toISOString().split('T')[0],
    slug: "",
    slug_en: "",
    featured: false,
    special: false,
  });
  const [isUploading, setIsUploading] = useState(false);
  const [isUpdatingSlugs, setIsUpdatingSlugs] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const postsData = await getBlogPosts();
        setPosts(postsData);
        setIsLoading(false);
        
        // Silent update of all slugs on component mount to ensure consistency
        try {
          console.log("BlogManager: Automatically updating all blog post slugs on mount");
          await updateAllBlogPostSlugs();
          console.log("BlogManager: Successfully updated all blog post slugs");
          // No need to show a notification for this background operation
        } catch (error) {
          console.error("BlogManager: Error updating slugs on mount:", error);
          // Don't show an error toast to avoid confusion
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load blog posts data",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      setFormData({ ...formData, [name]: (e.target as HTMLInputElement).checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleTelegramPostChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTelegramPost(e.target.value);
  };

  const handleProcessWithAI = async () => {
    if (!telegramPost.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى لصق محتوى منشور تيليجرام أولاً",
        variant: "destructive",
      });
      return;
    }

    setIsAiProcessing(true);
    try {
      const parsedData = await parseBlogPostDetails(telegramPost, selectedModel);
      setFormData({
        ...formData,
        ...parsedData
      });
      
      toast({
        title: "تم بنجاح",
        description: "تم معالجة المنشور وملء النموذج بالبيانات المنسقة",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في المعالجة بالذكاء الاصطناعي: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setIsAiProcessing(false);
    }
  };

  // Generate a slug from the title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  };

  // Update slug when title changes
  useEffect(() => {
    if (formData.title && !editingPost) {
      setFormData(prev => ({ ...prev, slug: generateSlug(prev.title || '') }));
    }
  }, [formData.title, editingPost]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form - ensure an image is provided
    if (!formData.image) {
      toast({
        title: "خطأ",
        description: "يرجى رفع صورة للمقال",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);

    try {
      // Ensure a slug is provided, or generate one from the title
      const dataToSubmit = {
        ...formData,
        slug: formData.slug?.trim() || generateSlug(formData.title)
      };

      if (editingPost) {
        // Update existing post
        await updateBlogPost(editingPost.id!, dataToSubmit);
        
        // Update local state
        setPosts(
          posts.map((post) =>
            post.id === editingPost.id
              ? { ...post, ...dataToSubmit }
              : post
          )
        );
        
        toast({
          title: "Success",
          description: "Blog post updated successfully",
        });
        
        setEditingPost(null);
      } else {
        // Add new post
        const id = await addBlogPost(dataToSubmit as Omit<BlogPostModel, "id" | "createdAt" | "updatedAt">);
        
        // Update local state
        setPosts([...posts, { ...dataToSubmit, id } as BlogPostModel]);
        
        toast({
          title: "Success",
          description: "Blog post added successfully",
        });
      }
      
      // Reset form and close dialog
      resetForm();
      setShowDialog(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save blog post",
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  const resetForm = () => {
    setFormData({
      title: "",
      excerpt: "",
      content: "",
      image: "",
      author: "",
      date: new Date().toISOString().split('T')[0],
      slug: "",
      featured: false,
      special: false,
    });
    setTelegramPost("");
  };

  const handleEdit = (post: BlogPostModel) => {
    setEditingPost(post);
    setFormData({
      title: post.title,
      title_en: post.title_en || "",
      excerpt: post.excerpt,
      excerpt_en: post.excerpt_en || "",
      content: post.content,
      content_en: post.content_en || "",
      image: post.image,
      author: post.author,
      author_en: post.author_en || "",
      date: post.date,
      slug: post.slug,
      slug_en: post.slug_en || "",
      featured: post.featured || false,
      special: post.special || false,
    });
    setShowDialog(true);
    setTelegramPost("");
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this blog post?")) {
      setIsLoading(true);
      
      try {
        await deleteBlogPost(id);
        
        // Update local state
        setPosts(posts.filter((post) => post.id !== id));
        
        toast({
          title: "Success",
          description: "Blog post deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete blog post",
          variant: "destructive",
        });
      }
      
      setIsLoading(false);
    }
  };

  const resetFormAndClose = () => {
    setEditingPost(null);
    setShowDialog(false);
    resetForm();
  };

  const handleUpdateAllSlugs = async () => {
    setIsUpdatingSlugs(true);
    
    try {
      await updateAllBlogPostSlugs();
      
      // Refresh the posts list
      const updatedPosts = await getBlogPosts();
      setPosts(updatedPosts);
      
      toast({
        title: "Success",
        description: "All blog post slugs have been updated",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update blog post slugs",
        variant: "destructive",
      });
    }
    
    setIsUpdatingSlugs(false);
  };

  const handleImageUpload = async (imageData: string) => {
    if (!imageData) return;
    
    setIsUploading(true);
    
    try {
      // Convert the base64 string to a File object
      const blob = await fetch(imageData).then(res => res.blob());
      const file = new File([blob], "image.jpg", { type: "image/jpeg" });
      
      const imageUrl = await uploadImage(file);
      
      if (imageUrl) {
        setFormData({
          ...formData,
          image: imageUrl
        });
        
        toast({
          title: "Success",
          description: "Image uploaded successfully",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upload image",
        variant: "destructive",
      });
    }
    
    setIsUploading(false);
  };

  if (isLoading && posts.length === 0) {
    return <div className="text-center py-8">Loading blog posts data...</div>;
  }

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h2 className="text-xl font-bold text-white mb-1">إدارة المقالات</h2>
          <p className="text-sm text-muted-foreground">إضافة وتعديل وحذف المقالات في الموقع</p>
        </div>
        <div className="flex flex-wrap gap-2 justify-end w-full sm:w-auto">
          <Button
            onClick={() => {
              resetForm();
              setShowDialog(true);
            }}
            className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 h-8 px-2 py-1 text-xs sm:text-sm"
          >
            <Plus className="ml-1 h-3 w-3" />
            إضافة مقال جديد
          </Button>
          
          <Button
            onClick={handleUpdateAllSlugs}
            disabled={isUpdatingSlugs}
            variant="outline"
            className="text-pubg-orange border-pubg-orange/30 hover:text-pubg-orange/80 hover:border-pubg-orange/80 h-8 px-2 py-1 text-xs sm:text-sm"
          >
            <RefreshCw className={`ml-1 h-3 w-3 ${isUpdatingSlugs ? 'animate-spin' : ''}`} />
            تحديث روابط المقالات
          </Button>
        </div>
      </div>

      <Alert className={cn("mb-6 bg-[var(--pubg-black-color)] border-[var(--pubg-orange-color)]")}>
        <Sparkles className="h-5 w-5 text-pubg-orange" />
        <AlertTitle className="font-bold text-white">مساعد الذكاء الاصطناعي متاح الآن!</AlertTitle>
        <AlertDescription className="text-muted-foreground">
          يمكنك الآن استخدام الذكاء الاصطناعي Gemini لتحليل منشورات التليجرام وملء نماذج المنشورات تلقائيًا.
          فقط انسخ والصق المحتوى واترك الذكاء الاصطناعي يقوم بالباقي!
        </AlertDescription>
      </Alert>

      {/* Blog Posts List */}
      <div className="space-y-4">
        {posts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {posts.map((post) => (
              <div
                key={post.id}
                className="glass-card rounded-lg p-3 flex flex-col"
              >
                <div className="flex items-start mb-3">
                  <div className="w-20 h-16 ml-3 shrink-0">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-full object-cover rounded-md"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-base text-white mb-1 line-clamp-1">{post.title}</h3>
                    <p className="text-xs text-muted-foreground mb-1">
                      {post.author} · {post.date}
                    </p>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {post.excerpt}
                    </p>
                  </div>
                </div>
                
                <div className="mt-auto pt-2 border-t border-border flex justify-end gap-1">
                  <Button
                    onClick={() => handleEdit(post)}
                    size="sm"
                    className="bg-pubg-blue text-white hover:bg-pubg-blue/90 h-7 px-2 text-xs"
                  >
                    <Pencil className="ml-1 h-3 w-3" />
                    تعديل
                  </Button>
                  <Button
                    onClick={() => handleDelete(post.id!)}
                    size="sm"
                    variant="destructive"
                    className="h-7 px-2 text-xs"
                  >
                    <Trash2 className="ml-1 h-3 w-3" />
                    حذف
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-lg text-muted-foreground">لا توجد مقالات متاحة بعد</p>
            <Button 
              onClick={() => {
                resetForm();
                setShowDialog(true);
              }} 
              className="mt-4 bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
            >
              <Plus className="ml-2 h-4 w-4" />
              إضافة مقال جديد
            </Button>
          </div>
        )}
      </div>

      {/* Add/Edit Blog Post Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto p-0 w-[95vw] sm:w-auto bg-pubg-black border-pubg-gray/30 shadow-lg">
          <DialogHeader className="p-4 md:p-6 border-b border-pubg-gray/20 bg-[rgba(0,0,0,0.95)]">
            <DialogTitle className="text-xl font-bold text-white flex items-center">
              {editingPost ? (
                <>
                  <Pencil className="mr-2 h-5 w-5 text-pubg-orange" />
                  تعديل المقال
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-5 w-5 text-pubg-orange" />
                  إضافة مقال جديد
                </>
              )}
            </DialogTitle>
            <DialogDescription className="text-muted-foreground text-sm mt-1">
              {editingPost ? "قم بتعديل محتوى المقال وحفظ التغييرات" : "قم بإدخال تفاصيل المقال الجديد"}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 p-4 md:p-6">
            {/* Telegram AI Assistant */}
            <div className="glass-card p-4 md:p-5 rounded-lg border border-pubg-gray/20 bg-[rgba(20,20,20,0.6)] shadow-md">
              <div className="flex items-center mb-3">
                <div className="p-2 rounded-full bg-pubg-gray/10 mr-3">
                  <Sparkles className="h-5 w-5 text-pubg-gray" />
                </div>
                <div>
                  <h3 className="text-white font-semibold text-base">مساعد الذكاء الاصطناعي</h3>
                  <p className="text-xs text-muted-foreground mt-0.5">استخدم الذكاء الاصطناعي لتحليل منشورات التليجرام وملئ النموذج تلقائيًا</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="telegramPost" className="text-sm font-medium flex items-center">
                    <span>محتوى منشور التليجرام</span>
                    <span className="mr-2 text-xs bg-pubg-gray/20 text-pubg-gray px-1.5 py-0.5 rounded-md">انسخ والصق</span>
                  </Label>
                  <Textarea
                    id="telegramPost"
                    value={telegramPost}
                    onChange={handleTelegramPostChange}
                    placeholder="انسخ والصق منشور التليجرام هنا..."
                    className="min-h-[120px] bg-[rgba(0,0,0,0.7)] border-pubg-gray/20 focus:border-pubg-orange/50 focus:ring-pubg-orange/20"
                  />
                </div>
                
                <div className="flex flex-col sm:flex-row gap-3 items-center">
                  <div className="w-full sm:w-auto">
                    <ModelSelector
                      onModelChange={setSelectedModel}
                    />
                  </div>
                  
                  <Button
                    type="button"
                    onClick={handleProcessWithAI}
                    disabled={isAiProcessing || !telegramPost.trim()}
                    className="w-full sm:w-auto bg-gradient-to-r from-pubg-orange to-pubg-orange/80 text-white hover:from-pubg-orange/90 hover:to-pubg-orange/70 transition-all shadow-md h-10 px-4 text-sm font-medium"
                  >
                    {isAiProcessing ? (
                      <>
                        <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
                        جاري المعالجة...
                      </>
                    ) : (
                      <>
                        <Sparkles className="ml-2 h-4 w-4" />
                        معالجة بالذكاء الاصطناعي
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-3">
              {/* Language Info Section */}
              <div className="mb-5 p-4 rounded-lg bg-[rgba(20,20,20,0.6)] border border-pubg-gray/20 shadow-md">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 rounded-full bg-pubg-gray/10">
                    <GlobeIcon className="h-5 w-5 text-pubg-gray" />
                  </div>
                  <div>
                    <h3 className="text-white text-base font-semibold">دعم اللغات المتعددة</h3>
                    <p className="text-xs text-muted-foreground mt-0.5">أدخل المحتوى بكلتا اللغتين لعرضه حسب تفضيل المستخدم</p>
                  </div>
                </div>
                <div className="p-3 rounded-md bg-[rgba(0,0,0,0.7)] flex flex-col sm:flex-row gap-3 sm:items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 px-3 py-2 rounded-md bg-red-500/10 border border-red-500/20">
                      <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                        <span className="text-[10px] text-white font-bold">ع</span>
                      </div>
                      <span className="text-sm text-red-400 font-medium">العربية</span>
                    </div>
                    <div className="flex items-center gap-2 px-3 py-2 rounded-md bg-blue-500/10 border border-blue-500/20">
                      <div className="w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center">
                        <span className="text-[10px] text-white font-bold">E</span>
                      </div>
                      <span className="text-sm text-blue-400 font-medium">English</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">سيتم عرض المحتوى المناسب بناءً على لغة المستخدم</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Title Fields - Arabic & English */}
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-sm flex items-center font-medium">
                    <span>عنوان المقال</span>
                    <div className="mr-2 flex items-center gap-1 bg-red-500/10 border border-red-500/20 px-1.5 py-0.5 rounded-md">
                      <div className="w-3 h-3 rounded-full bg-red-500 flex items-center justify-center">
                        <span className="text-[8px] text-white font-bold">ع</span>
                      </div>
                      <span className="text-xs text-red-400">عربي</span>
                    </div>
                  </Label>
                  <ClearableInput
                    id="title"
                    name="title"
                    value={formData.title || ""}
                    onChange={handleInputChange}
                    onClear={() => setFormData({ ...formData, title: "" })}
                    required
                    dir="rtl"
                    className="bg-[rgba(0,0,0,0.7)] border-pubg-gray/20 focus:border-pubg-orange/50 focus:ring-pubg-orange/20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="title_en" className="text-sm flex items-center font-medium">
                    <span>Article Title</span>
                    <div className="mr-2 flex items-center gap-1 bg-blue-500/10 border border-blue-500/20 px-1.5 py-0.5 rounded-md">
                      <div className="w-3 h-3 rounded-full bg-blue-500 flex items-center justify-center">
                        <span className="text-[8px] text-white font-bold">E</span>
                      </div>
                      <span className="text-xs text-blue-400">English</span>
                    </div>
                  </Label>
                  <ClearableInput
                    id="title_en"
                    name="title_en"
                    value={formData.title_en || ""}
                    onChange={handleInputChange}
                    onClear={() => setFormData({ ...formData, title_en: "" })}
                    dir="ltr"
                    className="bg-[rgba(0,0,0,0.7)] border-pubg-gray/20 focus:border-pubg-orange/50 focus:ring-pubg-orange/20"
                  />
                </div>
                
                {/* Author Fields - Arabic & English */}
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="author" className="text-sm flex items-center">
                    <span>الكاتب</span>
                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                  </Label>
                  <ClearableInput
                    id="author"
                    name="author"
                    value={formData.author || ""}
                    onChange={handleInputChange}
                    onClear={() => setFormData({ ...formData, author: "" })}
                    required
                    dir="rtl"
                  />
                </div>
                
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="author_en" className="text-sm flex items-center">
                    <span>Author</span>
                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                  </Label>
                  <ClearableInput
                    id="author_en"
                    name="author_en"
                    value={formData.author_en || ""}
                    onChange={handleInputChange}
                    onClear={() => setFormData({ ...formData, author_en: "" })}
                    dir="ltr"
                  />
                </div>
                
                {/* Date & Slug Fields */}
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="date" className="text-sm">التاريخ</Label>
                  <Input
                    id="date"
                    name="date"
                    type="date"
                    value={formData.date || ""}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="slug" className="text-sm flex items-center">
                    <span>الرابط (Slug)</span>
                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                  </Label>
                  <ClearableInput
                    id="slug"
                    name="slug"
                    value={formData.slug || ""}
                    onChange={handleInputChange}
                    onClear={() => setFormData({ ...formData, slug: "" })}
                    placeholder="سيتم إنشاؤه تلقائيًا من العنوان"
                    dir="rtl"
                  />
                </div>
                
                {/* English Slug */}
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="slug_en" className="text-sm flex items-center">
                    <span>Slug (English)</span>
                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                  </Label>
                  <ClearableInput
                    id="slug_en"
                    name="slug_en"
                    value={formData.slug_en || ""}
                    onChange={handleInputChange}
                    onClear={() => setFormData({ ...formData, slug_en: "" })}
                    placeholder="Auto-generated from English title"
                    dir="ltr"
                  />
                </div>
                
                {/* Image Upload */}
                <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                  <Label htmlFor="image" className="text-sm">صورة المقال</Label>
                  <div className="flex flex-col space-y-3">
                    {formData.image && (
                      <div className="relative w-full h-32 rounded-md overflow-hidden">
                        <img 
                          src={formData.image} 
                          alt="Blog Preview" 
                          className="w-full h-full object-cover" 
                        />
                      </div>
                    )}
                    <ImageUploader
                      imageUrl={formData.image || ""}
                      onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                      fieldName="image"
                      aspectRatio={16/9}
                    />
                  </div>
                </div>
                
                {/* Checkboxes */}
                <div className="space-y-1 sm:space-y-2 sm:col-span-2 grid grid-cols-2 gap-2">
                  <div className="flex items-center">
                    <input
                      id="featured"
                      name="featured"
                      type="checkbox"
                      checked={formData.featured || false}
                      onChange={(e) => setFormData({ ...formData, featured: e.target.checked })}
                      className="ml-2 w-4 h-4"
                    />
                    <Label htmlFor="featured" className="cursor-pointer text-sm">
                      عرض في الصفحة الرئيسية
                    </Label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="special"
                      name="special"
                      type="checkbox"
                      checked={formData.special || false}
                      onChange={(e) => setFormData({ ...formData, special: e.target.checked })}
                      className="ml-2 w-4 h-4"
                    />
                    <Label htmlFor="special" className="cursor-pointer text-sm">
                      مقال مميز (خاص)
                    </Label>
                  </div>
                </div>
                
                {/* Excerpt Fields - Arabic & English */}
                <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="text-sm font-semibold text-white">ملخص المقال</h3>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <span className="text-xs bg-red-500/20 text-red-500 px-2 py-0.5 rounded-md">عربي</span>
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-0.5 rounded-md">English</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-1 sm:space-y-2 sm:col-span-1">
                  <Label htmlFor="excerpt" className="text-sm flex items-center">
                    <span>ملخص المقال (عربي)</span>
                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                  </Label>
                  <Textarea
                    id="excerpt"
                    name="excerpt"
                    value={formData.excerpt || ""}
                    onChange={handleInputChange}
                    required
                    className="min-h-16"
                    dir="rtl"
                  />
                </div>
                
                <div className="space-y-1 sm:space-y-2 sm:col-span-1">
                  <Label htmlFor="excerpt_en" className="text-sm flex items-center">
                    <span>Article Excerpt (English)</span>
                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                  </Label>
                  <Textarea
                    id="excerpt_en"
                    name="excerpt_en"
                    value={formData.excerpt_en || ""}
                    onChange={handleInputChange}
                    className="min-h-16"
                    dir="ltr"
                  />
                </div>
                
                {/* Content Fields - Arabic & English */}
                <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="text-sm font-semibold text-white">محتوى المقال</h3>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <span className="text-xs bg-red-500/20 text-red-500 px-2 py-0.5 rounded-md">عربي</span>
                      <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-0.5 rounded-md">English</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-1 sm:space-y-2 sm:col-span-1">
                  <Label htmlFor="content" className="text-sm flex items-center">
                    <span>محتوى المقال (عربي)</span>
                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                  </Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={formData.content || ""}
                    onChange={handleInputChange}
                    required
                    className="min-h-[200px] font-mono text-sm"
                    placeholder="محتوى المقال الكامل..."
                    dir="rtl"
                  />
                  <p className="text-muted-foreground text-xs mt-1">
                    قم بإدخال المحتوى الكامل للمقال. يمكنك تنسيق النص بإضافة سطور جديدة للفقرات المختلفة.
                  </p>
                </div>
                
                <div className="space-y-1 sm:space-y-2 sm:col-span-1">
                  <Label htmlFor="content_en" className="text-sm flex items-center">
                    <span>Article Content (English)</span>
                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                  </Label>
                  <Textarea
                    id="content_en"
                    name="content_en"
                    value={formData.content_en || ""}
                    onChange={handleInputChange}
                    className="min-h-[200px] font-mono text-sm"
                    placeholder="Full article content in English..."
                    dir="ltr"
                  />
                  <p className="text-muted-foreground text-xs mt-1">
                    Enter the full content of the article in English. You can format the text by adding new lines for different paragraphs.
                  </p>
                </div>
              </div>
              
              <DialogFooter className="mt-6 gap-3 flex-col sm:flex-row border-t border-pubg-gray/20 pt-5">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetFormAndClose}
                  className="w-full sm:w-auto border-pubg-gray/30 text-white hover:bg-pubg-gray/10 hover:text-white transition-all"
                >
                  <X className="ml-2 h-4 w-4" />
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full sm:w-auto bg-gradient-to-r from-pubg-orange to-pubg-orange/80 text-white hover:from-pubg-orange/90 hover:to-pubg-orange/70 transition-all shadow-md h-10 px-4 font-medium"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : editingPost ? (
                    <>
                      <Save className="ml-2 h-4 w-4" />
                      حفظ التغييرات
                    </>
                  ) : (
                    <>
                      <Plus className="ml-2 h-4 w-4" />
                      إضافة المقال
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BlogManager;
