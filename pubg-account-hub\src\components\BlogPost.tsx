import { Link } from "react-router-dom";
import { Calendar, User, ArrowLeft, Globe } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { motion } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import { useTranslation } from "react-i18next";

interface BlogPostProps {
  post: {
    id: string;
    title: string;
    title_en?: string;
    excerpt: string;
    excerpt_en?: string;
    content: string;
    content_en?: string;
    image: string;
    author: string;
    author_en?: string;
    date: string;
    slug?: string;
    slug_en?: string;
    special?: boolean;
  };
  featured?: boolean;
  viewMode?: "grid" | "list";
}

const BlogPost = ({ post, featured = false, viewMode = "grid" }: BlogPostProps) => {
  const isMobile = useIsMobile();
  const iconSize = isMobile ? 12 : featured ? 14 : 12;
  const isSpecial = post.special;
  const { language } = useLanguage();
  const { t } = useTranslation('common');
  
  // Memoize text content based on language for better performance
  const title = language === 'en' && post.title_en ? post.title_en : post.title;
  const excerpt = language === 'en' && post.excerpt_en ? post.excerpt_en : post.excerpt;
  const author = language === 'en' && post.author_en ? post.author_en : post.author;
  
  // Function to format date in Arabic
  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (e) {
      return dateStr;
    }
  };

  // Generate a slug from the title if one is not provided
  const generateSlug = (title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
    return slug;
  };

  // Use the appropriate slug based on language
  const getSlug = () => {
    if (language === 'en' && post.slug_en) {
      return post.slug_en;
    }
    return post.slug || generateSlug(language === 'en' && post.title_en ? post.title_en : post.title);
  };
  
  const postSlug = getSlug();
  const blogPath = `/blog/${postSlug || post.id}`;
  console.log(`BlogPost: "${title}" (${language}), ID: ${post.id}, slug: ${postSlug}, has alternative slug: ${language === 'en' ? Boolean(post.slug) : Boolean(post.slug_en)}`);
  
  if (featured) {
    return (
      <Link to={blogPath} className="block w-full" key={`featured-post-${post.id}-${language}`}>
        <motion.div 
          className={`glass-card rounded-xl overflow-hidden transition-all duration-300 hover:translate-y-[-5px] hover:shadow-lg ${
            isSpecial ? 'hover:shadow-pubg-orange/30' : 'hover:shadow-pubg-blue/20'
          } grid grid-cols-1 md:grid-cols-2 group cursor-pointer relative`}
          whileHover={{
            boxShadow: isSpecial 
              ? "0 10px 30px -10px rgba(242, 169, 0, 0.4)" 
              : "0 10px 30px -10px rgba(80, 120, 255, 0.3)",
            scale: 1.02,
            y: -5,
            transition: { duration: 0.4 }
          }}
        >
          <div className={`absolute top-0 right-0 w-full h-full bg-gradient-to-br ${
            isSpecial ? 'from-pubg-orange/5' : 'from-pubg-blue/5'
          } via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none`}></div>
          <div className="relative overflow-hidden h-64 md:h-full">
            <img
              src={post.image}
              alt={post.title}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          <div className="p-4 md:p-6 flex flex-col relative">
            <div className={`absolute -z-10 bottom-0 right-0 w-32 h-32 ${
              isSpecial ? 'bg-pubg-orange/5' : 'bg-pubg-blue/5'
            } rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
            <div className="flex items-center text-xs sm:text-sm text-muted-foreground mb-3 md:mb-4">
              <div className="flex items-center ml-3 md:ml-4">
                <User size={iconSize} className="ml-1" />
                <span>{author}</span>
              </div>
              <div className="flex items-center">
                <Calendar size={iconSize} className="ml-1" />
                <span>{formatDate(post.date)}</span>
              </div>
              {((language === 'en' && post.title_en) || (language === 'ar' && post.title_en)) && (
                <div className="flex items-center mr-3 md:mr-4">
                  <Globe size={iconSize} className="ml-1 text-pubg-blue" />
                </div>
              )}
            </div>
            <h3 className="text-xl md:text-2xl font-bold text-white mb-2 md:mb-3">
              {title}
            </h3>
            <p className="text-muted-foreground mb-4 md:mb-6 line-clamp-3">
              {excerpt}
            </p>
            <motion.div 
              className={`${isSpecial ? 'text-pubg-orange' : 'text-pubg-blue'} font-medium hover:${isSpecial ? 'text-pubg-orange/80' : 'text-pubg-blue/80'} transition-colors inline-flex items-center group`}
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              {t('blog.read_more')}
              <ArrowLeft size={16} className="mr-1 group-hover:translate-x-[-5px] transition-transform" />
            </motion.div>
          </div>
        </motion.div>
      </Link>
    );
  }
  
  // List view mode
  if (viewMode === "list") {
    return (
      <Link to={blogPath} className="block w-full" key={`list-post-${post.id}-${language}`}>
        <motion.div 
          className={`glass-card rounded-xl overflow-hidden transition-all duration-300 hover:translate-y-[-2px] hover:shadow-lg ${
            isSpecial ? 'hover:shadow-pubg-orange/25' : 'hover:shadow-pubg-blue/20'
          } group flex flex-col sm:flex-row cursor-pointer relative`}
          whileHover={{
            boxShadow: isSpecial 
              ? "0 8px 20px -8px rgba(242, 169, 0, 0.35)" 
              : "0 8px 20px -8px rgba(80, 120, 255, 0.3)",
            scale: 1.01, 
            y: -2,
            transition: { duration: 0.3 }
          }}
        >
          <div className={`absolute top-0 right-0 w-full h-full bg-gradient-to-r ${
            isSpecial ? 'from-pubg-orange/5' : 'from-pubg-blue/5'
          } via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none`}></div>
          <div className="relative overflow-hidden h-40 sm:h-auto sm:w-48 md:w-64">
            <img
              src={post.image}
              alt={post.title}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          <div className="p-4 flex flex-col flex-1 relative">
            <div className="flex items-center text-xs text-muted-foreground mb-2">
              <div className="flex items-center ml-3">
                <User size={iconSize} className="ml-1" />
                <span>{author}</span>
              </div>
              <div className="flex items-center">
                <Calendar size={iconSize} className="ml-1" />
                <span>{formatDate(post.date)}</span>
              </div>
              {((language === 'en' && post.title_en) || (language === 'ar' && post.title_en)) && (
                <div className="flex items-center mr-2">
                  <Globe size={iconSize} className="ml-1 text-pubg-blue" />
                </div>
              )}
            </div>
            <h3 className="text-lg font-bold text-white mb-2">
              {title}
            </h3>
            <p className="text-muted-foreground text-sm mb-3 flex-grow line-clamp-2 sm:line-clamp-3">
              {excerpt}
            </p>
            <motion.div 
              className={`${isSpecial ? 'text-pubg-orange' : 'text-pubg-blue'} text-sm font-medium hover:${isSpecial ? 'text-pubg-orange/80' : 'text-pubg-blue/80'} transition-colors inline-flex items-center mt-auto group`}
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              {t('blog.read_more')}
              <ArrowLeft size={14} className="mr-1 group-hover:translate-x-[-5px] transition-transform" />
            </motion.div>
          </div>
        </motion.div>
      </Link>
    );
  }
  
  // Default grid view mode
  return (
    <Link to={blogPath} className="block w-full h-full" key={`post-${post.id}-${language}`}>
      <motion.div 
        className={`glass-card rounded-xl overflow-hidden transition-all duration-300 hover:translate-y-[-5px] hover:shadow-lg ${
          isSpecial ? 'hover:shadow-pubg-orange/25' : 'hover:shadow-pubg-blue/20'
        } group h-full flex flex-col cursor-pointer relative`}
        whileHover={{
          boxShadow: isSpecial 
            ? "0 10px 20px -5px rgba(242, 169, 0, 0.35)" 
            : "0 10px 20px -5px rgba(80, 120, 255, 0.25)",
          scale: 1.02,
          y: -5,
          transition: { duration: 0.3 }
        }}
      >
        <div className={`absolute inset-0 bg-gradient-to-br ${
          isSpecial ? 'from-pubg-orange/5' : 'from-pubg-blue/5'
        } via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none`}></div>
        <div className="relative overflow-hidden h-40 sm:h-48">
          <img
            src={post.image}
            alt={post.title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
        <div className="p-3 sm:p-4 flex flex-col flex-1">
          <div className="flex items-center text-xs text-muted-foreground mb-2 sm:mb-3">
            <div className="flex items-center ml-2 sm:ml-3">
              <User size={iconSize} className="ml-1" />
              <span>{author}</span>
            </div>
            <div className="flex items-center">
              <Calendar size={iconSize} className="ml-1" />
              <span>{formatDate(post.date)}</span>
            </div>
            {((language === 'en' && post.title_en) || (language === 'ar' && post.title_en)) && (
              <div className="flex items-center mr-2">
                <Globe size={iconSize} className="ml-1 text-pubg-blue" />
              </div>
            )}
          </div>
          <h3 className="text-base sm:text-lg font-bold text-white mb-1 sm:mb-2 line-clamp-1">
            {title}
          </h3>
          <p className="text-muted-foreground text-xs sm:text-sm mb-2 sm:mb-3 line-clamp-2 flex-grow">
            {excerpt}
          </p>
          <motion.div 
            className={`${isSpecial ? 'text-pubg-orange' : 'text-pubg-blue'} text-xs sm:text-sm font-medium hover:${isSpecial ? 'text-pubg-orange/80' : 'text-pubg-blue/80'} transition-colors inline-flex items-center mt-auto group`}
            whileHover={{ x: 5 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {t('blog.read_more')}
            <ArrowLeft size={14} className="mr-1 group-hover:translate-x-[-5px] transition-transform" />
          </motion.div>
        </div>
      </motion.div>
    </Link>
  );
};

export default BlogPost;
