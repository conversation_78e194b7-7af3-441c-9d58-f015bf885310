"use client"

import { useState, useMemo } from "react"
import { Search } from "lucide-react"
import { mockProducts } from "../data/mockData"
import ProductCard from "../components/ProductCard"

export default function ShopPage() {
  const [searchQuery, setSearchQuery] = useState("")

  // TODO: Replace with actual user role from Supabase auth
  const userRole = "user"

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = mockProducts

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (product) =>
          product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase())),
      )
    }

    // Sort by popular by default
    filtered.sort((a, b) => b.commentCount - a.commentCount)

    return filtered
  }, [searchQuery])

  return (
    <div className="container mx-auto px-4 py-8">
      {/* <PERSON><PERSON> */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          متجر الألعاب
        </h1>
        <p className="text-gray-400 text-lg">اكتشف ألعاب رائعة واشحن ألعابك المفضلة فوراً</p>
      </div>

      {/* Search */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 mb-8 border border-gray-700/50 shadow-xl">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="ابحث عن الألعاب أو الفئات أو العلامات..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg pr-10 pl-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
          />
        </div>

        <div className="mt-4 text-sm text-gray-400">تم العثور على {filteredProducts.length} منتج</div>
      </div>

      {/* Products Grid - 3 columns on mobile */}
      {filteredProducts.length > 0 ? (
        <div className="grid grid-cols-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-6">
          {filteredProducts.map((product) => (
            <ProductCard key={product.id} product={product} userRole={userRole} />
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold mb-2">لم يتم العثور على منتجات</h3>
          <p className="text-gray-400">جرب تعديل معايير البحث</p>
        </div>
      )}
    </div>
  )
}
