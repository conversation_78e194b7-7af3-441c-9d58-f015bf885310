import React, { useState, useEffect, useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useNavigate, useLocation } from "react-router-dom";
import { 
  Save, 
  Home, 
  FileText, 
  BarChart, 
  SlidersHorizontal, 
  Sparkles, 
  ChevronDown,
  Eye,
  EyeOff,
  GripVertical,
  LayoutGrid,
  ArrowUpDown,
  ChevronUp,
  ChevronRight,
  GlobeIcon,
  Globe,
  Zap,
  Layers,
  Search
} from "lucide-react";
import { SettingsModel, getSettings, updateSettings, PageVisibilityModel, getPageVisibilitySettings, updatePageVisibility, initializePageVisibilitySettings, DEFAULT_HERO_IMAGE } from "@/services/firestore";
import { Card, CardContent, CardDes<PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AVAILABLE_MODELS, getSavedModel, saveModelPreference, DEFAULT_MODEL } from "@/services/gemini";
import { Switch } from "@/components/ui/switch";
import ImageUploader from "./ImageUploader";
import "./settingsManager.css";

const SettingsManager = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedModel, setSelectedModel] = useState(getSavedModel());
  const [pages, setPages] = useState<PageVisibilityModel[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Define tab navigation items for reusability
  const tabItems = [
    { id: "homepage", label: "الصفحة الرئيسية", icon: Home },
    { id: "sections", label: "عناوين الأقسام", icon: FileText },
    { id: "stats", label: "الإحصائيات", icon: BarChart },
    { id: "homeSections", label: "أقسام الصفحة الرئيسية", icon: SlidersHorizontal },
    { id: "pageVisibility", label: "إدارة النظام", icon: Eye },
    { id: "aiModels", label: "الذكاء الاصطناعي", icon: Sparkles },
  ];

  // Get the tab from URL query params or default to "homepage"
  const getTabFromUrl = () => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('settings_section');
    // Validate that the tab exists in tabItems
    return tabItems.some(item => item.id === tab) ? tab : "homepage";
  };
  
  const [activeTab, setActiveTab] = useState(getTabFromUrl());
  
  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    console.log("Tab change requested to:", value);
    setActiveTab(value);
    
    // Update URL with the new tab
    const params = new URLSearchParams(location.search);
    
    // Keep the main tab parameter
    const mainTab = params.get('tab') || 'settings';
    
    // Update settings section parameter
    params.set('tab', mainTab);
    params.set('settings_section', value);
    
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });
    
    // Force update after state change for mobile view
    setTimeout(() => {
      const tabContent = document.querySelector(`[data-value="${value}"]`);
      if (tabContent) {
        console.log("Found tab content, forcing visibility");
        tabContent.setAttribute('data-state', 'active');
        
        // Make other tabs inactive
        document.querySelectorAll(`[data-value]:not([data-value="${value}"])`).forEach(el => {
          el.setAttribute('data-state', 'inactive');
        });
      }
    }, 50);
  };
  
  // Update active tab if URL changes
  useEffect(() => {
    const newTab = getTabFromUrl();
    if (newTab !== activeTab) {
      setActiveTab(newTab);
    }
  }, [location.search]);

  const [formData, setFormData] = useState<SettingsModel>({
    siteName: "PUBG STORE",
    heroTitle: "منصة حسابات ببجي الأولى",
    heroTitle_en: "The First PUBG Accounts Platform",
    heroSubtitle: "المكان الأمثل للحصول على حسابات PUBG بأفضل الأسعار وأرقى الخدمات",
    heroSubtitle_en: "The best place to get PUBG accounts at the best prices and the finest services",
    heroImage: DEFAULT_HERO_IMAGE,
    siteName_en: "PUBG STORE",
    // Stats with default values
    statsTitle: "إحصائيات",
    statsTitle_en: "Statistics",
    statCustomers: "10K+",
    statCustomersLabel: "عملاء سعداء",
    statCustomersLabel_en: "Happy Customers",
    statAccounts: "500+",
    statAccountsLabel: "حساب تم بيعه",
    statAccountsLabel_en: "Accounts Sold",
    statSupport: "24/7",
    statSupportLabel: "دعم فني",
    statSupportLabel_en: "Support",
    statSecurity: "100%",
    statSecurityLabel: "أمان للمدفوعات",
    statSecurityLabel_en: "Payment Security",
    // Section titles with default values
    testimonialsSectionTitle: "آراء عملائنا",
    testimonialsSectionTitle_en: "Customer Reviews",
    whyChooseUsSectionTitle: "لماذا تختارنا؟",
    whyChooseUsSectionTitle_en: "Why Choose Us?",
    featuredAccountsSectionTitle: "حسابات PUBG مميزة",
    featuredAccountsSectionTitle_en: "Featured PUBG Accounts",
    featuredAccountsSectionSubtitle: "اختر من بين أفضل حسابات PUBG المميزة بأسعار منافسة",
    featuredAccountsSectionSubtitle_en: "Choose from the best featured PUBG accounts at competitive prices",
    featuredModsSectionTitle: "مودات PUBG المميزة",
    featuredModsSectionTitle_en: "Featured PUBG Mods",
    featuredModsSectionSubtitle: "أفضل مودات PUBG المتاحة بأحدث الميزات والتقنيات",
    featuredModsSectionSubtitle_en: "The best PUBG mods available with the latest features and technologies",
    // Home page section order
    homeSectionOrder: [
      "stats",
      "featuredAccounts", 
      "featuredMods", 
      "gameFeatures", 
      "ucStore",
      "testimonials", 
      "whyChooseUs", 
      "blogPosts", 
      "newsletter", 
      "callToAction"
    ],
    // Home page section visibility
    homeSectionVisibility: {
      stats: true,
      featuredAccounts: true,
      featuredMods: true,
      gameFeatures: true,
      ucStore: true,
      testimonials: true,
      whyChooseUs: true,
      blogPosts: true,
      newsletter: true,
      callToAction: true
    }
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const settingsData = await getSettings();
        if (settingsData) {
          // Merge with defaults to ensure all fields exist
          setFormData({
            ...formData,
            ...settingsData
          });
        }
        
        // Initialize default page visibility settings if needed
        await initializePageVisibilitySettings();
        
        // Get current page visibility settings
        const pagesData = await getPageVisibilitySettings();
        
        // Sort pages by order
        const sortedPages = pagesData.sort((a, b) => {
          return (a.order || 99) - (b.order || 99);
        });
        
        setPages(sortedPages);
        setIsLoading(false);
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في تحميل إعدادات الموقع",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Add debugging log to track active tab changes
  useEffect(() => {
    console.log("Active tab changed to:", activeTab);
  }, [activeTab]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await updateSettings(formData);
      
      toast({
        title: "تم بنجاح",
        description: "تم حفظ الإعدادات بنجاح",
        duration: 2000, // 2 seconds
      });
      
      setHasUnsavedChanges(false);
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث إعدادات الموقع",
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  const handleModelChange = (value: string) => {
    setSelectedModel(value);
    saveModelPreference(value);
    
    toast({
      title: "تم بنجاح",
      description: "تم حفظ نموذج الذكاء الاصطناعي الافتراضي",
      duration: 2000,
    });
  };

  const resetModelToDefault = () => {
    setSelectedModel(DEFAULT_MODEL);
    saveModelPreference(DEFAULT_MODEL);
    
    toast({
      title: "تم بنجاح",
      description: "تم إعادة ضبط نموذج الذكاء الاصطناعي إلى الافتراضي",
      duration: 2000,
    });
  };

  const handleHeroImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value) {
      setFormData(prev => ({ ...prev, heroImage: e.target.value }));
    } else if (e.target.name === "image" && typeof e.target.value === "string") {
      setFormData(prev => ({ ...prev, heroImage: e.target.value }));
    }
  };

  const handleToggleVisibility = async (pageId: string, isVisible: boolean) => {
    try {
      // Update in Firestore
      await updatePageVisibility(pageId, isVisible);
      
      // Update local state
      setPages(prev => 
        prev.map(page => 
          page.pageId === pageId 
            ? { ...page, isVisible } 
            : page
        )
      );
      
      toast({
        title: "تم بنجاح",
        description: `تم ${isVisible ? 'إظهار' : 'إخفاء'} الصفحة بنجاح`,
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث حالة إظهار الصفحة",
        variant: "destructive",
      });
    }
  };

  // Get section name in Arabic
  const getSectionName = (sectionId: string): string => {
    switch(sectionId) {
      case "stats": return "الإحصائيات";
      case "featuredAccounts": return "حسابات PUBG المميزة";
      case "featuredMods": return "مودات PUBG المميزة";
      case "gameFeatures": return "مميزات اللعبة";
      case "ucStore": return "متجر UC";
      case "testimonials": return "آراء العملاء";
      case "whyChooseUs": return "لماذا تختارنا";
      case "blogPosts": return "أحدث المقالات";
      case "newsletter": return "النشرة الإخبارية";
      case "callToAction": return "دعوة للعمل";
      default: return sectionId;
    }
  };

  // Handle section visibility toggle
  const handleSectionVisibilityChange = (sectionId: string, isVisible: boolean) => {
    setFormData(prev => ({
      ...prev,
      homeSectionVisibility: {
        ...(prev.homeSectionVisibility || {}),
        [sectionId]: isVisible
      }
    }));
    setHasUnsavedChanges(true);
  };

  // Handle section order change (move up/down)
  const handleSectionOrderChange = (sectionId: string, direction: 'up' | 'down') => {
    if (!formData.homeSectionOrder) return;
    
    const currentIndex = formData.homeSectionOrder.indexOf(sectionId);
    if (currentIndex === -1) return;
    
    const newOrder = [...formData.homeSectionOrder];
    
    if (direction === 'up' && currentIndex > 0) {
      // Move section up
      [newOrder[currentIndex], newOrder[currentIndex - 1]] = 
      [newOrder[currentIndex - 1], newOrder[currentIndex]];
    } else if (direction === 'down' && currentIndex < newOrder.length - 1) {
      // Move section down
      [newOrder[currentIndex], newOrder[currentIndex + 1]] = 
      [newOrder[currentIndex + 1], newOrder[currentIndex]];
    }
    
    setFormData(prev => ({
      ...prev,
      homeSectionOrder: newOrder
    }));
    
    setHasUnsavedChanges(true);
    
    // Add animation to the moved item
    setTimeout(() => {
      const sectionElements = document.querySelectorAll('.section-item');
      const targetElement = direction === 'up' 
        ? sectionElements[currentIndex - 1] 
        : sectionElements[currentIndex + 1];
        
      if (targetElement) {
        targetElement.classList.add('item-moved');
        setTimeout(() => {
          targetElement.classList.remove('item-moved');
        }, 800);
      }
    }, 50);
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    // Prevent text selection during drag
    if (e.dataTransfer && e.dataTransfer.setData) {
      e.dataTransfer.setData('text/plain', index.toString());
      // Set a custom drag image (optional)
      const draggedEl = e.currentTarget;
      
      // This helps with the drag preview
      e.dataTransfer.effectAllowed = 'move';
      
      // Try to create a "ghost" image for dragging
      try {
        const ghostImage = draggedEl.cloneNode(true) as HTMLElement;
        ghostImage.style.position = 'absolute';
        ghostImage.style.top = '-1000px';
        ghostImage.style.opacity = '0.8';
        document.body.appendChild(ghostImage);
        e.dataTransfer.setDragImage(ghostImage, 20, 20);
        setTimeout(() => {
          document.body.removeChild(ghostImage);
        }, 0);
      } catch (err) {
        console.error('Error setting drag image:', err);
      }
    }
    
    e.currentTarget.classList.add('dragging');
    
    // Prevent text selection during drag
    document.body.classList.add('dragging-active');
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    e.currentTarget.classList.add('drag-over');
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.currentTarget.classList.remove('drag-over');
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, dropIndex: number) => {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');
    
    const dragIndexStr = e.dataTransfer.getData('text/plain');
    if (!dragIndexStr) return;
    
    const dragIndex = parseInt(dragIndexStr);
    if (isNaN(dragIndex) || dragIndex === dropIndex) return;
    
    if (formData.homeSectionOrder) {
      const newOrder = [...formData.homeSectionOrder];
      const draggedItem = newOrder[dragIndex];
      
      // Remove item from old position
      newOrder.splice(dragIndex, 1);
      // Insert at new position
      newOrder.splice(dropIndex, 0, draggedItem);
      
      setFormData(prev => ({
        ...prev,
        homeSectionOrder: newOrder
      }));
      
      setHasUnsavedChanges(true);
      
      // Add animation to the moved item
      setTimeout(() => {
        const sectionElements = document.querySelectorAll('.section-item');
        if (sectionElements[dropIndex]) {
          sectionElements[dropIndex].classList.add('item-moved');
          setTimeout(() => {
            sectionElements[dropIndex].classList.remove('item-moved');
          }, 800);
        }
      }, 50);
    }
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    e.currentTarget.classList.remove('dragging');
    document.querySelectorAll('.section-item').forEach(item => {
      item.classList.remove('drag-over');
    });
    
    // Re-enable text selection
    document.body.classList.remove('dragging-active');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-pubg-orange"></div>
        <span className="mr-2 text-sm md:text-base">جاري تحميل الإعدادات...</span>
      </div>
    );
  }

  return (
    <div>
      <h2 className="admin-title">إعدادات النظام</h2>
      
      {/* Language Info Section */}
      <div className="mb-6 p-4 rounded-lg bg-[rgba(18,20,25,0.95)] border border-[rgba(45,50,60,0.7)] shadow-md">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 rounded-full bg-pubg-orange/10">
            <GlobeIcon className="h-5 w-5 text-pubg-orange" />
          </div>
          <h3 className="text-white font-semibold">دعم اللغات المتعددة</h3>
        </div>
        <p className="text-sm text-gray-300 mb-3">
          يدعم الموقع اللغتين العربية والإنجليزية. تتيح لك حقول الإدخال المزدوجة إدخال المحتوى بكلتا اللغتين.
        </p>
        <div className="flex items-center justify-between rounded-md p-2 bg-[rgba(25,27,35,0.6)]">
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
              <span className="text-xs text-gray-300">الحقول الافتراضية (بدون لاحقة)</span>
            </div>
            <p className="text-xs text-gray-400 mt-1">مثال: <code className="text-pubg-orange">heroTitle</code></p>
          </div>
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
              <span className="text-xs text-gray-300">الحقول مع لاحقة "_en"</span>
            </div>
            <p className="text-xs text-gray-400 mt-1">مثال: <code className="text-pubg-orange">heroTitle_en</code></p>
          </div>
        </div>
        <p className="text-xs text-gray-400 mt-3">
          سيتم عرض المحتوى المناسب بناءً على تفضيل اللغة للمستخدم عند تصفح الموقع.
        </p>
      </div>
      
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        {/* Mobile Dropdown Navigation */}
        <div className="md:hidden mb-4">
          <div className="glass-card rounded-lg p-3 bg-[rgba(18,20,25,0.95)] border border-[rgba(45,50,60,0.7)] shadow-md">
            <Select value={activeTab} onValueChange={(value) => {
              console.log("Mobile dropdown selected:", value);
              handleTabChange(value);
            }}>
              <SelectTrigger className="flex items-center justify-between admin-input">
                <div className="flex items-center gap-2">
                  {React.createElement(
                    tabItems.find(item => item.id === activeTab)?.icon || Home, 
                    { className: "w-4 h-4 ml-2 text-pubg-orange" }
                  )}
                  <span className="text-sm">{tabItems.find(item => item.id === activeTab)?.label}</span>
                </div>
                <ChevronDown className="w-4 h-4" />
              </SelectTrigger>
              <SelectContent>
                {tabItems.map((item) => (
                  <SelectItem key={item.id} value={item.id}>
                    <div className="flex items-center">
                      <item.icon className="ml-2 w-4 h-4 text-pubg-orange" />
                      <span className="text-sm">{item.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Desktop Tabs */}
        <TabsList className="hidden md:flex mb-6 flex-wrap tabs-list">
          {tabItems.map((item) => (
            <TabsTrigger 
              key={item.id} 
              value={item.id} 
              className="flex items-center tabs-trigger"
            >
              <item.icon className="ml-2 h-4 w-4 text-pubg-orange" />
              <span>{item.label}</span>
            </TabsTrigger>
          ))}
        </TabsList>
        
        {/* Tab Contents - Only render the active tab to avoid layout issues */}
        <div className="tab-content-container">
          {activeTab === "homepage" && (
            <TabsContent value="homepage" className="block" data-value="homepage">
              <form onSubmit={handleSubmit}>
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <Home className="ml-2 h-5 w-5 text-pubg-orange" />
                      إعدادات الصفحة الرئيسية
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      تخصيص قسم الترحيب (Hero) في الصفحة الرئيسية
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">اسم الموقع</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="siteName" className="admin-label flex items-center">
                          <span>اسم الموقع (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="siteName"
                          name="siteName"
                          value={formData.siteName}
                          onChange={handleInputChange}
                          placeholder="اسم الموقع بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="siteName_en" className="admin-label flex items-center">
                          <span>Site Name (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="siteName_en"
                          name="siteName_en"
                          value={formData.siteName_en}
                          onChange={handleInputChange}
                          placeholder="Site name in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-6 mb-2">
                      <h3 className="text-base font-semibold text-white">قسم الترحيب (Hero)</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="heroTitle" className="admin-label flex items-center">
                          <span>عنوان قسم الترحيب (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="heroTitle"
                          name="heroTitle"
                          value={formData.heroTitle}
                          onChange={handleInputChange}
                          placeholder="عنوان قسم الترحيب بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="heroTitle_en" className="admin-label flex items-center">
                          <span>Hero Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="heroTitle_en"
                          name="heroTitle_en"
                          value={formData.heroTitle_en}
                          onChange={handleInputChange}
                          placeholder="Hero title in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="heroSubtitle" className="admin-label flex items-center">
                          <span>وصف قسم الترحيب (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Textarea
                          id="heroSubtitle"
                          name="heroSubtitle"
                          value={formData.heroSubtitle}
                          onChange={handleInputChange}
                          placeholder="وصف قسم الترحيب بالعربية"
                          rows={3}
                          className="admin-textarea"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="heroSubtitle_en" className="admin-label flex items-center">
                          <span>Hero Description (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Textarea
                          id="heroSubtitle_en"
                          name="heroSubtitle_en"
                          value={formData.heroSubtitle_en}
                          onChange={handleInputChange}
                          placeholder="Hero description in English"
                          rows={3}
                          className="admin-textarea"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    <div className="admin-form-group mt-2">
                      <Label htmlFor="heroImage" className="admin-label">صورة قسم الترحيب</Label>
                      <div className="flex flex-col space-y-3">
                        <div className="relative w-full h-24 md:h-32 rounded-md overflow-hidden">
                          <img 
                            src={formData.heroImage} 
                            alt="Hero Preview" 
                            className="w-full h-full object-cover" 
                          />
                          <div className="absolute inset-0 bg-black/40"></div>
                        </div>
                        <ImageUploader 
                          imageUrl={formData.heroImage}
                          onChange={handleHeroImageChange}
                          label="صورة قسم الترحيب"
                          fieldName="heroImage"
                          placeholder="رابط صورة قسم الترحيب"
                          aspectRatio={16/9}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="submit" 
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      <Save className="ml-2 h-4 w-4" />
                      حفظ إعدادات الصفحة الرئيسية
                    </Button>
                  </CardFooter>
                </Card>
              </form>
            </TabsContent>
          )}
          
          {activeTab === "sections" && (
            <TabsContent value="sections" className="block" data-value="sections">
              <form onSubmit={handleSubmit}>
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <FileText className="ml-2 h-5 w-5 text-pubg-orange" />
                      عناوين الأقسام
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      تخصيص عناوين وأوصاف الأقسام المختلفة في الموقع
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="bg-[rgba(25,27,35,0.4)] rounded-lg p-3 md:p-4 mb-4 text-xs md:text-sm">
                      <p className="text-white flex items-center gap-2 mb-2">
                        <GripVertical className="h-5 w-5 text-pubg-orange" />
                        <span>يمكنك ترتيب أقسام الصفحة الرئيسية عن طريق السحب والإفلات</span>
                      </p>
                      <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                        <li>اسحب العنصر وأفلته في المكان المطلوب للترتيب</li>
                        <li>يمكنك أيضًا استخدام الأسهم لتحريك القسم للأعلى أو للأسفل</li>
                        <li>استخدم مفتاح التبديل لإظهار أو إخفاء القسم من الصفحة الرئيسية</li> 
                        <li>اضغط على زر الحفظ بعد الانتهاء من التعديلات</li>
                      </ul>
                    </div>
                    
                    <div className="space-y-2">
                      {formData.homeSectionOrder?.map((sectionId, index) => {
                        const sectionName = getSectionName(sectionId);
                        const isVisible = formData.homeSectionVisibility?.[sectionId] ?? true;
                        
                        return (
                          <div
                            key={sectionId}
                            className="section-item"
                            draggable={true}
                            onDragStart={(e) => handleDragStart(e, index)}
                            onDragOver={(e) => handleDragOver(e)}
                            onDragLeave={(e) => handleDragLeave(e)}
                            onDrop={(e) => handleDrop(e, index)}
                            onDragEnd={(e) => handleDragEnd(e)}
                          >
                            <div className="flex items-center justify-between p-3 md:p-4">
                              <div className="flex items-center gap-3">
                                <div className="drag-handle w-10 h-10 flex items-center justify-center rounded-md cursor-grab active:cursor-grabbing">
                                  <GripVertical className="h-5 w-5" />
                                </div>
                                <div className="section-actions flex flex-col rounded-md">
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 hover:text-white hover:bg-pubg-orange/20 transition-colors"
                                    onClick={() => handleSectionOrderChange(sectionId, 'up')}
                                    disabled={index === 0}
                                    title="نقل لأعلى"
                                  >
                                    <ChevronUp className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 hover:text-white hover:bg-pubg-orange/20 transition-colors"
                                    onClick={() => handleSectionOrderChange(sectionId, 'down')}
                                    disabled={index === formData.homeSectionOrder.length - 1}
                                    title="نقل لأسفل"
                                  >
                                    <ChevronDown className="h-4 w-4" />
                                  </Button>
                                </div>
                                <span className={`text-sm md:text-base font-medium ${!isVisible ? 'text-muted-foreground' : 'text-white'}`}>
                                  {sectionName}
                                </span>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={isVisible}
                                  onCheckedChange={(checked) => handleSectionVisibilityChange(sectionId, checked)}
                                  className={isVisible ? "bg-pubg-orange" : ""}
                                />
                                <Label className="text-xs md:text-sm text-muted-foreground">
                                  {isVisible ? 'ظاهر' : 'مخفي'}
                                </Label>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="submit" 
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      <Save className="ml-2 h-4 w-4" />
                      حفظ عناوين الأقسام
                    </Button>
                  </CardFooter>
                </Card>
              </form>
            </TabsContent>
          )}
          
          {activeTab === "stats" && (
            <TabsContent value="stats" className="block" data-value="stats">
              <form onSubmit={handleSubmit}>
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <BarChart className="ml-2 h-5 w-5 text-pubg-orange" />
                      إعدادات الإحصائيات
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      تخصيص الإحصائيات الظاهرة في الصفحة الرئيسية
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">عنوان قسم الإحصائيات</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="statsTitle" className="admin-label flex items-center">
                          <span>عنوان قسم الإحصائيات (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="statsTitle"
                          name="statsTitle"
                          value={formData.statsTitle}
                          onChange={handleInputChange}
                          placeholder="إحصائيات"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statsTitle_en" className="admin-label flex items-center">
                          <span>Stats Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="statsTitle_en"
                          name="statsTitle_en"
                          value={formData.statsTitle_en}
                          onChange={handleInputChange}
                          placeholder="Statistics"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-6 mb-2">
                      <h3 className="text-base font-semibold text-white">إحصائيات العملاء</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="admin-form-grid">
                      {/* Stats Group 1: Customers */}
                      <div className="admin-form-group">
                        <Label htmlFor="statCustomers" className="admin-label">إحصائية العملاء (العدد)</Label>
                        <Input
                          id="statCustomers"
                          name="statCustomers"
                          value={formData.statCustomers}
                          onChange={handleInputChange}
                          placeholder="10K+"
                          className="admin-input"
                        />
                      </div>
                      
                      <div></div> {/* Empty div for grid alignment */}
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statCustomersLabel" className="admin-label">عنوان إحصائية العملاء (عربي)</Label>
                        <Input
                          id="statCustomersLabel"
                          name="statCustomersLabel"
                          value={formData.statCustomersLabel}
                          onChange={handleInputChange}
                          placeholder="عملاء سعداء"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statCustomersLabel_en" className="admin-label">Customers Stat Label (English)</Label>
                        <Input
                          id="statCustomersLabel_en"
                          name="statCustomersLabel_en"
                          value={formData.statCustomersLabel_en}
                          onChange={handleInputChange}
                          placeholder="Happy Customers"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                      
                      <div className="mt-4 col-span-2 border-t border-border pt-4">
                        <h3 className="text-base font-semibold text-white mb-4">إحصائيات الحسابات</h3>
                      </div>
                      
                      {/* Stats Group 2: Accounts */}
                      <div className="admin-form-group">
                        <Label htmlFor="statAccounts" className="admin-label">إحصائية الحسابات (العدد)</Label>
                        <Input
                          id="statAccounts"
                          name="statAccounts"
                          value={formData.statAccounts}
                          onChange={handleInputChange}
                          placeholder="500+"
                          className="admin-input"
                        />
                      </div>
                      
                      <div></div> {/* Empty div for grid alignment */}
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statAccountsLabel" className="admin-label">عنوان إحصائية الحسابات (عربي)</Label>
                        <Input
                          id="statAccountsLabel"
                          name="statAccountsLabel"
                          value={formData.statAccountsLabel}
                          onChange={handleInputChange}
                          placeholder="حساب تم بيعه"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statAccountsLabel_en" className="admin-label">Accounts Stat Label (English)</Label>
                        <Input
                          id="statAccountsLabel_en"
                          name="statAccountsLabel_en"
                          value={formData.statAccountsLabel_en}
                          onChange={handleInputChange}
                          placeholder="Accounts Sold"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                      
                      <div className="mt-4 col-span-2 border-t border-border pt-4">
                        <h3 className="text-base font-semibold text-white mb-4">إحصائيات الدعم الفني</h3>
                      </div>
                      
                      {/* Stats Group 3: Support */}
                      <div className="admin-form-group">
                        <Label htmlFor="statSupport" className="admin-label">إحصائية الدعم الفني</Label>
                        <Input
                          id="statSupport"
                          name="statSupport"
                          value={formData.statSupport}
                          onChange={handleInputChange}
                          placeholder="24/7"
                          className="admin-input"
                        />
                      </div>
                      
                      <div></div> {/* Empty div for grid alignment */}
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statSupportLabel" className="admin-label">عنوان إحصائية الدعم (عربي)</Label>
                        <Input
                          id="statSupportLabel"
                          name="statSupportLabel"
                          value={formData.statSupportLabel}
                          onChange={handleInputChange}
                          placeholder="دعم فني"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statSupportLabel_en" className="admin-label">Support Stat Label (English)</Label>
                        <Input
                          id="statSupportLabel_en"
                          name="statSupportLabel_en"
                          value={formData.statSupportLabel_en}
                          onChange={handleInputChange}
                          placeholder="Support"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                      
                      <div className="mt-4 col-span-2 border-t border-border pt-4">
                        <h3 className="text-base font-semibold text-white mb-4">إحصائيات الأمان</h3>
                      </div>
                      
                      {/* Stats Group 4: Security */}
                      <div className="admin-form-group">
                        <Label htmlFor="statSecurity" className="admin-label">إحصائية الأمان</Label>
                        <Input
                          id="statSecurity"
                          name="statSecurity"
                          value={formData.statSecurity}
                          onChange={handleInputChange}
                          placeholder="100%"
                          className="admin-input"
                        />
                      </div>
                      
                      <div></div> {/* Empty div for grid alignment */}
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statSecurityLabel" className="admin-label">عنوان إحصائية الأمان (عربي)</Label>
                        <Input
                          id="statSecurityLabel"
                          name="statSecurityLabel"
                          value={formData.statSecurityLabel}
                          onChange={handleInputChange}
                          placeholder="أمان للمدفوعات"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="statSecurityLabel_en" className="admin-label">Security Stat Label (English)</Label>
                        <Input
                          id="statSecurityLabel_en"
                          name="statSecurityLabel_en"
                          value={formData.statSecurityLabel_en}
                          onChange={handleInputChange}
                          placeholder="Payment Security"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button type="submit" className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90">
                      <Save className="ml-2 h-4 w-4" />
                      حفظ إعدادات الإحصائيات
                    </Button>
                  </CardFooter>
                </Card>
              </form>
            </TabsContent>
          )}
          
          {activeTab === "homeSections" && (
            <TabsContent value="homeSections" className="block" data-value="homeSections">
              <form onSubmit={handleSubmit}>
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <SlidersHorizontal className="ml-2 h-5 w-5 text-pubg-orange" />
                      أقسام الصفحة الرئيسية
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      التحكم في ترتيب وإظهار أقسام الصفحة الرئيسية
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="bg-[rgba(25,27,35,0.4)] rounded-lg p-3 md:p-4 mb-4 text-xs md:text-sm">
                      <p className="text-white flex items-center gap-2 mb-2">
                        <GripVertical className="h-5 w-5 text-pubg-orange" />
                        <span>يمكنك ترتيب أقسام الصفحة الرئيسية عن طريق السحب والإفلات</span>
                      </p>
                      <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                        <li>اسحب العنصر وأفلته في المكان المطلوب للترتيب</li>
                        <li>يمكنك أيضًا استخدام الأسهم لتحريك القسم للأعلى أو للأسفل</li>
                        <li>استخدم مفتاح التبديل لإظهار أو إخفاء القسم من الصفحة الرئيسية</li> 
                        <li>اضغط على زر الحفظ بعد الانتهاء من التعديلات</li>
                      </ul>
                    </div>
                    
                    <div className="space-y-2">
                      {formData.homeSectionOrder?.map((sectionId, index) => {
                        const sectionName = getSectionName(sectionId);
                        const isVisible = formData.homeSectionVisibility?.[sectionId] ?? true;
                        
                        return (
                          <div
                            key={sectionId}
                            className="section-item"
                            draggable={true}
                            onDragStart={(e) => handleDragStart(e, index)}
                            onDragOver={(e) => handleDragOver(e)}
                            onDragLeave={(e) => handleDragLeave(e)}
                            onDrop={(e) => handleDrop(e, index)}
                            onDragEnd={(e) => handleDragEnd(e)}
                          >
                            <div className="flex items-center justify-between p-3 md:p-4">
                              <div className="flex items-center gap-3">
                                <div className="drag-handle w-10 h-10 flex items-center justify-center rounded-md cursor-grab active:cursor-grabbing">
                                  <GripVertical className="h-5 w-5" />
                                </div>
                                <div className="section-actions flex flex-col rounded-md">
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 hover:text-white hover:bg-pubg-orange/20 transition-colors"
                                    onClick={() => handleSectionOrderChange(sectionId, 'up')}
                                    disabled={index === 0}
                                    title="نقل لأعلى"
                                  >
                                    <ChevronUp className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 hover:text-white hover:bg-pubg-orange/20 transition-colors"
                                    onClick={() => handleSectionOrderChange(sectionId, 'down')}
                                    disabled={index === formData.homeSectionOrder.length - 1}
                                    title="نقل لأسفل"
                                  >
                                    <ChevronDown className="h-4 w-4" />
                                  </Button>
                                </div>
                                <span className={`text-sm md:text-base font-medium ${!isVisible ? 'text-muted-foreground' : 'text-white'}`}>
                                  {sectionName}
                                </span>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={isVisible}
                                  onCheckedChange={(checked) => handleSectionVisibilityChange(sectionId, checked)}
                                  className={isVisible ? "bg-pubg-orange" : ""}
                                />
                                <Label className="text-xs md:text-sm text-muted-foreground">
                                  {isVisible ? 'ظاهر' : 'مخفي'}
                                </Label>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="submit" 
                      className={`w-full admin-button text-white ${hasUnsavedChanges 
                        ? 'bg-pubg-orange hover:bg-pubg-orange/90' 
                        : 'bg-pubg-orange hover:bg-pubg-orange/90'}`}
                    >
                      <Save className="ml-2 h-4 w-4" />
                      {hasUnsavedChanges ? 'حفظ التغييرات' : 'حفظ ترتيب الأقسام'}
                      {hasUnsavedChanges && <span className="mr-2 animate-pulse">•</span>}
                    </Button>
                  </CardFooter>
                </Card>
              </form>
            </TabsContent>
          )}
          
          {activeTab === "pageVisibility" && (
            <TabsContent value="pageVisibility" className="block" data-value="pageVisibility">
              <Card className="admin-card">
                <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                  <CardTitle className="flex items-center text-white admin-subtitle">
                    <Eye className="ml-2 h-5 w-5 text-pubg-orange" />
                    إدارة النظام
                  </CardTitle>
                  <CardDescription className="text-xs md:text-sm">
                    إعدادات عامة للنظام وإدارة إظهار الصفحات
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                  <div className="admin-form-group mb-6">
                    <Label htmlFor="siteName" className="admin-label">اسم الموقع</Label>
                    <Input
                      id="siteName"
                      name="siteName"
                      value={formData.siteName}
                      onChange={handleInputChange}
                      placeholder="اسم الموقع"
                      className="admin-input"
                    />
                  </div>

                  <div className="border-t border-[rgba(45,50,60,0.7)] pt-6"></div>
                  
                  <div>
                    <h3 className="text-white text-sm md:text-base font-medium mb-4">إظهار وإخفاء الصفحات</h3>
                    <div className="bg-[rgba(25,27,35,0.4)] rounded-lg p-3 md:p-4 mb-4 text-xs md:text-sm">
                      <p className="text-white">
                        يمكنك التحكم في إظهار أو إخفاء الصفحات من القائمة. الصفحات المخفية لن تظهر في شريط التنقل ولن تكون متاحة للزوار.
                      </p>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    {pages.map((page) => (
                      <div 
                        key={page.pageId} 
                        className="flex items-center justify-between bg-[rgba(25,27,35,0.6)] p-3 md:p-4 rounded-lg border border-[rgba(45,50,60,0.7)] hover:border-[rgba(242,169,0,0.4)]"
                      >
                        <div className="flex items-center">
                          {page.isVisible ? (
                            <Eye className="ml-2 h-4 w-4 text-green-500" />
                          ) : (
                            <EyeOff className="ml-2 h-4 w-4 text-red-500" />
                          )}
                          <span className="text-sm md:text-base font-medium text-white">
                            {page.pageName}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch
                            id={`visibility-${page.pageId}`}
                            checked={page.isVisible}
                            onCheckedChange={(checked) => 
                              handleToggleVisibility(page.pageId, checked)
                            }
                            className={page.isVisible ? "bg-pubg-orange" : ""}
                          />
                          <Label 
                            htmlFor={`visibility-${page.pageId}`}
                            className="text-xs md:text-sm text-muted-foreground mr-2"
                          >
                            {page.isVisible ? 'ظاهرة' : 'مخفية'}
                          </Label>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                
                <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                  <Button 
                    type="button" 
                    onClick={handleSubmit} 
                    className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                  >
                    <Save className="ml-2 h-4 w-4" />
                    حفظ الإعدادات
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          )}
          
          {activeTab === "aiModels" && (
            <TabsContent value="aiModels" className="block" data-value="aiModels">
              <Card className="admin-card">
                <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                  <CardTitle className="flex items-center text-white admin-subtitle">
                    <Sparkles className="ml-2 h-5 w-5 text-pubg-orange" />
                    نماذج الذكاء الاصطناعي
                  </CardTitle>
                  <CardDescription className="text-xs md:text-sm">
                    اختر نموذج الذكاء الاصطناعي الافتراضي لجميع الصفحات
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                  <div className="admin-form-group">
                    <Label htmlFor="default-model" className="admin-label">النموذج الافتراضي</Label>
                    <Select value={selectedModel} onValueChange={handleModelChange}>
                      <SelectTrigger id="default-model" className="w-full admin-input">
                        <SelectValue placeholder="اختر نموذجًا" />
                      </SelectTrigger>
                      <SelectContent>
                        <div className="p-2 text-xs font-semibold text-white bg-[rgba(25,27,35,0.6)]">Meta (ميتا)</div>
                        {AVAILABLE_MODELS.filter(model => model.name.includes("Meta")).map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex items-center gap-2">
                              <Globe className="h-4 w-4 text-pubg-orange" />
                              <span>{model.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                        
                        <div className="p-2 text-xs font-semibold text-white bg-[rgba(25,27,35,0.6)]">NVIDIA (نفيديا)</div>
                        {AVAILABLE_MODELS.filter(model => model.name.includes("NVIDIA")).map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex items-center gap-2">
                              <Zap className="h-4 w-4 text-pubg-orange" />
                              <span>{model.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                        
                        <div className="p-2 text-xs font-semibold text-white bg-[rgba(25,27,35,0.6)]">DeepSeek (ديب سيك)</div>
                        {AVAILABLE_MODELS.filter(model => model.name.includes("DeepSeek")).map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex items-center gap-2">
                              <Layers className="h-4 w-4 text-pubg-orange" />
                              <span>{model.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                        
                        <div className="p-2 text-xs font-semibold text-white bg-[rgba(25,27,35,0.6)]">Google (جوجل)</div>
                        {AVAILABLE_MODELS.filter(model => model.name.includes("Google")).map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex items-center gap-2">
                              <Search className="h-4 w-4 text-pubg-orange" />
                              <span>{model.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                        
                        <div className="p-2 text-xs font-semibold text-white bg-[rgba(25,27,35,0.6)]">نماذج أخرى</div>
                        {AVAILABLE_MODELS.filter(model => 
                          !model.name.includes("Meta") && 
                          !model.name.includes("NVIDIA") && 
                          !model.name.includes("DeepSeek") && 
                          !model.name.includes("Google")
                        ).map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex items-center gap-2">
                              <Sparkles className="h-4 w-4 text-pubg-orange" />
                              <span>{model.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs md:text-sm text-muted-foreground mt-1">
                      سيتم استخدام نموذج Meta Llama 4 Maverick افتراضيًا في جميع عمليات الذكاء الاصطناعي ما لم يتم تحديد خلاف ذلك.
                    </p>
                  </div>
                  
                  <div className="pt-3 md:pt-4 border-t border-[rgba(45,50,60,0.7)]">
                    <h4 className="text-sm md:text-md font-semibold mb-2 text-white">معلومات عن النماذج</h4>
                    <ul className="text-xs md:text-sm space-y-2 list-disc pr-5 text-muted-foreground">
                      <li><span className="font-medium text-white">Meta Llama 4:</span> أحدث نماذج Meta مع دعم ممتاز للغة العربية وسياق طويل (256K)</li>
                      <li><span className="font-medium text-white">NVIDIA Nemotron:</span> نماذج قوية من NVIDIA مع قدرات استدلال عالية</li>
                      <li><span className="font-medium text-white">DeepSeek:</span> مجموعة متنوعة من النماذج المتخصصة مع أداء ممتاز</li>
                      <li><span className="font-medium text-white">Google:</span> نماذج Gemini و Gemma للمهام العامة والمتخصصة</li>
                      <li><span className="font-medium text-white">Qwen/Mistral:</span> نماذج سريعة وفعالة مناسبة للاستخدام اليومي</li>
                    </ul>
                    
                    <div className="bg-[rgba(25,27,35,0.4)] rounded-lg p-3 md:p-4 mt-4 text-xs md:text-sm">
                      <p className="text-white flex items-center gap-2 mb-2">
                        <Sparkles className="h-5 w-5 text-pubg-orange" />
                        <span>معلومات عن سياق النماذج</span>
                      </p>
                      <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                        <li>Llama 4 Scout: أطول سياق (512K توكن)</li>
                        <li>Llama 4 Maverick: سياق طويل (256K توكن)</li>
                        <li>نماذج NVIDIA وDeepSeek: سياق جيد (131K-163K توكن)</li>
                        <li>Gemini 2.0 Flash: سياق ممتاز (1M توكن)</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                  <Button 
                    variant="outline" 
                    onClick={resetModelToDefault}
                    className="admin-button bg-transparent hover:bg-pubg-orange/10 border-pubg-orange text-pubg-orange"
                  >
                    إعادة للافتراضي
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          )}
        </div>
      </Tabs>
    </div>
  );
};

export default SettingsManager;
