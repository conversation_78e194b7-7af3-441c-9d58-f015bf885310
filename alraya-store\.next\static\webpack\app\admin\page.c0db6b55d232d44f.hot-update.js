"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"text\",\n        placeholder: \"\",\n        required: false\n    });\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n    };\n    const handleSave = async ()=>{\n        var _formData_name, _formData_category;\n        setIsLoading(true);\n        // Basic validation\n        if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n            alert(\"يرجى إدخال اسم المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!((_formData_category = formData.category) === null || _formData_category === void 0 ? void 0 : _formData_category.trim())) {\n            alert(\"يرجى إدخال فئة المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!formData.packages || formData.packages.length === 0) {\n            alert(\"يرجى إضافة حزمة واحدة على الأقل\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const productData = {\n                name: formData.name,\n                description: formData.description,\n                category: formData.category,\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: formData.fields,\n                packages: formData.packages,\n                features: formData.features,\n                tags: formData.tags,\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.updateProduct)(product.id, productData);\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.createProduct)(productData);\n            }\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            alert(\"حدث خطأ أثناء حفظ المنتج\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"text\",\n            placeholder: \"\",\n            required: false\n        });\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            discount: pkg.discount || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800/90 backdrop-blur-md rounded-xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 md:p-6 border-b border-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg md:text-2xl font-bold text-white\",\n                            children: isEditing ? \"تعديل المنتج\" : \"إضافة منتج جديد\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onCancel,\n                            className: \"text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 md:w-6 md:h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 md:p-6 space-y-4 md:space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2 text-white\",\n                                        children: \"اسم المنتج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name || \"\",\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                        placeholder: \"أدخل اسم المنتج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2 text-white\",\n                                        children: \"الفئة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.category || \"\",\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    category: e.target.value\n                                                })),\n                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2 text-white\",\n                                children: \"الوصف\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: formData.description || \"\",\n                                onChange: (e)=>setFormData((prev)=>({\n                                            ...prev,\n                                            description: e.target.value\n                                        })),\n                                rows: 3,\n                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                placeholder: \"وصف المنتج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2 text-white\",\n                                children: \"العلامات (مفصولة بفاصلة)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                onChange: (e)=>setFormData((prev)=>({\n                                            ...prev,\n                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                        })),\n                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                placeholder: \"شائع, مميز, جديد\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2 text-white\",\n                                children: \"صورة الغلاف\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.image || \"\",\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    image: e.target.value\n                                                })),\n                                        className: \"flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                        placeholder: \"رابط الصورة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"رفع\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-6 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 space-x-reverse text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: formData.isFeatured || false,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    isFeatured: e.target.checked\n                                                })),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"منتج مميز\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 space-x-reverse text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    isActive: e.target.checked\n                                                })),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"منتج نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isEditing ? \"تحديث المنتج\" : \"إضافة المنتج\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 742,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 616,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 763,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 785,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 762,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 761,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"LE3g/gyLSgQ/gpklPFWvfGJTS+o=\");\n_c = SimpleProductForm;\nvar _c;\n$RefreshReg$(_c, \"SimpleProductForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});