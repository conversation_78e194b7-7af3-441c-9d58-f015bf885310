import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function FeaturedDeals() {
  const deals = [
    {
      id: 1,
      title: "Summer Sale",
      discount: "50%",
      description: "Get up to 50% off on selected titles.",
    },
    {
      id: 2,
      title: "New Release Bundle",
      discount: "20%",
      description: "Buy the latest game and get its DLC at a 20% discount.",
    },
    {
      id: 3,
      title: "Indie Game Pack",
      discount: "70%",
      description: "Discover hidden gems with our indie game collection.",
    },
    {
      id: 4,
      title: "VR Experience Kit",
      discount: "15%",
      description: "Step into new worlds with our VR starter pack.",
    },
  ]

  return (
    <section className="py-16 bg-sky-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">العروض المميزة</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            اغتنم الفرصة واحصل على أفضل العروض والخصومات الحصرية
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {deals.map((deal) => (
            <div key={deal.id} className="bg-card p-6 rounded-lg shadow-lg">
              <h3 className="text-2xl font-bold mb-2">{deal.title}</h3>
              <p className="text-5xl font-extrabold text-primary mb-4">{deal.discount}</p>
              <p className="text-muted-foreground mb-6">{deal.description}</p>
              <Button>View Deal</Button>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button
            variant="outline"
            size="lg"
            className="border-slate-900 text-slate-900 hover:bg-slate-900 hover:text-white"
          >
            عرض جميع العروض
          </Button>
        </div>
      </div>
    </section>
  )
}
