import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Plus, Pencil, Trash2, Save, X, List, Trash, Copy, Sparkles, Globe, Monitor, Smartphone } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { ModModel as BaseModModel, getMods, addMod, updateMod, deleteMod } from "@/services/firestore";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import ImageUploader from "./ImageUploader";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Extend the ModModel to support string values for price fields during input
interface ModModel extends Omit<BaseModModel, 'priceUSD' | 'localCurrency'> {
  priceUSD: number | string;
  localCurrency: number | string;
  title_en?: string;
  description_en?: string;
  platform?: "pc" | "mobile";
  mobileType?: "android" | "ios";
  rootRequired?: boolean;
  jailbreakRequired?: boolean;
}

// Create a custom clearable input component
const ClearableInput = ({ 
  value, 
  onChange, 
  onClear, 
  ...props 
}: { 
  value: string | number; 
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; 
  onClear: () => void; 
} & React.InputHTMLAttributes<HTMLInputElement>) => {
  return (
    <div className="relative">
      <Input
        value={value}
        onChange={onChange}
        className="pr-8"
        {...props}
      />
      {value && (
        <button
          type="button"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-5 w-5 rounded-full bg-gray-500/20 flex items-center justify-center text-gray-400 hover:bg-gray-500/30 hover:text-gray-100"
          onClick={onClear}
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </div>
  );
};

// Add default content templates after the ClearableInput component
const DEFAULT_CONTENT = {
  pc: {
    features: [
      "نظام ESP المتقدم وتتبع اللاعبين | Advanced ESP system and player tracking",
      "نظام مساعدة التصويب الذكي | Smart aim assistance system",
      "واجهة إعدادات قابلة للتخصيص | Customizable settings interface",
      "تحليل بيانات اللعبة في الوقت الفعلي | Real-time game data analysis",
      "الوصول إلى قناة الدعم ذات الأولوية | Access to priority support channel",
      "تحديثات وإصلاحات تلقائية | Automatic updates and fixes"
    ],
    requirements: [
      "ويندوز 10 برو x64bit 1909/20H2 | Windows 10 Pro x64bit 1909/20H2",
      "نظام ويندوز جديد مع التعريفات | Fresh Windows system with drivers",
      "مطلوب حساب موبايل جديد/مستخدم | New/alt mobile account required",
      "معرفة أساسية بالأدوات مطلوبة | Basic knowledge of tools required"
    ],
    includedSoftware: [
      "ويندوز 10 20H2 | Windows 10 20H2",
      "AIO Runtime 2.5 | AIO Runtime 2.5",
      "التعريفات الكاملة | Full drivers",
      "التحكم في الحماية | Security control"
    ],
    defaultDescription: "هاك PUBG متطور مع نظام ESP كامل ومضاد للحظر. يتضمن رادار سلس لكشف اللاعبين والمركبات والعناصر، نظام تصويب دقيق ومستقر مع حماية 100%. تم تطويره خصيصًا للاعبين المحترفين مع دعم فني مستمر وتحديثات منتظمة. | Advanced PUBG hack with full ESP system and anti-ban protection. Features smooth radar for player, vehicle, and item detection, precise and stable aim system with 100% protection. Specially developed for pro players with continuous technical support and regular updates."
  },
  android: {
    features: [
      "نظام ESP المتقدم وتتبع اللاعبين | Advanced ESP system and player tracking",
      "نظام مساعدة التصويب الذكي | Smart aim assistance system",
      "حماية مضادة للحظر | Anti-ban protection",
      "تحديث تلقائي للسكربت | Auto-script update",
      "دعم لجميع إصدارات أندرويد 8+ | Support for all Android 8+ versions",
      "أداء محسن مع استقرار عالي | Optimized performance with high stability"
    ],
    requirements: [
      "جهاز أندرويد بإصدار 8.0 أو أحدث | Android device with version 8.0 or newer",
      "مساحة فارغة 2GB على الأقل | At least 2GB of free space",
      "صلاحيات ROOT مطلوبة | ROOT permissions required",
      "مطلوب حساب جديد للتجربة | New account required for testing",
      "إيقاف مضاد الفيروسات | Disable antivirus"
    ],
    includedSoftware: [
      "تطبيق الهاك المميز | Premium hack application",
      "برنامج حماية إضافي | Additional protection software",
      "أداة تعديل الذاكرة | Memory modification tool",
      "تطبيق Virtual Space | Virtual Space application"
    ],
    defaultDescription: "هاك PUBG للاندرويد مع حماية وأمان 100%. يتميز برادار سلس لكشف أماكن اللاعبين والعربيات والعناصر، تصغير مؤشر، منظور ايباد، تأثير ضرر، ايمبوت SDK آمن 100%، ثبات سلاح، ماجك جسم لاعب وماجك رأس. متوافق مع آخر تحديثات اللعبة ويدعم وضع Ocean Odyssey بالكامل. | Advanced Android PUBG hack with 100% security and protection. Features smooth ESP for players, vehicles, and items, small crosshair, iPad view, hit effect, safe SDK aimbot, weapon stability, magic body and magic head. Compatible with the latest game updates and fully supports Ocean Odyssey mode."
  },
  ios: {
    features: [
      "نظام ESP المتقدم وتتبع اللاعبين | Advanced ESP system and player tracking",
      "نظام مساعدة التصويب الذكي | Smart aim assistance system",
      "حماية مضادة للحظر | Anti-ban protection",
      "تحديثات فورية | Instant updates",
      "توافق مع أحدث إصدار iOS | Compatible with latest iOS version",
      "استهلاك منخفض للبطارية | Low battery consumption"
    ],
    requirements: [
      "جهاز iOS بإصدار 14.0 أو أحدث | iOS device with version 14.0 or newer",
      "مطلوب جيلبريك | Jailbreak required",
      "تثبيت Cydia/Sileo | Cydia/Sileo installation",
      "مساحة فارغة 1GB على الأقل | At least 1GB of free space",
      "النسخ الاحتياطي قبل التثبيت | Backup before installation"
    ],
    includedSoftware: [
      "حزمة الهاك الكاملة | Complete hack package",
      "أدوات الجيلبريك | Jailbreak tools",
      "إضافات Cydia المطلوبة | Required Cydia tweaks",
      "برنامج الحماية والإخفاء | Protection and hiding software"
    ],
    defaultDescription: "هاك PUBG للايفون مع حماية وأمان 100%. يتضمن رادار سلس لكشف اللاعبين والمركبات، تصغير مؤشر، منظور ايباد واسع، تأثير ضرب محسن، ايمبوت آمن 100% بمدى 100 متر، ماجك جسم وماجك رأس. يدعم آخر تحديثات اللعبة بما فيها وضع Ocean Odyssey ويتميز بسهولة التحكم وزر سريع لتفعيل وإيقاف الايمبوت. | Premium iOS PUBG hack with 100% protection. Features smooth ESP radar for players and vehicles, small crosshair, wide iPad view, enhanced hit effect, 100% safe aimbot with 100m range, magic body and magic head features. Supports the latest game updates including Ocean Odyssey mode with easy controls and quick aimbot toggle button."
  }
};

const ModManager = () => {
  const { toast } = useToast();
  const [mods, setMods] = useState<ModModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingMod, setEditingMod] = useState<ModModel | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showBulkAddDialog, setShowBulkAddDialog] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isBulkProcessing, setIsBulkProcessing] = useState(false);
  const [bulkModData, setBulkModData] = useState("");
  const [selectedMods, setSelectedMods] = useState<Set<string>>(new Set());
  const [formData, setFormData] = useState<Partial<ModModel>>({
    title: "",
    title_en: "",
    description: "",
    description_en: "",
    priceUSD: "",
    localCurrency: "",
    localCurrencyCode: "",
    image: "https://rngvip.online/images/RNGANDROID.webp",
    features: [],
    requirements: [],
    includedSoftware: [],
    platform: "pc",
    mobileType: "android",
    rootRequired: false,
    jailbreakRequired: false,
    featured: false,
    special: false,
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const modsData = await getMods();
        // Convert BaseModModel to ModModel with string|number for price fields
        const convertedMods: ModModel[] = modsData.map(mod => ({
          ...mod,
          priceUSD: mod.priceUSD,
          localCurrency: mod.localCurrency || 0
        }));
        setMods(convertedMods);
        setIsLoading(false);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load mods data",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      setFormData({ ...formData, [name]: (e.target as HTMLInputElement).checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleArrayInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>, field: keyof ModModel) => {
    const value = e.target.value;
    const array = value.split("\n").filter(item => item.trim());
    setFormData({ ...formData, [field]: array });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Determine which platform template to use
      const platformType = formData.platform || 'pc';
      const mobileType = formData.mobileType || 'android';
      const platformKey = platformType === 'pc' ? 'pc' : mobileType;
      
      // Apply default content for empty arrays
      const features = formData.features && formData.features.length > 0 
        ? formData.features 
        : DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].features;
      
      const requirements = formData.requirements && formData.requirements.length > 0 
        ? formData.requirements 
        : DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].requirements;
      
      const includedSoftware = formData.includedSoftware && formData.includedSoftware.length > 0 
        ? formData.includedSoftware 
        : DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].includedSoftware;

      // Get default description if empty
      const descriptionTemplate = DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].defaultDescription || '';
      const descriptionParts = descriptionTemplate.split(' | ');
      
      // Default descriptions in Arabic and English
      const defaultDescription = descriptionParts.length > 1 ? descriptionParts[1] : descriptionTemplate;
      const defaultDescriptionAr = descriptionParts.length > 1 ? descriptionParts[0] : descriptionTemplate;

      // Process numeric fields to ensure they are numbers
      const processedData: Omit<BaseModModel, "id" | "createdAt" | "updatedAt"> = {
        ...formData,
        priceUSD: typeof formData.priceUSD === 'string' ? parseFloat(formData.priceUSD) || 0 : formData.priceUSD,
        localCurrency: typeof formData.localCurrency === 'string' ? parseFloat(formData.localCurrency) || 0 : formData.localCurrency,
        features: features,
        requirements: requirements,
        includedSoftware: includedSoftware,
        title: formData.title || '',
        title_en: formData.title_en || '',
        description: formData.description || defaultDescriptionAr,
        description_en: formData.description_en || defaultDescription,
        localCurrencyCode: formData.localCurrencyCode || '',
        platform: formData.platform || 'pc',
        mobileType: formData.mobileType || 'android',
        rootRequired: platformKey === 'android' ? Boolean(formData.rootRequired) : false,
        jailbreakRequired: platformKey === 'ios' ? Boolean(formData.jailbreakRequired) : false,
        image: formData.image || '',
        featured: Boolean(formData.featured),
        special: Boolean(formData.special),
      };

      if (editingMod) {
        await updateMod(editingMod.id!, processedData);
        setMods(mods.map((mod) =>
          mod.id === editingMod.id ? { ...mod, ...processedData, id: editingMod.id } : mod
        ));
        toast({
          title: "Success",
          description: "Mod updated successfully",
        });
        setEditingMod(null);
      } else {
        const id = await addMod(processedData);
        setMods([...mods, { ...processedData, id } as ModModel]);
        toast({
          title: "Success",
          description: "Mod added successfully",
        });
        setShowAddDialog(false);
      }

      resetForm();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save mod",
        variant: "destructive",
      });
    }

    setIsLoading(false);
  };

  const resetForm = () => {
    setFormData({
      title: "",
      title_en: "",
      description: "",
      description_en: "",
      priceUSD: "",
      localCurrency: "",
      localCurrencyCode: "",
      image: "https://rngvip.online/images/RNGANDROID.webp",
      features: [],
      requirements: [],
      includedSoftware: [],
      platform: "pc",
      mobileType: "android",
      rootRequired: false,
      jailbreakRequired: false,
      featured: false,
      special: false,
    });
  };

  const handleEdit = (mod: ModModel) => {
    setEditingMod(mod);
    
    // Determine platform values
    const platform = mod.platform || 'pc';
    const mobileType = mod.mobileType || 'android';
    
    // Set appropriate platform-specific flags
    const rootRequired = platform === 'mobile' && mobileType === 'android' ? (mod.rootRequired || false) : false;
    const jailbreakRequired = platform === 'mobile' && mobileType === 'ios' ? (mod.jailbreakRequired || false) : false;
    
    setFormData({
      title: mod.title,
      title_en: mod.title_en || '',
      description: mod.description,
      description_en: mod.description_en || '',
      priceUSD: mod.priceUSD,
      localCurrency: mod.localCurrency || '',
      localCurrencyCode: mod.localCurrencyCode || '',
      image: mod.image,
      features: mod.features,
      requirements: mod.requirements,
      includedSoftware: mod.includedSoftware,
      platform: platform,
      mobileType: mobileType,
      rootRequired: rootRequired,
      jailbreakRequired: jailbreakRequired,
      featured: mod.featured || false,
      special: mod.special || false,
    });
    
    setShowAddDialog(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this mod?")) {
      setIsLoading(true);
      try {
        await deleteMod(id);
        setMods(mods.filter((mod) => mod.id !== id));
        toast({
          title: "Success",
          description: "Mod deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete mod",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    }
  };

  const resetFormAndClose = () => {
    setEditingMod(null);
    setShowAddDialog(false);
    resetForm();
  };

  const handleSelectMod = (id: string) => {
    setSelectedMods(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedMods.size === mods.length) {
      setSelectedMods(new Set());
    } else {
      setSelectedMods(new Set(mods.map(mod => mod.id!)));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedMods.size === 0) return;

    if (window.confirm(`هل أنت متأكد من حذف ${selectedMods.size} هاك؟`)) {
      setIsLoading(true);
      const errors: string[] = [];

      try {
        for (const id of selectedMods) {
          try {
            await deleteMod(id);
          } catch (error) {
            errors.push(`Failed to delete mod ${id}: ${error instanceof Error ? error.message : String(error)}`);
          }
        }

        setMods(mods.filter(mod => !selectedMods.has(mod.id!)));
        setSelectedMods(new Set());

        if (errors.length === 0) {
          toast({
            title: "نجاح",
            description: `تم حذف ${selectedMods.size} هاك بنجاح`,
          });
        } else {
          toast({
            title: "تحذير",
            description: `تم حذف بعض المودات مع وجود ${errors.length} أخطاء`,
            variant: "destructive",
          });
          console.error("Bulk delete errors:", errors);
        }
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في حذف المودات المحددة",
          variant: "destructive",
        });
      }

      setIsLoading(false);
    }
  };

  const handleBulkModDataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setBulkModData(e.target.value);
  };

  const handleProcessBulkModData = async () => {
    setIsBulkProcessing(true);
    
    try {
      let modsToAdd = [];
      
      try {
        const parsedData = JSON.parse(bulkModData);
        if (Array.isArray(parsedData)) {
          modsToAdd = parsedData;
        } else {
          modsToAdd = [parsedData];
        }
      } catch (error) {
        toast({
          title: "خطأ في التنسيق",
          description: "تأكد من صحة تنسيق JSON",
          variant: "destructive",
        });
        setIsBulkProcessing(false);
        return;
      }
      
      // Process each mod
      for (const modData of modsToAdd) {
        try {
          // Determine platform values
          const platform = modData.platform || 'pc';
          const mobileType = modData.mobileType || 'android';
          const platformKey = platform === 'pc' ? 'pc' : mobileType;
          
          // Set appropriate platform-specific flags
          const rootRequired = platform === 'mobile' && mobileType === 'android' ? (modData.rootRequired || false) : false;
          const jailbreakRequired = platform === 'mobile' && mobileType === 'ios' ? (modData.jailbreakRequired || false) : false;
          
          // If fields are empty and platform is specified, use the default templates
          const features = modData.features && modData.features.length > 0 
            ? modData.features 
            : DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].features;
          
          const requirements = modData.requirements && modData.requirements.length > 0 
            ? modData.requirements 
            : DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].requirements;
          
          const includedSoftware = modData.includedSoftware && modData.includedSoftware.length > 0 
            ? modData.includedSoftware 
            : DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].includedSoftware;
          
          // Get default description if empty
          const descriptionTemplate = DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].defaultDescription || '';
          const descriptionParts = descriptionTemplate.split(' | ');
          
          // Default descriptions in Arabic and English
          const defaultDescription = descriptionParts.length > 1 ? descriptionParts[1] : descriptionTemplate;
          const defaultDescriptionAr = descriptionParts.length > 1 ? descriptionParts[0] : descriptionTemplate;
          
          // Ensure numeric fields are numbers
          const processedData: Omit<BaseModModel, "id" | "createdAt" | "updatedAt"> = {
            title: modData.title || "",
            title_en: modData.title_en || "",
            description: modData.description || defaultDescriptionAr,
            description_en: modData.description_en || defaultDescription,
            priceUSD: typeof modData.priceUSD === 'string' ? parseFloat(modData.priceUSD) || 0 : (modData.priceUSD || 0),
            localCurrency: typeof modData.localCurrency === 'string' ? parseFloat(modData.localCurrency) || 0 : (modData.localCurrency || 0),
            localCurrencyCode: modData.localCurrencyCode || "",
            platform: platform,
            mobileType: mobileType,
            rootRequired: rootRequired,
            jailbreakRequired: jailbreakRequired,
            image: modData.image || "https://rngvip.online/images/RNGANDROID.webp",
            features: features,
            requirements: requirements,
            includedSoftware: includedSoftware,
            featured: Boolean(modData.featured),
            special: Boolean(modData.special),
          };

          await addMod(processedData);
        } catch (error) {
          console.error("Error processing mod entry:", error);
        }
      }

      // Refresh the mods list
      const modsData = await getMods();
      // Convert BaseModModel to ModModel with string|number for price fields
      const convertedMods: ModModel[] = modsData.map(mod => ({
        ...mod,
        priceUSD: mod.priceUSD,
        localCurrency: mod.localCurrency || 0
      }));
      setMods(convertedMods);
      
      toast({
        title: "نجاح",
        description: "تم إضافة الهاكات بنجاح",
      });
      
      setShowBulkAddDialog(false);
      setBulkModData("");
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في معالجة البيانات",
        variant: "destructive",
      });
    } finally {
      setIsBulkProcessing(false);
    }
  };

  const handleBulkModDataSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await handleProcessBulkModData();
    setShowBulkAddDialog(false);
  };

  const handleDuplicate = async (mod: ModModel) => {
    try {
      // Determine platform values
      const platform = mod.platform || 'pc';
      const mobileType = mod.mobileType || 'android';
      
      // Set appropriate platform-specific flags
      const rootRequired = platform === 'mobile' && mobileType === 'android' ? (mod.rootRequired || false) : false;
      const jailbreakRequired = platform === 'mobile' && mobileType === 'ios' ? (mod.jailbreakRequired || false) : false;
      
      const duplicatedMod: Omit<BaseModModel, "id" | "createdAt" | "updatedAt"> = {
        ...mod,
        title: `${mod.title} (نسخة)`,
        title_en: mod.title_en ? `${mod.title_en} (Copy)` : '',
        priceUSD: typeof mod.priceUSD === 'string' ? parseFloat(mod.priceUSD) || 0 : mod.priceUSD,
        localCurrency: typeof mod.localCurrency === 'string' ? parseFloat(mod.localCurrency) || 0 : mod.localCurrency,
        localCurrencyCode: mod.localCurrencyCode || '',
        features: mod.features || [],
        requirements: mod.requirements || [],
        includedSoftware: mod.includedSoftware || [],
        description: mod.description || '',
        description_en: mod.description_en || '',
        platform: platform,
        mobileType: mobileType,
        rootRequired: rootRequired,
        jailbreakRequired: jailbreakRequired,
        image: mod.image || '',
        featured: Boolean(mod.featured),
        special: Boolean(mod.special),
      };
      
      const id = await addMod(duplicatedMod);
      
      const newMod = { ...duplicatedMod, id } as ModModel;
      setMods([...mods, newMod]);
      
      toast({
        title: "تم بنجاح",
        description: "تم إنشاء نسخة من الهاك بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في نسخ الهاك",
        variant: "destructive",
      });
    }
  };

  // Add function to show a preview of the default content based on selected platform
  const getDefaultContentPreview = (field: 'features' | 'requirements' | 'includedSoftware') => {
    const platformType = formData.platform || 'pc';
    const mobileType = formData.mobileType || 'android';
    const platformKey = platformType === 'pc' ? 'pc' : mobileType;
    
    const contentArray = DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT][field];
    return contentArray.join('\n');
  };

  // After the getDefaultContentPreview function, add a function to get description preview
  const getDefaultDescriptionPreview = () => {
    const platformType = formData.platform || 'pc';
    const mobileType = formData.mobileType || 'android';
    const platformKey = platformType === 'pc' ? 'pc' : mobileType;
    
    const template = DEFAULT_CONTENT[platformKey as keyof typeof DEFAULT_CONTENT].defaultDescription || '';
    const parts = template.split(' | ');
    
    return {
      ar: parts.length > 1 ? parts[0] : template,
      en: parts.length > 1 ? parts[1] : template
    };
  };

  // Modify the textarea fields in the form to show the default preview button
  const renderTextareaWithDefaultOption = (field: 'features' | 'requirements' | 'includedSoftware', label: string, labelEn: string) => {
    const value = formData[field] ? (formData[field] as string[]).join('\n') : '';
    const hasValue = value.trim().length > 0;
    
    return (
      <div className="space-y-1 sm:space-y-2 sm:col-span-2">
        <div className="flex justify-between items-center">
          <Label htmlFor={field} className="text-sm">{label}</Label>
          <div className="flex items-center gap-2">
            {!hasValue && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="text-xs h-5 px-2 text-muted-foreground"
                onClick={() => setFormData({
                  ...formData,
                  [field]: DEFAULT_CONTENT[
                    (formData.platform === 'pc' ? 'pc' : formData.mobileType || 'android') as keyof typeof DEFAULT_CONTENT
                  ][field]
                })}
              >
                استخدام القالب | Use template
              </Button>
            )}
            <div className="flex items-center gap-1 text-purple-500 text-xs">
              <span className="h-2 w-2 rounded-full bg-purple-500"></span>
              عربي/English
            </div>
          </div>
        </div>
        <Textarea
          id={field}
          placeholder={hasValue ? "" : `أدخل ${labelEn} بتنسيق: النص العربي | English text (سطر واحد لكل عنصر)\nEnter ${labelEn} in format: Arabic text | English text (one line per item)`}
          rows={6}
          value={value}
          onChange={(e) => handleArrayInputChange(e, field as keyof ModModel)}
          className="min-h-[120px] font-mono text-sm"
          dir="auto"
        />
        {!hasValue && (
          <div className="text-xs text-muted-foreground mt-1">
            <div className="flex items-center">
              <span className="h-1.5 w-1.5 rounded-full bg-yellow-500 mr-1.5"></span>
              سيتم استخدام القالب الافتراضي إذا تركت هذا الحقل فارغًا
            </div>
            <div className="flex items-center">
              <span className="h-1.5 w-1.5 rounded-full bg-yellow-500 mr-1.5"></span>
              Default template will be used if left empty
            </div>
          </div>
        )}
      </div>
    );
  };

  // Add a new function to handle platform changes
  const handlePlatformChange = (platform: "pc" | "mobile") => {
    setFormData({
      ...formData,
      platform,
      // Reset platform-specific fields when changing platforms
      rootRequired: platform === 'mobile' && formData.mobileType === 'android',
      jailbreakRequired: platform === 'mobile' && formData.mobileType === 'ios'
    });
  };

  // Add a new function to handle mobile type changes
  const handleMobileTypeChange = (mobileType: "android" | "ios") => {
    setFormData({
      ...formData,
      mobileType,
      // Update relevant flags based on mobile type
      rootRequired: mobileType === 'android',
      jailbreakRequired: mobileType === 'ios'
    });
  };

  if (isLoading && mods.length === 0) {
    return <div className="text-center py-8">Loading mods data...</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6 flex-col sm:flex-row gap-4">
        <h2 className="text-xl font-bold text-white">إدارة الهاكات</h2>
        <div className="flex flex-wrap gap-2 justify-end w-full sm:w-auto">
          {selectedMods.size > 0 && (
            <Button
              onClick={handleBulkDelete}
              className="bg-pubg-gray text-white hover:bg-pubg-gray/90 h-8 px-2 py-1 text-xs sm:text-sm"
              disabled={isLoading}
            >
              <Trash className="ml-1 h-3 w-3" />
              حذف {selectedMods.size}
            </Button>
          )}
          <Button
            onClick={() => setShowBulkAddDialog(true)}
            className="bg-pubg-gray text-white hover:bg-pubg-gray/90 h-8 px-2 py-1 text-xs sm:text-sm"
          >
            <List className="ml-1 h-3 w-3" />
            إضافة متعددة
          </Button>
          <Button
            onClick={() => setShowAddDialog(true)}
            className="bg-pubg-gray text-white hover:bg-pubg-gray/90 h-8 px-2 py-1 text-xs sm:text-sm"
          >
            <Plus className="ml-1 h-3 w-3" />
            إضافة هاك
          </Button>
        </div>
      </div>

      <Alert className="mb-6 bg-pubg-black border-pubg-orange">
        <Sparkles className="h-5 w-5 text-pubg-orange" />
        <AlertTitle className="font-bold text-white">مساعد الذكاء الاصطناعي متاح الآن!</AlertTitle>
        <AlertDescription className="text-muted-foreground">
          يمكنك الآن استخدام الذكاء الاصطناعي Gemini لتحليل منشورات التليجرام وملء نماذج المودات تلقائيًا. 
          فقط انسخ والصق المحتوى واترك الذكاء الاصطناعي يقوم بالباقي!
        </AlertDescription>
      </Alert>

      {/* Mods List */}
      <div className="space-y-4">
        {mods.length > 0 ? (
          <>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedMods.size === mods.length}
                  onChange={handleSelectAll}
                  className="ml-2 w-4 h-4"
                />
                <span className="text-sm text-muted-foreground">
                  {selectedMods.size > 0
                    ? `تم تحديد ${selectedMods.size} هاك`
                    : "تحديد الكل"}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4">
              {mods.map((mod) => (
                <div
                  key={mod.id}
                  className="glass-card rounded-lg p-3 flex flex-col"
                >
                  <div className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      checked={selectedMods.has(mod.id!)}
                      onChange={() => handleSelectMod(mod.id!)}
                      className="ml-2 w-4 h-4"
                    />
                    <div className="w-10 h-10 sm:w-12 sm:h-12 ml-2 sm:ml-4 shrink-0">
                      <img
                        src={mod.image}
                        alt={mod.title}
                        className="w-full h-full object-cover rounded-md"
                      />
                    </div>
                    <div className="truncate">
                      <div className="font-bold text-base sm:text-lg truncate">{mod.title}</div>
                      <div className="text-muted-foreground text-xs sm:text-sm">
                        {mod.localCurrency ? `${mod.localCurrency} ${mod.localCurrencyCode || ''}` : ''} / ${mod.priceUSD}
                      </div>
                    </div>
                  </div>
                  <div className="mt-auto pt-2 border-t border-border flex justify-end gap-1">
                    <Button
                      onClick={() => handleDuplicate(mod)}
                      size="sm"
                      variant="outline"
                      className="h-7 px-2 text-xs"
                    >
                      <Copy className="ml-1 h-3 w-3" />
                      نسخ
                    </Button>
                    <Button
                      onClick={() => handleEdit(mod)}
                      size="sm"
                      className="bg-pubg-gray text-white hover:bg-pubg-gray/90 h-7 px-2 text-xs"
                    >
                      <Pencil className="ml-1 h-3 w-3" />
                      تعديل
                    </Button>
                    <Button
                      onClick={() => handleDelete(mod.id!)}
                      size="sm"
                      variant="destructive"
                      className="h-7 px-2 text-xs"
                    >
                      <Trash2 className="ml-1 h-3 w-3" />
                      حذف
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-10">
            <p className="text-lg text-muted-foreground">لا توجد هاكات حتى الآن</p>
            <Button 
              onClick={() => setShowAddDialog(true)} 
              className="mt-4 bg-pubg-gray text-white hover:bg-pubg-gray/90"
            >
              <Plus className="ml-2 h-4 w-4" />
              إضافة هاك جديد
            </Button>
          </div>
        )}
      </div>

      {/* Add/Edit Mod Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-3 sm:p-6 w-[95vw] sm:w-auto">
          <DialogHeader className="pb-2">
            <DialogTitle>{editingMod ? "تعديل هاك" : "إضافة هاك جديد"}</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm">
              {editingMod ? "قم بتعديل بيانات الهاك" : "قم بإضافة بيانات الهاك الجديد"}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="title" className="text-sm">اسم الهاك</Label>
                  <div className="flex items-center gap-1 text-red-500 text-xs">
                    <span className="h-2 w-2 rounded-full bg-red-500"></span>
                    العربية
                  </div>
                </div>
                <ClearableInput
                  id="title"
                  name="title"
                  value={formData.title || ""}
                  onChange={handleInputChange}
                  onClear={() => setFormData({ ...formData, title: "" })}
                  required
                  dir="rtl"
                />
              </div>
              
              <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="title_en" className="text-sm">Hack Name</Label>
                  <div className="flex items-center gap-1 text-blue-500 text-xs">
                    <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                    English
                  </div>
                </div>
                <ClearableInput
                  id="title_en"
                  name="title_en"
                  value={formData.title_en || ""}
                  onChange={handleInputChange}
                  onClear={() => setFormData({ ...formData, title_en: "" })}
                  dir="ltr"
                />
              </div>
              
              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="priceUSD" className="text-sm">السعر (USD)</Label>
                <ClearableInput
                  id="priceUSD"
                  name="priceUSD"
                  type="number"
                  value={formData.priceUSD || ""}
                  onChange={handleInputChange}
                  onClear={() => setFormData({ ...formData, priceUSD: "" })}
                  required
                />
              </div>
              
              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="localCurrencyCode" className="text-sm">رمز العملة المحلية</Label>
                <ClearableInput
                  id="localCurrencyCode"
                  name="localCurrencyCode"
                  value={formData.localCurrencyCode || ""}
                  onChange={handleInputChange}
                  onClear={() => setFormData({ ...formData, localCurrencyCode: "" })}
                  placeholder="SAR, AED, etc."
                />
              </div>
              
              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="localCurrency" className="text-sm">السعر بالعملة المحلية</Label>
                <ClearableInput
                  id="localCurrency"
                  name="localCurrency"
                  type="number"
                  value={formData.localCurrency || ""}
                  onChange={handleInputChange}
                  onClear={() => setFormData({ ...formData, localCurrency: "" })}
                />
              </div>
              
              <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="description" className="text-sm">وصف الهاك (العربية)</Label>
                  <div className="flex items-center gap-1 text-red-500 text-xs">
                    {!formData.description && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="text-xs h-5 px-2 text-muted-foreground"
                        onClick={() => setFormData({
                          ...formData,
                          description: getDefaultDescriptionPreview().ar
                        })}
                      >
                        استخدام القالب
                      </Button>
                    )}
                    <span className="h-2 w-2 rounded-full bg-red-500"></span>
                    العربية
                  </div>
                </div>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description || ""}
                  onChange={handleInputChange}
                  className="resize-y min-h-[100px]"
                  dir="rtl"
                />
                {!formData.description && (
                  <div className="text-xs text-amber-500 mt-1 flex items-center gap-1">
                    <span className="h-2 w-2 rounded-full bg-amber-500"></span>
                    سيتم استخدام وصف تلقائي
                  </div>
                )}
              </div>
              
              <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="description_en" className="text-sm">Hack Description (English)</Label>
                  <div className="flex items-center gap-1 text-blue-500 text-xs">
                    {!formData.description_en && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="text-xs h-5 px-2 text-muted-foreground"
                        onClick={() => setFormData({
                          ...formData,
                          description_en: getDefaultDescriptionPreview().en
                        })}
                      >
                        Use template
                      </Button>
                    )}
                    <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                    English
                  </div>
                </div>
                <Textarea
                  id="description_en"
                  name="description_en"
                  value={formData.description_en || ""}
                  onChange={handleInputChange}
                  className="resize-y min-h-[100px]"
                  dir="ltr"
                />
                {!formData.description_en && (
                  <div className="text-xs text-amber-500 mt-1 flex items-center gap-1">
                    <span className="h-2 w-2 rounded-full bg-amber-500"></span>
                    Auto description will be used
                  </div>
                )}
              </div>
              
              <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                <Label htmlFor="image" className="text-sm">رابط الصورة</Label>
                <div className="flex space-x-2 space-x-reverse">
                  <div className="flex-1 relative">
                    <Input
                      id="image"
                      name="image"
                      value={formData.image || ""}
                      onChange={handleInputChange}
                      className="pr-8"
                    />
                    {formData.image && (
                      <button
                        type="button"
                        className="absolute right-2 top-1/2 -translate-y-1/2 h-5 w-5 rounded-full bg-gray-500/20 flex items-center justify-center text-gray-400 hover:bg-gray-500/30 hover:text-gray-100"
                        onClick={() => setFormData({ ...formData, image: "" })}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    )}
                  </div>
                  <ImageUploader
                    imageUrl={formData.image || ""}
                    onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                    fieldName="image"
                    aspectRatio={1}
                  />
                </div>
              </div>
              
              <div className="space-y-4 sm:col-span-2 mt-2">
                <div className="glass-card p-4 rounded-lg">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <Monitor className="ml-2 h-4 w-4" />
                    منصة الهاك
                  </h3>
                  <RadioGroup
                    value={formData.platform}
                    onValueChange={(value) => handlePlatformChange(value as "pc" | "mobile")}
                    className="flex space-x-4 space-x-reverse"
                  >
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <RadioGroupItem value="pc" id="platform-pc" />
                      <Label htmlFor="platform-pc" className="flex items-center cursor-pointer">
                        <Monitor className="mr-2 rtl:ml-2 rtl:mr-0 h-4 w-4" />
                        PC / كمبيوتر
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <RadioGroupItem value="mobile" id="platform-mobile" />
                      <Label htmlFor="platform-mobile" className="flex items-center cursor-pointer">
                        <Smartphone className="mr-2 rtl:ml-2 rtl:mr-0 h-4 w-4" />
                        Mobile / جوال
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
                
                {formData.platform === "mobile" && (
                  <div className="glass-card p-4 rounded-lg">
                    <h3 className="text-sm font-medium mb-3 flex items-center">
                      <Smartphone className="ml-2 h-4 w-4" />
                      نوع الجوال
                    </h3>
                    <RadioGroup
                      value={formData.mobileType}
                      onValueChange={(value) => handleMobileTypeChange(value as "android" | "ios")}
                      className="flex space-x-4 space-x-reverse"
                    >
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <RadioGroupItem value="android" id="mobile-android" />
                        <Label htmlFor="mobile-android" className="flex items-center cursor-pointer">
                          <svg className="mr-2 rtl:ml-2 rtl:mr-0 h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.523 15.3414c-.5511 0-.999-.4478-.999-.9989v-4.6976c0-.5511.4479-.999.999-.999s.999.4479.999.999v4.6976c0 .5511-.4479.9989-.999.9989zm-11.046 0c-.5511 0-.999-.4478-.999-.9989v-4.6976c0-.5511.4479-.999.999-.999s.999.4479.999.999v4.6976c0 .5511-.4479.9989-.999.9989zM7.04 18.1858c0 .5511.4479.999.999.999s.999-.4479.999-.999-.4479-.999-.999-.999-.999.4479-.999.999zM12 22.5c-1.9072 0-3.5-1.5928-3.5-3.5v-6.1972c0-.5511.4479-.999.999-.999s.999.4479.999.999V19c0 .8284.6716 1.5 1.5 1.5s1.5-.6716 1.5-1.5v-6.1972c0-.5511.4479-.999.999-.999s.999.4479.999.999V19c0 1.9072-1.5928 3.5-3.5 3.5zm5-4.3142c0 .5511.4479.999.999.999s.999-.4479.999-.999-.4479-.999-.999-.999-.999.4479-.999.999zM12 1c2.1237 0 4.1565.8359 5.6569 2.3362 1.5004 1.5003 2.3431 3.5331 2.3431 5.6569 0 .5511-.4479.999-.999.999s-.999-.4479-.999-.999c0-1.5955-.6268-3.1304-1.7574-4.2608C15.1304 3.6268 13.5955 3 12 3c-1.5955 0-3.1304.6268-4.2608 1.7574C6.6268 5.8879 6 7.4227 6 9.0002c0 .5511-.4479.999-.999.999s-.999-.4479-.999-.999c0-2.1237.8359-4.1565 2.3362-5.6569C7.8435 1.8359 9.8763 1 12 1zM5.3906 9.8518h13.2118c.5511 0 .999.4479.999.999s-.4479.999-.999.999H5.3906c-.5511 0-.999-.4479-.999-.999s.4478-.999.999-.999z" />
                          </svg>
                          Android / أندرويد
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <RadioGroupItem value="ios" id="mobile-ios" />
                        <Label htmlFor="mobile-ios" className="flex items-center cursor-pointer">
                          <svg className="mr-2 rtl:ml-2 rtl:mr-0 h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.0374 12.7339c-.0357-3.2335 2.6424-4.8087 2.7623-4.8867-1.5098-2.2067-3.8567-2.5096-4.6845-2.5428-1.9747-.2088-3.8932 1.179-4.899 1.179-1.0299 0-2.5945-1.1571-4.2903-1.1253-2.1777.033-4.2128 1.2851-5.3336 3.2385-2.2935 3.9754-.5853 9.8204 1.6257 13.0352 1.0939 1.565 2.3828 3.3122 4.0686 3.2485 1.6425-.0675 2.2585-1.0519 4.2432-1.0519 1.9747 0 2.5486 1.0519 4.2693 1.0161 1.77-.0291 2.8863-1.5916 3.9579-3.1646 1.2693-1.8276 1.7842-3.6125 1.8062-3.7049-.0408-.0158-3.4579-1.3256-3.4958-5.2621zm-3.2682-9.6725c.8962-1.1028 1.5048-2.6149 1.3372-4.1343-1.2952.0541-2.918.877-3.8477 1.9612-.8294.9658-1.5695 2.5461-1.3753 4.0346 1.4553.1088 2.9484-.7274 3.8858-1.8615z"/>
                          </svg>
                          iOS / آي أو إس
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                )}
                
                {formData.platform === "mobile" && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                    {formData.mobileType === "android" && (
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <Switch
                            id="root-required"
                            checked={formData.rootRequired || false}
                            onCheckedChange={(checked) => setFormData({ ...formData, rootRequired: checked })}
                          />
                          <Label htmlFor="root-required" className="cursor-pointer">ROOT مطلوب / ROOT Required</Label>
                        </div>
                      </div>
                    )}
                    
                    {formData.mobileType === "ios" && (
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <Switch
                            id="jailbreak-required"
                            checked={formData.jailbreakRequired || false}
                            onCheckedChange={(checked) => setFormData({ ...formData, jailbreakRequired: checked })}
                          />
                          <Label htmlFor="jailbreak-required" className="cursor-pointer">جيلبريك مطلوب / Jailbreak Required</Label>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              <div className="flex flex-col gap-2 sm:flex-row sm:gap-6 mt-4">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Switch
                    id="featured"
                    checked={formData.featured || false}
                    onCheckedChange={(checked) => setFormData({ ...formData, featured: checked })}
                  />
                  <Label htmlFor="featured" className="cursor-pointer">
                    مميز / Featured
                  </Label>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Switch
                    id="special"
                    checked={formData.special || false}
                    onCheckedChange={(checked) => setFormData({ ...formData, special: checked })}
                  />
                  <Label htmlFor="special" className="cursor-pointer">
                    خاص / Special
                  </Label>
                </div>
              </div>
              
              {renderTextareaWithDefaultOption('features', 'المميزات', 'Features')}
              
              {renderTextareaWithDefaultOption('requirements', 'المتطلبات', 'Requirements')}
              
              {renderTextareaWithDefaultOption('includedSoftware', 'البرامج المتضمنة', 'Included Software')}
            </div>
            
            <DialogFooter className="mt-4 gap-2 flex-col sm:flex-row">
              <Button
                type="button"
                variant="outline"
                onClick={resetFormAndClose}
                className="w-full sm:w-auto"
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-pubg-gray text-white hover:bg-pubg-gray/90 w-full sm:w-auto"
              >
                {isLoading ? "جاري الحفظ..." : (editingMod ? "حفظ التغييرات" : "إضافة الهاك")}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Bulk Add Mods Dialog */}
      <Dialog open={showBulkAddDialog} onOpenChange={setShowBulkAddDialog}>
        <DialogContent className="sm:max-w-[600px] w-[95vw] sm:w-auto p-3 sm:p-6">
          <DialogHeader className="pb-2">
            <DialogTitle>إضافة هاكات متعددة</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm">
              أدخل بيانات الهاكات المراد إضافتها
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleBulkModDataSubmit} className="space-y-3">
            <div className="space-y-1 sm:space-y-2">
              <Label htmlFor="bulkModData" className="text-sm">قائمة الهاكات (JSON)</Label>
              <Textarea
                id="bulkModData"
                className="h-48 sm:h-56 font-mono text-sm"
                value={bulkModData}
                onChange={handleBulkModDataChange}
                placeholder='[
  {
    "title": "اسم الهاك",
    "title_en": "Hack Name",
    "description": "وصف الهاك بالعربية",
    "description_en": "Hack Description in English",
    "priceUSD": 10,
    "localCurrency": 300,
    "localCurrencyCode": "SAR",
    "platform": "pc",
    "features": [
      "نظام ESP المتقدم وتتبع اللاعبين | Advanced ESP system",
      "نظام مساعدة التصويب الذكي | Smart aim assistance"
    ],
    "requirements": [
      "ويندوز 10 برو x64bit | Windows 10 Pro x64bit",
      "نظام ويندوز جديد مع التعريفات | Fresh Windows system"
    ],
    "includedSoftware": [
      "ويندوز 10 20H2 | Windows 10 20H2",
      "AIO Runtime 2.5 | AIO Runtime 2.5"
    ],
    "image": "https://example.com/image.jpg",
    "featured": true,
    "special": false
  },
  {
    "title": "هاك الاندرويد",
    "title_en": "Android Hack",
    "platform": "mobile",
    "mobileType": "android",
    "rootRequired": true,
    "priceUSD": 15,
    "localCurrency": 450,
    "localCurrencyCode": "SAR",
    "image": "https://example.com/mobile-hack.jpg",
    "featured": false,
    "special": true
  },
  {
    "title": "هاك الايفون",
    "title_en": "iOS Hack",
    "platform": "mobile",
    "mobileType": "ios",
    "jailbreakRequired": true,
    "priceUSD": 20,
    "localCurrency": 600,
    "localCurrencyCode": "SAR",
    "image": "https://example.com/ios-hack.jpg"
  }
]'
                required
              />
            </div>
            
            <DialogFooter className="flex-col sm:flex-row gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowBulkAddDialog(false);
                  setBulkModData("");
                }}
                className="w-full sm:w-auto ml-0 sm:ml-2"
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isBulkProcessing || !bulkModData.trim()}
                className="bg-pubg-gray text-white hover:bg-pubg-gray/90 w-full sm:w-auto"
              >
                {isBulkProcessing ? "جاري المعالجة..." : "إضافة الهاكات"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ModManager; 