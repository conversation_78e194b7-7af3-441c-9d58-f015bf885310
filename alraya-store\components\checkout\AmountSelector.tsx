"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import { Currency, RechargeOption } from "@/lib/types"
import { getActiveRechargeOptions } from "@/lib/utils/localStorage"
import { formatCurrency } from "@/lib/data/currencies"
import { Wallet, Edit3 } from "lucide-react"

interface AmountSelectorProps {
  selectedAmount: number
  selectedCurrency: Currency
  onAmountChange: (amount: number) => void
  onCurrencyChange: (currency: Currency) => void
  className?: string
}

export function AmountSelector({
  selectedAmount,
  selectedCurrency,
  onAmountChange,
  onCurrencyChange,
  className
}: AmountSelectorProps) {
  const [customAmount, setCustomAmount] = useState("")
  const [isCustomMode, setIsCustomMode] = useState(false)
  const [rechargeOptions, setRechargeOptions] = useState<RechargeOption[]>([])

  // Load recharge options for selected currency
  useEffect(() => {
    const options = getActiveRechargeOptions(selectedCurrency)
    setRechargeOptions(options)
  }, [selectedCurrency])

  // Update custom amount when selected amount changes
  useEffect(() => {
    if (selectedAmount > 0) {
      const isPresetAmount = rechargeOptions.some(option => option.amount === selectedAmount)
      if (!isPresetAmount) {
        setCustomAmount(selectedAmount.toString())
        setIsCustomMode(true)
      } else {
        setIsCustomMode(false)
        setCustomAmount("")
      }
    }
  }, [selectedAmount, rechargeOptions])

  const handlePresetAmountSelect = (amount: number) => {
    setIsCustomMode(false)
    setCustomAmount("")
    onAmountChange(amount)
  }

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value)
    const numericValue = parseInt(value) || 0
    if (numericValue > 0) {
      onAmountChange(numericValue)
    }
  }

  const handleCustomModeToggle = () => {
    setIsCustomMode(!isCustomMode)
    if (!isCustomMode) {
      setCustomAmount(selectedAmount > 0 ? selectedAmount.toString() : "")
    } else {
      setCustomAmount("")
      if (rechargeOptions.length > 0) {
        onAmountChange(rechargeOptions[0].amount)
      }
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Currency Selector */}
      <div className="space-y-3">
        <Label className="text-slate-300 font-medium">
          العملة
        </Label>
        <div className="grid grid-cols-2 gap-3">
          <Button
            type="button"
            variant={selectedCurrency === "SDG" ? "default" : "outline"}
            className={cn(
              "h-12 text-lg font-semibold transition-all duration-300",
              selectedCurrency === "SDG"
                ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600"
                : "border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"
            )}
            onClick={() => onCurrencyChange("SDG")}
          >
            جنيه سوداني (SDG)
          </Button>
          <Button
            type="button"
            variant={selectedCurrency === "EGP" ? "default" : "outline"}
            className={cn(
              "h-12 text-lg font-semibold transition-all duration-300",
              selectedCurrency === "EGP"
                ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600"
                : "border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"
            )}
            onClick={() => onCurrencyChange("EGP")}
          >
            جنيه مصري (EGP)
          </Button>
        </div>
      </div>

      {/* Amount Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-slate-300 font-medium">
            مبلغ الشحن
          </Label>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleCustomModeToggle}
            className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400/10"
          >
            <Edit3 className="h-4 w-4 ml-2" />
            {isCustomMode ? "المبالغ المحددة مسبقاً" : "مبلغ مخصص"}
          </Button>
        </div>

        {!isCustomMode ? (
          // Preset Amounts
          <div className="grid grid-cols-2 gap-3">
            {rechargeOptions.map((option) => (
              <Card
                key={option.id}
                className={cn(
                  "cursor-pointer transition-all duration-300 hover:scale-105",
                  "bg-slate-800/50 backdrop-blur-xl border-slate-700/50",
                  selectedAmount === option.amount
                    ? "border-yellow-400 bg-yellow-400/10 shadow-yellow-400/20"
                    : "hover:border-slate-600 hover:shadow-lg"
                )}
                onClick={() => handlePresetAmountSelect(option.amount)}
              >
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Wallet className="h-5 w-5 text-yellow-400" />
                  </div>
                  <p className="text-xl font-bold text-white">
                    {formatCurrency(option.amount, option.currency)}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {option.currency === "SDG" ? "جنيه سوداني" : "جنيه مصري"}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          // Custom Amount Input
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
            <CardContent className="p-4">
              <div className="space-y-3">
                <Label htmlFor="custom-amount" className="text-slate-300">
                  أدخل المبلغ المطلوب
                </Label>
                <div className="relative">
                  <Input
                    id="custom-amount"
                    type="number"
                    placeholder="0"
                    value={customAmount}
                    onChange={(e) => handleCustomAmountChange(e.target.value)}
                    className="text-xl font-semibold text-center bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500"
                    min="1"
                    step="1"
                  />
                  <div className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400">
                    {selectedCurrency}
                  </div>
                </div>
                {customAmount && parseInt(customAmount) > 0 && (
                  <p className="text-center text-yellow-400 font-medium">
                    {formatCurrency(parseInt(customAmount), selectedCurrency)}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Selected Amount Display */}
      {selectedAmount > 0 && (
        <Card className="bg-gradient-to-r from-yellow-400/10 to-orange-500/10 border-yellow-400/20">
          <CardContent className="p-4 text-center">
            <p className="text-slate-300 text-sm mb-1">
              المبلغ المحدد
            </p>
            <p className="text-2xl font-bold text-yellow-400">
              {formatCurrency(selectedAmount, selectedCurrency)}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
