import React from 'react';
import { Helmet } from 'react-helmet-async';

// Organization data
interface OrganizationProps {
  name?: string;
  url?: string;
  logo?: string;
  contactPoint?: {
    telephone: string;
    contactType: string;
  }[];
  sameAs?: string[];
}

export const OrganizationJsonLd: React.FC<OrganizationProps> = ({
  name = 'PUBG STORE',
  url = 'https://pubg-sd.netlify.app',
  logo = 'https://pubg-sd.netlify.app/favicon.png',
  contactPoint = [
    {
      telephone: '+20123456789',
      contactType: 'customer service'
    }
  ],
  sameAs = []
}) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    url,
    logo,
    contactPoint,
    sameAs
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(data)}
      </script>
    </Helmet>
  );
};

// Product data
interface ProductProps {
  name: string;
  description?: string;
  image?: string;
  price?: number;
  priceCurrency?: string;
  availability?: 'InStock' | 'OutOfStock' | 'PreOrder';
  url?: string;
  sku?: string;
  brand?: string;
}

export const ProductJsonLd: React.FC<ProductProps> = ({
  name,
  description = '',
  image = '',
  price,
  priceCurrency = 'EGP',
  availability = 'InStock',
  url = '',
  sku = '',
  brand = 'PUBG'
}) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name,
    description,
    image,
    sku,
    brand: {
      '@type': 'Brand',
      name: brand
    },
    offers: {
      '@type': 'Offer',
      price,
      priceCurrency,
      availability: `https://schema.org/${availability}`,
      url
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(data)}
      </script>
    </Helmet>
  );
};

// Article data
interface ArticleProps {
  headline: string;
  image?: string; 
  datePublished?: string;
  dateModified?: string;
  authorName?: string;
  description?: string;
  url?: string;
  publisherName?: string;
  publisherLogo?: string;
}

export const ArticleJsonLd: React.FC<ArticleProps> = ({
  headline,
  image = '',
  datePublished,
  dateModified,
  authorName = 'PUBG STORE Team',
  description = '',
  url = '',
  publisherName = 'PUBG STORE',
  publisherLogo = 'https://pubg-sd.netlify.app/favicon.png'
}) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline,
    image,
    datePublished,
    dateModified: dateModified || datePublished,
    author: {
      '@type': 'Person',
      name: authorName
    },
    description,
    url,
    publisher: {
      '@type': 'Organization',
      name: publisherName,
      logo: {
        '@type': 'ImageObject',
        url: publisherLogo
      }
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(data)}
      </script>
    </Helmet>
  );
};

// Local business data
export const LocalBusinessJsonLd: React.FC<OrganizationProps> = ({
  name = 'PUBG STORE',
  url = 'https://pubg-sd.netlify.app',
  logo = 'https://pubg-sd.netlify.app/favicon.png',
  contactPoint,
  sameAs = []
}) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name,
    url,
    logo,
    contactPoint,
    sameAs
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(data)}
      </script>
    </Helmet>
  );
};

// FAQ data
interface FAQItem {
  question: string;
  answer: string;
}

interface FAQProps {
  faqs: FAQItem[];
}

export const FAQJsonLd: React.FC<FAQProps> = ({ faqs }) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(data)}
      </script>
    </Helmet>
  );
};