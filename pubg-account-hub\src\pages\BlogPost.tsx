import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { useTranslation } from "react-i18next";
import { getBlogPosts, getBlogPostBySlug, getBlogPostById, BlogPostModel } from "@/services/firestore";
import { ArrowRight, Calendar, User, Clock, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/utils";
import { Lightbulb, Globe, Sparkles, Rocket } from "lucide-react";
import SEO from "@/components/SEO";
import { ArticleJsonLd } from "@/components/JsonLd";
import ReactMarkdown from 'react-markdown';
import GlobalLoader from "@/components/ui/GlobalLoader";
import { shouldShowLoader } from "@/lib/utils";

// Animation variants
const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.7, ease: "easeOut" } },
};

const slideUp = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.2,
    },
  },
};

// Helper to estimate reading time
const calculateReadingTime = (content: string): number => {
  const wordsPerMinute = 200;
  
  // Check if content is a string and not empty
  if (!content || typeof content !== 'string') {
    return 1; // Return minimum reading time if no content or invalid type
  }
  
  const words = content.split(/\s+/).length;
  return Math.max(1, Math.ceil(words / wordsPerMinute));
};

const BlogContent = ({ content }: { content: string | null | undefined }) => {
  const { language } = useLanguage();
  if (!content || typeof content !== 'string') {
    return null;
  }
  return (
    <div
      className={
        `glass-card rounded-2xl p-6 md:p-8 mb-6 bg-pubg-black/80 shadow-lg border border-pubg-blue/10` +
        ` prose prose-invert max-w-none text-white prose-p:leading-8 prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-strong:text-pubg-orange prose-li:marker:text-pubg-blue prose-blockquote:border-pubg-orange prose-blockquote:text-pubg-orange` +
        (language === 'ar' ? ' text-right rtl' : ' text-left ltr')
      }
      dir={language === 'ar' ? 'rtl' : 'ltr'}
      style={{
        fontFamily: 'inherit',
        lineHeight: 2,
        letterSpacing: language === 'ar' ? '0.01em' : undefined,
      }}
    >
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  );
};

const BlogPostPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { language } = useLanguage();
  const { t } = useTranslation('common');
  
  const [post, setPost] = useState<BlogPostModel | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPostModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [nextPost, setNextPost] = useState<BlogPostModel | null>(null);
  const [prevPost, setPrevPost] = useState<BlogPostModel | null>(null);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    const fetchPost = async () => {
      setIsLoading(true);
      try {
        // Try to find post by slug
        let post;
        if (slug) {
          console.log(`BlogPost Page: Looking up post with slug "${slug}" (current language: ${language})`);
          
          // First try direct slug lookup
          post = await getBlogPostBySlug(slug);
          
          if (post) {
            console.log(`BlogPost Page: Found post directly with slug "${slug}"`);
          } else {
            console.log(`BlogPost Page: No post found directly with slug "${slug}", trying alternative lookups`);
          }
          
          // If not found by slug, try other methods
          if (!post) {
            // Try to match with all posts to find by alternative slugs or ID
            const allPosts = await getBlogPosts();
            
            // Look for post with matching English slug
            const postByEnSlug = allPosts.find(p => p.slug_en === slug);
            if (postByEnSlug) {
              post = postByEnSlug;
              console.log(`BlogPost Page: Found post by English slug match: "${slug}"`);
            }
            
            // If still not found, try by ID
            if (!post) {
              const postById = allPosts.find(p => p.id === slug);
              if (postById) {
                post = postById;
                console.log(`BlogPost Page: Found post by ID match: "${slug}"`);
              } else {
                console.log(`BlogPost Page: No post found with ID, English slug, or Arabic slug "${slug}"`);
              }
            }
          }
        }
        
        if (post) {
          setPost(post);
          
          // Set page title based on language
          const title = language === 'en' && post.title_en ? post.title_en : post.title;
          document.title = `${title} | PUBG STORE`;
          
          // Debug info about available slugs
          console.log(`BlogPost Page: Post found details:
            - ID: ${post.id}
            - AR Slug: ${post.slug || 'none'}
            - EN Slug: ${post.slug_en || 'none'}
            - Current URL slug: ${slug}
            - Current language: ${language}
          `);
          
          // Check if we need to update the URL based on language
          // For English language: the slug should match slug_en if available
          // For Arabic language: the slug should match the Arabic slug if available
          const currentUrlMatchesLanguage = (
            (language === 'en' && post.slug_en === slug) || 
            (language === 'ar' && post.slug === slug)
          );
          
          if (!currentUrlMatchesLanguage) {
            const correctSlug = language === 'en' ? 
              (post.slug_en || post.id) : 
              (post.slug || post.id);
              
            console.log(`BlogPost Page: URL slug does not match language. Should navigate to: ${correctSlug}`);
            
            // Only navigate if we have a different slug to go to
            if (correctSlug && correctSlug !== slug) {
              console.log(`BlogPost Page: Navigating to correct slug for ${language}: ${correctSlug}`);
              navigate(`/blog/${correctSlug}`, { replace: true });
              return; // Exit early as we're going to remount with the new URL
            } else {
              console.log(`BlogPost Page: No alternative slug available for ${language}, staying on current URL`);
            }
          } else {
            console.log(`BlogPost Page: URL already matches ${language} language, no need to navigate`);
          }
          
          // Get related posts
          const postsData = await getBlogPosts();
          // Get 3 related posts excluding current
          const relatedData = postsData
            .filter(p => p.id !== post.id)
            .sort(() => 0.5 - Math.random())
            .slice(0, 3);
          setRelatedPosts(relatedData);
        } else {
          setNotFound(true);
        }
      } catch (error) {
        console.error("Error fetching blog post:", error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء تحميل المقالة",
          variant: "destructive",
        });
        setNotFound(true);
      } finally {
        setIsLoading(false);
      }
    };
    fetchPost();
  }, [slug, language, toast, navigate]);

  // Scroll to top when component mounts or slug changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [slug]);

  if (shouldShowLoader(isLoading)) {
    return (
      <div className="min-h-screen pt-24 pb-16 px-4">
        <SEO title={language === 'en' ? "Loading Article..." : "تحميل المقال..."} />
        <GlobalLoader fullPage />
      </div>
    );
  }

  if (notFound) {
    return (
      <div className="min-h-screen pt-24 pb-16 px-4">
        <SEO title={t('blog.article_not_found')} />
        <div className="container mx-auto text-center">
          <h1 className="text-3xl font-bold text-white mb-4">
            {t('blog.article_not_found')}
          </h1>
          <p className="text-muted-foreground mb-6">
            {t('blog.article_not_found_desc')}
          </p>
          <Button asChild>
            <Link to="/blog">{t('blog.return_to_blog')}</Link>
          </Button>
        </div>
      </div>
    );
  }

  if (!post) {
    return null;
  }

  // Get the appropriate content based on language preference
  const contentToUse = language === 'en' && post.content_en ? post.content_en : post.content;
  const readingTime = calculateReadingTime(contentToUse);
  const currentUrl = window.location.href;

  // Display reading time
  const displayReadingTime = (content: string | null | undefined) => {
    const time = calculateReadingTime(content || "");
    return (
      <div className="flex items-center text-muted-foreground">
        <Clock size={14} className="ml-1" />
        <span>
          {time} {t('blog.min_read')}
        </span>
      </div>
    );
  };

  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <SEO 
        title={language === 'en' && post.title_en ? post.title_en : post.title}
        description={language === 'en' && post.excerpt_en ? post.excerpt_en : post.excerpt}
        ogImage={post.image}
        ogType="article"
        canonical={currentUrl}
      />
      <ArticleJsonLd 
        headline={language === 'en' && post.title_en ? post.title_en : post.title}
        image={post.image}
        datePublished={post.date}
        dateModified={post.updatedAt ? new Date(post.updatedAt.seconds * 1000).toISOString() : post.date}
        authorName={language === 'en' && post.author_en ? post.author_en : (post.author || "PUBG STORE Team")}
        description={language === 'en' && post.excerpt_en ? post.excerpt_en : post.excerpt}
        url={currentUrl}
      />
      <div className="container mx-auto">
        {/* Back link */}
        <motion.div 
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="mb-6"
        >
          <div className="inline-block relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-pubg-orange via-yellow-500 to-pubg-orange rounded-full blur-md opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"></div>
            <Button 
              variant="outline" 
              asChild 
              className="relative px-6 py-2.5 bg-background border-pubg-orange/20 hover:border-pubg-orange/50 text-pubg-orange rounded-full overflow-hidden group-hover:shadow-[0_0_25px_rgba(242,169,0,0.3)] transition-all duration-300"
            >
              <Link to="/blog" className="flex items-center gap-3">
                <span className="relative z-10 tracking-wide font-medium">{t('blog.return_to_blog')}</span>
                <motion.span 
                  className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-pubg-orange to-amber-400"
                  initial={{ scaleX: 0, originX: 0 }}
                  whileHover={{ scaleX: 1 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                />
              </Link>
            </Button>
          </div>
        </motion.div>
        
        {/* Hero section with title and featured image */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="mb-8 relative"
        >
          <div className="absolute -z-10 top-1/3 left-1/4 w-64 h-64 bg-pubg-blue/5 rounded-full blur-3xl"></div>
          <div className="absolute -z-10 bottom-0 right-1/3 w-40 h-40 bg-pubg-orange/5 rounded-full blur-3xl"></div>
          
          <div className="relative h-64 md:h-96 rounded-xl overflow-hidden mb-6 shadow-lg">
            <img
              src={post.image}
              alt={post.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-pubg-blue/20 via-transparent to-pubg-orange/10 opacity-30"></div>
          </div>
          
          <motion.h1 
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 relative"
            variants={slideUp}
          >
            {language === 'en' && post.title_en ? post.title_en : post.title}
            <span className="absolute -z-10 blur-[40px] opacity-30 h-12 w-48 bg-pubg-blue/30 left-0 bottom-0"></span>
          </motion.h1>
          
          <motion.div 
            className="flex flex-wrap items-center text-sm text-muted-foreground mb-6 gap-4"
            variants={slideUp}
          >
            <div className="flex items-center">
              <User className="ml-2 h-4 w-4 text-pubg-blue" />
              <span>{language === 'en' && post.author_en ? post.author_en : post.author}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="ml-2 h-4 w-4 text-pubg-blue" />
              <span>{formatDate(post.date)}</span>
            </div>
            <div className="flex items-center">
              {displayReadingTime(contentToUse)}
            </div>
          </motion.div>
          
          <motion.p 
            className="text-lg text-muted-foreground mb-8 border-r-4 border-pubg-blue pr-4 py-2 relative"
            variants={slideUp}
          >
            <span className="absolute -z-10 left-0 top-0 h-full w-full bg-pubg-blue/5 blur-xl opacity-60"></span>
            {language === 'en' && post.excerpt_en ? post.excerpt_en : post.excerpt}
          </motion.p>
        </motion.div>
        
        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="lg:col-span-2 glass-card rounded-xl p-6 relative overflow-hidden"
          >
            <div className="absolute -z-10 top-0 right-0 w-full h-full bg-gradient-to-br from-pubg-blue/5 via-transparent to-transparent pointer-events-none"></div>
            <div className="absolute -z-10 bottom-0 left-0 w-64 h-64 bg-pubg-blue/5 rounded-full blur-3xl"></div>
            <BlogContent content={contentToUse} />
          </motion.div>
          
          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Author info */}
            <motion.div 
              initial="hidden"
              animate="visible" 
              variants={slideUp}
              className="glass-card rounded-xl p-6 relative overflow-hidden"
              whileHover={{ 
                boxShadow: "0 10px 30px -10px rgba(80, 120, 255, 0.2)",
                y: -5,
                transition: { duration: 0.3 }
              }}
            >
              <div className="absolute -z-10 bottom-0 right-0 w-32 h-32 bg-pubg-blue/10 rounded-full blur-3xl"></div>
              <h3 className="text-lg font-bold text-white mb-3 relative">
                {t('blog.author')}
                <span className="absolute -z-10 blur-[20px] opacity-30 h-4 w-16 bg-pubg-blue/40 left-0 bottom-0"></span>
              </h3>
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full bg-pubg-blue/20 flex items-center justify-center ml-3 relative">
                  <div className="absolute inset-0 bg-pubg-blue/20 rounded-full animate-ping opacity-70"></div>
                  <User className="h-6 w-6 text-pubg-blue" />
                </div>
                <div>
                  <h4 className="font-medium text-white">{language === 'en' && post.author_en ? post.author_en : post.author}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('blog.writer_at')}
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* Related posts */}
            {relatedPosts.length > 0 && (
              <motion.div 
                initial="hidden"
                animate="visible" 
                variants={slideUp}
                className="glass-card rounded-xl p-6 relative overflow-hidden"
                whileHover={{ 
                  boxShadow: "0 10px 30px -10px rgba(80, 120, 255, 0.2)",
                  y: -5,
                  transition: { duration: 0.3 }
                }}
              >
                <div className="absolute -z-10 top-0 left-0 w-32 h-32 bg-pubg-blue/5 rounded-full blur-3xl"></div>
                <h3 className="text-lg font-bold text-white mb-3 relative">
                  {t('blog.related_articles')}
                  <span className="absolute -z-10 blur-[20px] opacity-30 h-4 w-20 bg-pubg-blue/40 left-0 bottom-0"></span>
                </h3>
                <div className="space-y-4">
                  {relatedPosts.map((relatedPost) => (
                    <Link 
                      key={relatedPost.id} 
                      to={`/blog/${relatedPost.slug || relatedPost.id}`}
                      className="flex items-start group"
                    >
                      <div className="w-16 h-16 overflow-hidden rounded-md ml-3 flex-shrink-0">
                        <img 
                          src={relatedPost.image} 
                          alt={relatedPost.title} 
                          className="w-full h-full object-cover transition-transform group-hover:scale-110"
                        />
                      </div>
                      <div>
                        <h4 className="font-medium text-white line-clamp-1 group-hover:text-pubg-blue transition-colors">
                          {language === 'en' && relatedPost.title_en ? relatedPost.title_en : relatedPost.title}
                        </h4>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(relatedPost.date)}
                        </p>
                      </div>
                    </Link>
                  ))}
                </div>
              </motion.div>
            )}
          </div>
        </div>
        
        {/* Navigation between posts */}
        {(nextPost || prevPost) && (
          <div className="container mx-auto mt-12 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {prevPost && (
                <Link to={`/blog/${prevPost.slug || prevPost.id}`}>
                  <div className="glass-card p-4 rounded-xl flex items-center cursor-pointer transition-all duration-300 hover:bg-card-foreground/5">
                    <div className="ml-2">
                      <ChevronLeft size={20} className="text-pubg-blue" />
                    </div>
                    <div className="flex-1 overflow-hidden">
                      <p className="text-sm text-muted-foreground">
                        {t('blog.previous_article')}
                      </p>
                      <h4 className="font-medium text-white line-clamp-1">
                        {language === 'en' && prevPost.title_en ? prevPost.title_en : prevPost.title}
                      </h4>
                    </div>
                  </div>
                </Link>
              )}
              
              {nextPost && (
                <Link to={`/blog/${nextPost.slug || nextPost.id}`} className="md:ml-auto">
                  <div className="glass-card p-4 rounded-xl flex items-center cursor-pointer transition-all duration-300 hover:bg-card-foreground/5">
                    <div className="flex-1 text-left overflow-hidden">
                      <p className="text-sm text-muted-foreground">
                        {t('blog.next_article')}
                      </p>
                      <h4 className="font-medium text-white line-clamp-1">
                        {language === 'en' && nextPost.title_en ? nextPost.title_en : nextPost.title}
                      </h4>
                    </div>
                    <div className="mr-2">
                      <ChevronRight size={20} className="text-pubg-blue" />
                    </div>
                  </div>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BlogPostPage; 