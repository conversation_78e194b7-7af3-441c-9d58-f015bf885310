import React, { useState, useEffect, useRef } from "react";
import AccountCard from "@/components/AccountCard";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { getAccounts, getCategories, AccountModel, CategoryModel } from "@/services/firestore";
import { 
  Search, 
  SlidersHorizontal, 
  ChevronDown, 
  Check, 
  X,
  Filter,
  Globe,
  Inbox
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import { shouldShowLoader } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import { useLanguage } from "@/contexts/LanguageContext";
import GlobalLoader from "@/components/ui/GlobalLoader";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const filterVariants = {
  hidden: { y: -20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.3 } },
  exit: { y: -20, opacity: 0, transition: { duration: 0.2 } }
};

const Accounts = () => {
  const { toast } = useToast();
  const [accounts, setAccounts] = useState<AccountModel[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState("featured");
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [showEnglishContent, setShowEnglishContent] = useState(false);
  const filtersRef = useRef<HTMLDivElement>(null);
  const { language, setLanguage } = useLanguage();
  const [categoryMap, setCategoryMap] = useState<{[key: string]: CategoryModel}>({});

  // Handle click outside to close filters on mobile
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filtersRef.current && !filtersRef.current.contains(event.target as Node)) {
        setShowFilters(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Set initial language based on context
  useEffect(() => {
    setShowEnglishContent(language === "en");
  }, [language]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [accountsData, categoriesData] = await Promise.all([
          getAccounts(),
          getCategories()
        ]);
        
        setAccounts(accountsData);
        
        // Get account categories
        const accountCategories = categoriesData.filter(cat => cat.type === "account");
        setCategories(accountCategories);
        
        // Create a map of category IDs to category objects for easy lookup
        const catMap = accountCategories.reduce((acc, cat) => {
          if (cat.id) {
            acc[cat.id] = cat;
          }
          return acc;
        }, {} as {[key: string]: CategoryModel});
        
        setCategoryMap(catMap);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: showEnglishContent ? "Error" : "خطأ",
          description: showEnglishContent 
            ? "Failed to load account data" 
            : "فشل في تحميل بيانات الحسابات",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast, showEnglishContent]);

  // Filter accounts by category and search query
  const filteredAccounts = accounts.filter(account => {
    // Category filter
    const categoryMatch = selectedCategoryId === null || account.category === selectedCategoryId;

    // Search in appropriate language fields
    const titleMatch = showEnglishContent && account.title_en 
      ? account.title_en.toLowerCase().includes(searchQuery.toLowerCase())
      : account.title.toLowerCase().includes(searchQuery.toLowerCase());
      
    const descriptionMatch = showEnglishContent && account.description_en 
      ? account.description_en.toLowerCase().includes(searchQuery.toLowerCase())
      : account.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    return categoryMatch && (searchQuery === "" || titleMatch || descriptionMatch);
  });

  // Sort accounts based on selected option
  const sortedAccounts = [...filteredAccounts].sort((a, b) => {
    if (sortOption === "price-asc") {
      const priceA = a.priceUSD;
      const priceB = b.priceUSD;
      return priceA - priceB;
    } else if (sortOption === "price-desc") {
      const priceA = a.priceUSD;
      const priceB = b.priceUSD;
      return priceB - priceA;
    } else if (sortOption === "featured" && a.featured && !b.featured) {
      return -1;
    } else if (sortOption === "featured" && !a.featured && b.featured) {
      return 1;
    }
    return 0;
  });
  
  const sortOptions = [
    { value: "featured", label: showEnglishContent ? "Most Popular" : "الأكثر شهرة" },
    { value: "price-asc", label: showEnglishContent ? "Price: Low to High" : "السعر: من الأقل إلى الأعلى" },
    { value: "price-desc", label: showEnglishContent ? "Price: High to Low" : "السعر: من الأعلى إلى الأقل" }
  ];

  const handleClearFilters = () => {
    setSelectedCategoryId(null);
    setSortOption("featured");
    setSearchQuery("");
  };

  const handleLanguageToggle = () => {
    const newLanguage = showEnglishContent ? 'ar' : 'en';
    setShowEnglishContent(!showEnglishContent);
    setLanguage(newLanguage);
  };

  // Get category name based on current language
  const getCategoryName = (categoryId: string | null) => {
    if (!categoryId) return showEnglishContent ? "All Categories" : "الكل";
    
    const category = categoryMap[categoryId];
    if (!category) return categoryId;
    
    return showEnglishContent && category.name_en 
      ? category.name_en 
      : category.name;
  };

  if (shouldShowLoader(isLoading)) {
    return (
      <div className="min-h-screen pt-24 pb-16">
        <GlobalLoader fullPage />
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 sm:pt-24 pb-16 px-4" dir={showEnglishContent ? "ltr" : "rtl"}>
      <div className="container mx-auto">
        <motion.div 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="mb-6 sm:mb-8 flex justify-between items-center"
        >
          <div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-2 sm:mb-4">
              {showEnglishContent ? "PUBG Accounts" : "حسابات PUBG"}
            </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
              {showEnglishContent 
                ? "Browse our collection of premium PUBG accounts with various levels and outfits" 
                : "تصفح مجموعتنا من حسابات PUBG المميزة مع مستويات وبدلات مختلفة"}
          </p>
          </div>
        </motion.div>

        {/* Search and Filters - Desktop & Mobile */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-center mb-4">
            <div className="relative w-full sm:w-auto sm:flex-1 max-w-xl">
              <Search className={`absolute ${showEnglishContent ? "left-3" : "right-3"} top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4`} />
              <Input
                type="text"
                placeholder={showEnglishContent ? "Search accounts..." : "البحث عن حسابات..."}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`bg-background/50 backdrop-blur-sm border-border w-full ${showEnglishContent ? "pr-4 pl-10" : "pl-4 pr-10"} py-2 rounded-xl text-white placeholder:text-muted-foreground`}
                dir={showEnglishContent ? "ltr" : "rtl"}
              />
              {searchQuery && (
                <button 
                  className={`absolute ${showEnglishContent ? "right-3" : "left-3"} top-1/2 -translate-y-1/2 text-muted-foreground hover:text-white`}
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
            
            <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto justify-end">
              {/* Sort Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="border-border text-white hover:bg-background/50 hover:text-pubg-orange">
                    <ChevronDown className={`h-4 w-4 ${showEnglishContent ? "mr-2" : "ml-2"}`} />
                    <span className="text-sm">{showEnglishContent ? "Sort" : "الترتيب"}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align={showEnglishContent ? "end" : "start"} className="bg-background/95 backdrop-blur-md border-border">
                  <DropdownMenuLabel>{showEnglishContent ? "Sort By" : "ترتيب حسب"}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {sortOptions.map((option) => (
                    <DropdownMenuItem 
                      key={option.value}
                      className={`flex items-center justify-between ${sortOption === option.value ? 'text-pubg-orange' : 'text-white'}`}
                      onClick={() => setSortOption(option.value)}
                    >
                      {option.label}
                      {sortOption === option.value && <Check className={`h-4 w-4 ${showEnglishContent ? "ml-2" : "mr-2"}`} />}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Filter Button - Mobile Only */}
              <Button 
                variant="outline" 
                className="sm:hidden border-border text-white hover:bg-background/50 hover:text-pubg-orange"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className={`h-4 w-4 ${showEnglishContent ? "mr-2" : "ml-2"}`} />
                <span className="text-sm">{showEnglishContent ? "Filters" : "الفلاتر"}</span>
              </Button>
            </div>
          </div>

          {/* Active Filters Display */}
          {(selectedCategoryId !== null || sortOption !== "featured" || searchQuery) && (
            <motion.div 
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex flex-wrap items-center gap-2 mt-4 mb-2"
            >
              <span className="text-sm text-muted-foreground">{showEnglishContent ? "Active Filters:" : "الفلاتر النشطة:"}</span>
              
              {selectedCategoryId !== null && (
                <Badge className="bg-pubg-orange/20 text-pubg-orange border border-pubg-orange/30 hover:bg-pubg-orange/30 flex items-center gap-1">
                  {getCategoryName(selectedCategoryId)}
                  <button onClick={() => setSelectedCategoryId(null)}>
                    <X className={`h-3 w-3 ${showEnglishContent ? "ml-1" : "mr-1"}`} />
                  </button>
                </Badge>
              )}
              
              {sortOption !== "featured" && (
                <Badge className="bg-pubg-orange/20 text-pubg-orange border border-pubg-orange/30 hover:bg-pubg-orange/30 flex items-center gap-1">
                  {sortOptions.find(o => o.value === sortOption)?.label}
                  <button onClick={() => setSortOption("featured")}>
                    <X className={`h-3 w-3 ${showEnglishContent ? "ml-1" : "mr-1"}`} />
                  </button>
                </Badge>
              )}
              
              {searchQuery && (
                <Badge className="bg-pubg-orange/20 text-pubg-orange border border-pubg-orange/30 hover:bg-pubg-orange/30 flex items-center gap-1">
                  {showEnglishContent ? `Search: ${searchQuery}` : `بحث: ${searchQuery}`}
                  <button onClick={() => setSearchQuery("")}>
                    <X className={`h-3 w-3 ${showEnglishContent ? "ml-1" : "mr-1"}`} />
                  </button>
                </Badge>
              )}
              
              <Button 
                variant="link" 
                className="text-xs text-muted-foreground hover:text-pubg-orange p-0 h-auto"
                onClick={handleClearFilters}
              >
                {showEnglishContent ? "Clear All" : "مسح الكل"}
              </Button>
            </motion.div>
          )}

          {/* Mobile Filters */}
          <AnimatePresence>
            {showFilters && (
              <motion.div 
                ref={filtersRef}
                initial="hidden"
                animate="visible"
                exit="exit"
                variants={filterVariants}
                className="sm:hidden bg-background/95 backdrop-blur-md rounded-xl border border-border p-4 mt-4"
              >
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-white font-bold">{showEnglishContent ? "Filters" : "الفلاتر"}</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0" 
                    onClick={() => setShowFilters(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="mb-4">
                  <h4 className="text-sm text-muted-foreground mb-2">{showEnglishContent ? "Categories" : "التصنيفات"}</h4>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      key="all"
                      variant={selectedCategoryId === null ? "default" : "outline"}
                      size="sm"
                      className={`text-xs h-8 ${
                        selectedCategoryId === null
                          ? "bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                          : "border-muted text-white hover:text-pubg-orange hover:border-pubg-orange"
                      }`}
                      onClick={() => setSelectedCategoryId(null)}
                    >
                      {showEnglishContent ? "All Categories" : "الكل"}
                    </Button>
                    
                    {categories.map((category) => (
                      <Button
                        key={category.id}
                        variant={selectedCategoryId === category.id ? "default" : "outline"}
                        size="sm"
                        className={`text-xs h-8 ${
                          selectedCategoryId === category.id
                            ? "bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                            : "border-muted text-white hover:text-pubg-orange hover:border-pubg-orange"
                        }`}
                        onClick={() => setSelectedCategoryId(category.id || null)}
                      >
                        {showEnglishContent && category.name_en ? category.name_en : category.name}
                      </Button>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm text-muted-foreground mb-2">{showEnglishContent ? "Sort By" : "الترتيب حسب"}</h4>
                  <div className="space-y-1">
                    {sortOptions.map((option) => (
                      <button
                        key={option.value}
                        className={`w-full text-${showEnglishContent ? "left" : "right"} px-2 py-1.5 rounded-md text-sm ${
                          sortOption === option.value
                            ? "bg-pubg-orange/20 text-pubg-orange"
                            : "text-white hover:bg-background/50"
                        }`}
                        onClick={() => setSortOption(option.value)}
                      >
                        <div className="flex items-center justify-between">
                          {option.label}
                          {sortOption === option.value && <Check className="h-4 w-4" />}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
                
                <Button
                  className="w-full mt-4 bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                  onClick={() => setShowFilters(false)}
                >
                  {showEnglishContent ? "Apply Filters" : "تطبيق الفلاتر"}
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Category Tabs - Desktop Only */}
        <div className="hidden sm:block mb-8">
          <div className="overflow-x-auto pb-2">
            <div className={`flex space-x-2 ${showEnglishContent ? "" : "space-x-reverse"}`}>
              <Button
                key="all"
                variant={selectedCategoryId === null ? "default" : "outline"}
                className={`whitespace-nowrap ${
                  selectedCategoryId === null
                    ? "bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                    : "border-border text-white hover:text-pubg-orange hover:border-pubg-orange"
                }`}
                onClick={() => setSelectedCategoryId(null)}
              >
                {showEnglishContent ? "All Categories" : "الكل"}
              </Button>
              
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategoryId === category.id ? "default" : "outline"}
                  className={`whitespace-nowrap ${
                    selectedCategoryId === category.id
                      ? "bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
                      : "border-border text-white hover:text-pubg-orange hover:border-pubg-orange"
                  }`}
                  onClick={() => setSelectedCategoryId(category.id || null)}
                >
                  {showEnglishContent && category.name_en ? category.name_en : category.name}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="flex justify-between items-center mb-6">
          <div className="text-sm text-muted-foreground">
            {sortedAccounts.length > 0 ? (
              <span>
                {showEnglishContent 
                  ? `Showing ${sortedAccounts.length} account${sortedAccounts.length !== 1 ? 's' : ''}`
                  : `عرض ${sortedAccounts.length} حساب`}
              </span>
            ) : null}
          </div>
        </div>

        {/* Accounts Grid */}
        {sortedAccounts.length > 0 ? (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 md:gap-5 lg:gap-6"
          >
            {sortedAccounts.map((account) => (
              <motion.div key={account.id} variants={fadeInUp}>
                <AccountCard 
                  account={account} 
                  categoryData={categoryMap}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12 px-4 border border-border rounded-xl mt-6 bg-background/50 backdrop-blur-sm"
          >
            <Inbox className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-xl text-white font-medium">
              {showEnglishContent ? "No Accounts Available" : "لا توجد حسابات متاحة"}
            </h3>
            <p className="text-muted-foreground mt-2 mb-4">
              {searchQuery 
                ? (showEnglishContent ? "We couldn't find any accounts matching your search." : "لم نتمكن من العثور على حسابات مطابقة لبحثك.")
                : (showEnglishContent ? "There are no accounts available in this category." : "لا توجد حسابات متاحة في هذه الفئة.")
              }
            </p>
            <Button
              variant="outline"
              className="border-pubg-orange text-pubg-orange hover:bg-pubg-orange hover:text-pubg-dark"
              onClick={handleClearFilters}
            >
              {showEnglishContent ? "Clear Filters" : "مسح الفلاتر"}
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Accounts;
