import { db } from "@/lib/firebase";
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  serverTimestamp,
  DocumentData,
  QuerySnapshot,
  Timestamp,
  setDoc,
  FieldValue
} from "firebase/firestore";

// Collection names
const COLLECTIONS = {
  ACCOUNTS: "accounts",
  UC_PACKAGES: "ucPackages",
  BLOG_POSTS: "blogPosts",
  CATEGORIES: "categories",
  CONTACT_INFO: "contactInfo",
  SETTINGS: "settings",
  MODS: "mods",
  USERS: "users",
  UC_STORE_SETTINGS: "ucStoreSettings",
  PAGE_VISIBILITY: "pageVisibility",
};

// Generic type for data models
type FirestoreModel = {
  id?: string;
  createdAt?: any;
  updatedAt?: any;
};

// Types for each collection
export interface AccountModel {
  id?: string;
  title: string;
  title_en?: string;
  description: string;
  description_en?: string;
  priceUSD: number;
  priceEGP: number;
  image: string;
  category: string;
  featured: boolean;
  localCurrencyCode?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UCPackageModel extends FirestoreModel {
  amount: number;
  priceEGP: number;
  priceUSD: number;
  image: string;
  featured?: boolean;
  localCurrencyCode?: string;
  exchangeRate?: number;
  originalPrice?: number;
  discountPercent?: number;
  highlights?: string[];
  category?: string;
}

export interface BlogPostModel extends FirestoreModel {
  title: string;
  title_en?: string;
  excerpt: string;
  excerpt_en?: string;
  content: string;
  content_en?: string;
  image: string;
  author: string;
  author_en?: string;
  date: string;
  slug?: string;
  slug_en?: string;
  featured?: boolean;
  special?: boolean;
}

export interface CategoryModel extends FirestoreModel {
  name: string;
  name_en?: string;
  type: "account" | "blog" | "uc";
}

export interface ContactInfoModel extends FirestoreModel {
  telegramLink: string;
  whatsappNumber: string;
  email: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;
  discord?: string;
}

export interface SettingsModel extends FirestoreModel {
  siteName: string;
  siteName_en?: string;
  heroTitle: string;
  heroTitle_en?: string;
  heroSubtitle: string;
  heroSubtitle_en?: string;
  heroImage: string;
  // Stats section
  statsTitle?: string;
  statsTitle_en?: string;
  statCustomers?: string;
  statCustomersLabel?: string;
  statCustomersLabel_en?: string;
  statAccounts?: string;
  statAccountsLabel?: string;
  statAccountsLabel_en?: string;
  statSupport?: string;
  statSupportLabel?: string;
  statSupportLabel_en?: string;
  statSecurity?: string;
  statSecurityLabel?: string;
  statSecurityLabel_en?: string;
  // Section titles
  testimonialsSectionTitle?: string;
  testimonialsSectionTitle_en?: string;
  whyChooseUsSectionTitle?: string;
  whyChooseUsSectionTitle_en?: string;
  featuredAccountsSectionTitle?: string;
  featuredAccountsSectionTitle_en?: string;
  featuredAccountsSectionSubtitle?: string;
  featuredAccountsSectionSubtitle_en?: string;
  featuredModsSectionTitle?: string;
  featuredModsSectionTitle_en?: string;
  featuredModsSectionSubtitle?: string;
  featuredModsSectionSubtitle_en?: string;
  // Home page section order and visibility
  homeSectionOrder?: string[];
  homeSectionVisibility?: {[key: string]: boolean};
}

export interface UCStoreSettingsModel extends FirestoreModel {
  heroImage: string;
  heroTitle: string;
  heroSubtitle: string;
}

export interface ModModel extends FirestoreModel {
  title: string;
  title_en?: string;
  description: string;
  description_en?: string;
  priceUSD: number;
  localCurrency?: number;
  localCurrencyCode?: string; // SAR, AED, etc.
  exchangeRate?: number;
  image: string;
  features: string[]; // Format: "Feature in English | الميزة بالعربية"
  requirements: string[]; // Format: "Requirement in English | المتطلب بالعربية"
  includedSoftware: string[]; // Format: "Software in English | البرنامج بالعربية"
  platform?: "pc" | "mobile";
  mobileType?: "android" | "ios";
  rootRequired?: boolean; // For Android
  jailbreakRequired?: boolean; // For iOS
  featured?: boolean;
  special?: boolean;
}

export interface AboutPageContent {
  id?: string;
  header: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    backgroundImage: string;
  };
  story: {
    title: string;
    title_en?: string;
    content: string[];
    content_en?: string[];
    image: string;
  };
  values: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    items: Array<{
      icon: string;
      title: string;
      title_en?: string;
      description: string;
      description_en?: string;
    }>;
  };
  team: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    members: Array<{
      name: string;
      name_en?: string;
      role: string;
      role_en?: string;
      avatar: string;
      bio: string;
      bio_en?: string;
    }>;
  };
  whyChooseUs: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    points: Array<{
      icon: string;
      title: string;
      title_en?: string;
      description: string;
      description_en?: string;
    }>;
  };
  faq?: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    questions: Array<{
      question: string;
      question_en?: string;
      answer: string;
      answer_en?: string;
    }>;
  };
  createdAt?: Timestamp | FieldValue;
  updatedAt?: Timestamp | FieldValue;
}

export interface UserModel extends FirestoreModel {
  uid: string;
  email: string;
  displayName?: string;
  phoneNumber?: string;
  photoURL?: string;
  role: "admin" | "user";
  isActive: boolean;
  lastLogin?: Timestamp;
  orders?: string[];
  metadata?: {
    [key: string]: any;
  };
}

// Add the PageVisibility interface after other model interfaces
export interface PageVisibilityModel extends FirestoreModel {
  pageId: string;
  pageName: string;
  isVisible: boolean;
  order?: number;
  isInNavbar?: boolean;
  isInFooter?: boolean;
  isInMobile?: boolean;
  requiresAuth?: boolean;
}

// Helper function to generate a slug from a title
const generateSlug = (title: string) => {
  return title
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
};

// Helper function to transform Firebase documents
const transformDoc = <T extends FirestoreModel>(doc: DocumentData): T => {
  const data = doc.data();
  // If this is a blog post and it doesn't have a slug, generate one from the title
  if (doc.ref.parent.id === COLLECTIONS.BLOG_POSTS && !data.slug && data.title) {
    data.slug = generateSlug(data.title);
  }
  return { id: doc.id, ...data } as T;
};

const transformCollection = <T extends FirestoreModel>(
  querySnapshot: QuerySnapshot<DocumentData, DocumentData>
): T[] => {
  return querySnapshot.docs.map((doc) => transformDoc<T>(doc));
};

// Generic functions to interact with Firestore

// Get all documents from a collection
export const getAll = async <T extends FirestoreModel>(
  collectionName: string
): Promise<T[]> => {
  const querySnapshot = await getDocs(collection(db, collectionName));
  return transformCollection<T>(querySnapshot);
};

// Get a document by ID
export const getById = async <T extends FirestoreModel>(
  collectionName: string,
  id: string
): Promise<T | null> => {
  const docRef = doc(db, collectionName, id);
  const docSnap = await getDoc(docRef);
  
  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() } as T;
  }
  
  return null;
};

// Get documents with a filter
export const getWhere = async <T extends FirestoreModel>(
  collectionName: string,
  field: string,
  operator: "==" | "<" | "<=" | ">" | ">=" | "!=",
  value: any
): Promise<T[]> => {
  const q = query(collection(db, collectionName), where(field, operator, value));
  const querySnapshot = await getDocs(q);
  return transformCollection<T>(querySnapshot);
};

// Add a new document
export const add = async <T extends FirestoreModel>(
  collectionName: string,
  data: Omit<T, "id" | "createdAt" | "updatedAt">
): Promise<string> => {
  try {
    console.log(`Adding document to collection ${collectionName}:`, data);
    const docRef = await addDoc(collection(db, collectionName), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    console.log(`Successfully added document with ID: ${docRef.id} to ${collectionName}`);
    return docRef.id;
  } catch (error) {
    console.error(`Error adding document to ${collectionName}:`, error);
    throw error;
  }
};

// Update a document
export const update = async <T extends FirestoreModel>(
  collectionName: string,
  id: string,
  data: Partial<Omit<T, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log(`Updating document ID ${id} in collection ${collectionName}:`, data);
    const docRef = doc(db, collectionName, id);
    
    // Remove any undefined values from the data object
    const cleanData: any = { ...data };
    Object.keys(cleanData).forEach(key => {
      if (cleanData[key] === undefined) {
        delete cleanData[key];
      }
    });
    
    // Add updatedAt timestamp
    await updateDoc(docRef, {
      ...cleanData,
      updatedAt: serverTimestamp(),
    });
    
    console.log(`Successfully updated document ID: ${id} in ${collectionName}`);
  } catch (error) {
    console.error(`Error updating document ID ${id} in ${collectionName}:`, error);
    throw error;
  }
};

// Delete a document
export const remove = async (
  collectionName: string,
  id: string
): Promise<void> => {
  try {
    console.log(`Deleting document ID ${id} from collection ${collectionName}`);
    const docRef = doc(db, collectionName, id);
    await deleteDoc(docRef);
    console.log(`Successfully deleted document ID: ${id} from ${collectionName}`);
  } catch (error) {
    console.error(`Error deleting document ID ${id} from ${collectionName}:`, error);
    throw error;
  }
};

// Specific functions for each collection

// Accounts
export const getAccounts = () => getAll<AccountModel>(COLLECTIONS.ACCOUNTS);
export const getAccountById = (id: string) => getById<AccountModel>(COLLECTIONS.ACCOUNTS, id);
export const getAccountsByCategory = (category: string) => 
  getWhere<AccountModel>(COLLECTIONS.ACCOUNTS, "category", "==", category);
export const getFeaturedAccounts = () => 
  getWhere<AccountModel>(COLLECTIONS.ACCOUNTS, "featured", "==", true);
export const addAccount = (account: Omit<AccountModel, "id" | "createdAt" | "updatedAt">) => 
  add<AccountModel>(COLLECTIONS.ACCOUNTS, account);
export const updateAccount = (id: string, account: Partial<Omit<AccountModel, "id" | "createdAt">>) => 
  update<AccountModel>(COLLECTIONS.ACCOUNTS, id, account);
export const deleteAccount = (id: string) => remove(COLLECTIONS.ACCOUNTS, id);

// UC Packages
export const getUCPackages = () => getAll<UCPackageModel>(COLLECTIONS.UC_PACKAGES);
export const getUCPackageById = (id: string) => getById<UCPackageModel>(COLLECTIONS.UC_PACKAGES, id);
export const getFeaturedUCPackages = () => 
  getWhere<UCPackageModel>(COLLECTIONS.UC_PACKAGES, "featured", "==", true);
export const addUCPackage = (ucPackage: Omit<UCPackageModel, "id" | "createdAt" | "updatedAt">) => 
  add<UCPackageModel>(COLLECTIONS.UC_PACKAGES, ucPackage);
export const updateUCPackage = (id: string, ucPackage: Partial<Omit<UCPackageModel, "id" | "createdAt">>) => 
  update<UCPackageModel>(COLLECTIONS.UC_PACKAGES, id, ucPackage);
export const deleteUCPackage = (id: string) => remove(COLLECTIONS.UC_PACKAGES, id);

// Blog Posts
export const getBlogPosts = async () => {
  const querySnapshot = await getDocs(collection(db, COLLECTIONS.BLOG_POSTS));
  return transformCollection<BlogPostModel>(querySnapshot);
};
export const getBlogPostById = (id: string) => getById<BlogPostModel>(COLLECTIONS.BLOG_POSTS, id);
export const getBlogPostBySlug = async (slug: string) => {
  try {
    console.log(`Looking for blog post with slug: ${slug}`);
    
    // First try to find a post with matching slug in either field
    const qArabic = query(collection(db, COLLECTIONS.BLOG_POSTS), where("slug", "==", slug));
    const qEnglish = query(collection(db, COLLECTIONS.BLOG_POSTS), where("slug_en", "==", slug));
    
    // Execute both queries
    const [arabicResults, englishResults] = await Promise.all([
      getDocs(qArabic),
      getDocs(qEnglish)
    ]);
    
    // Check if we found a post with Arabic slug
    if (!arabicResults.empty) {
      console.log(`Found blog post with Arabic slug match: ${slug}`);
      const post = transformDoc<BlogPostModel>(arabicResults.docs[0]);
      return post;
    }
    
    // Check if we found a post with English slug
    if (!englishResults.empty) {
      console.log(`Found blog post with English slug match: ${slug}`);
      const post = transformDoc<BlogPostModel>(englishResults.docs[0]);
      return post;
    }
    
    // If no direct matches were found, try to match generated slugs
    console.log(`No exact slug match found, trying to match generated slugs`);
    const allPostsSnapshot = await getDocs(collection(db, COLLECTIONS.BLOG_POSTS));
    const posts = transformCollection<BlogPostModel>(allPostsSnapshot);
    
    // Try to find by generated slug from title (either language)
    const post = posts.find(post => {
      const generatedArabicSlug = post.title ? generateSlug(post.title) : '';
      const generatedEnglishSlug = post.title_en ? generateSlug(post.title_en) : '';
      return generatedArabicSlug === slug || generatedEnglishSlug === slug;
    });
    
    // If found by generated slug, update the post with the appropriate slug
    if (post) {
      console.log(`Found post by generated slug match. Updating post with slug: ${slug}`);
      const isEnglishSlug = post.title_en && generateSlug(post.title_en) === slug;
      const update = isEnglishSlug ? { slug_en: slug } : { slug };
      await updateBlogPost(post.id!, update);
      return post;
    }
    
    console.log(`No blog post found with slug or matching generated slug: ${slug}`);
    return null;
  } catch (error) {
    console.error("Error getting blog post by slug:", error);
    return null;
  }
};
export const getFeaturedBlogPosts = () => 
  getWhere<BlogPostModel>(COLLECTIONS.BLOG_POSTS, "featured", "==", true);
export const addBlogPost = async (blogPost: Omit<BlogPostModel, "id" | "createdAt" | "updatedAt">) => {
  // Ensure blog post has a slug
  const postWithSlug = {
    ...blogPost,
    slug: blogPost.slug || generateSlug(blogPost.title)
  };
  return add<BlogPostModel>(COLLECTIONS.BLOG_POSTS, postWithSlug);
};
export const updateBlogPost = async (id: string, blogPost: Partial<Omit<BlogPostModel, "id" | "createdAt">>) => {
  // If title is being updated and no slug is provided, regenerate the slug
  if (blogPost.title && !blogPost.slug) {
    blogPost.slug = generateSlug(blogPost.title);
  }
  return update<BlogPostModel>(COLLECTIONS.BLOG_POSTS, id, blogPost);
};
export const deleteBlogPost = (id: string) => remove(COLLECTIONS.BLOG_POSTS, id);

// Categories
export const getCategories = () => getAll<CategoryModel>(COLLECTIONS.CATEGORIES);

export const getCategoryById = (id: string) => getById<CategoryModel>(COLLECTIONS.CATEGORIES, id);

export const getCategoriesByType = (type: "account" | "blog" | "uc") => 
  getWhere<CategoryModel>(COLLECTIONS.CATEGORIES, "type", "==", type);
export const addCategory = (category: Omit<CategoryModel, "id" | "createdAt" | "updatedAt">) => 
  add<CategoryModel>(COLLECTIONS.CATEGORIES, category);
export const updateCategory = (id: string, category: Partial<Omit<CategoryModel, "id" | "createdAt">>) => 
  update<CategoryModel>(COLLECTIONS.CATEGORIES, id, category);
export const deleteCategory = (id: string) => remove(COLLECTIONS.CATEGORIES, id);

// Contact Info
export const getContactInfo = async (): Promise<ContactInfoModel | null> => {
  const contacts = await getAll<ContactInfoModel>(COLLECTIONS.CONTACT_INFO);
  return contacts.length > 0 ? contacts[0] : null;
};
export const updateContactInfo = async (data: Partial<Omit<ContactInfoModel, "id" | "createdAt">>) => {
  const contacts = await getAll<ContactInfoModel>(COLLECTIONS.CONTACT_INFO);
  if (contacts.length > 0) {
    await update<ContactInfoModel>(COLLECTIONS.CONTACT_INFO, contacts[0].id!, data);
  } else {
    await add<ContactInfoModel>(COLLECTIONS.CONTACT_INFO, data as any);
  }
};

// Site Settings
export const getSettings = async (): Promise<SettingsModel | null> => {
  const settings = await getAll<SettingsModel>(COLLECTIONS.SETTINGS);
  
  // If no settings exist or settings don't have a hero image, use the default
  if (settings.length === 0) {
    return null;
  }
  
  // Ensure heroImage has a default value if not set
  if (!settings[0].heroImage) {
    settings[0].heroImage = DEFAULT_HERO_IMAGE;
  }
  
  return settings[0];
};

export const updateSettings = async (data: Partial<Omit<SettingsModel, "id" | "createdAt">>) => {
  const settings = await getAll<SettingsModel>(COLLECTIONS.SETTINGS);
  
  // Make sure heroImage has a value
  if (!data.heroImage) {
    data.heroImage = DEFAULT_HERO_IMAGE;
  }
  
  if (settings.length > 0) {
    await update<SettingsModel>(COLLECTIONS.SETTINGS, settings[0].id!, data);
  } else {
    await add<SettingsModel>(COLLECTIONS.SETTINGS, data as any);
  }
};

// UC Store Settings
export const getUCStoreSettings = async (): Promise<UCStoreSettingsModel | null> => {
  const settings = await getAll<UCStoreSettingsModel>(COLLECTIONS.UC_STORE_SETTINGS);
  return settings.length > 0 ? settings[0] : null;
};

export const updateUCStoreSettings = async (data: Partial<Omit<UCStoreSettingsModel, "id" | "createdAt">>) => {
  const settings = await getAll<UCStoreSettingsModel>(COLLECTIONS.UC_STORE_SETTINGS);
  if (settings.length > 0) {
    await update<UCStoreSettingsModel>(COLLECTIONS.UC_STORE_SETTINGS, settings[0].id!, data);
  } else {
    await add<UCStoreSettingsModel>(COLLECTIONS.UC_STORE_SETTINGS, data as any);
  }
};

// Mods
export const getMods = () => getAll<ModModel>(COLLECTIONS.MODS);
export const getModById = (id: string) => getById<ModModel>(COLLECTIONS.MODS, id);
export const getFeaturedMods = () => 
  getWhere<ModModel>(COLLECTIONS.MODS, "featured", "==", true);
export const addMod = (mod: Omit<ModModel, "id" | "createdAt" | "updatedAt">) => 
  add<ModModel>(COLLECTIONS.MODS, mod);
export const updateMod = (id: string, mod: Partial<Omit<ModModel, "id" | "createdAt">>) => 
  update<ModModel>(COLLECTIONS.MODS, id, mod);
export const deleteMod = (id: string) => remove(COLLECTIONS.MODS, id);

// Utility function to update all blog posts with proper slugs
export const updateAllBlogPostSlugs = async () => {
  try {
    console.log("Starting to update all blog post slugs...");
    const posts = await getBlogPosts();
    let updatedCount = 0;
    
    for (const post of posts) {
      let needsUpdate = false;
      let updateData: Partial<BlogPostModel> = {};
      
      // Check and update Arabic slug if needed
      if ((!post.slug || post.slug === "") && post.title) {
        updateData.slug = generateSlug(post.title);
        needsUpdate = true;
        console.log(`Generating Arabic slug for "${post.title}": ${updateData.slug}`);
      }
      
      // Check and update English slug if needed
      if ((!post.slug_en || post.slug_en === "") && post.title_en) {
        updateData.slug_en = generateSlug(post.title_en);
        needsUpdate = true;
        console.log(`Generating English slug for "${post.title_en}": ${updateData.slug_en}`);
      }
      
      // If we need to update, do so
      if (needsUpdate && post.id) {
        await updateBlogPost(post.id, updateData);
        console.log(`Updated post "${post.title}" with new slugs`);
        updatedCount++;
      }
    }
    
    console.log(`Completed updating ${updatedCount} blog posts with slugs`);
    return updatedCount;
  } catch (error) {
    console.error("Error updating blog post slugs:", error);
    throw error;
  }
};

export async function getAboutPageContent(): Promise<AboutPageContent | null> {
  try {
    console.log("Fetching about page content from Firestore");
    const docRef = doc(db, "pageContent", "about");
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as AboutPageContent;
    } else {
      console.log("No about page content found");
      return null;
    }
  } catch (error: any) {
    // Check if it's a permission error
    if (error.code === 'permission-denied') {
      console.error("Permission denied while accessing about page content. This is likely due to Firestore security rules.");
    } else {
      console.error("Error getting about page content:", error);
    }
    return null;
  }
}

export async function updateAboutPageContent(data: AboutPageContent): Promise<boolean> {
  try {
    const docRef = doc(db, "pageContent", "about");
    const updateData = {
      ...data,
      updatedAt: serverTimestamp()
    };
    
    if (!data.id) {
      // It's a new document
      updateData.createdAt = serverTimestamp();
    }
    
    await setDoc(docRef, updateData);
    
    console.log("About page content updated successfully");
    return true;
  } catch (error) {
    console.error("Error updating about page content:", error);
    return false;
  }
}

// User related functions
export const getUsers = () => getAll<UserModel>(COLLECTIONS.USERS);

export const getUserById = (id: string) => getById<UserModel>(COLLECTIONS.USERS, id);

export const getUserByUid = async (uid: string) => {
  const users = await getWhere<UserModel>(COLLECTIONS.USERS, "uid", "==", uid);
  return users.length > 0 ? users[0] : null;
};

export const addUser = (user: Omit<UserModel, "id" | "createdAt" | "updatedAt">) => 
  add<UserModel>(COLLECTIONS.USERS, user);

export const updateUser = (id: string, user: Partial<Omit<UserModel, "id" | "createdAt">>) => {
  // Make sure the uid field is never undefined
  if (user && user.uid === undefined) {
    // Get a copy of the user object without the uid field to avoid undefined values
    const { uid, ...userData } = user;
    return update<UserModel>(COLLECTIONS.USERS, id, userData);
  }
  return update<UserModel>(COLLECTIONS.USERS, id, user);
};

export const deleteUser = (id: string) => remove(COLLECTIONS.USERS, id);

export const getAdmins = () => 
  getWhere<UserModel>(COLLECTIONS.USERS, "role", "==", "admin");

export const getActiveUsers = () => 
  getWhere<UserModel>(COLLECTIONS.USERS, "isActive", "==", true);

// Get all page visibility settings
export const getPageVisibilitySettings = async (): Promise<PageVisibilityModel[]> => {
  return getAll<PageVisibilityModel>(COLLECTIONS.PAGE_VISIBILITY);
};

// Get page visibility by page ID
export const getPageVisibilityById = async (pageId: string): Promise<PageVisibilityModel | null> => {
  const pages = await getWhere<PageVisibilityModel>(COLLECTIONS.PAGE_VISIBILITY, "pageId", "==", pageId);
  return pages.length > 0 ? pages[0] : null;
};

// Update page visibility setting
export const updatePageVisibility = async (pageId: string, isVisible: boolean) => {
  const pages = await getWhere<PageVisibilityModel>(COLLECTIONS.PAGE_VISIBILITY, "pageId", "==", pageId);
  
  if (pages.length > 0) {
    // Update existing record
    await update<PageVisibilityModel>(COLLECTIONS.PAGE_VISIBILITY, pages[0].id!, { isVisible });
  } else {
    // Create new record with defaults
    await add<PageVisibilityModel>(COLLECTIONS.PAGE_VISIBILITY, {
      pageId,
      pageName: pageId, // Default name is the same as the ID
      isVisible,
      isInNavbar: true,
      isInFooter: true,
      isInMobile: true,
      order: 99, // Default to end of lists
    });
  }
};

// Initialize default page visibility settings if not already set
export const initializePageVisibilitySettings = async () => {
  const defaultPages = [
    { pageId: "home", pageName: "الرئيسية", isVisible: true, order: 1 },
    { pageId: "accounts", pageName: "الحسابات", isVisible: true, order: 2 },
    { pageId: "uc-store", pageName: "متجر UC", isVisible: true, order: 3 },
    { pageId: "mods", pageName: "هاكات", isVisible: true, order: 4 },
    { pageId: "about", pageName: "من نحن", isVisible: true, order: 5 },
    { pageId: "blog", pageName: "المدونة", isVisible: true, order: 6 },
    { pageId: "contact", pageName: "اتصل بنا", isVisible: true, order: 7 },
  ];

  const existingPages = await getPageVisibilitySettings();
  const existingPageIds = existingPages.map(page => page.pageId);

  for (const page of defaultPages) {
    if (!existingPageIds.includes(page.pageId)) {
      await add<PageVisibilityModel>(COLLECTIONS.PAGE_VISIBILITY, {
        ...page,
        isInNavbar: true,
        isInFooter: true,
        isInMobile: true,
      });
    }
  }
};

// Default image constants
export const DEFAULT_HERO_IMAGE = "https://i.ibb.co/N2Jb4PXP/cropped-image.jpg";

export { COLLECTIONS };
