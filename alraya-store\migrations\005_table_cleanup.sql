-- =====================================================
-- Al-Raya Store Multi-Currency Platform Migration
-- Phase 5: Table Cleanup and Finalization
-- =====================================================

-- =====================================================
-- 1. BACKUP OLD TABLES (OPTIONAL - FOR SAFETY)
-- =====================================================
-- Create backup tables with timestamp suffix
DO $$
DECLARE
  backup_suffix TEXT := '_backup_' || to_char(NOW(), 'YYYYMMDD_HH24MISS');
BEGIN
  -- Backup existing tables if they exist
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_wallets') THEN
    EXECUTE format('CREATE TABLE user_wallets%s AS SELECT * FROM user_wallets', backup_suffix);
    RAISE NOTICE 'Created backup table: user_wallets%', backup_suffix;
  END IF;
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'wallet_transactions') THEN
    EXECUTE format('CREATE TABLE wallet_transactions%s AS SELECT * FROM wallet_transactions', backup_suffix);
    RAISE NOTICE 'Created backup table: wallet_transactions%', backup_suffix;
  END IF;
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_orders') THEN
    EXECUTE format('CREATE TABLE product_orders%s AS SELECT * FROM product_orders', backup_suffix);
    RAISE NOTICE 'Created backup table: product_orders%', backup_suffix;
  END IF;
END $$;

-- =====================================================
-- 2. DROP OLD TABLES (AFTER CONFIRMING MIGRATION SUCCESS)
-- =====================================================
-- WARNING: Only run this after confirming the migration was successful
-- and you have verified the data in the new tables

-- Drop old tables if they exist
DROP TABLE IF EXISTS user_wallets CASCADE;
DROP TABLE IF EXISTS wallet_transactions CASCADE;
DROP TABLE IF EXISTS product_orders CASCADE;

-- =====================================================
-- 3. RENAME NEW TABLES TO ORIGINAL NAMES
-- =====================================================
-- Rename the new tables to replace the old ones
ALTER TABLE user_wallets_new RENAME TO user_wallets;
ALTER TABLE wallet_transactions_new RENAME TO wallet_transactions;
ALTER TABLE product_orders_new RENAME TO product_orders;

-- =====================================================
-- 4. UPDATE INDEX NAMES
-- =====================================================
-- Rename indexes to match the new table names
ALTER INDEX idx_user_wallets_new_user_currency RENAME TO idx_user_wallets_user_currency;
ALTER INDEX idx_user_wallets_new_currency RENAME TO idx_user_wallets_currency;
ALTER INDEX idx_user_wallets_new_updated RENAME TO idx_user_wallets_updated;

ALTER INDEX idx_wallet_transactions_new_user RENAME TO idx_wallet_transactions_user;
ALTER INDEX idx_wallet_transactions_new_wallet RENAME TO idx_wallet_transactions_wallet;
ALTER INDEX idx_wallet_transactions_new_type RENAME TO idx_wallet_transactions_type;
ALTER INDEX idx_wallet_transactions_new_status RENAME TO idx_wallet_transactions_status;
ALTER INDEX idx_wallet_transactions_new_currency RENAME TO idx_wallet_transactions_currency;
ALTER INDEX idx_wallet_transactions_new_created RENAME TO idx_wallet_transactions_created;
ALTER INDEX idx_wallet_transactions_new_order RENAME TO idx_wallet_transactions_order;

ALTER INDEX idx_product_orders_new_user RENAME TO idx_product_orders_user;
ALTER INDEX idx_product_orders_new_status RENAME TO idx_product_orders_status;
ALTER INDEX idx_product_orders_new_currency RENAME TO idx_product_orders_currency;
ALTER INDEX idx_product_orders_new_created RENAME TO idx_product_orders_created;
ALTER INDEX idx_product_orders_new_template RENAME TO idx_product_orders_template;

-- =====================================================
-- 5. UPDATE FOREIGN KEY REFERENCES
-- =====================================================
-- Update any foreign key references that might have been affected
-- Note: Most references should automatically update with the table rename

-- Update wallet_transactions foreign key to reference the renamed user_wallets table
ALTER TABLE wallet_transactions 
DROP CONSTRAINT wallet_transactions_wallet_id_fkey,
ADD CONSTRAINT wallet_transactions_wallet_id_fkey 
  FOREIGN KEY (wallet_id) REFERENCES user_wallets(id) ON DELETE CASCADE;

-- =====================================================
-- 6. CREATE VIEWS FOR BACKWARD COMPATIBILITY
-- =====================================================
-- Create views that match the old table structure for backward compatibility
-- This helps existing code continue to work during the transition

CREATE OR REPLACE VIEW user_wallets_legacy AS
SELECT 
  id,
  user_id,
  currency_code as currency,
  balance,
  created_at,
  updated_at
FROM user_wallets;

CREATE OR REPLACE VIEW wallet_transactions_legacy AS
SELECT 
  id,
  user_id,
  transaction_type as type,
  amount,
  currency_code as currency,
  description,
  reference_number as reference,
  status,
  created_at
FROM wallet_transactions;

CREATE OR REPLACE VIEW product_orders_legacy AS
SELECT 
  id,
  user_id,
  template_id,
  template_name,
  template_category,
  product_data,
  pricing_data as pricing,
  jsonb_build_object(
    'fullName', customer_name,
    'email', customer_email,
    'phone', customer_phone
  ) as user_details,
  status,
  processing_type,
  timeline_events as timeline,
  metadata,
  created_at,
  updated_at,
  completed_at
FROM product_orders;

-- =====================================================
-- 7. CREATE HELPER VIEWS FOR REPORTING
-- =====================================================
-- View for wallet balances with currency information
CREATE OR REPLACE VIEW wallet_balances_with_currency AS
SELECT 
  uw.id,
  uw.user_id,
  uw.currency_code,
  c.name as currency_name,
  c.symbol as currency_symbol,
  c.arabic_name as currency_arabic_name,
  uw.balance,
  uw.reserved_balance,
  uw.total_deposits,
  uw.total_withdrawals,
  uw.total_purchases,
  uw.last_transaction_at,
  uw.created_at,
  uw.updated_at
FROM user_wallets uw
JOIN currencies c ON c.code = uw.currency_code
WHERE c.is_active = true;

-- View for transactions with currency information
CREATE OR REPLACE VIEW transactions_with_currency AS
SELECT 
  wt.id,
  wt.user_id,
  wt.wallet_id,
  wt.transaction_type,
  wt.amount,
  wt.currency_code,
  c.name as currency_name,
  c.symbol as currency_symbol,
  wt.original_amount,
  wt.original_currency_code,
  oc.name as original_currency_name,
  wt.exchange_rate,
  wt.conversion_fee,
  wt.description,
  wt.reference_number,
  wt.status,
  wt.created_at,
  wt.processed_at
FROM wallet_transactions wt
JOIN currencies c ON c.code = wt.currency_code
LEFT JOIN currencies oc ON oc.code = wt.original_currency_code;

-- View for orders with currency information
CREATE OR REPLACE VIEW orders_with_currency AS
SELECT 
  po.id,
  po.user_id,
  po.template_id,
  po.template_name,
  po.template_category,
  po.currency_code,
  c.name as currency_name,
  c.symbol as currency_symbol,
  po.base_price_usd,
  po.exchange_rate_snapshot,
  po.total_price,
  po.customer_name,
  po.customer_email,
  po.status,
  po.processing_type,
  po.created_at,
  po.completed_at
FROM product_orders po
JOIN currencies c ON c.code = po.currency_code;

-- =====================================================
-- 8. FINAL VALIDATION AND STATISTICS
-- =====================================================
-- Run final validation to ensure migration completed successfully
DO $$
DECLARE
  currency_count INTEGER;
  rate_count INTEGER;
  wallet_count INTEGER;
  transaction_count INTEGER;
  order_count INTEGER;
  user_pref_count INTEGER;
BEGIN
  -- Count records in all tables
  SELECT COUNT(*) INTO currency_count FROM currencies WHERE is_active = true;
  SELECT COUNT(*) INTO rate_count FROM exchange_rates WHERE is_active = true;
  SELECT COUNT(*) INTO wallet_count FROM user_wallets;
  SELECT COUNT(*) INTO transaction_count FROM wallet_transactions;
  SELECT COUNT(*) INTO order_count FROM product_orders;
  SELECT COUNT(*) INTO user_pref_count FROM user_preferences;
  
  RAISE NOTICE '=== MIGRATION COMPLETED SUCCESSFULLY ===';
  RAISE NOTICE 'Final Statistics:';
  RAISE NOTICE '- Active currencies: %', currency_count;
  RAISE NOTICE '- Exchange rates: %', rate_count;
  RAISE NOTICE '- User wallets: %', wallet_count;
  RAISE NOTICE '- Wallet transactions: %', transaction_count;
  RAISE NOTICE '- Product orders: %', order_count;
  RAISE NOTICE '- User preferences: %', user_pref_count;
  
  -- Validate critical data
  IF currency_count < 2 THEN
    RAISE EXCEPTION 'MIGRATION FAILED: Insufficient currencies';
  END IF;
  
  IF rate_count < 2 THEN
    RAISE EXCEPTION 'MIGRATION FAILED: Insufficient exchange rates';
  END IF;
  
  RAISE NOTICE '=== ALL VALIDATIONS PASSED ===';
  RAISE NOTICE 'Multi-currency platform is ready for use!';
END $$;

-- =====================================================
-- 9. GRANT PERMISSIONS ON NEW VIEWS
-- =====================================================
GRANT SELECT ON user_wallets_legacy TO authenticated;
GRANT SELECT ON wallet_transactions_legacy TO authenticated;
GRANT SELECT ON product_orders_legacy TO authenticated;
GRANT SELECT ON wallet_balances_with_currency TO authenticated;
GRANT SELECT ON transactions_with_currency TO authenticated;
GRANT SELECT ON orders_with_currency TO authenticated;

-- =====================================================
-- 10. CREATE MIGRATION COMPLETION LOG
-- =====================================================
-- Log the completion of migration
INSERT INTO currency_audit_log (
  table_name,
  record_id,
  action,
  new_values,
  changed_by,
  changed_at
) VALUES (
  'migration_log',
  gen_random_uuid(),
  'MIGRATION_COMPLETED',
  jsonb_build_object(
    'migration_name', 'multi_currency_platform',
    'completion_time', NOW(),
    'version', '1.0.0'
  ),
  auth.uid(),
  NOW()
);

RAISE NOTICE 'Migration logged in currency_audit_log table';
RAISE NOTICE 'Multi-currency platform migration completed successfully!';
