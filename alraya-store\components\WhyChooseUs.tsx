import { Shield, Clock, DollarSign, Headphones, Star, Users } from "lucide-react"

export default function WhyChooseUs() {
  const features = [
    {
      icon: <Clock className="h-8 w-8" />,
      title: "شحن فوري",
      description: "احصل على منتجك خلال دقائق من إتمام الدفع",
    },
    {
      icon: <DollarSign className="h-8 w-8" />,
      title: "أسعار منافسة",
      description: "أفضل الأسعار في السوق مع ضمان الجودة",
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "دفع آمن",
      description: "معاملات آمنة ومحمية بأحدث تقنيات الأمان",
    },
    {
      icon: <Headphones className="h-8 w-8" />,
      title: "دعم فني 24/7",
      description: "فريق دعم متاح على مدار الساعة لمساعدتك",
    },
    {
      icon: <Star className="h-8 w-8" />,
      title: "جودة مضمونة",
      description: "منتجات أصلية وموثوقة 100%",
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "عملاء راضون",
      description: "أكثر من 50,000 عميل راضٍ عن خدماتنا",
    },
  ]

  return (
    <section className="py-16 bg-sky-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">لماذا تختار رايه شوب؟</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            نحن نقدم أفضل تجربة تسوق رقمية مع ضمان الجودة والسرعة
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-center"
            >
              <div className="bg-yellow-100 rounded-full p-4 w-16 h-16 mx-auto mb-6 flex items-center justify-center text-slate-900">
                {feature.icon}
              </div>

              <h3 className="text-xl font-bold text-slate-900 mb-4">{feature.title}</h3>

              <p className="text-slate-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
