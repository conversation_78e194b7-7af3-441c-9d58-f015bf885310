import React from "react";
import { MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useCart } from "@/contexts/CartContext";

interface TelegramCheckoutProps {
  telegramLink: string;
}

const TelegramCheckout: React.FC<TelegramCheckoutProps> = ({ telegramLink }) => {
  const { items, clearCart } = useCart();
  
  // Calculate total price
  const totalPrice = items.reduce((total, item) => total + (item.priceEGP * (item.quantity || 1)), 0);
  
  const handleCheckout = () => {
    // Create message with cart items for Telegram
    const message = `طلب جديد من PUBG STORE:\n\n${items
      .map(
        (item) =>
          `- ${item.title}\n  السعر: ${item.priceEGP} EGP\n  الكمية: ${item.quantity || 1}\n`
      )
      .join("\n")}
      \nالسعر الإجمالي: ${totalPrice} EGP`;
    
    // Encode message for URL
    const encodedMessage = encodeURIComponent(message);
    
    // Redirect to Telegram with pre-filled message
    window.open(`${telegramLink}?text=${encodedMessage}`, "_blank");
    
    // Clear cart after checkout
    clearCart();
  };
  
  return (
    <Button 
      onClick={handleCheckout}
      className="w-full py-6 text-lg bg-[#0088cc] hover:bg-[#0088cc]/90 text-white"
    >
      <MessageCircle className="ml-2 w-5 h-5" />
      إتمام الشراء عبر تلجرام
    </Button>
  );
};

export default TelegramCheckout;
