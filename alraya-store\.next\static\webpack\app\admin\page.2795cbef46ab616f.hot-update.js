"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"text\",\n        placeholder: \"\",\n        required: false\n    });\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n    };\n    const handleSave = async ()=>{\n        var _formData_name, _formData_category;\n        setIsLoading(true);\n        // Basic validation\n        if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n            alert(\"يرجى إدخال اسم المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!((_formData_category = formData.category) === null || _formData_category === void 0 ? void 0 : _formData_category.trim())) {\n            alert(\"يرجى إدخال فئة المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!formData.packages || formData.packages.length === 0) {\n            alert(\"يرجى إضافة حزمة واحدة على الأقل\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const productData = {\n                name: formData.name,\n                description: formData.description,\n                category: formData.category,\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: formData.fields,\n                packages: formData.packages,\n                features: formData.features,\n                tags: formData.tags,\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.updateProduct)(product.id, productData);\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.createProduct)(productData);\n            }\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            alert(\"حدث خطأ أثناء حفظ المنتج\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"text\",\n            placeholder: \"\",\n            required: false\n        });\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            discount: pkg.discount || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.image || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    image: e.target.value\n                                                                })),\n                                                        className: \"flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"رابط الصورة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700 px-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"رفع\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 499,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isEditing ? \"تحديث المنتج\" : \"إضافة المنتج\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 621,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 644,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 854,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 790,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 789,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"LE3g/gyLSgQ/gpklPFWvfGJTS+o=\");\n_c = SimpleProductForm;\nvar _c;\n$RefreshReg$(_c, \"SimpleProductForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});