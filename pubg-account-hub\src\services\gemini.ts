import { AccountModel } from './firestore';
import { UCPackageModel } from './firestore';
import { BlogPostModel } from './firestore';

const OPENROUTER_API_KEY = import.meta.env.VITE_OPENROUTER_API_KEY;
const API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const IMGBB_API_KEY = import.meta.env.VITE_IMGBB_API_KEY;

// Available AI models
export const AVAILABLE_MODELS = [
  { id: "meta-llama/llama-4-maverick:free", name: "Meta: Llama 4 Maverick" },
  { id: "meta-llama/llama-4-scout:free", name: "Meta: Llama 4 Scout" },
  { id: "nvidia/llama-3.1-nemotron-ultra-253b-v1:free", name: "NVIDIA: Llama 3.1 Nemotron Ultra 253B" },
  { id: "nvidia/llama-3.3-nemotron-super-49b-v1:free", name: "NVIDIA: Llama 3.3 Nemotron Super 49B" },
  { id: "deepseek/deepseek-v3-base:free", name: "DeepSeek: DeepSeek V3 Base" },
  { id: "deepseek/deepseek-chat-v3-0324:free", name: "DeepSeek: DeepSeek V3 0324" },
  { id: "deepseek/deepseek-chat:free", name: "DeepSeek: DeepSeek V3" },
  { id: "deepseek/deepseek-r1:free", name: "DeepSeek: R1" },
  { id: "deepseek/deepseek-r1-zero:free", name: "DeepSeek: DeepSeek R1 Zero" },
  { id: "deepseek/deepseek-prover-v2:free", name: "DeepSeek: DeepSeek Prover V2" },
  { id: "tngtech/deepseek-r1t-chimera:free", name: "TNG: DeepSeek R1T Chimera" },
  { id: "google/gemini-2.0-flash-exp:free", name: "Google: Gemini 2.0 Flash" },
  { id: "google/gemini-flash-1.5-8b-exp", name: "Google: Gemini 1.5 Flash 8B" },
  { id: "google/gemma-3-4b-it:free", name: "Google: Gemma 3 4B" },
  { id: "google/gemma-3-12b-it:free", name: "Google: Gemma 3 12B" },
  { id: "qwen/qwen3-4b:free", name: "Qwen: Qwen3 4B" },
  { id: "qwen/qwen2.5-vl-72b-instruct:free", name: "Qwen: Qwen2.5 VL 72B" },
  { id: "microsoft/mai-ds-r1:free", name: "Microsoft: MAI DS R1" },
  { id: "moonshotai/kimi-vl-a3b-thinking:free", name: "Moonshot AI: Kimi VL A3B Thinking" },
  { id: "meta-llama/llama-3.1-8b-instruct:free", name: "Meta: Llama 3.1 8B Instruct" },
  { id: "meta-llama/llama-3.2-1b-instruct:free", name: "Meta: Llama 3.2 1B Instruct" },
  { id: "meta-llama/llama-3.2-11b-vision-instruct:free", name: "Meta: Llama 3.2 11B Vision" },
  { id: "nousresearch/deephermes-3-llama-3-8b-preview:free", name: "Nous: DeepHermes 3 Llama 3 8B" },
  { id: "mistralai/mistral-nemo:free", name: "Mistral: Mistral Nemo" }
];

// Default model configuration
export const DEFAULT_MODEL = "meta-llama/llama-4-maverick:free";
export const DEFAULT_TEMPERATURE = 0.8;

// Get saved model preference or default
export function getSavedModel(): string {
  if (typeof window !== 'undefined') {
    const savedModel = localStorage.getItem('preferredAIModel') || DEFAULT_MODEL;
    
    // Check if the saved model is still in the available models list
    // If not, return the default model instead to prevent errors
    const modelExists = AVAILABLE_MODELS.some(model => model.id === savedModel);
    return modelExists ? savedModel : DEFAULT_MODEL;
  }
  return DEFAULT_MODEL;
}

// Save model preference
export function saveModelPreference(modelId: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('preferredAIModel', modelId);
  }
}

// Generic OpenRouter API call with configurable model
async function callOpenRouterAPI(prompt: string, modelId: string = getSavedModel(), temp: number = DEFAULT_TEMPERATURE): Promise<string> {
  const response = await fetch(API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
      'HTTP-Referer': 'https://pubg-sd.netlify.app',
      'X-Title': 'PUBG STORE'
    },
    body: JSON.stringify({
      model: modelId,
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: temp
    })
  });

  const data = await response.json();
  
  if (data.error) {
    console.error("OpenRouter API error:", data.error);
    throw new Error(data.error.message || "Error processing with AI");
  }

  const responseText = data.choices?.[0]?.message?.content || '';
  if (!responseText) {
    throw new Error("Invalid response from OpenRouter API");
  }

  return responseText;
}

// Upload image to ImgBB
async function uploadImageToImgBB(imageUrl: string): Promise<string> {
  try {
    const formData = new FormData();
    formData.append('image', imageUrl);

    const response = await fetch(`https://api.imgbb.com/1/upload?key=${IMGBB_API_KEY}`, {
      method: 'POST',
      body: formData
    });

    const data = await response.json();
    if (data.success) {
      return data.data.url;
    } else {
      throw new Error('Failed to upload image');
    }
  } catch (error) {
    console.error('Error uploading image:', error);
    return imageUrl; // Return original URL if upload fails
  }
}

// Parse PUBG account details from Telegram post text
export async function parseAccountDetails(postText: string, modelId: string = getSavedModel()): Promise<Partial<AccountModel>> {
  let responseText = ''; // Store the response text for error handling
  
  try {
    console.log("Starting to parse account details with text:", postText);

    const prompt = `
      قم بتحليل منشور حساب PUBG هذا واستخراج المعلومات التالية بتنسيق JSON:
      1. العنوان (عنوان قصير ووصفي للحساب)
      2. العنوان بالإنجليزية (ترجمة العنوان العربي للإنجليزية)
      3. الوصف (وصف مفصل للحساب - قم بتنسيق كل ميزة في سطر منفصل ووضع سطر فارغ بين الفقرات)
      4. الوصف بالإنجليزية (ترجمة الوصف العربي للإنجليزية، مع الحفاظ على التنسيق)
      5. السعر بالدولار (القيمة الرقمية فقط)
      6. السعر بالجنيه المصري (القيمة الرقمية فقط)
      7. التصنيف (مثل "Royal Pass"، "Exclusive Skins"، إلخ.)
      8. مميز (boolean - اجعله true إذا كان الحساب يبدو مميزًا/خاصًا)
      9. رابط الصورة (إذا وجدت في النص روابط تنتهي بـ .jpg أو .png أو .jpeg أو .gif أو روابط من مواقع الصور مثل imgur.com، أو ibb.co، أو pasteboard.co)

      قم بتنسيق الرد كـ JSON صالح باستخدام هذه المفاتيح بالضبط: title, title_en, description, description_en, priceUSD, priceEGP, category, featured, image

      ملاحظات مهمة:
      - إذا كان السعر بالدولار، قم بتحويله للجنيه المصري (1 دولار = 31 جنيه)
      - إذا كان السعر بالجنيه، قم بتحويله للدولار (اقسم على 31)
      - اجعل العنوان والوصف باللغة العربية
      - اجعل title_en و description_en ترجمة احترافية للمحتوى العربي
      - اذكر مستوى الحساب في العنوان إن وجد
      - للوصف: ضع كل ميزة أو خاصية في سطر منفصل مع علامة نقطية (-) أو رقمية مع سطر فارغ بين الأقسام المختلفة
      - قم بترتيب المميزات في فئات منطقية مثل: المستوى، الأسلحة، البدلات، الشخصيات، إلخ.
      - ابحث عن روابط صور في النص واستخرجها. الروابط تبدأ عادة بـ http:// أو https:// وقد تحتوي على مواقع مثل imgur.com أو ibb.co أو صيغ ملفات مثل .jpg أو .png
      - إذا لم تجد روابط للصور، اترك حقل image فارغًا ""

      المهم أن يكون الناتج JSON صحيح لا يحتوي على أي أخطاء في التنسيق.

      محتوى المنشور:
      ${postText}
    `;

    console.log("Sending request to OpenRouter API with model:", modelId);
    responseText = await callOpenRouterAPI(prompt, modelId, 0.2);
    console.log("Extracted text from response:", responseText);

    return parseJsonResponse(responseText);
  } catch (error) {
    console.error("Error parsing account details:", error);
    
    // If we have the response text, try emergency parsing
    if (responseText && error instanceof SyntaxError) {
      try {
        return emergencyJsonParsing(responseText);
      } catch (emergencyError) {
        console.error("Emergency parsing failed:", emergencyError);
      }
    }
    
    throw error;
  }
}

// Helper function for normal JSON parsing
function parseJsonResponse(text: string): Partial<AccountModel> {
  try {
    console.log("Attempting to parse JSON directly");
    // Clean the response text to remove control characters before parsing
    const cleanedText = text.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
    const parsedData = JSON.parse(cleanedText);
    console.log("Successfully parsed JSON:", parsedData);

    // Ensure prices are numbers
    const priceUSD = Number(parsedData.priceUSD) || 0;
    const priceEGP = Number(parsedData.priceEGP) || 0;

    return {
      title: parsedData.title || "",
      title_en: parsedData.title_en || "",
      description: parsedData.description || "",
      description_en: parsedData.description_en || "",
      priceUSD,
      priceEGP,
      category: parsedData.category || "",
      featured: Boolean(parsedData.featured) || false,
      image: parsedData.image || "",
    };
  } catch (e) {
    console.log("Direct JSON parsing failed, trying to extract JSON from text");
    
    // Replace control characters and try to extract a valid JSON object
    const cleanedText = text.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      console.error("No JSON pattern found in text");
      throw new Error("No valid JSON found in OpenRouter response");
    }
    
    console.log("Found JSON pattern:", jsonMatch[0]);
    // Use a more tolerant parsing approach
    const jsonStr = jsonMatch[0]
      .replace(/[\n\r]/g, " ")       // Replace newlines with spaces
      .replace(/,\s*}/g, "}")        // Remove trailing commas
      .replace(/,\s*,/g, ",")        // Remove double commas
      .replace(/:\s*,/g, ": null,")  // Replace empty values with null
      .replace(/\\([^"\\/bfnrtu])/g, "\\\\$1"); // Escape unescaped backslashes
      
    console.log("Cleaned JSON string:", jsonStr);
    const parsedData = JSON.parse(jsonStr);
    console.log("Successfully parsed extracted JSON:", parsedData);

    // Ensure prices are numbers
    const priceUSD = Number(parsedData.priceUSD) || 0;
    const priceEGP = Number(parsedData.priceEGP) || 0;

    return {
      title: parsedData.title || "",
      title_en: parsedData.title_en || "",
      description: parsedData.description || "",
      description_en: parsedData.description_en || "",
      priceUSD,
      priceEGP,
      category: parsedData.category || "",
      featured: Boolean(parsedData.featured) || false,
      image: parsedData.image || "",
    };
  }
}

// Emergency parsing for badly formatted JSON
function emergencyJsonParsing(text: string): Partial<AccountModel> {
  console.log("Starting emergency JSON parsing");
  
  // Extract fields manually
  const extractField = (fieldName: string): string => {
    const regex = new RegExp(`["']${fieldName}["']\\s*:\\s*["']([^"']*)["']`, 'i');
    const match = text.match(regex);
    return match ? match[1] : '';
  };
  
  const extractNumber = (fieldName: string): number => {
    const regex = new RegExp(`["']${fieldName}["']\\s*:\\s*([0-9.]+)`, 'i');
    const match = text.match(regex);
    return match ? Number(match[1]) : 0;
  };
  
  const extractBoolean = (fieldName: string): boolean => {
    const regex = new RegExp(`["']${fieldName}["']\\s*:\\s*(true|false)`, 'i');
    const match = text.match(regex);
    return match ? match[1].toLowerCase() === 'true' : false;
  };
  
  // Extract image URL separately in case it contains special characters
  const extractImageUrl = (): string => {
    const regex = new RegExp(`["']image["']\\s*:\\s*["']([^"']*)["']`, 'i');
    const match = text.match(regex);
    return match ? match[1] : '';
  };
  
  console.log("Extracting fields manually");
  const result: Partial<AccountModel> = {
    title: extractField('title'),
    title_en: extractField('title_en'),
    description: extractField('description'),
    description_en: extractField('description_en'),
    priceUSD: extractNumber('priceUSD'),
    priceEGP: extractNumber('priceEGP'),
    category: extractField('category'),
    featured: extractBoolean('featured'),
    image: extractImageUrl()
  };
  
  console.log("Emergency parsed result:", result);
  return result;
}

// Parse UC package details from text
export async function parseUCPackageDetails(postText: string, modelId: string = getSavedModel()): Promise<Partial<UCPackageModel>> {
  let responseText = ''; // Store the response text for error handling
  
  try {
    const prompt = `
      قم بتحليل منشور باقة UC في PUBG واستخراج المعلومات التالية بتنسيق JSON:
      1. الكمية (عدد الـ UC)
      2. السعر بالجنيه المصري (قيمة رقمية فقط)
      3. السعر بالدولار (قيمة رقمية فقط)
      4. رابط الصورة (إن وجد، وإلا اتركه فارغًا)
      5. مميز (boolean - اجعله true إذا كانت الباقة تبدو عرضًا خاصًا)

      قم بتنسيق الرد كـ JSON صالح باستخدام هذه المفاتيح بالضبط: amount, priceEGP, priceUSD, image, featured

      المهم أن يكون الناتج JSON صحيح لا يحتوي على أي أخطاء في التنسيق.

      محتوى المنشور:
      ${postText}
    `;

    responseText = await callOpenRouterAPI(prompt, modelId, 0.2);
    console.log("Extracted text from UC package response:", responseText);

    return parseUCJsonResponse(responseText);
  } catch (error) {
    console.error("Error parsing UC package details:", error);
    
    // If we have the response text, try emergency parsing
    if (responseText && error instanceof SyntaxError) {
      try {
        return emergencyUCJsonParsing(responseText);
      } catch (emergencyError) {
        console.error("Emergency UC package parsing failed:", emergencyError);
      }
    }
    
    throw error;
  }
}

// Helper function for normal UC package JSON parsing
function parseUCJsonResponse(text: string): Partial<UCPackageModel> {
  try {
    // Clean the response text to remove control characters before parsing
    const cleanedText = text.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
    const parsedData = JSON.parse(cleanedText);

    return {
      amount: Number(parsedData.amount) || 0,
      priceEGP: Number(parsedData.priceEGP) || 0,
      priceUSD: Number(parsedData.priceUSD) || 0,
      image: parsedData.image || "",
      featured: Boolean(parsedData.featured) || false,
    };
  } catch (e) {
    // Replace control characters and try to extract a valid JSON object
    const cleanedText = text.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("No valid JSON found in OpenRouter response");
    }
    
    // Use a more tolerant parsing approach
    const jsonStr = jsonMatch[0]
      .replace(/[\n\r]/g, " ")
      .replace(/,\s*}/g, "}")
      .replace(/,\s*,/g, ",")
      .replace(/:\s*,/g, ": null,")
      .replace(/\\([^"\\/bfnrtu])/g, "\\\\$1");
      
    const parsedData = JSON.parse(jsonStr);

    return {
      amount: Number(parsedData.amount) || 0,
      priceEGP: Number(parsedData.priceEGP) || 0,
      priceUSD: Number(parsedData.priceUSD) || 0,
      image: parsedData.image || "",
      featured: Boolean(parsedData.featured) || false,
    };
  }
}

// Emergency parsing for badly formatted UC package JSON
function emergencyUCJsonParsing(text: string): Partial<UCPackageModel> {
  // More aggressive cleaning: keep only basic characters
  const aggressiveClean = text.replace(/[^\x20-\x7E]/g, "")
    .replace(/\\([^"\\/bfnrtu])/g, "$1")
    .replace(/\\u[0-9a-fA-F]{0,3}([^0-9a-fA-F])/g, "$1");
    
  // Find anything that looks like a JSON object
  const match = aggressiveClean.match(/\{[\s\S]*\}/);
  if (!match) {
    throw new Error("Could not find JSON structure in response");
  }
  
  // Manual JSON parsing as a last resort
  const cleanJson = match[0]
    .replace(/[\n\r\t]/g, " ")
    .replace(/\s+/g, " ")
    .replace(/,\s*}/g, "}")
    .replace(/,\s*]/g, "]")
    .replace(/\\'/g, "'")
    .replace(/"/g, '"')
    .replace(/'/g, '"')
    .replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');
    
  const parsedData = JSON.parse(cleanJson);
  
  // Return the recovered data
  return {
    amount: Number(parsedData.amount) || 0,
    priceEGP: Number(parsedData.priceEGP) || 0,
    priceUSD: Number(parsedData.priceUSD) || 0,
    image: parsedData.image || "",
    featured: Boolean(parsedData.featured) || false,
  };
}

// Parse blog post details from text
export async function parseBlogPostDetails(postText: string, modelId: string = getSavedModel()): Promise<Partial<BlogPostModel>> {
  let responseText = '';
  try {
    const prompt = `
      Analyze this blog post content and extract the following information as valid JSON:
      1. Arabic Title (title): A catchy Arabic title for the article
      2. English Title (title_en): A catchy English title for the article
      3. Arabic Excerpt (excerpt): A short summary in Arabic (max 150 chars)
      4. English Excerpt (excerpt_en): A short summary in English (max 150 chars)
      5. Arabic Content (content): The full formatted article in Arabic
      6. English Content (content_en): The full formatted article in English
      7. Image URL (image): If found, otherwise leave empty
      8. Arabic Author (author): If mentioned, otherwise use "Admin"
      9. English Author (author_en): If mentioned, otherwise use "Admin"
      10. Date (date): Format YYYY-MM-DD, use today if not specified
      11. Arabic Slug (slug): URL-friendly version of the Arabic title (lowercase, dashes)
      12. English Slug (slug_en): URL-friendly version of the English title (lowercase, dashes)
      13. Featured (featured): boolean, true if the post seems important

      Important requirements:
      - All fields must be filled in both Arabic and English where possible.
      - Content should be creative, engaging, and well-formatted in both languages.
      - Use appropriate emojis in the content to make it more interactive and attractive.
      - Organize the text into well-structured paragraphs with clear subheadings.
      - Use bullet points and lists for organized information.
      - Make the style creative, unique, and fun to read.
      - Use correct punctuation and formatting.
      - Add motivational and exciting phrases where appropriate.
      - Format the content with separate lines for each important sentence or point.
      - Use a blank line between different paragraphs.
      - Write subheadings on separate lines.
      - Integrate emojis appropriately in both Arabic and English content.

      Format the response as valid JSON with these exact keys:
      title, title_en, excerpt, excerpt_en, content, content_en, image, author, author_en, date, slug, slug_en, featured

      The output must be valid JSON with no formatting errors.

      Blog post content:
      ${postText}
    `;

    responseText = await callOpenRouterAPI(prompt, modelId, 0.8);
    console.log("Extracted text from blog post response:", responseText);

    return parseBlogJsonResponse(responseText);
  } catch (error) {
    console.error("Error parsing blog post details:", error);
    if (responseText && error instanceof SyntaxError) {
      try {
        return emergencyBlogJsonParsing(responseText);
      } catch (emergencyError) {
        console.error("Emergency blog post parsing failed:", emergencyError);
      }
    }
    throw error;
  }
}

function parseBlogJsonResponse(text: string): Partial<BlogPostModel> {
  try {
    const cleanedText = text.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
    const parsedData = JSON.parse(cleanedText);
    if (!parsedData.date) {
      const today = new Date();
      parsedData.date = today.toISOString().split('T')[0];
    }
    return {
      title: parsedData.title || "",
      title_en: parsedData.title_en || "",
      excerpt: parsedData.excerpt || "",
      excerpt_en: parsedData.excerpt_en || "",
      content: parsedData.content || "",
      content_en: parsedData.content_en || "",
      image: parsedData.image || "",
      author: parsedData.author || "Admin",
      author_en: parsedData.author_en || "Admin",
      date: parsedData.date,
      slug: parsedData.slug || "",
      slug_en: parsedData.slug_en || "",
      featured: Boolean(parsedData.featured) || false,
    };
  } catch (e) {
    const cleanedText = text.replace(/[\x00-\x1F\x7F-\x9F]/g, "");
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("No valid JSON found in OpenRouter response");
    }
    const jsonStr = jsonMatch[0]
      .replace(/[\n\r]/g, " ")
      .replace(/,\s*}/g, "}")
      .replace(/,\s*,/g, ",")
      .replace(/:\s*,/g, ": null,")
      .replace(/\\([^"\\/bfnrtu])/g, "\\\\$1");
    const parsedData = JSON.parse(jsonStr);
    if (!parsedData.date) {
      const today = new Date();
      parsedData.date = today.toISOString().split('T')[0];
    }
    return {
      title: parsedData.title || "",
      title_en: parsedData.title_en || "",
      excerpt: parsedData.excerpt || "",
      excerpt_en: parsedData.excerpt_en || "",
      content: parsedData.content || "",
      content_en: parsedData.content_en || "",
      image: parsedData.image || "",
      author: parsedData.author || "Admin",
      author_en: parsedData.author_en || "Admin",
      date: parsedData.date,
      slug: parsedData.slug || "",
      slug_en: parsedData.slug_en || "",
      featured: Boolean(parsedData.featured) || false,
    };
  }
}

// Emergency parsing for badly formatted blog post JSON
function emergencyBlogJsonParsing(text: string): Partial<BlogPostModel> {
  // More aggressive cleaning: keep only basic characters
  const aggressiveClean = text.replace(/[^\x20-\x7E]/g, "")
    .replace(/\\([^"\\/bfnrtu])/g, "$1")
    .replace(/\\u[0-9a-fA-F]{0,3}([^0-9a-fA-F])/g, "$1");
    
  // Find anything that looks like a JSON object
  const match = aggressiveClean.match(/\{[\s\S]*\}/);
  if (!match) {
    throw new Error("Could not find JSON structure in response");
  }
  
  // Manual JSON parsing as a last resort
  const cleanJson = match[0]
    .replace(/[\n\r\t]/g, " ")
    .replace(/\s+/g, " ")
    .replace(/,\s*}/g, "}")
    .replace(/,\s*]/g, "]")
    .replace(/\\'/g, "'")
    .replace(/"/g, '"')
    .replace(/'/g, '"')
    .replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');
    
  const parsedData = JSON.parse(cleanJson);
  
  // Format today's date if none provided
  if (!parsedData.date) {
    const today = new Date();
    parsedData.date = today.toISOString().split('T')[0]; // YYYY-MM-DD format
  }
  
  // Return the recovered data
  return {
    title: parsedData.title || "",
    title_en: parsedData.title_en || "",
    excerpt: parsedData.excerpt || "",
    excerpt_en: parsedData.excerpt_en || "",
    content: parsedData.content || "",
    content_en: parsedData.content_en || "",
    image: parsedData.image || "",
    author: parsedData.author || "Admin",
    author_en: parsedData.author_en || "Admin",
    date: parsedData.date,
    slug: parsedData.slug || "",
    slug_en: parsedData.slug_en || "",
    featured: Boolean(parsedData.featured) || false,
  };
} 