(()=>{var e={};e.id=55,e.ids=[55],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},80607:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>g,serverHooks:()=>m,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{GET:()=>u,POST:()=>i,PUT:()=>l});var s=t(96559),n=t(48088),o=t(37719),c=t(32190);async function u(e){try{let{searchParams:r}=new URL(e.url),t=r.get("from"),a=r.get("to"),s=r.get("base")||"USD";if(t&&a){let e=await d(t,a);return c.NextResponse.json({success:!0,rate:{fromCurrency:t,toCurrency:a,rate:e,timestamp:new Date}})}{let e=await p(s);return c.NextResponse.json({success:!0,baseCurrency:s,rates:e,timestamp:new Date})}}catch(e){return console.error("Error fetching exchange rates:",e),c.NextResponse.json({success:!1,error:"Failed to fetch exchange rates",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function i(e){try{let r=await e.json();if(!r.rates||!Array.isArray(r.rates)||0===r.rates.length)return c.NextResponse.json({success:!1,error:"Rates array is required and must not be empty"},{status:400});let t=await f(r);return c.NextResponse.json(t,{status:t.success?200:400})}catch(e){return console.error("Error updating exchange rates:",e),c.NextResponse.json({success:!1,error:"Failed to update exchange rates",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(e,r){if(e===r)return 1;try{let{data:t}=await supabase.rpc("get_exchange_rate",{p_from_currency:e,p_to_currency:r});if(t)return t;let{data:a}=await supabase.from("exchange_rates").select("rate").eq("from_currency_code","USD").eq("to_currency_code",e).eq("is_active",!0).order("effective_date",{ascending:!1}).limit(1).single(),{data:s}=await supabase.from("exchange_rates").select("rate").eq("from_currency_code","USD").eq("to_currency_code",r).eq("is_active",!0).order("effective_date",{ascending:!1}).limit(1).single();if(a&&s)return s.rate/a.rate;throw Error(`Exchange rate not found for ${e} to ${r}`)}catch(t){throw console.error(`Error getting exchange rate for ${e} to ${r}:`,t),t}}async function p(e){try{let{data:r,error:t}=await supabase.from("exchange_rates").select("to_currency_code, rate").eq("from_currency_code",e).eq("is_active",!0).order("effective_date",{ascending:!1});if(t)throw Error(`Failed to fetch rates: ${t.message}`);let a={};return a[e]=1,r?.forEach(e=>{a[e.to_currency_code]=e.rate}),a}catch(r){throw console.error(`Error getting all exchange rates for ${e}:`,r),r}}async function f(e){let r=[],t=[];try{for(let a of e.rates)try{if(a.rate<=0){t.push(`Invalid rate for ${a.fromCurrency} to ${a.toCurrency}: Rate must be positive`);continue}if(a.fromCurrency===a.toCurrency&&1!==a.rate){t.push(`Invalid rate for ${a.fromCurrency} to ${a.toCurrency}: Same currency rate must be 1.0`);continue}await supabase.from("exchange_rates").update({is_active:!1}).eq("from_currency_code",a.fromCurrency).eq("to_currency_code",a.toCurrency);let{data:s,error:n}=await supabase.from("exchange_rates").insert({from_currency_code:a.fromCurrency,to_currency_code:a.toCurrency,rate:a.rate,effective_date:e.effectiveDate||new Date().toISOString(),is_active:!0}).select().single();if(n){t.push(`Failed to update rate for ${a.fromCurrency} to ${a.toCurrency}: ${n.message}`);continue}r.push({id:s.id,fromCurrencyCode:s.from_currency_code,toCurrencyCode:s.to_currency_code,rate:s.rate,effectiveDate:new Date(s.effective_date),createdAt:new Date(s.created_at),isActive:s.is_active})}catch(e){t.push(`Error updating rate for ${a.fromCurrency} to ${a.toCurrency}: ${e}`)}return{success:0===t.length,message:0===t.length?`Successfully updated ${r.length} exchange rates`:`Updated ${r.length} rates with ${t.length} errors`,updatedRates:r,errors:t.length>0?t:void 0}}catch(e){return console.error("Error in updateExchangeRates:",e),{success:!1,message:"Failed to update exchange rates",updatedRates:[],errors:[`Internal server error: ${e}`]}}}async function l(e){try{let{rates:r,source:t}=await e.json();if(!r||"object"!=typeof r)return c.NextResponse.json({success:!1,error:"Rates object is required"},{status:400});let a=Object.entries(r).map(([e,r])=>({fromCurrency:"USD",toCurrency:e,rate:r})),s=await f({rates:a,effectiveDate:new Date,source:t});return c.NextResponse.json(s,{status:s.success?200:400})}catch(e){return console.error("Error in bulk update exchange rates:",e),c.NextResponse.json({success:!1,error:"Failed to bulk update exchange rates",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/exchange-rates/route",pathname:"/api/exchange-rates",filename:"route",bundlePath:"app/api/exchange-rates/route"},resolvedPagePath:"D:\\VS-projects\\try\\alraya-store\\app\\api\\exchange-rates\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:y,workUnitAsyncStorage:h,serverHooks:m}=g;function _(){return(0,o.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:h})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,580],()=>t(80607));module.exports=a})();