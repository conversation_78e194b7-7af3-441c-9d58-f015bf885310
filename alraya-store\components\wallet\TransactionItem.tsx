"use client"

import { useState } from "react"
import { Transaction } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { toast } from "sonner"
import {
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle,
  Gift,
  Copy,
  Eye,
  EyeOff,
  Sparkles
} from "lucide-react"

interface TransactionItemProps {
  transaction: Transaction
}

// Digital Content Modal for Transaction
function DigitalContentModal({ transaction }: { transaction: Transaction }) {
  const [revealedCodes, setRevealedCodes] = useState<Set<string>>(new Set())

  // Consistent date formatting to prevent hydration mismatch
  const formatDate = (date: Date) => {
    const dateObj = new Date(date)
    const year = dateObj.getFullYear()
    const month = String(dateObj.getMonth() + 1).padStart(2, '0')
    const day = String(dateObj.getDate()).padStart(2, '0')
    const hours = String(dateObj.getHours()).padStart(2, '0')
    const minutes = String(dateObj.getMinutes()).padStart(2, '0')

    return `${day}/${month}/${year} ${hours}:${minutes}`
  }

  const handleCopyCode = async (content: string, title: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast.success(`تم نسخ ${title} بنجاح!`)
    } catch (error) {
      toast.error("فشل في نسخ الكود")
    }
  }

  const toggleRevealCode = (codeId: string) => {
    const newRevealed = new Set(revealedCodes)
    if (newRevealed.has(codeId)) {
      newRevealed.delete(codeId)
    } else {
      newRevealed.add(codeId)
    }
    setRevealedCodes(newRevealed)
  }

  if (!transaction.digitalContent || !transaction.digitalContent.contents.length) {
    return (
      <div className="text-center py-8">
        <div className="text-slate-400 mb-2">لا يوجد محتوى رقمي</div>
      </div>
    )
  }

  return (
    <div className="space-y-4 max-h-[70vh] overflow-y-auto">
      {/* Transaction Details */}
      <div className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4">
        <h3 className="text-white font-medium mb-2">{transaction.description}</h3>
        <div className="flex items-center gap-4 text-sm text-slate-400">
          <span>المبلغ: {formatCurrency(transaction.amount, transaction.currency)}</span>
          <span>التاريخ: {formatDate(transaction.date)}</span>
          <span className="text-green-400">✅ مكتمل</span>
        </div>
      </div>

      {/* Digital Content Items */}
      {transaction.digitalContent.contents.map((content, index) => {
        const codeId = `${transaction.id}_${index}`
        const isRevealed = revealedCodes.has(codeId)

        return (
          <div key={codeId} className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                <Gift className="h-4 w-4 text-white" />
              </div>
              <div>
                <h4 className="text-white font-medium">{content.title}</h4>
                <p className="text-sm text-slate-400">كود رقمي</p>
              </div>
            </div>

            {/* Code Display */}
            <div className="bg-slate-800/50 rounded-lg p-3 mb-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-400">الكود:</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleRevealCode(codeId)}
                  className="text-slate-400 hover:text-white h-8 px-2"
                >
                  {isRevealed ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-1" />
                      إخفاء
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-1" />
                      عرض
                    </>
                  )}
                </Button>
              </div>

              <div className="font-mono text-lg text-white bg-slate-900/50 rounded p-3 border border-slate-600/50 mb-3">
                {isRevealed ? content.content : '••••••••••••••••'}
              </div>

              {isRevealed && (
                <Button
                  onClick={() => handleCopyCode(content.content, content.title)}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  size="sm"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  نسخ الكود
                </Button>
              )}
            </div>

            {/* Instructions */}
            {content.instructions && (
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                <h5 className="text-blue-400 font-medium mb-2">كيفية الاستخدام:</h5>
                <p className="text-sm text-blue-200 whitespace-pre-line">{content.instructions}</p>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

export function TransactionItem({ transaction }: TransactionItemProps) {
  const hasDigitalContent = transaction.hasDigitalContent && transaction.digitalContent
  const digitalContentReady = hasDigitalContent && transaction.digitalContent?.status === 'ready'

  // Consistent date formatting to prevent hydration mismatch
  const formatDate = (date: Date) => {
    const dateObj = new Date(date)
    const year = dateObj.getFullYear()
    const month = String(dateObj.getMonth() + 1).padStart(2, '0')
    const day = String(dateObj.getDate()).padStart(2, '0')
    const hours = String(dateObj.getHours()).padStart(2, '0')
    const minutes = String(dateObj.getMinutes()).padStart(2, '0')

    return `${day}/${month}/${year} ${hours}:${minutes}`
  }

  const getTransactionIcon = () => {
    switch (transaction.type) {
      case "deposit":
        return <TrendingUp className="h-5 w-5 text-green-400" />
      case "withdrawal":
        return <TrendingDown className="h-5 w-5 text-red-400" />
      case "purchase":
        return hasDigitalContent ? (
          <div className="relative">
            <ShoppingCart className="h-5 w-5 text-blue-400" />
            <Sparkles className="h-3 w-3 text-yellow-400 absolute -top-1 -right-1" />
          </div>
        ) : (
          <ShoppingCart className="h-5 w-5 text-blue-400" />
        )
      default:
        return <Clock className="h-5 w-5 text-slate-400" />
    }
  }

  const getStatusIcon = () => {
    switch (transaction.status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-400" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-400" />
      default:
        return null
    }
  }

  const getTransactionTypeText = () => {
    switch (transaction.type) {
      case "deposit":
        return "إيداع"
      case "withdrawal":
        return "سحب"
      case "purchase":
        return "شراء"
      default:
        return "معاملة"
    }
  }

  const getStatusText = () => {
    switch (transaction.status) {
      case "completed":
        return "مكتملة"
      case "pending":
        return "قيد المعالجة"
      case "failed":
        return "فاشلة"
      default:
        return "غير معروف"
    }
  }

  const getAmountColor = () => {
    switch (transaction.type) {
      case "deposit":
        return "text-green-400"
      case "withdrawal":
        return "text-red-400"
      case "purchase":
        return "text-blue-400"
      default:
        return "text-slate-300"
    }
  }

  const getAmountPrefix = () => {
    switch (transaction.type) {
      case "deposit":
        return "+"
      case "withdrawal":
      case "purchase":
        return "-"
      default:
        return ""
    }
  }



  return (
    <div className={cn(
      "flex items-center gap-4 p-4 rounded-xl transition-all duration-300 hover:scale-[1.02]",
      "bg-slate-700/30 hover:bg-slate-700/50 border border-slate-600/30 hover:border-slate-500/50"
    )}>
      {/* Transaction Icon */}
      <div className="flex-shrink-0">
        <div className="w-12 h-12 rounded-full bg-slate-600/50 flex items-center justify-center">
          {getTransactionIcon()}
        </div>
      </div>

      {/* Transaction Details */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-white truncate text-base">
                {transaction.description}
              </h4>
              {hasDigitalContent && (
                <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-300 border-purple-500/30 text-xs">
                  رقمي
                </Badge>
              )}
            </div>
            <div className="flex flex-wrap items-center gap-2 mt-2">
              <span className="text-sm text-slate-400 font-medium">
                {getTransactionTypeText()}
              </span>
              <span className="text-slate-500">•</span>
              <span className="text-sm text-slate-400">
                {formatDate(transaction.date)}
              </span>
            </div>
            {transaction.reference && (
              <div className="text-xs text-slate-500 mt-2 bg-slate-700/30 px-2 py-1 rounded-md inline-block">
                المرجع: {transaction.reference}
              </div>
            )}
          </div>

          {/* Amount */}
          <div className="text-right lg:text-left flex-shrink-0 lg:min-w-[120px]">
            <div className={cn("font-bold text-lg lg:text-xl", getAmountColor())}>
              {getAmountPrefix()}{formatCurrency(transaction.amount, transaction.currency)}
            </div>
            <div className="flex items-center gap-1 justify-end lg:justify-start mt-1">
              {getStatusIcon()}
              <span className="text-xs text-slate-400 font-medium">
                {getStatusText()}
              </span>
            </div>
          </div>
        </div>

        {/* Digital Content Section */}
        {hasDigitalContent && (
          <div className="mt-4 pt-3 border-t border-slate-600/30">
            {digitalContentReady ? (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-green-400">
                  <CheckCircle className="h-4 w-4" />
                  <span>المحتوى الرقمي جاهز!</span>
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                    {transaction.digitalContent.contents.length} عنصر
                  </Badge>
                </div>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium"
                      size="sm"
                    >
                      <Gift className="h-4 w-4 mr-2" />
                      عرض تفاصيل الطلب والأكواد
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="bg-slate-800 border-slate-700 max-w-2xl">
                    <DialogHeader>
                      <DialogTitle className="text-white flex items-center gap-2">
                        <Gift className="h-5 w-5 text-green-400" />
                        تفاصيل الطلب والمحتوى الرقمي
                      </DialogTitle>
                    </DialogHeader>
                    <DigitalContentModal transaction={transaction} />
                  </DialogContent>
                </Dialog>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-sm text-yellow-400">
                <Clock className="h-4 w-4" />
                <span>جاري تحضير المحتوى الرقمي...</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
