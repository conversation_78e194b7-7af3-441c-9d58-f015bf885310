# نظام إدارة المنتجات - Al-Raya Store
# Product Management System - Al-Raya Store

## 🌟 نظرة عامة | Overview

نظام إدارة منتجات شامل مصمم خصيصاً للمتاجر الإلكترونية العربية مع دعم كامل للمنتجات الرقمية والتسليم الفوري.

A comprehensive product management system designed specifically for Arabic e-commerce stores with full support for digital products and instant delivery.

## ✨ المميزات الرئيسية | Key Features

### 🎯 إدارة المنتجات | Product Management
- **واجهة إدارية متقدمة** - لوحة تحكم شاملة لإنشاء وتعديل المنتجات
- **حقول ديناميكية** - نظام حقول مرن يدعم 8 أنواع مختلفة
- **إدارة الحزم** - نظام حزم متقدم مع دعم الخصومات والعروض
- **منتجات رقمية** - دعم كامل للمنتجات الرقمية مع التسليم الفوري

### 🛒 تجربة التسوق | Shopping Experience
- **واجهة عربية أولاً** - مصممة خصيصاً للمستخدمين العرب
- **نماذج تفاعلية** - نماذج ذكية تتكيف مع نوع المنتج
- **اختيار الحزم** - واجهة سهلة لاختيار الحزم والكميات
- **معاينة الأسعار** - حساب فوري للأسعار مع دعم العملات المتعددة

### 💎 المحتوى الرقمي | Digital Content
- **تسليم فوري** - نظام تسليم أكواد رقمية فوري
- **تشفير آمن** - حماية الأكواد الرقمية بالتشفير
- **إدارة المخزون** - تتبع الأكواد المتاحة والمستخدمة
- **عرض في المحفظة** - عرض الأكواد في محفظة المستخدم

## 🏗️ البنية التقنية | Technical Architecture

### 📁 هيكل المشروع | Project Structure

```
📦 Al-Raya Store Product System
├── 📁 components/
│   ├── 📁 admin/                    # مكونات الإدارة
│   │   ├── ProductDashboard.tsx     # لوحة تحكم المنتجات
│   │   ├── ProductForm.tsx          # نموذج إنشاء/تعديل المنتج
│   │   ├── PackageEditor.tsx        # محرر الحزم
│   │   ├── FieldEditor.tsx          # محرر الحقول المخصصة
│   │   └── CustomField.tsx          # مكون الحقل المخصص
│   ├── 📁 products/                 # مكونات المنتجات
│   │   ├── PackageSelector.tsx      # محدد الحزم
│   │   ├── InteractiveProductForm.tsx # نموذج المنتج التفاعلي
│   │   └── DynamicProductPage.tsx   # صفحة المنتج الديناميكية
│   └── 📁 wallet/                   # مكونات المحفظة
│       └── WalletOrders.tsx         # طلبات المحفظة
├── 📁 lib/
│   ├── 📁 services/                 # الخدمات
│   │   ├── productService.ts        # خدمة المنتجات
│   │   └── digitalContentService.ts # خدمة المحتوى الرقمي
│   ├── 📁 data/                     # البيانات
│   │   └── defaultProductTemplates.ts # قوالب المنتجات الافتراضية
│   └── 📁 types/                    # أنواع البيانات
│       └── index.ts                 # تعريفات TypeScript
├── 📁 migrations/                   # ملفات قاعدة البيانات
│   └── 006_product_management_schema.sql
└── 📁 app/                          # صفحات التطبيق
    ├── admin/                       # صفحات الإدارة
    ├── shop/                        # صفحات المتجر
    └── wallet/                      # صفحات المحفظة
```

### 🔧 التقنيات المستخدمة | Technologies Used

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS with RTL support
- **UI Components**: Shadcn/ui
- **State Management**: React Hooks
- **Database**: Supabase (PostgreSQL) - مُعد للتكامل
- **Storage**: Supabase Storage - للصور والملفات

## 🚀 البدء السريع | Quick Start

### 1. تشغيل التطبيق | Run the Application

```bash
# تثبيت المتطلبات
npm install

# تشغيل الخادم المحلي
npm run dev

# فتح المتصفح على
http://localhost:3000
```

### 2. الوصول للوحة الإدارة | Access Admin Panel

```bash
# لوحة تحكم المنتجات
http://localhost:3000/admin

# إدارة المنتجات
http://localhost:3000/admin/products
```

### 3. تصفح المتجر | Browse Shop

```bash
# صفحة المتجر الرئيسية
http://localhost:3000/shop

# صفحة منتج محدد
http://localhost:3000/shop/[product-slug]
```

## 📋 دليل الاستخدام | Usage Guide

### 🎛️ إدارة المنتجات | Product Management

#### إنشاء منتج جديد | Create New Product

1. **الانتقال للوحة الإدارة** - `/admin`
2. **النقر على "إضافة منتج جديد"**
3. **ملء المعلومات الأساسية**:
   - اسم المنتج (عربي وإنجليزي)
   - الوصف والفئة
   - نوع المنتج (رقمي/مادي/خدمة)
   - نوع المعالجة (فوري/يدوي)

4. **إضافة الحزم**:
   - اسم الحزمة والسعر
   - خصومات وعروض خاصة
   - تحديد الحزمة الشائعة

5. **إعداد الحقول المخصصة**:
   - حقول النص والأرقام
   - القوائم المنسدلة
   - محددات الكمية
   - حقول رفع الصور

#### أنواع الحقول المدعومة | Supported Field Types

| النوع | الوصف | مثال |
|-------|--------|------|
| `text` | حقل نص عادي | اسم المستخدم |
| `number` | حقل رقمي | معرف اللعبة |
| `email` | بريد إلكتروني | البريد الإلكتروني |
| `dropdown` | قائمة منسدلة | اختيار الخادم |
| `package` | محدد الحزمة | اختيار حزمة UC |
| `quantity` | محدد الكمية | عدد القطع |
| `textarea` | نص متعدد الأسطر | ملاحظات |
| `image` | رفع صورة | صورة الملف الشخصي |

### 🛍️ تجربة التسوق | Shopping Experience

#### عملية الشراء | Purchase Process

1. **تصفح المنتجات** - في صفحة المتجر
2. **اختيار المنتج** - النقر على المنتج المطلوب
3. **اختيار الحزمة** - من الحزم المتاحة
4. **ملء المعلومات** - الحقول المطلوبة
5. **تأكيد الطلب** - مراجعة والدفع

#### المنتجات الرقمية | Digital Products

- **تسليم فوري** - الأكواد تظهر فوراً بعد الدفع
- **عرض في المحفظة** - الأكواد متاحة في صفحة المحفظة
- **نسخ الأكواد** - إمكانية نسخ الأكواد بنقرة واحدة
- **إخفاء/إظهار** - حماية الأكواد من النظر العابر

## 🔗 نقاط التكامل | Integration Points

### 🗄️ قاعدة البيانات | Database Integration

جميع الوظائف مُعدة للتكامل مع Supabase:

```typescript
// ## TODO: تنفيذ التكامل مع Supabase
// ## DATABASE LATER: ربط بجداول قاعدة البيانات
```

#### الجداول المطلوبة | Required Tables

- `products` - المنتجات الأساسية
- `product_packages` - حزم المنتجات
- `custom_fields` - الحقول المخصصة
- `encrypted_codes` - الأكواد الرقمية المشفرة
- `product_categories` - فئات المنتجات

### 🔐 الأمان | Security

- **Row Level Security (RLS)** - حماية على مستوى الصفوف
- **تشفير الأكواد** - تشفير الأكواد الرقمية
- **صلاحيات المستخدمين** - تحكم في الوصول
- **التحقق من البيانات** - فلترة وتنظيف المدخلات

### 📱 التصميم المتجاوب | Responsive Design

- **Mobile-First** - مصمم للهواتف أولاً
- **RTL Support** - دعم كامل للغة العربية
- **Touch-Friendly** - أزرار بحجم 44px للمس
- **Adaptive UI** - واجهة تتكيف مع الشاشة

## 🧪 الاختبار | Testing

### اختبار الوظائف | Functional Testing

```bash
# اختبار إنشاء المنتجات
1. إنشاء منتج PUBG Mobile UC
2. إضافة حزم مختلفة
3. إعداد حقول مخصصة
4. اختبار النموذج في المتجر

# اختبار المحتوى الرقمي
1. إضافة أكواد رقمية للحزم
2. محاكاة عملية شراء
3. التحقق من ظهور الأكواد في المحفظة
4. اختبار نسخ وإخفاء الأكواد
```

### بيانات تجريبية | Test Data

النظام يأتي مع منتجات تجريبية جاهزة:

- **PUBG Mobile UC** - شحن يوسي
- **Free Fire Diamonds** - شحن جواهر
- **Google Play Gift Card** - بطاقات هدايا

## 🔄 خطة التطوير | Development Roadmap

### المرحلة الحالية | Current Phase
- ✅ واجهة إدارة المنتجات
- ✅ نظام الحقول الديناميكية
- ✅ تجربة التسوق
- ✅ المحتوى الرقمي الأساسي

### المرحلة التالية | Next Phase
- 🔄 تكامل Supabase الكامل
- 🔄 نظام الدفع المتقدم
- 🔄 إشعارات فورية
- 🔄 تحليلات المبيعات

### المستقبل | Future
- 📋 API للتطبيقات الخارجية
- 📋 نظام الخصومات المتقدم
- 📋 تكامل مع منصات الدفع
- 📋 تطبيق الهاتف المحمول

## 🆘 الدعم | Support

### الملفات المهمة | Important Files

- `README_PRODUCT_SYSTEM.md` - هذا الملف
- `migrations/006_product_management_schema.sql` - مخطط قاعدة البيانات
- `lib/types/index.ts` - تعريفات الأنواع
- `lib/services/productService.ts` - خدمة المنتجات

### نقاط التكامل | Integration Points

ابحث عن هذه التعليقات في الكود:

```typescript
// ## TODO: تنفيذ مع Supabase
// ## DATABASE LATER: ربط بقاعدة البيانات
```

---

**🎯 مصمم خصيصاً لخدمات شحن الألعاب والمنتجات الرقمية**

**Built with ❤️ for Arabic gaming and digital services**

## 📚 ملفات إضافية | Additional Documentation

- `INTEGRATION_GUIDE.md` - دليل التكامل التقني
- `API_REFERENCE.md` - مرجع واجهات البرمجة
- `DEPLOYMENT_GUIDE.md` - دليل النشر والتشغيل
