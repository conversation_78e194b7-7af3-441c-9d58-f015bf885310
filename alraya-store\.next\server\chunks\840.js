"use strict";exports.id=840,exports.ids=[840],exports.modules={5525:(e,t,r)=>{function n(){}r.d(t,{F:()=>n})},26269:(e,t,r)=>{r.d(t,{tU:()=>D,av:()=>G,j7:()=>N,Xi:()=>T});var n=r(60687),a=r(43210),o=r(70569),i=r(11273),s=r(72942),l=r(46059),u=r(14163),c=r(43),d=r(65551),f=r(96963),v="Tabs",[p,m]=(0,i.A)(v,[s.RG]),b=(0,s.RG)(),[y,g]=p(v),h=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:o,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:p="automatic",...m}=e,b=(0,c.jH)(l),[g,h]=(0,d.i)({prop:a,onChange:o,defaultProp:i??"",caller:v});return(0,n.jsx)(y,{scope:r,baseId:(0,f.B)(),value:g,onValueChange:h,orientation:s,dir:b,activationMode:p,children:(0,n.jsx)(u.sG.div,{dir:b,"data-orientation":s,...m,ref:t})})});h.displayName=v;var w="TabsList",x=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...o}=e,i=g(w,r),l=b(r);return(0,n.jsx)(s.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:a,children:(0,n.jsx)(u.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});x.displayName=w;var R="TabsTrigger",A=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:i=!1,...l}=e,c=g(R,r),d=b(r),f=F(c.baseId,a),v=I(c.baseId,a),p=a===c.value;return(0,n.jsx)(s.q7,{asChild:!0,...d,focusable:!i,active:p,children:(0,n.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":v,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:f,...l,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(a)})})})});A.displayName=R;var j="TabsContent",k=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:s,...c}=e,d=g(j,r),f=F(d.baseId,o),v=I(d.baseId,o),p=o===d.value,m=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(l.C,{present:i||p,children:({present:r})=>(0,n.jsx)(u.sG.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:v,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&s})})});function F(e,t){return`${e}-trigger-${t}`}function I(e,t){return`${e}-content-${t}`}k.displayName=j;var C=r(96241);let D=h,N=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(x,{ref:r,className:(0,C.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));N.displayName=x.displayName;let T=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(A,{ref:r,className:(0,C.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));T.displayName=A.displayName;let G=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(k,{ref:r,className:(0,C.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));G.displayName=k.displayName},35071:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},72942:(e,t,r)=>{r.d(t,{RG:()=>x,bL:()=>N,q7:()=>T});var n=r(43210),a=r(70569),o=r(9510),i=r(98599),s=r(11273),l=r(96963),u=r(14163),c=r(13495),d=r(65551),f=r(43),v=r(60687),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[y,g,h]=(0,o.N)(b),[w,x]=(0,s.A)(b,[h]),[R,A]=w(b),j=n.forwardRef((e,t)=>(0,v.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(k,{...e,ref:t})})}));j.displayName=b;var k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:s=!1,dir:l,currentTabStopId:y,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:A=!1,...j}=e,k=n.useRef(null),F=(0,i.s)(t,k),I=(0,f.jH)(l),[C,N]=(0,d.i)({prop:y,defaultProp:h??null,onChange:w,caller:b}),[T,G]=n.useState(!1),E=(0,c.c)(x),L=g(r),M=n.useRef(!1),[K,S]=n.useState(0);return n.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(p,E),()=>e.removeEventListener(p,E)},[E]),(0,v.jsx)(R,{scope:r,orientation:o,dir:I,loop:s,currentTabStopId:C,onItemFocus:n.useCallback(e=>N(e),[N]),onItemShiftTab:n.useCallback(()=>G(!0),[]),onFocusableItemAdd:n.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>S(e=>e-1),[]),children:(0,v.jsx)(u.sG.div,{tabIndex:T||0===K?-1:0,"data-orientation":o,...j,ref:F,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),A)}}M.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>G(!1))})})}),F="RovingFocusGroupItem",I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:s,children:c,...d}=e,f=(0,l.B)(),p=s||f,m=A(F,r),b=m.currentTabStopId===p,h=g(r),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:R}=m;return n.useEffect(()=>{if(o)return w(),()=>x()},[o,w,x]),(0,v.jsx)(y.ItemSlot,{scope:r,id:p,focusable:o,active:i,children:(0,v.jsx)(u.sG.span,{tabIndex:b?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return C[a]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>D(r))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=R}):c})})});I.displayName=F;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var N=j,T=I},78122:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},93613:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};