"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { ChevronLeft, ChevronRight } from "lucide-react"
import type { BannerSlide } from "../types"

interface BannerSliderProps {
  banners: BannerSlide[]
}

export default function BannerSlider({ banners }: BannerSliderProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const activeSlides = banners.filter((slide) => slide.active).sort((a, b) => a.order - b.order)

  useEffect(() => {
    if (activeSlides.length === 0) return

    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % activeSlides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [activeSlides.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % activeSlides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + activeSlides.length) % activeSlides.length)
  }

  const getBannerLink = (banner: BannerSlide) => {
    switch (banner.linkType) {
      case "product":
        return `/product/${banner.linkValue}`
      case "collection":
        return `/collection/${banner.linkValue}`
      case "custom":
        return banner.linkValue || "#"
      default:
        return "#"
    }
  }

  if (activeSlides.length === 0) return null

  return (
    <div className="relative w-full overflow-hidden rounded-xl shadow-2xl">
      {/* Fixed 3:1 Aspect Ratio Container */}
      <div className="relative w-full" style={{ paddingBottom: "33.333%" }}>
        {activeSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
              index === currentSlide ? "translate-x-0" : index < currentSlide ? "-translate-x-full" : "translate-x-full"
            }`}
          >
            <div className="relative w-full h-full">
              <Image
                src={slide.image || "/logo.jpg"}
                alt={slide.title}
                fill
                className="object-cover"
                priority={index === 0}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent" />
              <div className="absolute inset-0 flex items-center">
                <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="max-w-lg">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-2 sm:mb-4 text-white leading-tight">
                      {slide.title}
                    </h2>
                    {slide.subtitle && (
                      <p className="text-sm sm:text-lg md:text-xl text-gray-200 mb-4 sm:mb-6 leading-relaxed">
                        {slide.subtitle}
                      </p>
                    )}
                    {slide.linkType !== "none" && (
                      <Link
                        href={getBannerLink(slide)}
                        className="inline-block btn-primary text-sm sm:text-lg px-4 sm:px-8 py-2 sm:py-3 shadow-xl hover:shadow-purple-500/25 transition-all duration-300"
                      >
                        تسوق الآن
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Buttons */}
      {activeSlides.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 sm:p-3 rounded-full transition-all duration-300 backdrop-blur-sm shadow-lg"
            aria-label="الشريحة السابقة"
          >
            <ChevronLeft className="w-4 h-4 sm:w-6 sm:h-6" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 sm:p-3 rounded-full transition-all duration-300 backdrop-blur-sm shadow-lg"
            aria-label="الشريحة التالية"
          >
            <ChevronRight className="w-4 h-4 sm:w-6 sm:h-6" />
          </button>
        </>
      )}

      {/* Dots Indicator */}
      {activeSlides.length > 1 && (
        <div className="absolute bottom-2 sm:bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 space-x-reverse">
          {activeSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 ${
                index === currentSlide ? "bg-white shadow-lg" : "bg-white/50 hover:bg-white/75"
              }`}
              aria-label={`الذهاب إلى الشريحة ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}
