import React from "react";
import { <PERSON> } from "react-router-dom";
import { Facebook, Twitter, Instagram, Youtube, Phone, Mail, MapPin, MessageCircle } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useConfig, useDiscordLink } from "@/contexts/ConfigContext";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";

const Footer = () => {
  // Get current year for copyright
  const currentYear = new Date().getFullYear();
  const isMobile = useIsMobile();
  const iconSize = isMobile ? 16 : 20;
  const { contactInfo, isPageVisible, siteName } = useConfig();
  const discordLink = useDiscordLink();
  const { t } = useTranslation('common');
  const { language } = useLanguage();

  // Parse site name for styled display (similar to Navbar)
  const parseSiteName = () => {
    // If there's a space in the name, make the second part orange
    const parts = siteName.split(' ');
    if (parts.length > 1) {
      const firstPart = parts[0];
      const restParts = parts.slice(1).join(' ');
      return (
        <>
          {firstPart}<span className="text-pubg-orange">{restParts}</span>
        </>
      );
    }
    return siteName; // Return as is if no space
  };

  // Footer links with page IDs
  const footerLinks = [
    { id: "home", name: t('navbar.home'), path: "/" },
    { id: "accounts", name: t('navbar.accounts'), path: "/accounts" },
    { id: "uc-store", name: t('navbar.uc-store'), path: "/uc-store" },
    { id: "mods", name: t('navbar.mods'), path: "/mods" },
    { id: "blog", name: t('navbar.blog'), path: "/blog" },
    { id: "contact", name: t('navbar.contact'), path: "/contact" },
  ];

  // Filter links based on visibility
  const visibleFooterLinks = footerLinks.filter(link => isPageVisible(link.id));

  return (
    <footer className="bg-card/95 backdrop-blur-md mt-8 sm:mt-12 md:mt-16">
      <div className="container mx-auto px-4 py-8 sm:py-10 md:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {/* Logo and About */}
          <div>
            <Link to="/" className="flex items-center mb-3 sm:mb-4">
              <h1 className="text-xl sm:text-2xl font-bold text-white">{parseSiteName()}</h1>
            </Link>
            <p className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4">
              {t('footer.description')}
            </p>
            <div className="flex space-x-3 sm:space-x-4 space-x-reverse">
              {contactInfo?.facebook && (
                <a href={contactInfo.facebook} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-[#1877F2] transition-colors">
                  <Facebook size={iconSize} />
                </a>
              )}
              {contactInfo?.twitter && (
                <a href={contactInfo.twitter} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-[#1DA1F2] transition-colors">
                  <Twitter size={iconSize} />
                </a>
              )}
              {contactInfo?.instagram && (
                <a href={contactInfo.instagram} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-[#E4405F] transition-colors">
                  <Instagram size={iconSize} />
                </a>
              )}
              {discordLink && (
                <a href={discordLink} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-[#5865F2] transition-colors">
                  <MessageCircle size={iconSize} />
                </a>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-base sm:text-lg font-bold text-white mb-3 sm:mb-4">{t('footer.quick_links')}</h3>
            <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm">
              {visibleFooterLinks.map((link) => (
                <li key={link.id}>
                  <Link to={link.path} className="text-muted-foreground hover:text-pubg-orange transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-base sm:text-lg font-bold text-white mb-3 sm:mb-4">{t('footer.contact_info')}</h3>
            <ul className="space-y-2 sm:space-y-4 text-xs sm:text-sm">
              {contactInfo?.whatsappNumber && (
                <li className="flex items-center">
                  <Phone size={isMobile ? 14 : 18} className="ml-2 text-pubg-orange" />
                  <span className="text-muted-foreground">{contactInfo.whatsappNumber}</span>
                </li>
              )}
              {contactInfo?.email && (
                <li className="flex items-center">
                  <Mail size={isMobile ? 14 : 18} className="ml-2 text-pubg-orange" />
                  <span className="text-muted-foreground">{contactInfo.email}</span>
                </li>
              )}
              <li className="flex items-center">
                <MapPin size={isMobile ? 14 : 18} className="ml-2 text-pubg-orange" />
                <span className="text-muted-foreground">{t('footer.location')}</span>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-base sm:text-lg font-bold text-white mb-3 sm:mb-4">{t('footer.newsletter')}</h3>
            <p className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4">
              {t('footer.newsletter_desc')}
            </p>
            <form className="flex">
              <input
                type="email"
                placeholder={t('footer.email_placeholder')}
                className="bg-muted text-white rounded-r-lg py-1.5 sm:py-2 px-2 sm:px-4 w-full text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-pubg-orange"
              />
              <button
                type="submit"
                className="bg-pubg-orange text-pubg-dark font-bold rounded-l-lg py-1.5 sm:py-2 px-2 sm:px-4 text-xs sm:text-sm hover:bg-pubg-orange/90 transition-colors"
              >
                {t('footer.subscribe')}
              </button>
            </form>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-border mt-6 sm:mt-8 pt-4 sm:pt-6 text-center">
          <p className="text-xs sm:text-sm text-muted-foreground">
            &copy; {currentYear} {siteName}. {t('footer.rights_reserved')}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
