import { useState, useEffect, lazy, Suspense } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useConfig } from "@/contexts/ConfigContext";
import { Navigate, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { LogIn, Mail, Lock, UserPlus, AlertTriangle } from "lucide-react";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "@/components/LanguageSwitcher";

// Lazy load the background component
const BackgroundBoxesDemo = lazy(() => 
  import("@/components/ui/background-boxes-demo").then(mod => ({ 
    default: mod.BackgroundBoxesDemo 
  }))
);

// Enhanced animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: "beforeChildren",
      staggerChildren: 0.1,
      duration: 0.6,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      when: "afterChildren",
      staggerChildren: 0.05,
      staggerDirection: -1,
      duration: 0.4,
    },
  },
};

const cardVariants = {
  hidden: { opacity: 0, y: 50, scale: 0.95 },
  visible: {
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: { 
      type: "spring", 
      damping: 15, 
      stiffness: 200, 
      duration: 0.8 
    }
  },
  exit: { 
    opacity: 0, 
    y: -30, 
    scale: 0.9,
    transition: { duration: 0.3, ease: "easeInOut" } 
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
  exit: { opacity: 0, y: 10, transition: { duration: 0.2 } },
};

const buttonVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 20,
    },
  },
  hover: {
    scale: 1.05,
    boxShadow: "0px 0px 15px rgba(255, 126, 0, 0.5)",
    transition: { duration: 0.2, ease: "easeInOut" },
  },
  tap: { scale: 0.98 },
  exit: { opacity: 0, scale: 0.9, transition: { duration: 0.2 } },
};

const inputIconVariants = {
  hidden: { opacity: 0, x: -10 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { 
      type: "spring", 
      stiffness: 300, 
      damping: 25 
    }
  },
  focusIn: { 
    scale: 1.2, 
    color: "#ff7e00", 
    transition: { duration: 0.2 } 
  },
  focusOut: { 
    scale: 1, 
    color: "currentColor", 
    transition: { duration: 0.2 } 
  }
};

const glowVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: [0.2, 0.4, 0.2], 
    transition: { 
      repeat: Infinity, 
      duration: 2.5, 
      ease: "easeInOut" 
    }
  }
};

type AuthFormValues = {
  email: string;
  password: string;
};

const Login = () => {
  const { currentUser, login, signup } = useAuth();
  const { siteName } = useConfig();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation("common");
  const language = i18n.language;
  
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [signupError, setSignupError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("login");
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Add state to control background rendering
  const [shouldRenderBackground, setShouldRenderBackground] = useState(false);
  
  const loginForm = useForm<AuthFormValues>({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const registerForm = useForm<AuthFormValues>({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Add background animation effect
  useEffect(() => {
    document.body.classList.add('auth-page-background');
    
    // Delay background rendering to prioritize UI first
    const timer = setTimeout(() => {
      setShouldRenderBackground(true);
    }, 300);
    
    return () => {
      clearTimeout(timer);
      document.body.classList.remove('auth-page-background');
    };
  }, []);

  // Parse the site name to handle any formatting
  const parseSiteName = () => {
    // If the site name contains a space, assume the second part might be styled differently
    const parts = siteName.split(' ');
    if (parts.length > 1) {
      return (
        <>
          {parts[0]}<span className="text-pubg-orange">{parts.slice(1).join(' ')}</span>
        </>
      );
    }
    return siteName; // Return as is if no space
  };

  const handleLogin = async (data: AuthFormValues) => {
    try {
      setIsLoading(true);
      setLoginError(null);
      await login(data.email, data.password);
      toast({
        title: t("auth.success.login"),
        description: t("auth.success.login_message", { siteName }),
        variant: "default",
      });
      navigate("/");
    } catch (error: any) {
      console.error("Login error:", error);
      setLoginError(t("auth.login.error_message"));
      toast({
        title: t("auth.login.error_title"),
        description: t("auth.login.error_message"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (data: AuthFormValues) => {
    try {
      setIsLoading(true);
      setSignupError(null);
      await signup(data.email, data.password);
      toast({
        title: t("auth.success.register"),
        description: t("auth.success.register_message", { siteName }),
        variant: "default",
      });
      navigate("/");
    } catch (error: any) {
      console.error("Signup error:", error);
      setSignupError(t("auth.register.error_message"));
      toast({
        title: t("auth.register.error_title"),
        description: t("auth.register.error_message"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Redirect if already logged in - MOVED AFTER ALL HOOKS
  if (currentUser) {
    return <Navigate to="/" />;
  }

  return (
    <motion.div 
      className="min-h-screen w-full flex items-center justify-center pt-16 md:pt-24 pb-12 md:pb-16 px-4 relative overflow-hidden hardware-accelerated"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Background boxes fill the entire screen, but only render when needed */}
      {shouldRenderBackground && (
        <div className="absolute inset-0 z-0">
          <Suspense fallback={null}>
            <BackgroundBoxesDemo />
          </Suspense>
        </div>
      )}
      
      {/* Language switcher positioned in the top-right corner */}
      <motion.div 
        className="absolute top-4 right-4 md:top-6 md:right-6 z-20"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.5, type: "spring", stiffness: 100 }}
        whileHover={{ y: -2 }}
      >
        <LanguageSwitcher />
      </motion.div>
      
      <div className="w-full max-w-md relative z-10">
        {/* Site logo/name at the top */}
        <motion.div 
          className="text-center mb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h1 className="text-3xl font-bold text-white mb-2">{parseSiteName()}</h1>
          <p className="text-muted-foreground">{t("auth.welcome_title")}</p>
        </motion.div>

        <Tabs
          defaultValue="login"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <TabsList className="grid grid-cols-2 mb-6 md:mb-8">
              <TabsTrigger value="login" className="text-base relative overflow-hidden group">
                {t("auth.login.title")}
                <motion.div 
                  className="absolute bottom-0 left-0 right-0 h-[2px] bg-pubg-orange"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: activeTab === "login" ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              </TabsTrigger>
              <TabsTrigger value="register" className="text-base relative overflow-hidden group">
                {t("auth.register.title")}
                <motion.div 
                  className="absolute bottom-0 left-0 right-0 h-[2px] bg-pubg-orange"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: activeTab === "register" ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              </TabsTrigger>
            </TabsList>
          </motion.div>

          <AnimatePresence mode="wait">
            {activeTab === "login" && (
              <TabsContent value="login" className="mt-0">
                <motion.div
                  key="login-card"
                  variants={cardVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="backdrop-blur-sm"
                >
                  <Card className="glass-card border-border relative">
                    {/* Subtle card glow effect */}
                    <motion.div 
                      className="absolute inset-0 bg-gradient-to-tr from-pubg-orange/5 to-blue-500/5 rounded-xl opacity-0"
                      animate={{ opacity: [0.1, 0.3, 0.1] }}
                      transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                    />
                    
                    <CardHeader className="space-y-2">
                      <motion.div variants={itemVariants}>
                        <CardTitle className="text-xl text-white flex items-center">
                          <LogIn className="mr-2 h-5 w-5 text-pubg-orange" />
                          {t("auth.login.title")}
                        </CardTitle>
                      </motion.div>
                      <motion.div variants={itemVariants}>
                        <CardDescription>
                          {t("auth.login.description", { siteName })}
                        </CardDescription>
                      </motion.div>
                    </CardHeader>
                    
                    <CardContent>
                      <AnimatePresence>
                        {loginError && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Alert variant="destructive" className="mb-4 bg-red-500/20 border-red-600 text-right">
                              <AlertTriangle className="h-4 w-4 ml-2" />
                              <AlertTitle className="text-right">{t("auth.login.error_title")}</AlertTitle>
                              <AlertDescription className="text-right">
                                {loginError}
                              </AlertDescription>
                            </Alert>
                          </motion.div>
                        )}
                      </AnimatePresence>
                      
                      <Form {...loginForm}>
                        <form
                          onSubmit={loginForm.handleSubmit(handleLogin)}
                          className="space-y-4"
                        >
                          <motion.div variants={itemVariants}>
                            <FormField
                              control={loginForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">{t("auth.login.email")}</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "login-email" ? "focusIn" : "focusOut"}
                                      >
                                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                                      <Input
                                        placeholder={t("auth.login.email_placeholder")}
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("login-email")}
                                        onBlur={() => setFocusedField(null)}
                                      />
                                      <motion.span 
                                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-pubg-orange/70 rounded"
                                        initial={{ scaleX: 0, opacity: 0 }}
                                        animate={{ 
                                          scaleX: focusedField === "login-email" ? 1 : 0,
                                          opacity: focusedField === "login-email" ? 1 : 0
                                        }}
                                        transition={{ duration: 0.2 }}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div variants={itemVariants}>
                            <FormField
                              control={loginForm.control}
                              name="password"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">{t("auth.login.password")}</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "login-password" ? "focusIn" : "focusOut"}
                                      >
                                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                                      <Input
                                        type="password"
                                        placeholder={t("auth.login.password_placeholder")}
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("login-password")}
                                        onBlur={() => setFocusedField(null)}
                                      />
                                      <motion.span 
                                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-pubg-orange/70 rounded"
                                        initial={{ scaleX: 0, opacity: 0 }}
                                        animate={{ 
                                          scaleX: focusedField === "login-password" ? 1 : 0,
                                          opacity: focusedField === "login-password" ? 1 : 0
                                        }}
                                        transition={{ duration: 0.2 }}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                          >
                            <Button
                              type="submit"
                              className="w-full bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 relative overflow-hidden group"
                              disabled={isLoading}
                            >
                              <motion.span
                                className="absolute inset-0 bg-gradient-to-r from-pubg-orange to-yellow-500 opacity-0 group-hover:opacity-100"
                                initial={{ x: '-100%' }}
                                whileHover={{ x: '100%' }}
                                transition={{ duration: 0.6 }}
                              />
                              <motion.span className="relative flex items-center justify-center">
                                {isLoading ? (
                                  <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    className="flex items-center"
                                  >
                                    <motion.div 
                                      className="h-5 w-5 mr-2 rounded-full border-2 border-transparent border-t-pubg-dark"
                                      animate={{ rotate: 360 }}
                                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                    />
                                    {t("auth.login.submitting")}
                                  </motion.div>
                                ) : (
                                  <>
                                    <LogIn className="ml-2 h-4 w-4" />
                                    {t("auth.login.submit")}
                                  </>
                                )}
                              </motion.span>
                            </Button>
                          </motion.div>
                        </form>
                      </Form>
                    </CardContent>
                    
                    <CardFooter className="flex justify-center">
                      <motion.div 
                        variants={itemVariants}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Button
                          variant="link"
                          className="text-muted-foreground hover:text-pubg-orange transition-colors duration-300"
                          onClick={() => setActiveTab("register")}
                        >
                          {t("auth.login.no_account")}
                        </Button>
                      </motion.div>
                    </CardFooter>
                  </Card>
                </motion.div>
              </TabsContent>
            )}

            {activeTab === "register" && (
              <TabsContent value="register" className="mt-0">
                <motion.div
                  key="register-card"
                  variants={cardVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="backdrop-blur-sm"
                >
                  <Card className="glass-card border-border relative">
                    {/* Subtle card glow effect */}
                    <motion.div 
                      className="absolute inset-0 bg-gradient-to-tr from-blue-500/5 to-pubg-orange/5 rounded-xl opacity-0"
                      animate={{ opacity: [0.1, 0.3, 0.1] }}
                      transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                    />
                    
                    <CardHeader className="space-y-2">
                      <motion.div variants={itemVariants}>
                        <CardTitle className="text-xl text-white flex items-center">
                          <UserPlus className="mr-2 h-5 w-5 text-pubg-orange" />
                          {t("auth.register.title")}
                        </CardTitle>
                      </motion.div>
                      <motion.div variants={itemVariants}>
                        <CardDescription>
                          {t("auth.register.description", { siteName })}
                        </CardDescription>
                      </motion.div>
                    </CardHeader>
                    
                    <CardContent>
                      <AnimatePresence>
                        {signupError && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Alert variant="destructive" className="mb-4 bg-red-500/20 border-red-600 text-right">
                              <AlertTriangle className="h-4 w-4 ml-2" />
                              <AlertTitle className="text-right">{t("auth.register.error_title")}</AlertTitle>
                              <AlertDescription className="text-right">
                                {signupError}
                              </AlertDescription>
                            </Alert>
                          </motion.div>
                        )}
                      </AnimatePresence>
                      
                      <Form {...registerForm}>
                        <form
                          onSubmit={registerForm.handleSubmit(handleSignup)}
                          className="space-y-4"
                        >
                          <motion.div variants={itemVariants}>
                            <FormField
                              control={registerForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">{t("auth.register.email")}</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "register-email" ? "focusIn" : "focusOut"}
                                      >
                                        <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                                      <Input
                                        placeholder={t("auth.register.email_placeholder")}
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("register-email")}
                                        onBlur={() => setFocusedField(null)}
                                      />
                                      <motion.span 
                                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-pubg-orange/70 rounded"
                                        initial={{ scaleX: 0, opacity: 0 }}
                                        animate={{ 
                                          scaleX: focusedField === "register-email" ? 1 : 0,
                                          opacity: focusedField === "register-email" ? 1 : 0
                                        }}
                                        transition={{ duration: 0.2 }}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div variants={itemVariants}>
                            <FormField
                              control={registerForm.control}
                              name="password"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-base">{t("auth.register.password")}</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <motion.div
                                        variants={inputIconVariants}
                                        animate={focusedField === "register-password" ? "focusIn" : "focusOut"}
                                      >
                                        <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                                      </motion.div>
                                      <Input
                                        type="password"
                                        placeholder={t("auth.register.password_placeholder")}
                                        className="pl-10 transition-all duration-300 border-transparent focus:border-pubg-orange/70 focus:ring-2 focus:ring-pubg-orange/20"
                                        {...field}
                                        onFocus={() => setFocusedField("register-password")}
                                        onBlur={() => setFocusedField(null)}
                                      />
                                      <motion.span 
                                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-pubg-orange/70 rounded"
                                        initial={{ scaleX: 0, opacity: 0 }}
                                        animate={{ 
                                          scaleX: focusedField === "register-password" ? 1 : 0,
                                          opacity: focusedField === "register-password" ? 1 : 0
                                        }}
                                        transition={{ duration: 0.2 }}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>

                          <motion.div
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                          >
                            <Button
                              type="submit"
                              className="w-full bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 relative overflow-hidden group"
                              disabled={isLoading}
                            >
                              <motion.span
                                className="absolute inset-0 bg-gradient-to-r from-pubg-orange to-yellow-500 opacity-0 group-hover:opacity-100"
                                initial={{ x: '-100%' }}
                                whileHover={{ x: '100%' }}
                                transition={{ duration: 0.6 }}
                              />
                              <motion.span className="relative flex items-center justify-center">
                                {isLoading ? (
                                  <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    className="flex items-center"
                                  >
                                    <motion.div 
                                      className="h-5 w-5 mr-2 rounded-full border-2 border-transparent border-t-pubg-dark"
                                      animate={{ rotate: 360 }}
                                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                    />
                                    {t("auth.register.submitting")}
                                  </motion.div>
                                ) : (
                                  <>
                                    <UserPlus className="ml-2 h-4 w-4" />
                                    {t("auth.register.submit")}
                                  </>
                                )}
                              </motion.span>
                            </Button>
                          </motion.div>
                        </form>
                      </Form>
                    </CardContent>
                    
                    <CardFooter className="flex justify-center">
                      <motion.div 
                        variants={itemVariants}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Button
                          variant="link"
                          className="text-muted-foreground hover:text-pubg-orange transition-colors duration-300"
                          onClick={() => setActiveTab("login")}
                        >
                          {t("auth.register.have_account")}
                        </Button>
                      </motion.div>
                    </CardFooter>
                  </Card>
                </motion.div>
              </TabsContent>
            )}
          </AnimatePresence>
        </Tabs>
      </div>
    </motion.div>
  );
};

export default Login;
