"use client"

import { useState } from "react"
import { <PERSON>u, X, ShoppingCart, User } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navItems = [
    { name: "الرئيسية", href: "#" },
    { name: "المتجر", href: "#store" },
    { name: "الدعم", href: "#support" },
    { name: "اتصل بنا", href: "#contact" },
  ]

  return (
    <header className="sticky top-0 z-50 bg-slate-900 shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <h1 className="text-2xl font-bold text-yellow-400">رايه شوب</h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-reverse space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="text-white hover:text-yellow-400 transition-colors duration-200 font-medium"
              >
                {item.name}
              </a>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-reverse space-x-4">
            <Button variant="ghost" size="sm" className="text-white hover:text-yellow-400">
              <User className="h-5 w-5 ml-2" />
              تسجيل الدخول
            </Button>
            <Button variant="ghost" size="sm" className="text-white hover:text-yellow-400">
              <ShoppingCart className="h-5 w-5 ml-2" />
              السلة
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="ghost" size="sm" onClick={() => setIsMenuOpen(!isMenuOpen)} className="text-white">
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-slate-800">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-white hover:text-yellow-400 transition-colors duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <div className="border-t border-slate-700 pt-3 mt-3">
                <a href="#" className="block px-3 py-2 text-white hover:text-yellow-400">
                  تسجيل الدخول
                </a>
                <a href="#" className="block px-3 py-2 text-white hover:text-yellow-400">
                  السلة
                </a>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
