import React, { createContext, useState, useContext, useEffect } from "react";
import { ModModel } from "@/services/firestore";
import { useAuth } from "@/contexts/AuthContext";
import { doc, getDoc, setDoc, arrayUnion, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";

// Define cart item type (extends existing models with quantity)
export interface CartItem {
  id: string;
  title: string;
  priceUSD: number;
  priceEGP: number;
  image: string;
  quantity: number;
  type: 'mod' | 'account' | 'uc';
  originalItem: any; // Original item data
}

// Define the context type
interface CartContextType {
  items: CartItem[];
  addItem: (item: any, type: 'mod' | 'account' | 'uc') => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  itemCount: number;
  totalUSD: number;
  totalEGP: number;
  isLoading: boolean;
}

// Create context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Custom hook to use the cart context
export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
};

// Cart provider component
export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentUser } = useAuth();
  
  // Load cart on initial render and when user changes
  useEffect(() => {
    const loadCart = async () => {
      setIsLoading(true);
      try {
        // If user is logged in, fetch cart from Firestore
        if (currentUser) {
          const cartDocRef = doc(db, "userCarts", currentUser.uid);
          const cartDoc = await getDoc(cartDocRef);
          
          if (cartDoc.exists()) {
            const cartData = cartDoc.data();
            if (cartData.items) {
              setItems(cartData.items);
            } else {
              setItems([]);
            }
          } else {
            // Initialize user cart document if it doesn't exist
            await setDoc(cartDocRef, { items: [] });
            setItems([]);
          }
        } else {
          // If not logged in, fall back to localStorage
          const savedCart = localStorage.getItem('cart');
          if (savedCart) {
            try {
              setItems(JSON.parse(savedCart));
            } catch (e) {
              console.error("Failed to parse cart from localStorage:", e);
              localStorage.removeItem('cart');
              setItems([]);
            }
          } else {
            setItems([]);
          }
        }
      } catch (error) {
        console.error("Failed to load cart:", error);
        setItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadCart();
  }, [currentUser]);
  
  // Save cart whenever it changes
  useEffect(() => {
    if (isLoading) return; // Don't save during initial loading
    
    const saveCart = async () => {
      // Save to Firestore if user is logged in
      if (currentUser) {
        try {
          const cartDocRef = doc(db, "userCarts", currentUser.uid);
          await setDoc(cartDocRef, { items });
        } catch (error) {
          console.error("Failed to save cart to Firestore:", error);
        }
      }
      
      // Always save to localStorage as fallback
      localStorage.setItem('cart', JSON.stringify(items));
    };
    
    saveCart();
  }, [items, currentUser, isLoading]);
  
  // Calculate totals
  const itemCount = items.reduce((total, item) => total + item.quantity, 0);
  const totalUSD = items.reduce((total, item) => total + (item.priceUSD * item.quantity), 0);
  const totalEGP = items.reduce((total, item) => total + (item.priceEGP * item.quantity), 0);
  
  // Add item to cart
  const addItem = (item: any, type: 'mod' | 'account' | 'uc') => {
    setItems(prevItems => {
      // Check if item already exists in cart
      const existingItemIndex = prevItems.findIndex(i => i.id === item.id);
      
      if (existingItemIndex >= 0) {
        // Item exists, increase quantity
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex].quantity += 1;
        return updatedItems;
      } else {
        // New item, add to cart with unique ID if none provided
        const newItem: CartItem = {
          id: item.id || `temp-${Date.now()}`,
          title: item.title || `${item.amount} UC`,
          priceUSD: item.priceUSD,
          priceEGP: item.priceEGP,
          image: item.image,
          quantity: 1,
          type,
          originalItem: item
        };
        return [...prevItems, newItem];
      }
    });
  };
  
  // Remove item from cart
  const removeItem = (itemId: string) => {
    setItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };
  
  // Update item quantity
  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }
    
    setItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };
  
  // Clear cart
  const clearCart = async () => {
    setItems([]);
  };
  
  return (
    <CartContext.Provider value={{
      items,
      addItem,
      removeItem,
      updateQuantity,
      clearCart,
      itemCount,
      totalUSD,
      totalEGP,
      isLoading
    }}>
      {children}
    </CartContext.Provider>
  );
};
