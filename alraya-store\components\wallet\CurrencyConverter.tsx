"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, ArrowUpDown, Info, AlertTriangle } from "lucide-react"
import { 
  Currency, 
  CurrencyDisplay, 
  CurrencyConversion, 
  CurrencyConverterProps,
  WalletBalance
} from "@/lib/types"
import { formatCurrency } from "@/lib/utils/currency"
import { cn } from "@/lib/utils"

interface CurrencyConverterComponentProps {
  availableCurrencies: CurrencyDisplay[]
  walletBalances: WalletBalance[]
  onConvert: (fromCurrency: Cur<PERSON>cy, toCurrency: Currency, amount: number) => Promise<CurrencyConversion | null>
  onPreview: (fromCurrency: Currency, toCurrency: Currency, amount: number) => Promise<CurrencyConversion | null>
  isLoading?: boolean
  className?: string
}

export function CurrencyConverter({
  availableCurrencies,
  walletBalances,
  onConvert,
  onPreview,
  isLoading = false,
  className
}: CurrencyConverterComponentProps) {
  const [fromCurrency, setFromCurrency] = useState<Currency>("")
  const [toCurrency, setToCurrency] = useState<Currency>("")
  const [amount, setAmount] = useState<string>("")
  const [preview, setPreview] = useState<CurrencyConversion | null>(null)
  const [isConverting, setIsConverting] = useState(false)
  const [isLoadingPreview, setIsLoadingPreview] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get available balance for selected currency
  const getAvailableBalance = (currency: Currency): number => {
    const balance = walletBalances.find(b => b.currency === currency)
    return balance?.amount || 0
  }

  // Validate conversion
  const validateConversion = (): { isValid: boolean; error?: string } => {
    if (!fromCurrency) return { isValid: false, error: "Please select source currency" }
    if (!toCurrency) return { isValid: false, error: "Please select target currency" }
    if (fromCurrency === toCurrency) return { isValid: false, error: "Source and target currencies must be different" }
    
    const numAmount = parseFloat(amount)
    if (!amount || isNaN(numAmount) || numAmount <= 0) {
      return { isValid: false, error: "Please enter a valid amount" }
    }

    const availableBalance = getAvailableBalance(fromCurrency)
    if (numAmount > availableBalance) {
      return { isValid: false, error: "Insufficient balance" }
    }

    return { isValid: true }
  }

  // Get conversion preview
  const getConversionPreview = async () => {
    const validation = validateConversion()
    if (!validation.isValid) {
      setError(validation.error || "Invalid conversion")
      setPreview(null)
      return
    }

    setIsLoadingPreview(true)
    setError(null)

    try {
      const previewResult = await onPreview(fromCurrency, toCurrency, parseFloat(amount))
      setPreview(previewResult)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to get conversion preview")
      setPreview(null)
    } finally {
      setIsLoadingPreview(false)
    }
  }

  // Perform conversion
  const handleConvert = async () => {
    const validation = validateConversion()
    if (!validation.isValid) {
      setError(validation.error || "Invalid conversion")
      return
    }

    setIsConverting(true)
    setError(null)

    try {
      const result = await onConvert(fromCurrency, toCurrency, parseFloat(amount))
      if (result) {
        // Reset form on successful conversion
        setAmount("")
        setPreview(null)
        setError(null)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Conversion failed")
    } finally {
      setIsConverting(false)
    }
  }

  // Swap currencies
  const swapCurrencies = () => {
    if (fromCurrency && toCurrency) {
      setFromCurrency(toCurrency)
      setToCurrency(fromCurrency)
      setPreview(null)
    }
  }

  // Auto-preview when inputs change
  useEffect(() => {
    if (fromCurrency && toCurrency && amount && fromCurrency !== toCurrency) {
      const debounceTimer = setTimeout(() => {
        getConversionPreview()
      }, 500)

      return () => clearTimeout(debounceTimer)
    } else {
      setPreview(null)
    }
  }, [fromCurrency, toCurrency, amount])

  return (
    <Card className={cn("bg-slate-800/50 backdrop-blur-xl border-slate-700/50", className)}>
      <CardHeader>
        <CardTitle className="text-xl text-white flex items-center gap-2">
          <ArrowUpDown className="h-5 w-5 text-blue-400" />
          Currency Converter
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* From Currency */}
        <div className="space-y-2">
          <Label className="text-slate-300">From</Label>
          <div className="flex gap-3">
            <Select value={fromCurrency} onValueChange={setFromCurrency}>
              <SelectTrigger className="flex-1 bg-slate-700/50 border-slate-600 text-white">
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                {availableCurrencies.map((currency) => (
                  <SelectItem key={currency.code} value={currency.code}>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{currency.code}</span>
                      <span className="text-slate-400">{currency.symbol}</span>
                      <span className="text-sm text-slate-500">{currency.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="Amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="flex-1 bg-slate-700/50 border-slate-600 text-white"
              min="0"
              step="0.01"
            />
          </div>
          {fromCurrency && (
            <div className="text-sm text-slate-400">
              Available: {formatCurrency(getAvailableBalance(fromCurrency), fromCurrency)}
            </div>
          )}
        </div>

        {/* Swap Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={swapCurrencies}
            disabled={!fromCurrency || !toCurrency}
            className="bg-slate-700/50 border-slate-600 text-white hover:bg-slate-600/50"
          >
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>

        {/* To Currency */}
        <div className="space-y-2">
          <Label className="text-slate-300">To</Label>
          <Select value={toCurrency} onValueChange={setToCurrency}>
            <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              {availableCurrencies
                .filter(currency => currency.code !== fromCurrency)
                .map((currency) => (
                  <SelectItem key={currency.code} value={currency.code}>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{currency.code}</span>
                      <span className="text-slate-400">{currency.symbol}</span>
                      <span className="text-sm text-slate-500">{currency.name}</span>
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>

        {/* Preview */}
        {isLoadingPreview && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-5 w-5 animate-spin text-blue-400" />
            <span className="ml-2 text-slate-400">Getting conversion rate...</span>
          </div>
        )}

        {preview && !isLoadingPreview && (
          <Alert className="bg-blue-900/20 border-blue-700/50">
            <Info className="h-4 w-4 text-blue-400" />
            <AlertDescription className="text-blue-100">
              <div className="space-y-1">
                <div>
                  You will receive: <span className="font-semibold">
                    {formatCurrency(preview.convertedAmount, preview.targetCurrency)}
                  </span>
                </div>
                <div className="text-sm text-blue-200">
                  Exchange rate: 1 {preview.originalCurrency} = {preview.exchangeRate} {preview.targetCurrency}
                </div>
                {preview.conversionFee && preview.conversionFee > 0 && (
                  <div className="text-sm text-blue-200">
                    Conversion fee: {formatCurrency(preview.conversionFee, preview.targetCurrency)}
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Error */}
        {error && (
          <Alert className="bg-red-900/20 border-red-700/50">
            <AlertTriangle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-100">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Convert Button */}
        <Button
          onClick={handleConvert}
          disabled={!validateConversion().isValid || isConverting || isLoading}
          className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 disabled:opacity-50"
        >
          {isConverting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Converting...
            </>
          ) : (
            "Convert Currency"
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
