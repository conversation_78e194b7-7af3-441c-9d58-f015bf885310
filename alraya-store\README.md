# 🏪 Alraya Store - Dynamic Product CMS System

A powerful, WordPress-like CMS system for creating and managing dynamic product pages with complete customization control.

## 🚀 **Live Demo**
- **Admin Panel**: `/admin` - Complete CMS interface
- **Product Management**: Create, edit, and customize products
- **Mobile Responsive**: Perfect on all devices

## ✨ **Features**

### 🎯 **Dynamic Product Builder**
- **13 Field Types**: Text, email, number, textarea, select, radio, checkbox, image, package selector, quantity selector, price display, heading, divider
- **Drag & Drop**: Reorder fields with intuitive interface
- **Real-time Preview**: See changes instantly
- **Mobile Responsive**: Perfect on all screen sizes

### 🔧 **Advanced Configuration**
- **Field Validation**: Custom rules, patterns, required fields
- **Options Management**: Pricing per option, package deals
- **Image Upload**: Drag-and-drop with validation
- **Interactive Forms**: Live validation and error handling

### 📱 **Mobile-First Design**
- **Touch-Friendly**: Optimized for mobile administration
- **Responsive Modals**: Perfect sizing on all devices
- **Arabic RTL Support**: Full right-to-left layout

## 🛠️ **Tech Stack**

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS + Radix UI
- **Language**: TypeScript
- **Icons**: Lucide React
- **Deployment**: Vercel Ready

## 📦 **Installation**

```bash
# Clone the repository
git clone <your-repo-url>
cd alraya-store

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build
```

## 🚀 **Deploy to Vercel**

### **Option 1: GitHub Integration**

1. **Push to GitHub**:
```bash
git add .
git commit -m "Ready for deployment"
git push origin main
```

2. **Deploy to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Click "Deploy"

3. **Configure Domain** (Optional):
   - Go to Project Settings → Domains
   - Add your custom domain

### **Option 2: Vercel CLI**

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Follow the prompts to configure your deployment
```

## 🎮 **Usage Guide**

### **Creating Product Templates**

1. **Navigate to Admin**: Go to `/admin` → Products tab
2. **Create Template**: Click "إنشاء قالب جديد"
3. **Basic Info**: Add name, description, category
4. **Add Fields**: Use the + button to add different field types
5. **Configure Fields**: Click edit icon to customize each field
6. **Preview**: Toggle interactive mode to test validation
7. **Save**: Your template is ready to use!

---

**Built with ❤️ for the Arabic gaming community**
