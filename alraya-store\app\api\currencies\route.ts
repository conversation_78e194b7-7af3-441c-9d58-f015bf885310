import { NextRequest, NextResponse } from 'next/server'
import { Cur<PERSON>cy, CurrencyInfo, CurrencyManagementRequest, CurrencyManagementResponse } from '@/lib/types'

// Mock currencies data
const mockCurrencies: CurrencyInfo[] = [
  {
    id: '1',
    code: 'USD',
    name: 'الدولار الأمريكي',
    symbol: '$',
    decimalPlaces: 2,
    isRTL: false,
    isActive: true,
    sortOrder: 1,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '2',
    code: 'SDG',
    name: 'الجنيه السوداني',
    symbol: 'ج.س',
    decimalPlaces: 2,
    isRTL: true,
    isActive: true,
    sortOrder: 2,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '3',
    code: 'EGP',
    name: 'الجنيه المصري',
    symbol: 'ج.م',
    decimalPlaces: 2,
    isRTL: true,
    isActive: true,
    sortOrder: 3,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

/**
 * GET /api/currencies
 * Fetch all available currencies
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const activeOnly = searchParams.get('active') === 'true'
    const enabledOnly = searchParams.get('enabled') === 'true'

    // Use mock data instead of Supabase
    let currencies = [...mockCurrencies]

    if (activeOnly) {
      currencies = currencies.filter(c => c.isActive)
    }

    // If enabled only, filter by client settings (mock)
    let filteredCurrencies = currencies
    if (enabledOnly) {
      // Mock enabled currencies - in real implementation this would come from client_currency_settings
      const enabledCurrencyCodes = ['USD', 'SDG', 'EGP']
        .select('enabled_currencies')
        .is('client_id', null)
        .single()

      if (clientSettings?.enabled_currencies) {
        filteredCurrencies = currencies.filter(currency => 
          clientSettings.enabled_currencies.includes(currency.code)
        )
      }
    }

    // Transform to match CurrencyInfo interface
    const transformedCurrencies = filteredCurrencies.map(currency => ({
      id: currency.id,
      code: currency.code,
      name: currency.name,
      symbol: currency.symbol,
      arabicName: currency.arabic_name,
      decimalPlaces: currency.decimal_places,
      isRTL: currency.is_rtl,
      isActive: currency.is_active,
      sortOrder: currency.sort_order,
      createdAt: new Date(currency.created_at),
      updatedAt: new Date(currency.updated_at)
    }))

    return NextResponse.json({
      success: true,
      currencies: transformedCurrencies
    })

  } catch (error) {
    console.error('Unexpected error in GET /api/currencies:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/currencies
 * Create or update currency
 */
export async function POST(request: NextRequest) {
  try {
    const body: CurrencyManagementRequest = await request.json()

    // Validate request
    if (!body.action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    let response: CurrencyManagementResponse

    switch (body.action) {
      case 'create':
        response = await createCurrency(body.currency!)
        break
      case 'update':
        response = await updateCurrency(body.currency!)
        break
      case 'delete':
        response = await deleteCurrency(body.currencyCode!)
        break
      case 'activate':
        response = await toggleCurrencyStatus(body.currencyCode!, true)
        break
      case 'deactivate':
        response = await toggleCurrencyStatus(body.currencyCode!, false)
        break
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json(response, {
      status: response.success ? 200 : 400
    })

  } catch (error) {
    console.error('Unexpected error in POST /api/currencies:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper functions

async function createCurrency(currencyData: Partial<CurrencyInfo>): Promise<CurrencyManagementResponse> {
  try {
    // Validate required fields
    if (!currencyData.code || !currencyData.name || !currencyData.symbol) {
      return {
        success: false,
        message: 'Code, name, and symbol are required',
        error: 'Missing required fields'
      }
    }

    // Check if currency already exists
    const { data: existing } = await supabase
      .from('currencies')
      .select('id')
      .eq('code', currencyData.code)
      .single()

    if (existing) {
      return {
        success: false,
        message: 'Currency already exists',
        error: 'Duplicate currency code'
      }
    }

    // Insert new currency
    const { data, error } = await supabase
      .from('currencies')
      .insert({
        code: currencyData.code,
        name: currencyData.name,
        symbol: currencyData.symbol,
        arabic_name: currencyData.arabicName,
        decimal_places: currencyData.decimalPlaces || 2,
        is_rtl: currencyData.isRTL || false,
        is_active: currencyData.isActive !== false,
        sort_order: currencyData.sortOrder || 0
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating currency:', error)
      return {
        success: false,
        message: 'Failed to create currency',
        error: error.message
      }
    }

    return {
      success: true,
      message: 'Currency created successfully',
      currency: {
        id: data.id,
        code: data.code,
        name: data.name,
        symbol: data.symbol,
        arabicName: data.arabic_name,
        decimalPlaces: data.decimal_places,
        isRTL: data.is_rtl,
        isActive: data.is_active,
        sortOrder: data.sort_order,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      }
    }

  } catch (error) {
    console.error('Error in createCurrency:', error)
    return {
      success: false,
      message: 'Failed to create currency',
      error: 'Internal server error'
    }
  }
}

async function updateCurrency(currencyData: Partial<CurrencyInfo>): Promise<CurrencyManagementResponse> {
  try {
    if (!currencyData.id && !currencyData.code) {
      return {
        success: false,
        message: 'Currency ID or code is required for update',
        error: 'Missing identifier'
      }
    }

    const updateData: any = {}
    if (currencyData.name) updateData.name = currencyData.name
    if (currencyData.symbol) updateData.symbol = currencyData.symbol
    if (currencyData.arabicName) updateData.arabic_name = currencyData.arabicName
    if (currencyData.decimalPlaces !== undefined) updateData.decimal_places = currencyData.decimalPlaces
    if (currencyData.isRTL !== undefined) updateData.is_rtl = currencyData.isRTL
    if (currencyData.isActive !== undefined) updateData.is_active = currencyData.isActive
    if (currencyData.sortOrder !== undefined) updateData.sort_order = currencyData.sortOrder

    let query = supabase.from('currencies').update(updateData)

    if (currencyData.id) {
      query = query.eq('id', currencyData.id)
    } else {
      query = query.eq('code', currencyData.code)
    }

    const { data, error } = await query.select().single()

    if (error) {
      console.error('Error updating currency:', error)
      return {
        success: false,
        message: 'Failed to update currency',
        error: error.message
      }
    }

    return {
      success: true,
      message: 'Currency updated successfully',
      currency: {
        id: data.id,
        code: data.code,
        name: data.name,
        symbol: data.symbol,
        arabicName: data.arabic_name,
        decimalPlaces: data.decimal_places,
        isRTL: data.is_rtl,
        isActive: data.is_active,
        sortOrder: data.sort_order,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      }
    }

  } catch (error) {
    console.error('Error in updateCurrency:', error)
    return {
      success: false,
      message: 'Failed to update currency',
      error: 'Internal server error'
    }
  }
}

async function deleteCurrency(currencyCode: Currency): Promise<CurrencyManagementResponse> {
  try {
    // Check if currency is in use
    const { data: walletsInUse } = await supabase
      .from('user_wallets')
      .select('id')
      .eq('currency_code', currencyCode)
      .limit(1)

    if (walletsInUse && walletsInUse.length > 0) {
      return {
        success: false,
        message: 'Cannot delete currency that is in use',
        error: 'Currency in use'
      }
    }

    const { error } = await supabase
      .from('currencies')
      .delete()
      .eq('code', currencyCode)

    if (error) {
      console.error('Error deleting currency:', error)
      return {
        success: false,
        message: 'Failed to delete currency',
        error: error.message
      }
    }

    return {
      success: true,
      message: 'Currency deleted successfully'
    }

  } catch (error) {
    console.error('Error in deleteCurrency:', error)
    return {
      success: false,
      message: 'Failed to delete currency',
      error: 'Internal server error'
    }
  }
}

async function toggleCurrencyStatus(currencyCode: Currency, isActive: boolean): Promise<CurrencyManagementResponse> {
  try {
    const { data, error } = await supabase
      .from('currencies')
      .update({ is_active: isActive })
      .eq('code', currencyCode)
      .select()
      .single()

    if (error) {
      console.error('Error toggling currency status:', error)
      return {
        success: false,
        message: 'Failed to update currency status',
        error: error.message
      }
    }

    return {
      success: true,
      message: `Currency ${isActive ? 'activated' : 'deactivated'} successfully`,
      currency: {
        id: data.id,
        code: data.code,
        name: data.name,
        symbol: data.symbol,
        arabicName: data.arabic_name,
        decimalPlaces: data.decimal_places,
        isRTL: data.is_rtl,
        isActive: data.is_active,
        sortOrder: data.sort_order,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      }
    }

  } catch (error) {
    console.error('Error in toggleCurrencyStatus:', error)
    return {
      success: false,
      message: 'Failed to update currency status',
      error: 'Internal server error'
    }
  }
}
