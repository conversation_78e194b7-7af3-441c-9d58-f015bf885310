import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Plus, Pencil, Trash2, Save, X, Sparkles, Upload, RefreshCw, Globe, ChevronDown, ShoppingBag, Check } from "lucide-react";
import { AccountModel, getAccounts, addAccount, updateAccount, deleteAccount, getCategories, CategoryModel, addCategory, deleteCategory, updateCategory } from "@/services/firestore";
import { parseAccountDetails, getSavedModel } from "@/services/gemini";
import { uploadImage } from "@/services/imgbb";
import { convertUSDtoLocal, convertLocalToUSD } from "@/services/currency";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ModelSelector from "./ModelSelector";
import ImageUploader from "./ImageUploader";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/lib/theme";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

interface FormDataType {
  title: string;
  title_en: string;
  description: string;
  description_en: string;
  priceUSD: string | number;
  priceEGP: string | number;
  image: string;
  category: string;
  featured: boolean;
}

const AccountsManager = () => {
  const { toast } = useToast();
  const [accounts, setAccounts] = useState<AccountModel[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingAccount, setEditingAccount] = useState<AccountModel | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [telegramPost, setTelegramPost] = useState("");
  const [isAiProcessing, setIsAiProcessing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [localCurrencyCode, setLocalCurrencyCode] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState(getSavedModel());
  const [newCategory, setNewCategory] = useState("");
  const [newCategoryEn, setNewCategoryEn] = useState("");
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [isDeletingCategory, setIsDeletingCategory] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null);
  const [isCategoryManagerOpen, setIsCategoryManagerOpen] = useState(false);
  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(null);
  const [editCategoryName, setEditCategoryName] = useState("");
  const [editCategoryNameEn, setEditCategoryNameEn] = useState("");
  const [isUpdatingCategory, setIsUpdatingCategory] = useState(false);
  const [formData, setFormData] = useState<FormDataType>({
    title: "",
    title_en: "",
    description: "",
    description_en: "",
    priceUSD: "",
    priceEGP: "",
    image: "",
    category: "",
    featured: false,
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [accountsData, categoriesData] = await Promise.all([
          getAccounts(),
          getCategories(),
        ]);
        setAccounts(accountsData);
        setCategories(categoriesData.filter(cat => cat.type === "account"));
        setIsLoading(false);
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في تحميل البيانات",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData({ ...formData, [name]: checked });
    } else if (name === "localCurrencyCode") {
      setLocalCurrencyCode(value);
    } else if (name === "priceUSD") {
      setFormData({ 
        ...formData, 
        priceUSD: value === "" ? "" : Number(value)
      });
    } else if (name === "priceEGP") {
      setFormData({ 
        ...formData, 
        priceEGP: value === "" ? "" : Number(value)
      });
    } else if (name === "newCategory") {
      setNewCategory(value);
    } else if (name === "newCategoryEn") {
      setNewCategoryEn(value);
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleTelegramPostChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTelegramPost(e.target.value);
  };

  const handleProcessWithAI = async () => {
    if (!telegramPost.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى لصق محتوى منشور تيليجرام أولاً",
        variant: "destructive",
      });
      return;
    }

    setIsAiProcessing(true);
    try {
      const parsedData = await parseAccountDetails(telegramPost, selectedModel);
      
      // Try to find a matching category from the existing categories
      let categoryId = "";
      if (parsedData.category) {
        // Search for a category that includes the parsed category text in its name
        const foundCategory = categories.find(cat => 
          cat.name.toLowerCase().includes(parsedData.category.toLowerCase()) || 
          (cat.name_en && cat.name_en.toLowerCase().includes(parsedData.category.toLowerCase()))
        );
        
        // If found, use it. Otherwise use the first category if available
        categoryId = foundCategory ? foundCategory.id || "" : (categories.length > 0 ? categories[0].id || "" : "");
      } else {
        // Fallback to first category if available
        categoryId = categories.length > 0 ? categories[0].id || "" : "";
      }
      
      // Try to extract currency code if present
      let currencyCode = localCurrencyCode;
      if (parsedData.priceEGP && !currencyCode) {
        // Default currency codes based on common usage in the app
        if (parsedData.priceEGP >= 10000) {
          currencyCode = "EGP"; // Egyptian Pound for large values
        } else if (parsedData.priceEGP <= 100) {
          currencyCode = "USD"; // Maybe it's actually in USD
        }
      }
      
      // Check if an image URL was found
      const imageDetected = parsedData.image && parsedData.image.trim() !== "";
      
      // Update form with parsed data
      setFormData({
        title: parsedData.title || "",
        title_en: parsedData.title_en || "",
        description: parsedData.description || "",
        description_en: parsedData.description_en || "",
        priceUSD: parsedData.priceUSD || "",
        priceEGP: parsedData.priceEGP || "",
        image: parsedData.image || "",
        category: categoryId,
        featured: parsedData.featured || false,
      });
      
      // Also update the currency code if detected
      if (currencyCode) {
        setLocalCurrencyCode(currencyCode);
      }
      
      // Show an appropriate success message
      if (imageDetected) {
        toast({
          title: "تم بنجاح",
          description: "تم معالجة المنشور وملء النموذج بالبيانات وتم العثور على صورة",
          duration: 3000,
        });
      } else {
        toast({
          title: "تم بنجاح",
          description: "تم معالجة المنشور وملء النموذج بالبيانات",
          duration: 2000,
        });
      }
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في المعالجة بالذكاء الاصطناعي: " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setIsAiProcessing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Convert form data to AccountModel format
      const dataToSave: Omit<AccountModel, 'id' | 'createdAt' | 'updatedAt'> = {
        title: formData.title,
        title_en: formData.title_en,
        description: formData.description,
        description_en: formData.description_en,
        priceUSD: typeof formData.priceUSD === 'string' ? Number(formData.priceUSD) || 0 : formData.priceUSD,
        priceEGP: typeof formData.priceEGP === 'string' ? Number(formData.priceEGP) || 0 : formData.priceEGP,
        localCurrencyCode: localCurrencyCode,
        image: formData.image,
        category: formData.category,
        featured: formData.featured
      };

      if (editingAccount) {
        // Update existing account
        await updateAccount(editingAccount.id!, dataToSave);
        
        // Update local state
        setAccounts(
          accounts.map((account) =>
            account.id === editingAccount.id
              ? { ...account, ...dataToSave }
              : account
          )
        );
        
        toast({
          title: "تم بنجاح",
          description: "تم تحديث الحساب بنجاح",
          duration: 2000,
        });
        
        setEditingAccount(null);
      } else {
        // Add new account
        const id = await addAccount(dataToSave);
        
        // Update local state
        setAccounts([...accounts, { ...dataToSave, id } as AccountModel]);
        
        toast({
          title: "تم بنجاح",
          description: "تم إضافة الحساب بنجاح",
          duration: 2000,
        });
        
        setIsAdding(false);
      }
      
      // Reset form
      setFormData({
        title: "",
        title_en: "",
        description: "",
        description_en: "",
        priceUSD: "",
        priceEGP: "",
        image: "",
        category: "",
        featured: false,
      });
      setTelegramPost("");
      setDialogOpen(false);
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حفظ الحساب",
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  const handleEdit = (account: AccountModel) => {
    setEditingAccount(account);
    setFormData({
      title: account.title,
      title_en: account.title_en || "",
      description: account.description,
      description_en: account.description_en || "",
      priceUSD: account.priceUSD,
      priceEGP: account.priceEGP,
      image: account.image,
      category: account.category,
      featured: account.featured || false,
    });
    // Load currency code if available
    if (account.localCurrencyCode) {
      setLocalCurrencyCode(account.localCurrencyCode);
    } else {
      setLocalCurrencyCode("");
    }
    
    setIsAdding(false);
    setTelegramPost("");
    setDialogOpen(true);
  };

  const handleAdd = () => {
    setIsAdding(true);
    setEditingAccount(null);
    setFormData({
      title: "",
      title_en: "",
      description: "",
      description_en: "",
      priceUSD: "",
      priceEGP: "",
      image: "",
      category: "",
      featured: false,
    });
    setLocalCurrencyCode("");
    setTelegramPost("");
    setDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("هل أنت متأكد من حذف هذا الحساب؟")) {
      setIsLoading(true);
      
      try {
        await deleteAccount(id);
        
        // Update local state
        setAccounts(accounts.filter((account) => account.id !== id));
        
        toast({
          title: "تم بنجاح",
          description: "تم حذف الحساب بنجاح",
          duration: 2000,
        });
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في حذف الحساب",
          variant: "destructive",
        });
      }
      
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setEditingAccount(null);
    setIsAdding(false);
    setDialogOpen(false);
    setFormData({
      title: "",
      title_en: "",
      description: "",
      description_en: "",
      priceUSD: "",
      priceEGP: "",
      image: "",
      category: "",
      featured: false,
    });
    setLocalCurrencyCode("");
    setTelegramPost("");
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    const file = e.target.files[0];
    setIsUploading(true);
    
    try {
      const imageUrl = await uploadImage(file);
      setFormData(prev => ({ ...prev, image: imageUrl }));
      
      toast({
        title: "تم بنجاح",
        description: "تم رفع الصورة بنجاح",
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في رفع الصورة",
        variant: "destructive",
      });
    }
    
    setIsUploading(false);
  };

  // Add this new function to handle category deletion
  const handleDeleteCategory = async (categoryId: string) => {
    if (!categoryId) return;
    
    setCategoryToDelete(categoryId);
    setIsDeletingCategory(true);
    
    try {
      // Check if any account is using this category
      const categoryInUse = accounts.some(account => account.category === categoryId);
      
      if (categoryInUse) {
        toast({
          title: "تعذر الحذف",
          description: "لا يمكن حذف التصنيف لأنه مستخدم في حسابات موجودة",
          variant: "destructive",
        });
        return;
      }
      
      // Delete the category from Firestore
      await deleteCategory(categoryId);
      
      // Update local state
      setCategories(categories.filter(cat => cat.id !== categoryId));
      
      toast({
        title: "تم بنجاح",
        description: "تم حذف التصنيف",
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حذف التصنيف",
        variant: "destructive",
      });
    } finally {
      setIsDeletingCategory(false);
      setCategoryToDelete(null);
    }
  };

  // Add new function for dedicated category management
  const handleAddCategoryStandalone = async () => {
    if (!newCategory.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم التصنيف بالعربية",
        variant: "destructive",
      });
      return;
    }

    setIsAddingCategory(true);
    
    try {
      // Create a new category
      const categoryData = {
        name: newCategory.trim(),
        name_en: newCategoryEn.trim() || newCategory.trim(), // Use Arabic name as fallback if English is empty
        type: "account" as "account" | "blog" | "uc"
      };

      const categoryId = await addCategory(categoryData);
      
      // Add to local state
      const newCategoryObject: CategoryModel = {
        ...categoryData,
        id: categoryId
      };
      
      setCategories([...categories, newCategoryObject]);
      
      // Reset form
      setNewCategory("");
      setNewCategoryEn("");
      
      toast({
        title: "تم بنجاح",
        description: "تم إضافة التصنيف الجديد",
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إضافة التصنيف الجديد",
        variant: "destructive",
      });
    } finally {
      setIsAddingCategory(false);
    }
  };

  // Add function to handle starting category edit mode
  const handleEditCategoryStart = (category: CategoryModel) => {
    setEditingCategoryId(category.id || null);
    setEditCategoryName(category.name);
    setEditCategoryNameEn(category.name_en || "");
  };

  // Add function to cancel category editing
  const handleEditCategoryCancel = () => {
    setEditingCategoryId(null);
    setEditCategoryName("");
    setEditCategoryNameEn("");
  };

  // Add function to save category edits
  const handleUpdateCategory = async () => {
    if (!editingCategoryId || !editCategoryName.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم التصنيف بالعربية",
        variant: "destructive",
      });
      return;
    }

    setIsUpdatingCategory(true);
    
    try {
      // Prepare the update data
      const categoryData: Partial<CategoryModel> = {
        name: editCategoryName.trim(),
        name_en: editCategoryNameEn.trim() || undefined, // Don't send empty string
      };

      // Update in Firestore
      await updateCategory(editingCategoryId, categoryData);
      
      // Update in local state
      setCategories(categories.map(cat => 
        cat.id === editingCategoryId 
          ? { ...cat, ...categoryData } 
          : cat
      ));
      
      // Reset form
      setEditingCategoryId(null);
      setEditCategoryName("");
      setEditCategoryNameEn("");
      
      toast({
        title: "تم بنجاح",
        description: "تم تحديث التصنيف",
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث التصنيف",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingCategory(false);
    }
  };

  // Accounts display section
  const renderAccounts = () => {
    if (accounts.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-10 bg-pubg-black/20 rounded-lg border border-pubg-gray/20">
          <div className="mb-3 text-muted-foreground">
            <ShoppingBag className="w-12 h-12 opacity-30" />
          </div>
          <h3 className="text-lg font-medium text-muted-foreground">لا توجد حسابات</h3>
          <p className="text-sm text-muted-foreground mt-1">انقر على زر "إضافة حساب جديد" لإضافة حساب</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {accounts.map((account) => (
          <div
            key={account.id}
            className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-pubg-black to-pubg-gray/10 border border-pubg-gray/20 hover:border-pubg-orange/40 transition-all duration-300 shadow-md hover:shadow-pubg-orange/10"
          >
            <div className="relative aspect-video overflow-hidden">
              <img
                src={account.image}
                alt={account.title}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = "/images/placeholder.jpg";
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-pubg-black/90 via-pubg-black/50 to-transparent opacity-70"></div>
              
              {account.featured && (
                <div className="absolute top-2 right-2 bg-pubg-orange/90 text-white text-xs py-1 px-2 rounded shadow-lg">
                  <span>مميز</span>
                </div>
              )}
              
              <div className="absolute bottom-0 left-0 right-0 px-4 py-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-bold text-white text-lg leading-tight line-clamp-1">{account.title}</h3>
                    {account.title_en && (
                      <p className="text-xs text-gray-300 line-clamp-1" dir="ltr">{account.title_en}</p>
                    )}
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="text-lg font-bold text-pubg-orange">${account.priceUSD}</div>
                    <div className="text-sm text-gray-300">
                      {account.priceEGP} {account.localCurrencyCode || ""}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="px-4 py-3">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <span className="inline-block w-2 h-2 rounded-full bg-pubg-orange mr-2"></span>
                  <span className="text-sm text-gray-300">التصنيف: {
                    categories.find(cat => cat.id === account.category)?.name || "غير مصنف"
                  }</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-400">
                    {new Date(account.createdAt || Date.now()).toLocaleDateString("ar-SA")}
                  </span>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => handleEdit(account)}
                  variant="ghost"
                  size="sm"
                  className="flex-1 border border-pubg-gray/30 hover:border-pubg-orange hover:bg-pubg-orange/10"
                >
                  <Pencil className="mr-1 h-4 w-4" />
                  تعديل
                </Button>
                <Button
                  onClick={() => handleDelete(account.id!)}
                  variant="ghost"
                  size="sm"
                  className="flex-1 border border-pubg-gray/30 hover:border-red-500/70 hover:bg-red-500/10 hover:text-red-500"
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  حذف
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (isLoading && accounts.length === 0) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-pubg-orange"></div>
        <span className="mr-2 text-sm md:text-base">جاري تحميل الحسابات...</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 md:mb-6 gap-2">
        <h2 className="admin-title">إدارة الحسابات</h2>
        <Button
          onClick={handleAdd}
          variant="orange"
          className={cn("w-full sm:w-auto admin-button")}
          size="sm"
        >
          <Plus className="ml-2 h-4 w-4" />
          إضافة حساب جديد
        </Button>
      </div>

      {/* Category Management Section */}
      <div className="mb-6 border border-pubg-gray/20 rounded-lg overflow-hidden bg-pubg-black/20">
        <div 
          className="flex items-center justify-between p-3 cursor-pointer bg-gradient-to-r from-pubg-gray/10 to-pubg-black hover:from-pubg-gray/20"
          onClick={() => setIsCategoryManagerOpen(!isCategoryManagerOpen)}
        >
          <div className="flex items-center">
            <div className="w-1 h-6 bg-pubg-orange ml-3 rounded-full"></div>
            <h3 className="text-lg font-medium">إدارة التصنيفات</h3>
          </div>
          <ChevronDown className={`h-5 w-5 transition-transform duration-200 ${isCategoryManagerOpen ? 'rotate-180' : ''}`} />
        </div>
        
        {isCategoryManagerOpen && (
          <div className="p-4 border-t border-pubg-gray/20">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Add New Category Section */}
              <div className="border border-pubg-gray/20 rounded-lg p-4 bg-pubg-black/30">
                <h4 className="text-sm font-medium mb-3 flex items-center">
                  <Plus className="ml-2 h-4 w-4 text-pubg-orange" />
                  إضافة تصنيف جديد
                </h4>
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="newCategoryStandalone" className="text-sm">اسم التصنيف (بالعربية)</Label>
                    <Input
                      id="newCategoryStandalone"
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      placeholder="أدخل اسم التصنيف بالعربية"
                      className="admin-input"
                      dir="rtl"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newCategoryEnStandalone" className="text-sm">اسم التصنيف (بالإنجليزية)</Label>
                    <Input
                      id="newCategoryEnStandalone"
                      value={newCategoryEn}
                      onChange={(e) => setNewCategoryEn(e.target.value)}
                      placeholder="Enter category name in English"
                      className="admin-input"
                      dir="ltr"
                    />
                  </div>
                  <Button
                    type="button"
                    onClick={handleAddCategoryStandalone}
                    disabled={isAddingCategory || !newCategory.trim()}
                    variant="outline"
                    className="w-full mt-2 border-pubg-gray/30 hover:border-pubg-orange hover:bg-pubg-orange/10"
                  >
                    {isAddingCategory ? (
                      <>
                        <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
                        جاري الإضافة...
                      </>
                    ) : (
                      <>
                        <Plus className="ml-2 h-4 w-4" />
                        إضافة تصنيف
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Categories List with Edit Functionality */}
              <div className="border border-pubg-gray/20 rounded-lg p-4 bg-pubg-black/30">
                <h4 className="text-sm font-medium mb-3 flex items-center">
                  <Pencil className="ml-2 h-4 w-4 text-pubg-orange" />
                  التصنيفات الحالية
                </h4>
                
                {categories.length === 0 ? (
                  <div className="text-center py-6 text-muted-foreground">
                    <div className="opacity-30 mb-2">
                      <ShoppingBag className="w-8 h-8 mx-auto" />
                    </div>
                    <p>لا توجد تصنيفات حالياً</p>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-[250px] overflow-y-auto pr-1">
                    {categories.map((category) => (
                      <div 
                        key={category.id}
                        className="bg-pubg-gray/5 hover:bg-pubg-gray/10 rounded-md border border-pubg-gray/10"
                      >
                        {editingCategoryId === category.id ? (
                          // Edit Mode
                          <div className="p-2 space-y-2">
                            <Input
                              value={editCategoryName}
                              onChange={(e) => setEditCategoryName(e.target.value)}
                              placeholder="اسم التصنيف بالعربية"
                              className="admin-input mb-1"
                              dir="rtl"
                            />
                            <Input
                              value={editCategoryNameEn}
                              onChange={(e) => setEditCategoryNameEn(e.target.value)}
                              placeholder="Category name in English"
                              className="admin-input mb-1"
                              dir="ltr"
                            />
                            <div className="flex gap-2 pt-1">
                              <Button 
                                type="button" 
                                size="sm"
                                variant="ghost"
                                className="flex-1 border border-green-500/30 hover:border-green-500 hover:bg-green-500/10 text-green-500"
                                onClick={handleUpdateCategory}
                                disabled={isUpdatingCategory}
                              >
                                {isUpdatingCategory ? (
                                  <RefreshCw className="h-3 w-3 animate-spin ml-1" />
                                ) : (
                                  <Check className="h-3 w-3 ml-1" />
                                )}
                                حفظ
                              </Button>
                              <Button 
                                type="button" 
                                size="sm"
                                variant="ghost"
                                className="flex-1 border border-gray-500/30 hover:border-gray-500 hover:bg-gray-500/10 text-gray-500"
                                onClick={handleEditCategoryCancel}
                                disabled={isUpdatingCategory}
                              >
                                <X className="h-3 w-3 ml-1" />
                                إلغاء
                              </Button>
                            </div>
                          </div>
                        ) : (
                          // View Mode
                          <div className="flex items-center justify-between p-2">
                            <div className="flex-1 overflow-hidden">
                              <div className="font-medium truncate">{category.name}</div>
                              {category.name_en && (
                                <div className="text-xs text-muted-foreground truncate" dir="ltr">{category.name_en}</div>
                              )}
                            </div>
                            
                            <div className="flex items-center gap-1 ml-2">
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7 rounded-full hover:bg-blue-500/10 hover:text-blue-500"
                                onClick={() => handleEditCategoryStart(category)}
                              >
                                <Pencil className="h-3 w-3" />
                                <span className="sr-only">تعديل التصنيف</span>
                              </Button>
                              
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7 rounded-full hover:bg-red-500/10 hover:text-red-500"
                                onClick={() => handleDeleteCategory(category.id || "")}
                                disabled={isDeletingCategory && categoryToDelete === category.id}
                              >
                                {isDeletingCategory && categoryToDelete === category.id ? (
                                  <RefreshCw className="h-3 w-3 animate-spin" />
                                ) : (
                                  <Trash2 className="h-3 w-3" />
                                )}
                                <span className="sr-only">حذف التصنيف</span>
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Accounts List Section */}
      <div className="mb-6">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-pubg-orange"></div>
            <span className="mr-2 text-sm md:text-base">جاري تحميل الحسابات...</span>
          </div>
        ) : (
          renderAccounts()
        )}
      </div>

      {/* Account Edit Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto glass-card p-0 border-none shadow-xl shadow-pubg-gray/20 [&>button]:hidden">
          <DialogHeader className="bg-gradient-to-r from-pubg-gray to-pubg-black border-b border-pubg-orange/20 p-4 md:p-6 rounded-t-lg sticky top-0 z-10 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-1 h-8 bg-pubg-orange ml-3 rounded-full"></div>
                <DialogTitle className="text-white text-xl font-bold">
                  {editingAccount ? "تعديل الحساب" : "إضافة حساب جديد"}
                </DialogTitle>
              </div>
              <div className="relative sm:static">
                <div className="absolute -top-1 -right-1 w-16 h-16 sm:w-16 sm:h-16 animate-pulse-slow rounded-full bg-pubg-orange/10 -z-10"></div>
                <DialogClose className="rounded-full h-10 w-10 md:h-11 md:w-11 flex items-center justify-center bg-pubg-orange hover:bg-pubg-orange/80 border-2 border-pubg-orange hover:border-white transition-all focus:outline-none focus-visible:ring-2 focus-visible:ring-pubg-orange focus-visible:ring-offset-2 focus-visible:ring-offset-pubg-dark transform hover:scale-105 shadow-md z-20">
                  <X className="h-5 w-5 md:h-6 md:w-6 text-white" />
                  <span className="sr-only">إغلاق</span>
                </DialogClose>
                <span className="absolute -bottom-6 -right-1 text-xs text-white/70 hidden sm:block">إغلاق</span>
              </div>
            </div>
            <DialogDescription className="text-gray-200 text-sm pt-1 pr-4">
              {editingAccount ? "قم بتعديل بيانات الحساب أدناه" : "قم بإدخال بيانات الحساب الجديد"}
            </DialogDescription>
          </DialogHeader>
          
          <div className="p-4 md:p-6 space-y-6">
            <form onSubmit={handleSubmit}>
              {/* AI account details processing section */}
              <div className="mb-6 border border-pubg-gray/30 rounded-lg p-4 bg-pubg-gray/5 transition-all hover:bg-pubg-gray/10">
                <h4 className="text-md font-medium mb-3 text-white flex items-center">
                  <Sparkles className="ml-2 h-5 w-5 text-pubg-orange" />
                  استخراج البيانات بالذكاء الاصطناعي
                </h4>
                <div className="space-y-3">
                  <Textarea
                    value={telegramPost}
                    onChange={handleTelegramPostChange}
                    placeholder="انسخ والصق منشور تيليجرام هنا..."
                    className="admin-textarea font-mono border-pubg-gray/30 focus:border-pubg-orange/50 bg-pubg-black/30"
                    rows={4}
                  />
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                    <div className="w-full sm:w-auto">
                      <ModelSelector onModelChange={setSelectedModel} className="w-full sm:w-auto" />
                    </div>
                    <Button
                      type="button"
                      onClick={handleProcessWithAI}
                      disabled={isAiProcessing || !telegramPost.trim()}
                      variant="gray"
                      className={cn("admin-button w-full sm:w-auto transition-all border border-pubg-gray hover:border-pubg-orange")}
                    >
                      <Sparkles className="ml-2 h-4 w-4" />
                      {isAiProcessing ? "جاري المعالجة..." : "معالجة بالذكاء الاصطناعي"}
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Language Tabs */}
              <div className="mb-6">
                <Tabs defaultValue="ar" className="w-full">
                  <div className="mb-4 flex items-center justify-between">
                    <h4 className="text-md font-medium text-white flex items-center">
                      <Globe className="ml-2 h-5 w-5 text-pubg-orange" />
                      محتوى متعدد اللغات
                    </h4>
                    <TabsList className="grid grid-cols-2 max-w-[200px]">
                      <TabsTrigger value="ar" className="flex items-center gap-1">
                        <img src="/images/flags/sa.svg" alt="العربية" className="w-4 h-4" />
                        العربية
                      </TabsTrigger>
                      <TabsTrigger value="en" className="flex items-center gap-1">
                        <img src="/images/flags/us.svg" alt="English" className="w-4 h-4" />
                        English
                      </TabsTrigger>
                    </TabsList>
                  </div>
                  
                  {/* Arabic Content */}
                  <TabsContent value="ar" className="space-y-4">
                    <div className="admin-form-group col-span-full">
                      <Label htmlFor="title" className="admin-label flex items-center">
                        <div className="w-1 h-4 bg-pubg-orange ml-2 rounded-full"></div>
                        عنوان الحساب
                      </Label>
                      <Input
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="أدخل عنوان الحساب"
                        required
                        className="admin-input"
                        dir="rtl"
                      />
                    </div>
                    
                    <div className="admin-form-group col-span-full">
                      <Label htmlFor="description" className="admin-label flex items-center">
                        <div className="w-1 h-4 bg-pubg-orange ml-2 rounded-full"></div>
                        وصف الحساب
                      </Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder="أدخل وصف الحساب التفصيلي"
                        required
                        className="admin-textarea min-h-32"
                        dir="rtl"
                      />
                    </div>
                  </TabsContent>
                  
                  {/* English Content */}
                  <TabsContent value="en" className="space-y-4">
                    <div className="admin-form-group col-span-full">
                      <Label htmlFor="title_en" className="admin-label flex items-center">
                        <div className="w-1 h-4 bg-pubg-orange ml-2 rounded-full"></div>
                        Account Title (English)
                      </Label>
                      <Input
                        id="title_en"
                        name="title_en"
                        value={formData.title_en}
                        onChange={handleInputChange}
                        placeholder="Enter account title in English"
                        className="admin-input"
                        dir="ltr"
                      />
                    </div>
                    
                    <div className="admin-form-group col-span-full">
                      <Label htmlFor="description_en" className="admin-label flex items-center">
                        <div className="w-1 h-4 bg-pubg-orange ml-2 rounded-full"></div>
                        Account Description (English)
                      </Label>
                      <Textarea
                        id="description_en"
                        name="description_en"
                        value={formData.description_en}
                        onChange={handleInputChange}
                        placeholder="Enter detailed account description in English"
                        className="admin-textarea min-h-32"
                        dir="ltr"
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
              
              <div className="admin-form-grid">
                {/* Price and Category Section - Common for both languages */}
                <div className="col-span-full md:col-span-2 space-y-4 mb-4">
                  <h4 className="text-md font-medium text-white">السعر والتصنيف</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Price in USD */}
                    <div>
                      <Label htmlFor="priceUSD" className="admin-label">السعر (دولار أمريكي)</Label>
                      <div className="relative">
                        <Input
                          id="priceUSD"
                          name="priceUSD"
                          type="number"
                          value={formData.priceUSD}
                          onChange={handleInputChange}
                          placeholder="0.00"
                          className="admin-input pl-10 pr-4 text-left"
                          dir="ltr"
                        />
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <span className="text-gray-400">$</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Price in Local Currency */}
                    <div>
                      <Label htmlFor="priceEGP" className="admin-label">السعر (بالعملة المحلية)</Label>
                      <div className="relative">
                        <Input
                          id="priceEGP"
                          name="priceEGP"
                          type="number"
                          value={formData.priceEGP}
                          onChange={handleInputChange}
                          placeholder="0.00"
                          className="admin-input pl-10 pr-4 text-left"
                          dir="ltr"
                        />
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <span className="text-gray-400">{localCurrencyCode || "؟"}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Currency code */}
                    <div>
                      <Label htmlFor="localCurrencyCode" className="admin-label">رمز العملة المحلية</Label>
                      <div className="relative">
                        <Input
                          id="localCurrencyCode"
                          name="localCurrencyCode"
                          value={localCurrencyCode}
                          onChange={handleInputChange}
                          placeholder="مثال: EGP, SAR, AED"
                          className="admin-input text-left"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    {/* Category with Add New Option */}
                    <div className="col-span-2">
                      <Label htmlFor="category" className="admin-label">التصنيف</Label>
                      
                      {!isAddingCategory ? (
                        <div className="flex gap-2">
                          <Select
                            value={formData.category}
                            onValueChange={(value) => setFormData({ ...formData, category: value })}
                          >
                            <SelectTrigger className="admin-input flex-1">
                              <SelectValue placeholder="اختر تصنيف الحساب" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id || ""}>
                                  <div className="flex justify-between items-center w-full gap-2">
                                    <div className="flex flex-col flex-1 overflow-hidden">
                                      <span className="truncate">{category.name}</span>
                                      {category.name_en && (
                                        <span className="text-xs text-gray-400 truncate" dir="ltr">{category.name_en}</span>
                                      )}
                                    </div>
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 rounded-full hover:bg-red-500/10 hover:text-red-500"
                                      onClick={(e) => {
                                        e.stopPropagation(); // Prevent triggering the select item
                                        handleDeleteCategory(category.id || "");
                                      }}
                                      disabled={isDeletingCategory && categoryToDelete === category.id}
                                    >
                                      {isDeletingCategory && categoryToDelete === category.id ? (
                                        <RefreshCw className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-3 w-3" />
                                      )}
                                      <span className="sr-only">حذف التصنيف</span>
                                    </Button>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button 
                            type="button" 
                            variant="ghost"
                            className="flex-shrink-0 border border-pubg-gray/30 hover:border-pubg-orange hover:bg-pubg-orange/10"
                            onClick={() => setIsAddingCategory(true)}
                          >
                            <Plus className="w-4 h-4 ml-1" />
                            إضافة جديد
                          </Button>
                        </div>
                      ) : (
                        <div className="flex flex-col gap-2">
                          <Input
                            id="newCategory"
                            name="newCategory"
                            value={newCategory}
                            onChange={handleInputChange}
                            placeholder="أدخل اسم التصنيف بالعربية"
                            className="admin-input"
                            dir="rtl"
                          />
                          <Input
                            id="newCategoryEn"
                            name="newCategoryEn"
                            value={newCategoryEn}
                            onChange={handleInputChange}
                            placeholder="Enter category name in English"
                            className="admin-input"
                            dir="ltr"
                          />
                          <div className="flex gap-2 mt-1">
                            <Button 
                              type="button" 
                              variant="ghost"
                              className="flex-1 border border-green-500/30 hover:border-green-500 hover:bg-green-500/10 text-green-500"
                              onClick={handleAddCategoryStandalone}
                            >
                              <Check className="w-4 h-4 ml-1" />
                              إضافة التصنيف
                            </Button>
                            <Button 
                              type="button" 
                              variant="ghost"
                              className="flex-1 border border-red-500/30 hover:border-red-500 hover:bg-red-500/10 text-red-500"
                              onClick={() => {
                                setIsAddingCategory(false);
                                setNewCategory("");
                                setNewCategoryEn("");
                              }}
                            >
                              <X className="w-4 h-4 ml-1" />
                              إلغاء
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Featured Account */}
                <div className="col-span-full md:col-span-2">
                  <div className="flex items-center justify-start space-x-2 mb-4">
                    <Switch
                      id="featured"
                      name="featured"
                      checked={formData.featured}
                      onCheckedChange={(checked) => setFormData({ ...formData, featured: checked })}
                    />
                    <Label htmlFor="featured" className="admin-label mr-2 cursor-pointer">
                      حساب مميز (يظهر في القسم المميز بالصفحة الرئيسية)
                    </Label>
                  </div>
                </div>
                
                {/* Image Uploader */}
                <div className="col-span-full">
                  <Label className="admin-label block mb-2">صورة الحساب</Label>
                  <div className="flex flex-col md:flex-row gap-4 items-start">
                    <ImageUploader
                      currentImage={formData.image}
                      onImageChanged={(url) => setFormData({ ...formData, image: url })}
                      placeholderText="صورة الحساب"
                      className="w-full md:w-1/2"
                    />
                    
                    {formData.image && (
                      <div className="w-full md:w-1/2 overflow-hidden rounded-lg border border-pubg-gray/30 bg-pubg-black/30">
                        <img 
                          src={formData.image} 
                          alt="Account Preview" 
                          className="w-full h-auto object-cover rounded-lg"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex flex-col sm:flex-row items-center justify-end gap-3">
                <Button
                  type="button"
                  onClick={handleCancel}
                  variant="ghost"
                  className="w-full sm:w-auto order-2 sm:order-1"
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  variant="orange"
                  className="w-full sm:w-auto order-1 sm:order-2"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : editingAccount ? (
                    <>
                      <Save className="ml-2 h-4 w-4" />
                      حفظ التغييرات
                    </>
                  ) : (
                    <>
                      <Plus className="ml-2 h-4 w-4" />
                      إضافة الحساب
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AccountsManager;
