"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Search, 
  Filter, 
  RefreshCw,
  Package,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Edit,
  MoreVertical,
  Download,
  Calendar,
  User,
  DollarSign
} from "lucide-react"
import { 
  ProductOrder, 
  OrderFilters, 
  OrderListItem, 
  AdminOrderStats,
  OrderStatus 
} from "@/lib/types"
import {
  getFilteredOrders,
  getAdminOrderStats,
  getProductOrders
} from "@/lib/utils/orderStorage"
import { initializeMockProductOrders } from "@/lib/data/mockProductOrders"
import { formatCurrency } from "@/lib/data/currencies"
import { formatDate } from "@/lib/utils/dateUtils"
import { AdminOrderDetails } from "./AdminOrderDetails"
// import { AdminOrderFilters } from "./AdminOrderFilters"

interface AdminOrderDashboardProps {
  // ## Supabase Integration: Add user role checking
  userRole?: "admin" | "moderator" | "viewer"
}

export function AdminOrderDashboard({ userRole = "admin" }: AdminOrderDashboardProps) {
  // State management
  const [orders, setOrders] = useState<OrderListItem[]>([])
  const [stats, setStats] = useState<(AdminOrderStats & { approved?: number; declined?: number }) | null>(null)
  const [selectedOrder, setSelectedOrder] = useState<ProductOrder | null>(null)
  const [filters, setFilters] = useState<OrderFilters>({})
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [activeTab, setActiveTab] = useState<"all" | "pending" | "approved" | "declined">("all")

  // ## Supabase Integration: Replace with real-time subscription
  const loadOrders = useCallback(async () => {
    setIsLoading(true)
    try {
      // Initialize mock data if needed
      initializeMockProductOrders()

      // Apply tab filter to main filters with simplified status mapping
      let statusFilter: OrderStatus[] | undefined = undefined
      if (activeTab === "pending") {
        statusFilter = ["pending", "processing"] // Combine pending and processing
      } else if (activeTab === "approved") {
        statusFilter = ["completed"]
      } else if (activeTab === "declined") {
        statusFilter = ["failed", "cancelled"]
      }

      const tabFilters: OrderFilters = {
        ...filters,
        status: statusFilter,
        search: searchQuery || undefined
      }

      // ## Supabase Integration: Replace with supabase query
      const filteredOrders = getFilteredOrders(tabFilters)
      setOrders(filteredOrders)

      // Load stats with simplified categories
      const orderStats = getAdminOrderStats()
      // Transform stats to match simplified categories
      const simplifiedStats = {
        ...orderStats,
        pending: orderStats.pending + orderStats.processing, // Combine pending and processing
        approved: orderStats.completed, // Rename completed to approved
        declined: orderStats.failed + orderStats.cancelled // Combine failed and cancelled
      }
      setStats(simplifiedStats)
    } catch (error) {
      console.error("Error loading orders:", error)
    } finally {
      setIsLoading(false)
    }
  }, [filters, searchQuery, activeTab])

  // Load orders on component mount and when filters change
  useEffect(() => {
    loadOrders()
  }, [loadOrders])

  // Handle order selection for details view
  const handleOrderSelect = (orderId: string) => {
    // ## Supabase Integration: Replace with supabase query
    const allOrders = getProductOrders()
    const order = allOrders.find(o => o.id === orderId)
    setSelectedOrder(order || null)
  }

  // Handle filter changes
  const handleFiltersChange = (newFilters: OrderFilters) => {
    setFilters(newFilters)
  }

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  // Get status color with simplified categories
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case "pending":
      case "processing":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "failed":
      case "cancelled":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  // Get status icon with simplified categories
  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case "pending":
      case "processing":
        return <Clock className="h-4 w-4" />
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "failed":
      case "cancelled":
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  // Get status label in Arabic with simplified categories
  const getStatusLabel = (status: OrderStatus) => {
    const labels: Record<OrderStatus, string> = {
      pending: "قيد الانتظار",
      processing: "قيد الانتظار", // Combine with pending
      completed: "موافق عليها",
      failed: "مرفوضة",
      cancelled: "مرفوضة" // Combine with failed
    }
    return labels[status]
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">إدارة الطلبات</h1>
          <p className="text-slate-400">إدارة ومتابعة طلبات المنتجات</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadOrders}
            className="border-slate-600 text-slate-300"
          >
            <RefreshCw className="h-4 w-4 ml-2" />
            تحديث
          </Button>
        </div>
      </div>

      {/* Statistics Cards - Mobile-First 2 Column Layout */}
      {stats && (
        <div className="grid grid-cols-2 gap-4">
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{stats.total}</div>
                <div className="text-sm text-slate-400">إجمالي الطلبات</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">{stats.pending}</div>
                <div className="text-sm text-slate-400">قيد الانتظار</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">{stats.approved}</div>
                <div className="text-sm text-slate-400">موافق عليها</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">{stats.declined}</div>
                <div className="text-sm text-slate-400">مرفوضة</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="البحث برقم الطلب، اسم العميل، أو البريد الإلكتروني..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="bg-slate-700/50 border-slate-600 text-white pr-10"
              />
            </div>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <p className="text-slate-400">فلاتر متقدمة - قيد التطوير</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Orders Tabs and List */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-4 bg-slate-800/50">
          <TabsTrigger value="all" className="data-[state=active]:bg-slate-700">
            الكل ({stats?.total || 0})
          </TabsTrigger>
          <TabsTrigger value="pending" className="data-[state=active]:bg-slate-700">
            قيد الانتظار ({stats?.pending || 0})
          </TabsTrigger>
          <TabsTrigger value="approved" className="data-[state=active]:bg-slate-700">
            موافق عليها ({stats?.approved || 0})
          </TabsTrigger>
          <TabsTrigger value="declined" className="data-[state=active]:bg-slate-700">
            مرفوضة ({stats?.declined || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {isLoading ? (
            <div className="text-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-slate-400 mx-auto mb-4" />
              <p className="text-slate-400">جاري تحميل الطلبات...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-400 text-lg">لا توجد طلبات</p>
              <p className="text-slate-500">لم يتم العثور على طلبات تطابق المعايير المحددة</p>
            </div>
          ) : (
            <div className="space-y-4">
              {orders.map((order) => (
                <Card 
                  key={order.id} 
                  className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-colors cursor-pointer"
                  onClick={() => handleOrderSelect(order.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1">
                        <div className="p-2 bg-slate-700/50 rounded-lg">
                          {getStatusIcon(order.status)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-white truncate">
                              {order.templateName}
                            </h3>
                            <Badge className={getStatusColor(order.status)}>
                              {getStatusLabel(order.status)}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-slate-400">
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {order.customerName}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(order.createdAt)}
                            </span>
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              {formatCurrency(order.totalPrice, order.currency)}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleOrderSelect(order.id)
                          }}
                          className="text-slate-400 hover:text-white"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Order Details Modal */}
      {selectedOrder && (
        <AdminOrderDetails
          order={selectedOrder}
          onClose={() => setSelectedOrder(null)}
          onOrderUpdate={loadOrders}
          userRole={userRole}
        />
      )}
    </div>
  )
}
