import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Shield, Award, Clock, Users, Target, Lock, Star, DollarSign } from "lucide-react";
import { getDoc, doc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import GlobalLoader from "@/components/ui/GlobalLoader";
import { getAboutPageContent } from "@/services/firestore";
import SEO from "@/components/SEO";
import { OrganizationJsonLd, LocalBusinessJsonLd } from "@/components/JsonLd";
import { useConfig } from "@/contexts/ConfigContext";
import { useTranslation } from "react-i18next";
import { isChangingLanguageGlobal } from "@/components/LanguageSwitcher";

// Type definition for About Page content
interface AboutPageContent {
  id?: string;
  header: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    backgroundImage: string;
  };
  story: {
    title: string;
    title_en?: string;
    content: string[];
    content_en?: string[];
    image: string;
  };
  values: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    items: Array<{
      icon: string;
      title: string;
      title_en?: string;
      description: string;
      description_en?: string;
    }>;
  };
  team: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    members: Array<{
      name: string;
      name_en?: string;
      role: string;
      role_en?: string;
      avatar: string;
      bio: string;
      bio_en?: string;
    }>;
  };
  whyChooseUs: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    points: Array<{
      icon: string;
      title: string;
      title_en?: string;
      description: string;
      description_en?: string;
    }>;
  };
  faq: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    questions: Array<{
      question: string;
      question_en?: string;
      answer: string;
      answer_en?: string;
    }>;
  };
  createdAt?: any;
  updatedAt?: any;
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const fadeInLeft = {
  hidden: { opacity: 0, x: -50 },
  visible: { opacity: 1, x: 0 },
};

const fadeInRight = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Map icon names to actual icon components
const iconMap: { [key: string]: React.ReactNode } = {
  Shield: <Shield className="h-8 w-8" />,
  Award: <Award className="h-8 w-8" />,
  Clock: <Clock className="h-8 w-8" />,
  Users: <Users className="h-8 w-8" />,
  Target: <Target className="h-8 w-8" />,
  Lock: <Lock className="h-8 w-8" />,
  Star: <Star className="h-8 w-8" />,
  DollarSign: <DollarSign className="h-8 w-8" />
};

// Default values for the about page content
const defaultAboutData: AboutPageContent = {
  header: {
    title: "من نحن",
    title_en: "About Us",
    subtitle: "نحن فريق متخصص في توفير أفضل حسابات PUBG وشحن UC بأسعار منافسة وجودة عالية. هدفنا تقديم خدمة آمنة وسريعة للاعبين في جميع أنحاء العالم العربي.",
    subtitle_en: "We are a specialized team providing the best PUBG accounts and UC charging at competitive prices and high quality. Our goal is to provide a safe and fast service for players throughout the world.",
    backgroundImage: "https://images.unsplash.com/photo-*************-1ff1d85d1bdf?q=80&w=1920"
  },
  story: {
    title: "قصتنا",
    title_en: "Our Story",
    content: [
      "بدأنا رحلتنا عام 2020 كمجموعة من عشاق ألعاب الفيديو والمحترفين في مجال PUBG Mobile، مع رؤية واضحة لسد الفجوة في سوق حسابات اللعبة وشحن العملات الافتراضية.",
      "منذ ذلك الحين، نمت شبكتنا لتضم آلاف العملاء المخلصين، وتوسعت خدماتنا لتشمل الحسابات المميزة، والشحن الفوري لـ UC، والدعم الفني على مدار الساعة.",
      "نفتخر بكوننا أحد أكثر المواقع العربية موثوقية في مجال بيع وشراء حسابات PUBG، مع التزامنا الدائم بالجودة والأمان."
    ],
    content_en: [
      "We started our journey in 2020 as a group of video game enthusiasts and professionals in PUBG Mobile, with a clear vision to bridge the gap in the game accounts market and virtual currency charging.",
      "Since then, our network has grown to include thousands of loyal customers, and our services have expanded to include premium accounts, instant UC charging, and 24/7 technical support.",
      "We are proud to be one of the most reliable Arab websites in the field of buying and selling PUBG accounts, with our constant commitment to quality and security."
    ],
    image: "https://images.unsplash.com/photo-**********-3ec5d502959f?q=80&w=1920"
  },
  values: {
    title: "قيمنا",
    title_en: "Our Values",
    subtitle: "تقود هذه المبادئ كل قرار نتخذه وكل خدمة نقدمها",
    subtitle_en: "These principles guide every decision we make and every service we provide",
    items: [
      {
        icon: "Shield",
        title: "الأمان",
        title_en: "Security",
        description: "نضع أمان معلوماتك وحساباتك في المقام الأول، مع استخدام أحدث تقنيات التشفير وحماية البيانات.",
        description_en: "We put the security of your information and accounts first, using the latest encryption and data protection technologies."
      },
      {
        icon: "Award", 
        title: "الجودة",
        title_en: "Quality",
        description: "نقدم فقط الحسابات عالية الجودة والمميزة، ونتحقق من كل حساب قبل عرضه للبيع.",
        description_en: "We only offer high-quality and distinctive accounts, and we verify each account before offering it for sale."
      },
      {
        icon: "Clock",
        title: "السرعة",
        title_en: "Speed",
        description: "نؤمن بأهمية الوقت، لذلك نعمل على توفير خدمات سريعة ومباشرة لجميع عملائنا.",
        description_en: "We believe in the importance of time, so we work to provide fast and direct services to all our customers."
      }
    ]
  },
  team: {
    title: "فريقنا",
    title_en: "Our Team",
    subtitle: "نحن مجموعة من المحترفين الشغوفين بعالم PUBG",
    subtitle_en: "We are a group of professionals passionate about the world of PUBG",
    members: [
      {
        name: "محمد أحمد",
        name_en: "Mohammed Ahmed",
        role: "المؤسس والمدير التنفيذي",
        role_en: "Founder & CEO",
        avatar: "https://randomuser.me/api/portraits/men/1.jpg",
        bio: "مؤسس الشركة وخبير في مجال ألعاب الفيديو والتسويق الرقمي. يمتلك خبرة تزيد عن 10 سنوات في مجال الألعاب الإلكترونية.",
        bio_en: "Founder of the company and expert in video games and digital marketing. Has over 10 years of experience in the field of electronic games."
      },
      {
        name: "سارة خالد",
        name_en: "Sarah Khalid",
        role: "مديرة خدمة العملاء",
        role_en: "Customer Service Manager",
        avatar: "https://randomuser.me/api/portraits/women/1.jpg",
        bio: "تدير فريق دعم العملاء وتضمن تجربة مستخدم استثنائية لجميع العملاء.",
        bio_en: "Manages the customer support team and ensures an exceptional user experience for all customers."
      },
      {
        name: "أحمد علي",
        name_en: "Ahmed Ali",
        role: "خبير حسابات PUBG",
        role_en: "PUBG Accounts Expert",
        avatar: "https://randomuser.me/api/portraits/men/2.jpg",
        bio: "محترف في لعبة PUBG مع خبرة واسعة في تقييم وتصنيف الحسابات.",
        bio_en: "Professional in PUBG with extensive experience in evaluating and classifying accounts."
      }
    ]
  },
  whyChooseUs: {
    title: "لماذا تختارنا؟",
    title_en: "Why Choose Us?",
    subtitle: "نقدم مزايا فريدة تجعلنا الخيار الأمثل لعشاق PUBG",
    subtitle_en: "We offer unique advantages that make us the ideal choice for PUBG enthusiasts",
    points: [
      {
        icon: "Shield",
        title: "ضمان 100%",
        title_en: "100% Guarantee",
        description: "نقدم ضمان كامل على جميع الحسابات والخدمات، مع سياسة استرداد واضحة وشفافة.",
        description_en: "We provide a full guarantee on all accounts and services, with a clear and transparent refund policy."
      },
      {
        icon: "Star",
        title: "حسابات مميزة",
        title_en: "Premium Accounts",
        description: "نختار بعناية أفضل الحسابات ذات المواصفات الفريدة والمحتوى النادر.",
        description_en: "We carefully select the best accounts with unique specifications and rare content."
      },
      {
        icon: "Clock",
        title: "دعم 24/7",
        title_en: "24/7 Support",
        description: "فريق دعم فني متاح على مدار الساعة للإجابة على جميع استفساراتك ومساعدتك.",
        description_en: "Technical support team available 24/7 to answer all your inquiries and assist you."
      },
      {
        icon: "DollarSign",
        title: "أسعار تنافسية",
        title_en: "Competitive Prices",
        description: "نقدم أفضل الأسعار في السوق مع خيارات دفع متعددة وآمنة.",
        description_en: "We offer the best prices in the market with multiple and secure payment options."
      }
    ]
  },
  faq: {
    title: "الأسئلة الشائعة",
    title_en: "Frequently Asked Questions",
    subtitle: "إجابات على الأسئلة الأكثر شيوعًا",
    subtitle_en: "Answers to the most common questions",
    questions: [
      {
        question: "كيف يمكنني شراء حساب؟",
        question_en: "How can I purchase an account?",
        answer: "يمكنك تصفح الحسابات المتاحة في صفحة الحسابات واختيار الحساب المناسب، ثم إضافته إلى السلة وإتمام عملية الشراء عبر تلجرام.",
        answer_en: "You can browse available accounts on the Accounts page, select the appropriate account, add it to your cart, and complete the purchase process via Telegram."
      },
      {
        question: "ما هي طرق الدفع المتاحة؟",
        question_en: "What payment methods are available?",
        answer: "نوفر طرق دفع متعددة منها: فودافون كاش، بنوك مصرية، وي كاش، وباي بال. يمكنك الاستفسار أكثر عند التواصل معنا عبر تلجرام.",
        answer_en: "We provide multiple payment methods including: Vodafone Cash, Egyptian banks, We Cash, and PayPal. You can inquire more when contacting us via Telegram."
      },
      {
        question: "هل يمكنني استرجاع المبلغ بعد الشراء؟",
        question_en: "Can I get a refund after purchase?",
        answer: "نعم، نوفر ضمان استرجاع المبلغ خلال 24 ساعة في حالة وجود أي مشكلة في الحساب أو المنتج المقدم.",
        answer_en: "Yes, we provide a money-back guarantee within 24 hours in case of any problem with the account or product provided."
      },
      {
        question: "كم يستغرق وقت الرد على استفساراتي؟",
        question_en: "How long does it take to respond to my inquiries?",
        answer: "نحن متواجدون للرد على استفساراتك خلال ساعات العمل من 10 صباحًا حتى 10 مساءً. نسعى دائمًا للرد في أسرع وقت ممكن.",
        answer_en: "We are available to respond to your inquiries during working hours from 10 AM to 10 PM. We always strive to respond as quickly as possible."
      }
    ]
  }
};

const About = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [aboutData, setAboutData] = useState<AboutPageContent | null>(null);
  const { socialLinks } = useConfig();
  const { t, i18n } = useTranslation(['common', 'about']);
  const language = i18n.language;
  const isEnglish = language === 'en';
  
  // Helper function to get the text based on language
  const getLocalizedText = (arabicText: string | undefined, englishText: string | undefined): string => {
    if (isEnglish && englishText) {
      return englishText;
    }
    return arabicText || englishText || '';
  };

  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        setIsLoading(true);
        const data = await getAboutPageContent();
        
        if (data) {
          // Make sure the data includes the faq field
          const updatedData = {
            ...data,
            faq: data.faq || defaultAboutData.faq // Use default faq if not present
          };
          setAboutData(updatedData);
        } else {
          // If no data is returned (e.g., due to permissions), use default data
          console.log("Using default About page data");
          setAboutData(defaultAboutData);
        }
      } catch (error) {
        console.error("Error fetching about page data:", error);
        // Use default data on error
        console.log("Error occurred, using default About page data");
        setAboutData(defaultAboutData);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAboutData();
  }, [language]);

  if (isLoading && !isChangingLanguageGlobal.value) {
    return (
      <div className="min-h-screen pt-24 pb-16 px-4">
        <GlobalLoader fullPage />
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-24 pb-16 overflow-x-hidden">
      <SEO 
        title={t("about.seo.title")}
        description={getLocalizedText(aboutData?.header.subtitle, aboutData?.header.subtitle_en)}
        ogImage={aboutData?.header.backgroundImage} 
      />
      <OrganizationJsonLd 
        name={t("about.seo.company_name")}
        url="https://pubg-sd.netlify.app"
        logo="https://pubg-sd.netlify.app/favicon.png"
        sameAs={socialLinks || []}
      />
      <LocalBusinessJsonLd 
        name={t("about.seo.company_name")}
        url="https://pubg-sd.netlify.app"
        logo="https://pubg-sd.netlify.app/favicon.png"
      />
      
      {/* Hero Section */}
      <div className="relative mb-24 w-full">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
          style={{ backgroundImage: `url('${aboutData?.header.backgroundImage}')` }}
        ></div>
        <div className="relative container mx-auto px-4 py-20">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.3 }}
            variants={fadeInUp}
            className="max-w-3xl mx-auto text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {getLocalizedText(aboutData?.header.title, aboutData?.header.title_en)}
            </h1>
            <p className="text-xl text-muted-foreground leading-relaxed">
              {getLocalizedText(aboutData?.header.subtitle, aboutData?.header.subtitle_en)}
            </p>
          </motion.div>
        </div>
      </div>

      {/* Our Story */}
      <section className="py-20 px-4 w-full">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: false, amount: 0.3 }}
              variants={fadeInLeft}
              className="order-2 md:order-1"
            >
              <h2 className="text-3xl font-bold text-white mb-6">
                {getLocalizedText(aboutData?.story.title, aboutData?.story.title_en)}
              </h2>
              {isEnglish && aboutData?.story.content_en ? 
                aboutData.story.content_en.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-4 leading-relaxed">
                    {paragraph}
                  </p>
                )) :
                aboutData?.story.content.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-4 leading-relaxed">
                    {paragraph}
                  </p>
                ))
              }
            </motion.div>
            
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: false, amount: 0.3 }}
              variants={fadeInRight}
              className="order-1 md:order-2"
            >
              <div className="relative overflow-hidden rounded-xl">
                <img
                  src={aboutData?.story.image}
                  alt={getLocalizedText(aboutData?.story.title, aboutData?.story.title_en)}
                  className="w-full h-auto rounded-xl shadow-lg transform transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-tr from-pubg-blue/30 to-pubg-orange/30 mix-blend-overlay"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 px-4 bg-card/30 w-full">
        <div className="container mx-auto px-4">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.1 }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              {getLocalizedText(aboutData?.values.title, aboutData?.values.title_en)}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {getLocalizedText(aboutData?.values.subtitle, aboutData?.values.subtitle_en)}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.1 }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {aboutData?.values.items.map((value, index) => (
              <motion.div key={index} variants={fadeInUp} className="glass-card p-8 rounded-xl">
                <div className="w-16 h-16 bg-pubg-orange/20 rounded-full flex items-center justify-center mb-6">
                  {iconMap[value.icon] || <Shield className="text-pubg-orange w-8 h-8" />}
                </div>
                <h3 className="text-xl font-bold text-white mb-3">
                  {getLocalizedText(value.title, value.title_en)}
                </h3>
                <p className="text-muted-foreground">
                  {getLocalizedText(value.description, value.description_en)}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-20 px-4 w-full">
        <div className="container mx-auto">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.1 }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              {getLocalizedText(aboutData?.team.title, aboutData?.team.title_en)}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {getLocalizedText(aboutData?.team.subtitle, aboutData?.team.subtitle_en)}
            </p>
          </motion.div>

          {/* Dynamic grid with centering logic based on number of members */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.1 }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {aboutData?.team.members.map((member, index) => (
              <motion.div 
                key={index} 
                variants={fadeInUp}
                className="glass-card p-6 rounded-xl text-center"
              >
                <div className="mx-auto w-24 h-24 mb-4 overflow-hidden rounded-full border-2 border-pubg-orange/50">
                  <img 
                    src={member.avatar} 
                    alt={getLocalizedText(member.name, member.name_en)}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xl font-bold text-white mb-1">
                  {getLocalizedText(member.name, member.name_en)}
                </h3>
                <p className="text-pubg-orange mb-3 text-sm">
                  {getLocalizedText(member.role, member.role_en)}
                </p>
                <p className="text-muted-foreground text-sm">
                  {getLocalizedText(member.bio, member.bio_en)}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-card/30 w-full">
        <div className="container mx-auto">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.1 }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              {getLocalizedText(aboutData?.whyChooseUs.title, aboutData?.whyChooseUs.title_en)}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {getLocalizedText(aboutData?.whyChooseUs.subtitle, aboutData?.whyChooseUs.subtitle_en)}
            </p>
          </motion.div>

          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: false, amount: 0.1 }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 gap-6"
          >
            {aboutData?.whyChooseUs.points.map((point, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="flex bg-card/40 p-5 rounded-xl border border-border/50 hover:border-pubg-orange/30 transition-colors"
              >
                <div className="mr-4 w-12 h-12 bg-pubg-orange/20 rounded-full flex items-center justify-center flex-shrink-0">
                  {iconMap[point.icon] || <Shield className="text-pubg-orange w-6 h-6" />}
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white mb-2">
                    {getLocalizedText(point.title, point.title_en)}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    {getLocalizedText(point.description, point.description_en)}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      {aboutData?.faq && (
        <section className="py-20 px-4 w-full">
          <div className="container mx-auto">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: false, amount: 0.1 }}
              variants={fadeInUp}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold text-white mb-4">
                {getLocalizedText(aboutData?.faq.title, aboutData?.faq.title_en)}
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                {getLocalizedText(aboutData?.faq.subtitle, aboutData?.faq.subtitle_en)}
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: false, amount: 0.1 }}
              variants={staggerContainer}
              className="max-w-3xl mx-auto"
            >
              {aboutData?.faq.questions.map((faq, index) => (
                <motion.div 
                  key={index}
                  variants={fadeInUp}
                  className="mb-4 last:mb-0"
                >
                  <motion.div 
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-card/40 p-5 rounded-xl border border-border/50"
                  >
                    <h3 className="text-lg font-bold text-white mb-2">
                      {getLocalizedText(faq.question, faq.question_en)}
                    </h3>
                    <p className="text-muted-foreground">
                      {getLocalizedText(faq.answer, faq.answer_en)}
                    </p>
                  </motion.div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>
      )}
    </div>
  );
};

export default About; 