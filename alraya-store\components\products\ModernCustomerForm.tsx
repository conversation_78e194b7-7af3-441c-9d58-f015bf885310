"use client"

import React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DynamicField } from "@/lib/types"
import { User, Mail, Phone, Key, MessageCircle, Globe } from "lucide-react"

interface ModernCustomerFormProps {
  fields: DynamicField[]
  values: Record<string, string>
  errors: Record<string, string>
  onChange: (fieldName: string, value: string) => void
  disabled?: boolean
}

export function ModernCustomerForm({
  fields,
  values,
  errors,
  onChange,
  disabled = false
}: ModernCustomerFormProps) {
  // Filter out package and quantity fields as they're handled separately
  const customerFields = fields.filter(
    field => field.isActive && field.type !== "package" && field.type !== "quantity"
  )

  if (customerFields.length === 0) {
    return null
  }

  const getFieldIcon = (fieldType: string, fieldName: string) => {
    // Map common field types/names to icons
    if (fieldName.includes("email") || fieldType === "email") return Mail
    if (fieldName.includes("phone") || fieldName.includes("whatsapp")) return Phone
    if (fieldName.includes("password") || fieldName.includes("كلمة")) return Key
    if (fieldName.includes("id") || fieldName.includes("معرف")) return User
    if (fieldName.includes("server") || fieldName.includes("خادم")) return Globe
    if (fieldName.includes("note") || fieldName.includes("ملاحظ")) return MessageCircle
    return User
  }

  const renderField = (field: DynamicField) => {
    const Icon = getFieldIcon(field.type, field.name)
    const hasError = errors[field.name]
    const value = values[field.name] || ""

    const baseInputClasses = `
      bg-slate-700/50 border-slate-600 text-white h-12 text-right pr-12
      focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400
      transition-all duration-200
      ${hasError ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
    `

    const iconClasses = `
      absolute right-3 top-1/2 transform -translate-y-1/2 
      h-5 w-5 text-slate-400 pointer-events-none
      ${hasError ? 'text-red-400' : ''}
    `

    switch (field.type) {
      case "dropdown":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300 flex items-center gap-2">
              <Icon className="h-4 w-4" />
              {field.label}
              {field.required && <span className="text-red-400">*</span>}
            </Label>
            <div className="relative">
              <Select 
                onValueChange={(value) => onChange(field.name, value)}
                disabled={disabled}
              >
                <SelectTrigger className={baseInputClasses}>
                  <SelectValue placeholder={field.placeholder || `اختر ${field.label}`} />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  {field.options?.map((option) => (
                    <SelectItem 
                      key={option} 
                      value={option}
                      className="text-white hover:bg-slate-700 focus:bg-slate-700"
                    >
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Icon className={iconClasses} />
            </div>
            {hasError && (
              <p className="text-red-400 text-sm flex items-center gap-1">
                <span className="w-1 h-1 bg-red-400 rounded-full"></span>
                {errors[field.name]}
              </p>
            )}
          </div>
        )

      case "textarea":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300 flex items-center gap-2">
              <Icon className="h-4 w-4" />
              {field.label}
              {field.required && <span className="text-red-400">*</span>}
            </Label>
            <div className="relative">
              <Textarea
                placeholder={field.placeholder}
                value={value}
                onChange={(e) => onChange(field.name, e.target.value)}
                disabled={disabled}
                className={`${baseInputClasses} min-h-[80px] pt-3`}
                dir="rtl"
              />
              <Icon className="absolute right-3 top-3 h-5 w-5 text-slate-400 pointer-events-none" />
            </div>
            {hasError && (
              <p className="text-red-400 text-sm flex items-center gap-1">
                <span className="w-1 h-1 bg-red-400 rounded-full"></span>
                {errors[field.name]}
              </p>
            )}
          </div>
        )

      case "email":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300 flex items-center gap-2">
              <Mail className="h-4 w-4" />
              {field.label}
              {field.required && <span className="text-red-400">*</span>}
            </Label>
            <div className="relative">
              <Input
                type="email"
                placeholder={field.placeholder || "<EMAIL>"}
                value={value}
                onChange={(e) => onChange(field.name, e.target.value)}
                disabled={disabled}
                className={baseInputClasses}
                dir="ltr"
              />
              <Mail className={iconClasses} />
            </div>
            {hasError && (
              <p className="text-red-400 text-sm flex items-center gap-1">
                <span className="w-1 h-1 bg-red-400 rounded-full"></span>
                {errors[field.name]}
              </p>
            )}
          </div>
        )

      case "number":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300 flex items-center gap-2">
              <Icon className="h-4 w-4" />
              {field.label}
              {field.required && <span className="text-red-400">*</span>}
            </Label>
            <div className="relative">
              <Input
                type="number"
                placeholder={field.placeholder}
                value={value}
                onChange={(e) => onChange(field.name, e.target.value)}
                disabled={disabled}
                className={baseInputClasses}
                dir="ltr"
                min={field.validation?.min}
                max={field.validation?.max}
              />
              <Icon className={iconClasses} />
            </div>
            {hasError && (
              <p className="text-red-400 text-sm flex items-center gap-1">
                <span className="w-1 h-1 bg-red-400 rounded-full"></span>
                {errors[field.name]}
              </p>
            )}
          </div>
        )

      default: // text input
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300 flex items-center gap-2">
              <Icon className="h-4 w-4" />
              {field.label}
              {field.required && <span className="text-red-400">*</span>}
            </Label>
            <div className="relative">
              <Input
                type="text"
                placeholder={field.placeholder}
                value={value}
                onChange={(e) => onChange(field.name, e.target.value)}
                disabled={disabled}
                className={baseInputClasses}
                dir="rtl"
                minLength={field.validation?.minLength}
                maxLength={field.validation?.maxLength}
                pattern={field.validation?.pattern}
              />
              <Icon className={iconClasses} />
            </div>
            {hasError && (
              <p className="text-red-400 text-sm flex items-center gap-1">
                <span className="w-1 h-1 bg-red-400 rounded-full"></span>
                {errors[field.name]}
              </p>
            )}
          </div>
        )
    }
  }

  return (
    <div className="space-y-4" dir="rtl">
      <div className="flex items-center gap-2 mb-4">
        <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
          <User className="h-4 w-4 text-slate-900" />
        </div>
        <h3 className="text-white font-bold text-lg">معلومات الحساب</h3>
      </div>
      
      <div className="space-y-4">
        {customerFields.map(renderField)}
      </div>

      {/* Help Text */}
      <div className="bg-slate-800/30 border border-slate-700/50 rounded-lg p-3 mt-4">
        <p className="text-slate-400 text-sm text-center">
          🔒 جميع المعلومات محمية ومشفرة • لن نشارك بياناتك مع أي طرف ثالث
        </p>
      </div>
    </div>
  )
}

/**
 * Validation helper for customer form fields
 */
export function validateCustomerForm(
  fields: DynamicField[], 
  values: Record<string, string>
): Record<string, string> {
  const errors: Record<string, string> = {}

  fields.forEach(field => {
    if (!field.isActive || field.type === "package" || field.type === "quantity") {
      return
    }

    const value = values[field.name] || ""

    // Required field validation
    if (field.required && !value.trim()) {
      errors[field.name] = `${field.label} مطلوب`
      return
    }

    // Skip other validations if field is empty and not required
    if (!value.trim()) return

    // Email validation
    if (field.type === "email") {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        errors[field.name] = "البريد الإلكتروني غير صحيح"
      }
    }

    // Number validation
    if (field.type === "number") {
      const num = parseFloat(value)
      if (isNaN(num)) {
        errors[field.name] = "يجب أن يكون رقماً"
      } else {
        if (field.validation?.min !== undefined && num < field.validation.min) {
          errors[field.name] = `الحد الأدنى ${field.validation.min}`
        }
        if (field.validation?.max !== undefined && num > field.validation.max) {
          errors[field.name] = `الحد الأقصى ${field.validation.max}`
        }
      }
    }

    // Length validation
    if (field.validation?.minLength && value.length < field.validation.minLength) {
      errors[field.name] = `الحد الأدنى ${field.validation.minLength} أحرف`
    }
    if (field.validation?.maxLength && value.length > field.validation.maxLength) {
      errors[field.name] = `الحد الأقصى ${field.validation.maxLength} أحرف`
    }

    // Pattern validation
    if (field.validation?.pattern) {
      const regex = new RegExp(field.validation.pattern)
      if (!regex.test(value)) {
        errors[field.name] = "التنسيق غير صحيح"
      }
    }
  })

  return errors
}
