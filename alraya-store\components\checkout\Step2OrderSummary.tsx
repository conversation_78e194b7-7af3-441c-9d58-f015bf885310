"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useCheckout } from "@/components/checkout/CheckoutContext"
import { formatCurrency } from "@/lib/data/currencies"
import { cn } from "@/lib/utils"
import { ArrowLeft, ArrowRight, FileText, User, Phone, Mail, Wallet } from "lucide-react"

interface Step2OrderSummaryProps {
  onNext: () => void
  onPrevious: () => void
}

export function Step2OrderSummary({ onNext, onPrevious }: Step2OrderSummaryProps) {
  const { 
    state, 
    setUserDetails, 
    canProceedToStep3 
  } = useCheckout()

  const [formData, setFormData] = useState({
    firstName: state.userDetails?.firstName || "",
    lastName: state.userDetails?.lastName || "",
    phone: state.userDetails?.phone || "",
    email: state.userDetails?.email || ""
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.firstName.trim()) {
      newErrors.firstName = "الاسم الأول مطلوب"
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "الاسم الأخير مطلوب"
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "رقم الهاتف مطلوب"
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = "رقم الهاتف غير صحيح"
    }

    if (!formData.email.trim()) {
      newErrors.email = "البريد الإلكتروني مطلوب"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "البريد الإلكتروني غير صحيح"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const handleNext = () => {
    if (validateForm()) {
      setUserDetails(formData)
      onNext()
    }
  }

  // Check if current form data is valid (not context state)
  const isFormValid = () => {
    return (
      formData.firstName.trim() !== "" &&
      formData.lastName.trim() !== "" &&
      formData.phone.trim() !== "" &&
      formData.email.trim() !== ""
    )
  }

  return (
    <div className="space-y-6">
      {/* Step Header */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <FileText className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <CardTitle className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            ملخص الطلب
          </CardTitle>
          <p className="text-slate-300 text-base lg:text-lg">
            أدخل بياناتك الشخصية وراجع تفاصيل الطلب
          </p>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Details Form */}
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-xl text-white">
              <User className="h-6 w-6 text-yellow-400" />
              البيانات الشخصية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* First Name */}
            <div className="space-y-2">
              <Label htmlFor="firstName" className="text-slate-300 font-medium">
                الاسم الأول *
              </Label>
              <Input
                id="firstName"
                type="text"
                placeholder="أدخل الاسم الأول"
                value={formData.firstName}
                onChange={(e) => handleInputChange("firstName", e.target.value)}
                className={cn(
                  "bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",
                  errors.firstName ? "border-red-400 focus:border-red-400" : "focus:border-yellow-400"
                )}
              />
              {errors.firstName && (
                <p className="text-red-400 text-sm">{errors.firstName}</p>
              )}
            </div>

            {/* Last Name */}
            <div className="space-y-2">
              <Label htmlFor="lastName" className="text-slate-300 font-medium">
                الاسم الأخير *
              </Label>
              <Input
                id="lastName"
                type="text"
                placeholder="أدخل الاسم الأخير"
                value={formData.lastName}
                onChange={(e) => handleInputChange("lastName", e.target.value)}
                className={cn(
                  "bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",
                  errors.lastName ? "border-red-400 focus:border-red-400" : "focus:border-yellow-400"
                )}
              />
              {errors.lastName && (
                <p className="text-red-400 text-sm">{errors.lastName}</p>
              )}
            </div>

            {/* Phone */}
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-slate-300 font-medium">
                <Phone className="h-4 w-4 inline ml-2" />
                رقم الهاتف *
              </Label>
              <Input
                id="phone"
                type="tel"
                placeholder="+249 123 456 789"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className={cn(
                  "bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",
                  errors.phone ? "border-red-400 focus:border-red-400" : "focus:border-yellow-400"
                )}
              />
              {errors.phone && (
                <p className="text-red-400 text-sm">{errors.phone}</p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-slate-300 font-medium">
                <Mail className="h-4 w-4 inline ml-2" />
                البريد الإلكتروني *
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={cn(
                  "bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",
                  errors.email ? "border-red-400 focus:border-red-400" : "focus:border-yellow-400"
                )}
              />
              {errors.email && (
                <p className="text-red-400 text-sm">{errors.email}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-xl text-white">
              <Wallet className="h-6 w-6 text-yellow-400" />
              ملخص الطلب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Product */}
            <div className="flex justify-between items-center py-3 border-b border-slate-700">
              <span className="text-slate-300">المنتج</span>
              <span className="text-white font-medium">شحن المحفظة</span>
            </div>

            {/* Amount */}
            <div className="flex justify-between items-center py-3 border-b border-slate-700">
              <span className="text-slate-300">المبلغ</span>
              <span className="text-white font-medium">
                {formatCurrency(state.amount, state.currency)}
              </span>
            </div>

            {/* Currency */}
            <div className="flex justify-between items-center py-3 border-b border-slate-700">
              <span className="text-slate-300">العملة</span>
              <span className="text-white font-medium">
                {state.currency === "SDG" ? "جنيه سوداني" : "جنيه مصري"}
              </span>
            </div>

            {/* Total */}
            <div className="bg-gradient-to-r from-yellow-400/10 to-orange-500/10 border border-yellow-400/20 rounded-lg p-4 mt-6">
              <div className="flex justify-between items-center">
                <span className="text-yellow-400 font-semibold text-lg">الإجمالي</span>
                <span className="text-yellow-400 font-bold text-xl">
                  {formatCurrency(state.amount, state.currency)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex flex-col sm:flex-row gap-4 sm:justify-between">
        <Button
          onClick={onPrevious}
          variant="outline"
          className="w-full sm:w-auto px-6 py-3 text-lg border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"
        >
          <ArrowRight className="h-5 w-5 ml-2" />
          السابق
        </Button>

        <Button
          onClick={handleNext}
          disabled={!isFormValid()}
          className={cn(
            "w-full sm:w-auto px-8 py-3 text-lg font-semibold transition-all duration-300",
            isFormValid()
              ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600 hover:scale-105 shadow-lg hover:shadow-yellow-400/25"
              : "bg-slate-700 text-slate-400 cursor-not-allowed"
          )}
        >
          التالي
          <ArrowLeft className="h-5 w-5 mr-2" />
        </Button>
      </div>
    </div>
  )
}
