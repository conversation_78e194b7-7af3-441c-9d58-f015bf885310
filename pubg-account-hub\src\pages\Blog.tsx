import { useState, useEffect } from "react";
import BlogPost from "@/components/BlogPost";
import { motion } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { useTranslation } from "react-i18next";
import { getBlogPosts, BlogPostModel, updateAllBlogPostSlugs } from "@/services/firestore";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Grid, List, Search, X, Filter, Sparkles } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import GlobalLoader from "@/components/ui/GlobalLoader";
import { shouldShowLoader } from "@/lib/utils";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

const hoverScale = {
  scale: 1.02,
  boxShadow: "0 0 15px rgba(80, 120, 255, 0.2)",
  transition: { duration: 0.3 }
};

// Enhanced hover effect for special articles with orange glow
const specialHoverEffect = {
  scale: 1.02,
  boxShadow: "0 0 20px rgba(242, 169, 0, 0.35)",
  transition: { duration: 0.4 }
};

const Blog = () => {
  const { toast } = useToast();
  const { currentUser, isAdmin } = useAuth();
  const { language } = useLanguage();
  const { t } = useTranslation('common');
  const [blogPosts, setBlogPosts] = useState<BlogPostModel[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPostModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchTerm, setSearchTerm] = useState("");
  const [searchVisible, setSearchVisible] = useState(false);
  const [forceUpdateCounter, setForceUpdateCounter] = useState(0);
  
  // Get unique years from blog posts for filtering
  const [years, setYears] = useState<string[]>([]);
  const [selectedYear, setSelectedYear] = useState<string | null>(null);

  // Force re-render when language changes
  useEffect(() => {
    setForceUpdateCounter(prev => prev + 1);
  }, [language]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        console.log("Auth state before fetching posts:", currentUser ? "Logged in as " + currentUser.email : "Not logged in");
        
        // Only update slugs if logged in as admin
        if (currentUser && isAdmin) {
          try {
            await updateAllBlogPostSlugs();
          } catch (error) {
            console.error("Error updating blog slugs (non-critical):", error);
            // Continue anyway to fetch posts
          }
        }
        
        // Get blog posts (should work for everyone with public read access)
        const postsData = await getBlogPosts();
        setBlogPosts(postsData);
        setFilteredPosts(postsData);
        
        // Extract unique years from posts
        const uniqueYears = [...new Set(postsData.map(post => post.date.split('-')[0]))];
        setYears(uniqueYears);
      } catch (error) {
        console.error("Error fetching blog posts:", error);
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء تحميل المنشورات",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast, currentUser, isAdmin, language]);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Filter posts when search term or year filter changes
  useEffect(() => {
    let filtered = [...blogPosts];
    
    // Apply search filter based on language
    if (searchTerm) {
      filtered = filtered.filter(post => {
        // Search in both languages or in the specific language content
        const titleMatch = language === 'en' && post.title_en
          ? post.title_en.toLowerCase().includes(searchTerm.toLowerCase())
          : post.title.toLowerCase().includes(searchTerm.toLowerCase());
          
        const excerptMatch = language === 'en' && post.excerpt_en
          ? post.excerpt_en.toLowerCase().includes(searchTerm.toLowerCase())
          : post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
          
        const contentMatch = language === 'en' && post.content_en
          ? post.content_en.toLowerCase().includes(searchTerm.toLowerCase())
          : post.content.toLowerCase().includes(searchTerm.toLowerCase());
          
        return titleMatch || excerptMatch || contentMatch;
      });
    }
    
    // Apply year filter
    if (selectedYear) {
      filtered = filtered.filter(post => post.date.startsWith(selectedYear));
    }
    
    setFilteredPosts(filtered);
  }, [searchTerm, selectedYear, blogPosts, language]);

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedYear(null);
    setFilteredPosts(blogPosts);
    setSearchVisible(false);
  };

  if (shouldShowLoader(isLoading)) {
    return (
      <div className="min-h-screen pt-24 pb-16 px-4">
        <GlobalLoader fullPage />
      </div>
    );
  }

  if (filteredPosts.length === 0 && !searchTerm && !selectedYear) {
    return (
      <div className="min-h-screen pt-24 pb-16 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {t('blog.title')}
          </h1>
          <p className="text-muted-foreground mb-8">
            {t('blog.no_articles')}
          </p>
        </div>
      </div>
    );
  }

  // Sort posts by date (newest first) and get featured post
  const sortedPosts = [...filteredPosts].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
  
  // Find featured post or use the most recent
  const featuredPost = !searchTerm && !selectedYear 
    ? (sortedPosts.find(post => post.special) || sortedPosts.find(post => post.featured) || sortedPosts[0])
    : null;
    
  const regularPosts = featuredPost 
    ? sortedPosts.filter(post => post.id !== featuredPost.id)
    : sortedPosts;

  return (
    <div className="min-h-screen pt-24 pb-16 px-4" key={`blog-page-${language}-${forceUpdateCounter}`}>
      <div className="container mx-auto">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          className="mb-6 md:mb-8 flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-2 relative">
              {t('blog.title')}
              <span className="absolute -z-10 blur-[40px] opacity-50 h-12 w-24 bg-pubg-blue/30 left-0 -bottom-2"></span>
            </h1>
            <p className="text-muted-foreground">
              {t('blog.description')}
            </p>
          </div>
          
          <div className="flex items-center gap-3 mt-4 md:mt-0">
            {/* Search toggle button */}
            <motion.div whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}>
              <Button 
                variant="outline" 
                size="icon"
                onClick={() => setSearchVisible(!searchVisible)}
                className={searchVisible ? "bg-pubg-blue/20 text-pubg-blue shadow-sm shadow-pubg-blue/20" : ""}
              >
                <Search size={18} />
              </Button>
            </motion.div>
            
            {/* View mode toggle */}
            <div className="border border-border rounded-md flex overflow-hidden">
              <motion.div whileHover={{ backgroundColor: "rgba(80, 120, 255, 0.1)" }}>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => setViewMode("grid")}
                  className={viewMode === "grid" ? "bg-pubg-blue/20 text-pubg-blue shadow-inner shadow-pubg-blue/10" : ""}
                >
                  <Grid size={18} />
                </Button>
              </motion.div>
              <motion.div whileHover={{ backgroundColor: "rgba(80, 120, 255, 0.1)" }}>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => setViewMode("list")}
                  className={viewMode === "list" ? "bg-pubg-blue/20 text-pubg-blue shadow-inner shadow-pubg-blue/10" : ""}
                >
                  <List size={18} />
                </Button>
              </motion.div>
            </div>
          </div>
        </motion.div>
        
        {/* Search and filters */}
        {searchVisible && (
          <motion.div 
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="mb-6 glass-card p-4 rounded-xl relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-pubg-blue/5 to-transparent pointer-events-none"></div>
            <div className="absolute -bottom-8 -right-8 w-32 h-32 rounded-full bg-pubg-blue/5 blur-2xl"></div>
            <div className="flex flex-col md:flex-row md:items-center gap-4 relative z-10">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('blog.search_placeholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-3 pr-10"
                />
                {searchTerm && (
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="absolute left-1 top-1 h-8 w-8" 
                    onClick={() => setSearchTerm("")}
                  >
                    <X size={16} />
                  </Button>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2 flex-wrap">
                  <Badge variant="outline" className="flex gap-1 items-center text-sm">
                    <Filter size={14} /> {t('blog.filter')}
                  </Badge>
                  {years.map(year => (
                    <Badge 
                      key={year}
                      variant={selectedYear === year ? "default" : "outline"}
                      className="cursor-pointer transition-colors"
                      onClick={() => setSelectedYear(selectedYear === year ? null : year)}
                    >
                      {year}
                    </Badge>
                  ))}
                </div>
                
                {(searchTerm || selectedYear) && (
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    {t('blog.clear_filters')}
                  </Button>
                )}
              </div>
            </div>
            
            <div className="mt-3 text-sm text-muted-foreground">
              {filteredPosts.length === 0 ? (
                <p>{t('blog.no_results_description')}</p>
              ) : (
                <p>{t('blog.found_articles', { count: filteredPosts.length })}</p>
              )}
            </div>
          </motion.div>
        )}

        {/* Featured post */}
        {featuredPost && !searchTerm && !selectedYear && (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="mb-8 md:mb-12 relative"
          >
            {/* Special gradient effects for the background */}
            {featuredPost.special ? (
              <>
                <div className="absolute -z-10 top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-pubg-orange/10 rounded-full blur-3xl"></div>
                <div className="absolute -z-10 bottom-1/3 right-1/4 transform translate-x-1/2 translate-y-1/2 w-32 h-32 bg-pubg-orange/5 rounded-full blur-3xl"></div>
              </>
            ) : (
              <div className="absolute -z-10 top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-pubg-blue/10 rounded-full blur-3xl"></div>
            )}
            
            <div className="flex items-center mb-4">
              {featuredPost.special ? (
                <div className="flex items-center justify-center bg-pubg-orange/20 rounded-lg p-2 ml-3 shadow-sm shadow-pubg-orange/30">
                  <Sparkles className="h-4 w-4 text-pubg-orange animate-[pulse_1.5s_ease-in-out_infinite]" />
                </div>
              ) : (
                <div className="flex items-center justify-center bg-pubg-blue/20 rounded-lg p-2 ml-3 shadow-sm shadow-pubg-blue/20">
                  <Sparkles className="h-4 w-4 text-pubg-blue animate-pulse" />
                </div>
              )}
              <h2 className="text-xl text-white font-bold">
                {featuredPost.special 
                  ? t('blog.special_article')
                  : t('blog.featured_article')
                }
              </h2>
            </div>
            <BlogPost post={{
              id: featuredPost.id || '',
              title: featuredPost.title,
              title_en: featuredPost.title_en,
              excerpt: featuredPost.excerpt,
              excerpt_en: featuredPost.excerpt_en,
              content: featuredPost.content,
              content_en: featuredPost.content_en,
              image: featuredPost.image,
              author: featuredPost.author,
              author_en: featuredPost.author_en,
              date: featuredPost.date,
              slug: featuredPost.slug || '',
              slug_en: featuredPost.slug_en,
              special: featuredPost.special || false
            }} featured key={`featured-${featuredPost.id}-${language}`} />
          </motion.div>
        )}

        {/* Regular posts */}
        {regularPosts.length > 0 && (
          <div className="mb-6 relative">
            <div className="absolute -z-10 -top-10 left-0 w-40 h-40 bg-pubg-blue/5 rounded-full blur-3xl"></div>
            <div className="flex items-center mb-4">
              <h2 className="text-xl text-white font-bold relative">
                {searchTerm || selectedYear 
                  ? t('blog.search_results')
                  : t('blog.latest_articles')
                }
                <span className="absolute -z-10 blur-[20px] opacity-30 h-4 w-20 bg-pubg-blue/40 left-0 bottom-0"></span>
              </h2>
            </div>
            
            <motion.div
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className={
                viewMode === "grid"
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                  : "flex flex-col gap-4"
              }
            >
              {regularPosts.map((post) => (
                <motion.div 
                  key={post.id} 
                  variants={fadeInUp}
                >
                  <BlogPost 
                    post={{
                      id: post.id || '',
                      title: post.title,
                      title_en: post.title_en,
                      excerpt: post.excerpt,
                      excerpt_en: post.excerpt_en,
                      content: post.content,
                      content_en: post.content_en,
                      image: post.image,
                      author: post.author,
                      author_en: post.author_en,
                      date: post.date,
                      slug: post.slug || '',
                      slug_en: post.slug_en,
                      special: post.special || false
                    }} 
                    viewMode={viewMode}
                    key={`post-${post.id}-${language}`}
                  />
                </motion.div>
              ))}
            </motion.div>
          </div>
        )}
        
        {/* No results message */}
        {filteredPosts.length === 0 && (searchTerm || selectedYear) && (
          <div className="text-center py-12 glass-card rounded-xl">
            <h3 className="text-xl text-white font-bold mb-2">
              {t('blog.no_results')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('blog.no_results_description')}
            </p>
            <Button onClick={clearFilters}>
              {t('blog.show_all_articles')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Blog;
