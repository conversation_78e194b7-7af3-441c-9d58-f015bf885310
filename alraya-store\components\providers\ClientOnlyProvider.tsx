"use client"

import { useEffect, useState } from 'react'

interface ClientOnlyProviderProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * ClientOnlyProvider ensures that children are only rendered on the client side
 * This prevents SSR hydration mismatches with client-only state like Zustand stores
 */
export function ClientOnlyProvider({ children, fallback = null }: ClientOnlyProviderProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
