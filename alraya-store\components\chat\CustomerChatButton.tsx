/**
 * ## Secure Customer Chat Button
 *
 * Customer-facing chat button with integrated security:
 *
 * **Security Features:**
 * - User validation before rendering
 * - Role-based access control
 * - Secure chat modal integration
 *
 * **Customer Experience:**
 * - Direct support chat access
 * - Clean, focused interface
 * - Real-time message notifications
 * - Consistent design language
 */

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageCircle, Headphones } from 'lucide-react'
import { ChatModal } from './AdminChatModal'
import { useChat } from '@/lib/hooks/useChat'

interface CustomerChatButtonProps {
  userId: string
  userName?: string
  userEmail?: string
  // Position of the floating button
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  // Custom styling
  className?: string
}

export function CustomerChatButton({
  userId,
  userName,
  userEmail,
  position = 'bottom-right',
  className = ''
}: CustomerChatButtonProps) {

  // ## Security: User Validation
  if (!userId) {
    console.error('CustomerChatButton: Missing required userId')
    return null
  }

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [hasNewMessages, setHasNewMessages] = useState(false)

  // Get unread count from chat hook
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  // Animate button when new messages arrive
  useEffect(() => {
    if (unreadCount > 0 && !isModalOpen) {
      setHasNewMessages(true)
      const timer = setTimeout(() => setHasNewMessages(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [unreadCount, isModalOpen])

  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  }

  return (
    <>
      {/* Floating Support Button */}
      <div className={`fixed ${positionClasses[position]} z-50 ${className}`}>
        <div className="relative">
          <Button
            onClick={() => setIsModalOpen(true)}
            className={`
              w-14 h-14 rounded-full shadow-lg transition-all duration-300
              bg-gradient-to-r from-green-500 to-green-600 
              hover:from-green-600 hover:to-green-700
              hover:scale-110 active:scale-95
              ${hasNewMessages ? 'animate-bounce' : ''}
            `}
          >
            <Headphones className="w-6 h-6 text-white" />
          </Button>

          {/* Unread Badge */}
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full animate-pulse">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}

          {/* Pulse Ring Animation */}
          {hasNewMessages && (
            <div className="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-30"></div>
          )}
        </div>

        {/* Tooltip */}
        <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-slate-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
            الدعم الفني
          </div>
        </div>
      </div>

      {/* Role-Based Chat Modal */}
      <ChatModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        userId={userId}
        userName={userName}
        userEmail={userEmail}
        userRole="customer"
        position="center"
      />
    </>
  )
}

/**
 * ## Customer Chat Widget
 * Inline widget version for integration into customer dashboard
 */
interface CustomerChatWidgetProps {
  userId: string
  userName?: string
  userEmail?: string
  className?: string
}

export function CustomerChatWidget({ 
  userId, 
  userName, 
  userEmail,
  className = ''
}: CustomerChatWidgetProps) {
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  return (
    <div className={`bg-slate-800/50 border border-slate-700/50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
            <Headphones className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-white">الدعم الفني</h3>
            <p className="text-sm text-slate-400">نحن هنا لمساعدتك</p>
          </div>
        </div>
        
        {unreadCount > 0 && (
          <Badge className="bg-red-500 text-white">
            {unreadCount} رسالة جديدة
          </Badge>
        )}
      </div>

      {/* TODO: Implement inline chat widget using role-based system */}
      <div className="text-center py-8">
        <p className="text-slate-400 text-sm">انقر على زر الدعم الفني لبدء المحادثة</p>
      </div>
    </div>
  )
}

/**
 * ## Customer Chat Notification Bar
 * Compact notification bar for customer pages
 */
interface CustomerChatNotificationProps {
  userId: string
  onOpenChat: () => void
  className?: string
}

export function CustomerChatNotification({ 
  userId, 
  onOpenChat,
  className = ''
}: CustomerChatNotificationProps) {
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  if (unreadCount === 0) return null

  return (
    <div className={`bg-green-500/10 border border-green-500/20 rounded-lg p-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <MessageCircle className="w-4 h-4 text-white" />
          </div>
          <div>
            <p className="text-sm font-medium text-green-300">
              لديك {unreadCount} رسالة جديدة من فريق الدعم
            </p>
            <p className="text-xs text-green-400">انقر لعرض المحادثة</p>
          </div>
        </div>

        <Button
          onClick={onOpenChat}
          className="bg-green-500 hover:bg-green-600 text-white text-sm px-4 py-2"
        >
          عرض الرسائل
        </Button>
      </div>
    </div>
  )
}

/**
 * ## Usage Examples:
 * 
 * // Floating button (recommended for most customer pages)
 * <CustomerChatButton userId="customer-123" />
 * 
 * // Integrated widget (for customer dashboard)
 * <CustomerChatWidget userId="customer-123" className="col-span-1" />
 * 
 * // Notification bar (for customer header)
 * <CustomerChatNotification 
 *   userId="customer-123" 
 *   onOpenChat={() => setShowChat(true)} 
 * />
 */
