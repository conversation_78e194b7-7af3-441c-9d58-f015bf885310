import { useState, useEffect } from "react";
import UCPackage from "@/components/UCPackage";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { getUCPackages, UCPackageModel, getUCStoreSettings, UCStoreSettingsModel } from "@/services/firestore";
import { ChevronDown, ChevronUp, Search, ArrowUpDown, LayoutGrid, LayoutList, Star } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import GlobalLoader from "@/components/ui/GlobalLoader";
import { cn, shouldShowLoader } from "@/lib/utils";

// Default image for UC packages when none is provided
const DEFAULT_UC_IMAGE = "https://media.fab.com/image_previews/gallery_images/290b6414-e320-45c5-bb96-98d780ae2601/d27cce68-7be5-446a-beb7-b674b122f93b.jpeg";

// Default hero section settings
const DEFAULT_HERO_IMAGE = "/images/uc-store-default-bg.jpg";
// Hero titles and subtitles will be loaded from translations

// Extended UCPackage Model with marketing fields
interface ExtendedUCPackageModel extends UCPackageModel {
  priceLocal?: number | string;
  localCurrencySymbol?: string;
  originalPriceUSD?: number | string;
  originalPriceLocal?: number | string;
  discountUSD?: number | string;
  discountLocal?: number | string;
  highlights?: string[];
  category?: string;
  description?: string;
  description_en?: string;
  notes?: string[];
  notes_en?: string[];
  // Legacy fields for backward compatibility
  originalPrice?: number;
  discountPercent?: number;
  localCurrencyCode?: string;
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const UCStore = () => {
  const { t, i18n } = useTranslation('common');
  const { toast } = useToast();
  const [ucPackages, setUCPackages] = useState<ExtendedUCPackageModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'amount' | 'priceEGP'>('amount');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [categories, setCategories] = useState<string[]>([]);
  const [storeSettings, setStoreSettings] = useState<UCStoreSettingsModel>({
    heroImage: DEFAULT_HERO_IMAGE,
    heroTitle: "",
    heroSubtitle: ""
  });

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  const changeSortBy = (newSortBy: 'amount' | 'priceEGP') => {
    if (sortBy === newSortBy) {
      toggleSortOrder();
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
  };

  // Filter packages by search term and category
  const filteredPackages = ucPackages.filter(pkg => {
    const matchesSearch = !searchTerm || 
      pkg.amount.toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
      (pkg.category && pkg.category.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || 
      pkg.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Sort filtered packages based on current sorting criteria
  const sortedPackages = [...filteredPackages].sort((a, b) => {
    const valueA = a[sortBy];
    const valueB = b[sortBy];
    
    if (sortOrder === 'asc') {
      return valueA - valueB;
    } else {
      return valueB - valueA;
    }
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const packagesData = await getUCPackages();
        
        // Add default image for packages without images
        const enhancedPackages = packagesData.map(pkg => ({
          ...pkg,
          image: pkg.image || DEFAULT_UC_IMAGE
        }));
        
        // Extract unique categories
        const uniqueCategories = Array.from(
          new Set(
            enhancedPackages
              .map(pkg => pkg.category)
              .filter(Boolean) as string[]
          )
        );
        
        setCategories(uniqueCategories);
        setUCPackages(enhancedPackages);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching UC packages:", error);
        toast({
          title: t('home.error'),
          description: t('home.loading_error'),
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast, t]);

  // Add effect to fetch UC Store settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const settings = await getUCStoreSettings();
        if (settings) {
          // Use default hero image if none is provided
          if (!settings.heroImage || settings.heroImage.trim() === '') {
            settings.heroImage = DEFAULT_HERO_IMAGE;
          }
          setStoreSettings(settings);
        }
      } catch (error) {
        console.error("Error fetching UC Store settings:", error);
        // No need to show error toast, just use defaults
      }
    };

    fetchSettings();
  }, []);

  if (shouldShowLoader(isLoading)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <GlobalLoader fullPage />
      </div>
    );
  }

  return (
    <div className="min-h-screen pb-16">
      {/* Hero Section */}
      <div className="relative w-full h-80 sm:h-96 md:h-[420px] overflow-hidden mb-12">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 bg-pubg-dark/70 z-10"></div>
        <div className="absolute inset-0 z-0">
          <img
            src={storeSettings.heroImage}
            alt="PUBG UC Hero"
            className="w-full h-full object-cover opacity-50"
          />
        </div>
        
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10 z-5 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyMjIiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzBoLTZWMGg2djMwem0tMTggMEgxMlYwaDZ2MzB6bTE4IDBWMGg2djMwaC02ek0xMiAzMFYwaDZ2MzBoLTZ6Ii8+PC9nPjwvZz48L3N2Zz4=')]"></div>
        
        {/* Content */}
        <div className="container mx-auto relative z-30 h-full flex flex-col justify-center items-center text-center px-4">
          <motion.h1 
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {storeSettings.heroTitle || t('uc_store.hero_title')}
          </motion.h1>
          
          <motion.p 
            className="text-lg md:text-xl text-gray-200 max-w-2xl mx-auto mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            {storeSettings.heroSubtitle || t('uc_store.hero_subtitle')}
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="w-full max-w-md"
          >
            <div className="relative">
              <Input
                type="text"
                placeholder={t('home.uc_packages')}
                className="max-w-md mx-auto bg-pubg-dark/70 border-pubg-blue/30 focus:border-pubg-blue text-white placeholder:text-gray-400 px-4 py-3 rounded-lg pr-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4">
        {/* Filters & Controls */}
        <div className="glass-card p-4 md:p-6 rounded-xl mb-8">
          <div className="flex flex-col md:flex-row gap-4 justify-between items-stretch">
            {/* Categories */}
            <div className="flex flex-wrap gap-2 rtl">
              <Button
                onClick={() => setSelectedCategory("all")}
                className={`text-sm px-3 py-1 h-auto transition-colors ${
                  selectedCategory === "all"
                    ? "bg-pubg-blue text-white"
                    : "bg-pubg-dark/40 text-gray-300 hover:bg-pubg-dark/60"
                }`}
                size="sm"
              >
                {t('home.view_all_packages')}
              </Button>
              
              {categories.map((category) => (
                <Button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`text-sm px-3 py-1 h-auto transition-colors ${
                    selectedCategory === category
                      ? "bg-pubg-blue text-white"
                      : "bg-pubg-dark/40 text-gray-300 hover:bg-pubg-dark/60"
                  }`}
                  size="sm"
                >
                  {category}
                </Button>
              ))}
            </div>
            
            {/* Controls */}
            <div className="flex flex-wrap items-center gap-2 rtl">
              <div className="flex items-center gap-2 bg-pubg-dark/40 rounded p-1">
                <Button
                  onClick={() => changeSortBy('amount')}
                  variant="ghost"
                  className={`text-sm px-3 py-1 h-auto flex items-center gap-1 ${
                    sortBy === 'amount' ? 'text-pubg-blue' : 'text-gray-400'
                  }`}
                  size="sm"
                >
                  <Star size={14} className="ml-1" />
                  {t('uc_package.amount')}
                  {sortBy === 'amount' && (
                    sortOrder === 'asc' ? 
                      <ChevronUp size={14} /> : 
                      <ChevronDown size={14} />
                  )}
                </Button>
                
                <Button
                  onClick={() => changeSortBy('priceEGP')}
                  variant="ghost"
                  className={`text-sm px-3 py-1 h-auto flex items-center gap-1 ${
                    sortBy === 'priceEGP' ? 'text-pubg-blue' : 'text-gray-400'
                  }`}
                  size="sm"
                >
                  <ArrowUpDown size={14} className="ml-1" />
                  {t('uc_package.price')}
                  {sortBy === 'priceEGP' && (
                    sortOrder === 'asc' ? 
                      <ChevronUp size={14} /> : 
                      <ChevronDown size={14} />
                  )}
                </Button>
              </div>
              
              <div className="h-6 border-r border-gray-700 mx-1 hidden md:block"></div>
              
              {/* View Toggle */}
              <div className="flex items-center gap-0 bg-pubg-dark/40 rounded overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${
                    viewMode === 'grid' 
                      ? 'bg-pubg-blue text-white' 
                      : 'text-gray-400 hover:text-white hover:bg-pubg-dark/60'
                  }`}
                >
                  <LayoutGrid size={18} />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${
                    viewMode === 'list' 
                      ? 'bg-pubg-blue text-white' 
                      : 'text-gray-400 hover:text-white hover:bg-pubg-dark/60'
                  }`}
                >
                  <LayoutList size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="flex justify-between items-center mb-6 rtl">
          <h2 className="text-xl font-bold text-white">
            {searchTerm ? `${t('search_results')}: "${searchTerm}"` : t('home.uc_packages')}
          </h2>
          <p className="text-sm text-gray-400">{sortedPackages.length} {t('packages')}</p>
        </div>

        {/* UC Packages display */}
        <AnimatePresence mode="wait">
          {sortedPackages.length > 0 ? (
            <motion.div
              key="results"
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
              className={
                viewMode === 'grid' 
                  ? "grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-6" 
                  : "flex flex-col space-y-4"
              }
            >
              {sortedPackages.map((ucPackage) => (
                <motion.div 
                  key={ucPackage.id} 
                  variants={fadeInUp}
                >
                  <UCPackage 
                    ucPackage={{
                      id: ucPackage.id || '',
                      amount: ucPackage.amount,
                      priceEGP: ucPackage.priceEGP,
                      priceUSD: ucPackage.priceUSD,
                      priceLocal: ucPackage.priceLocal || ucPackage.priceEGP,
                      localCurrencySymbol: ucPackage.localCurrencySymbol || ucPackage.localCurrencyCode || "ج.م",
                      image: ucPackage.image || DEFAULT_UC_IMAGE,
                      description: ucPackage.description || "", // Empty string if undefined
                      description_en: ucPackage.description_en || "", // Add English description
                      originalPrice: ucPackage.originalPrice,
                      originalPriceUSD: ucPackage.originalPriceUSD,
                      originalPriceLocal: ucPackage.originalPriceLocal || ucPackage.originalPrice,
                      discountUSD: ucPackage.discountUSD,
                      discountLocal: ucPackage.discountLocal,
                      discountPercent: ucPackage.discountPercent,
                      highlights: ucPackage.highlights || ["instant_delivery", "support", "secure_payment"],
                      category: ucPackage.category,
                      notes: ucPackage.notes || [], // Empty array if undefined
                      notes_en: ucPackage.notes_en || [] // Add English notes
                    }} 
                    listView={viewMode === 'list'}
                  />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              key="no-results"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-16 glass-card rounded-xl"
            >
              <div className="w-20 h-20 mx-auto mb-6 flex items-center justify-center rounded-full bg-pubg-dark/60">
                <Search size={32} className="text-gray-400" />
              </div>
              <h3 className="text-xl text-white font-medium mb-2">{t('no_results')}</h3>
              <p className="text-muted-foreground">
                {t('try_different_keywords')}
              </p>
              <Button 
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("all");
                }} 
                className="mt-6 bg-pubg-blue text-white hover:bg-pubg-blue/90"
              >
                {t('home.view_all_packages')}
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Feature Section */}
      <div className="container mx-auto px-4 mt-16">
        <div className="text-center mb-10">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">{t('uc_store.features_title')}</h2>
          <p className="text-gray-400 max-w-xl mx-auto">
            {t('uc_store.features_description')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="glass-card p-6 rounded-xl">
            <div className="w-14 h-14 bg-pubg-blue/20 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-7 h-7 text-pubg-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-2 rtl">{t('uc_store.instant_delivery_title')}</h3>
            <p className="text-gray-400 rtl">
              {t('uc_store.instant_delivery_desc')}
            </p>
          </div>
          
          <div className="glass-card p-6 rounded-xl">
            <div className="w-14 h-14 bg-pubg-orange/20 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-7 h-7 text-pubg-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-2 rtl">{t('uc_store.secure_payment_title')}</h3>
            <p className="text-gray-400 rtl">
              {t('uc_store.secure_payment_desc')}
            </p>
          </div>
          
          <div className="glass-card p-6 rounded-xl">
            <div className="w-14 h-14 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-7 h-7 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-2 rtl">{t('uc_store.support_title')}</h3>
            <p className="text-gray-400 rtl">
              {t('uc_store.support_desc')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UCStore;
