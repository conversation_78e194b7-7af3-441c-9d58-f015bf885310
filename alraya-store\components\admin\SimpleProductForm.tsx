"use client"

import { useState } from "react"
import { Plus, Edit, Trash2, X, Upload, Key, AlertCircle } from "lucide-react"
import { ProductTemplate, ProductPackage, DynamicField } from "@/lib/types"
import { createProduct, updateProduct } from "@/lib/services/productService"
import { getCategories } from "@/lib/services/categoryService"

interface SimpleProductFormProps {
  product?: ProductTemplate
  onSave: (product: ProductTemplate) => void
  onCancel: () => void
  isEditing?: boolean
}

export default function SimpleProductForm({ product, onSave, onCancel, isEditing = false }: SimpleProductFormProps) {
  const [isLoading, setIsLoading] = useState(false)

  const [formData, setFormData] = useState<Partial<ProductTemplate>>({
    name: product?.name || "",
    description: product?.description || "",
    category: product?.category || "",
    tags: product?.tags || [],
    image: product?.image || "",
    packages: product?.packages || [],
    fields: product?.fields || [],
    features: product?.features || [],
    isActive: product?.isActive ?? true,
    isFeatured: product?.isFeatured || false,
    deliveryType: product?.deliveryType || "code_based",
    productType: product?.productType || "digital",
    processingType: product?.processingType || "instant",
  })

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "",
      tags: [],
      image: "",
      packages: [],
      fields: [],
      features: [],
      isActive: true,
      isFeatured: false,
      deliveryType: "code_based",
      productType: "digital",
      processingType: "instant",
    })
  }

  const handleSave = async () => {
    setIsLoading(true)

    // Basic validation
    if (!formData.name?.trim()) {
      alert("يرجى إدخال اسم المنتج")
      setIsLoading(false)
      return
    }

    if (!formData.category?.trim()) {
      alert("يرجى إدخال فئة المنتج")
      setIsLoading(false)
      return
    }

    if (!formData.packages || formData.packages.length === 0) {
      alert("يرجى إضافة حزمة واحدة على الأقل")
      setIsLoading(false)
      return
    }

    try {
      const productData: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name!,
        description: formData.description,
        category: formData.category!,
        image: formData.image,
        deliveryType: formData.deliveryType!,
        productType: formData.productType!,
        processingType: formData.processingType!,
        fields: formData.fields!,
        packages: formData.packages!,
        features: formData.features!,
        tags: formData.tags!,
        isActive: formData.isActive!,
        isFeatured: formData.isFeatured!,
        createdBy: undefined // TODO: Get from auth
      }

      let savedProduct: ProductTemplate

      if (isEditing && product) {
        savedProduct = await updateProduct(product.id, productData)
      } else {
        savedProduct = await createProduct(productData)
      }

      onSave(savedProduct)
    } catch (error) {
      console.error("Error saving product:", error)
      alert("حدث خطأ أثناء حفظ المنتج")
    } finally {
      setIsLoading(false)
    }
  }

  const addPackage = () => {
    const newPackage: ProductPackage = {
      id: Date.now().toString(),
      name: "",
      amount: "",
      price: 0,
      originalPrice: 0,
      discount: 0,
      description: "",
      popular: false,
      isActive: true,
      digitalCodes: []
    }
    setFormData((prev) => ({
      ...prev,
      packages: [...(prev.packages || []), newPackage],
    }))
  }

  const updatePackage = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      packages: prev.packages?.map((pkg, i) =>
        i === index ? { ...pkg, [field]: value } : pkg
      ) || [],
    }))
  }

  const removePackage = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      packages: prev.packages?.filter((_, i) => i !== index) || [],
    }))
  }

  const getPackageDigitalCodesText = (pkg: ProductPackage) => {
    return pkg.digitalCodes?.map(code => code.key).join('\n') || ''
  }

  const updatePackageDigitalCodes = (index: number, codesText: string) => {
    const codes = codesText
      .split('\n')
      .map(line => line.trim())
      .filter(Boolean)
      .map((key, i) => ({
        id: `${Date.now()}-${i}`,
        key,
        used: false,
        assignedToOrderId: null
      }))

    updatePackage(index, 'digitalCodes', codes)
  }

  const addCustomField = () => {
    const newField: DynamicField = {
      id: Date.now().toString(),
      type: "text",
      name: `field_${Date.now()}`,
      label: "",
      placeholder: "",
      required: false,
      isActive: true,
      validation: {}
    }
    setFormData((prev) => ({
      ...prev,
      fields: [...(prev.fields || []), newField],
    }))
  }

  const updateCustomField = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      fields: prev.fields?.map((f, i) =>
        i === index ? { ...f, [field]: value } : f
      ) || [],
    }))
  }

  const removeCustomField = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      fields: prev.fields?.filter((_, i) => i !== index) || [],
    }))
  }

  return (
    <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl">
      <div className="p-4 md:p-6 border-b border-gray-700/50">
        <div className="flex items-center justify-between">
          <h3 className="text-lg md:text-2xl font-bold text-white">
            {isEditing ? "تعديل المنتج" : "إضافة منتج جديد"}
          </h3>
          <button 
            onClick={onCancel} 
            className="text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors"
          >
            <X className="w-5 h-5 md:w-6 md:h-6" />
          </button>
        </div>
      </div>

      <div className="p-4 md:p-6 space-y-4 md:space-y-6">
        {/* Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div>
            <label className="block text-sm font-medium mb-2 text-white">اسم المنتج</label>
            <input
              type="text"
              value={formData.name || ""}
              onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
              placeholder="أدخل اسم المنتج"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2 text-white">الفئة</label>
            <input
              type="text"
              value={formData.category || ""}
              onChange={(e) => setFormData((prev) => ({ ...prev, category: e.target.value }))}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
              placeholder="مثل: MOBA, RPG, باتل رويال"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2 text-white">الوصف</label>
          <textarea
            value={formData.description || ""}
            onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
            rows={3}
            className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
            placeholder="وصف المنتج"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2 text-white">العلامات (مفصولة بفاصلة)</label>
          <input
            type="text"
            value={formData.tags?.join(", ") || ""}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                tags: e.target.value
                  .split(",")
                  .map((tag) => tag.trim())
                  .filter(Boolean),
              }))
            }
            className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
            placeholder="شائع, مميز, جديد"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2 text-white">صورة الغلاف</label>
          <div className="flex items-center space-x-3 space-x-reverse">
            <input
              type="text"
              value={formData.image || ""}
              onChange={(e) => setFormData((prev) => ({ ...prev, image: e.target.value }))}
              className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
              placeholder="رابط الصورة"
            />
            <button className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors">
              <Upload className="w-4 h-4" />
              <span>رفع</span>
            </button>
          </div>
        </div>

        {/* Status Toggles */}
        <div className="flex space-x-6 space-x-reverse">
          <label className="flex items-center space-x-2 space-x-reverse text-white">
            <input
              type="checkbox"
              checked={formData.isFeatured || false}
              onChange={(e) => setFormData((prev) => ({ ...prev, isFeatured: e.target.checked }))}
              className="rounded"
            />
            <span>منتج مميز</span>
          </label>
          <label className="flex items-center space-x-2 space-x-reverse text-white">
            <input
              type="checkbox"
              checked={formData.isActive ?? true}
              onChange={(e) => setFormData((prev) => ({ ...prev, isActive: e.target.checked }))}
              className="rounded"
            />
            <span>منتج نشط</span>
          </label>
        </div>

        {/* Packages */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-white">الحزم</h4>
            <button onClick={addPackage} className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors">
              <Plus className="w-4 h-4" />
              <span>إضافة حزمة</span>
            </button>
          </div>

          <div className="space-y-6">
            {formData.packages?.map((pkg, index) => (
              <div
                key={index}
                className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-6 border border-gray-600/50"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-4">
                  <input
                    type="text"
                    value={pkg.name}
                    onChange={(e) => updatePackage(index, "name", e.target.value)}
                    placeholder="اسم الحزمة"
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  />
                  <input
                    type="number"
                    step="0.01"
                    value={pkg.price}
                    onChange={(e) => updatePackage(index, "price", Number(e.target.value))}
                    placeholder="السعر"
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  />
                  <input
                    type="number"
                    step="0.01"
                    value={pkg.originalPrice || ""}
                    onChange={(e) => updatePackage(index, "originalPrice", Number(e.target.value) || undefined)}
                    placeholder="السعر الأصلي"
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  />
                  <input
                    type="number"
                    value={pkg.discount || ""}
                    onChange={(e) => updatePackage(index, "discount", Number(e.target.value) || undefined)}
                    placeholder="نسبة الخصم (%)"
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 mb-4">
                  <input
                    type="text"
                    value={pkg.amount}
                    onChange={(e) => updatePackage(index, "amount", e.target.value)}
                    placeholder="الكمية (مثل: 60 يوسي)"
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  />
                  <textarea
                    value={pkg.description || ""}
                    onChange={(e) => updatePackage(index, "description", e.target.value)}
                    placeholder="وصف الحزمة (اختياري)"
                    rows={2}
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none text-white"
                  />
                </div>

                {/* Digital Codes Section */}
                <div className="border-t border-gray-600/50 pt-4">
                  <div className="flex items-center space-x-2 space-x-reverse mb-3">
                    <Key className="w-5 h-5 text-blue-400" />
                    <h5 className="font-semibold text-blue-400">الأكواد الرقمية</h5>
                    <span className="text-sm text-gray-400">(اختياري)</span>
                  </div>

                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3">
                    <p className="text-sm text-blue-300 mb-2">💡 إرشادات الأكواد الرقمية:</p>
                    <ul className="text-xs text-blue-200 space-y-1">
                      <li>• أدخل كود واحد في كل سطر</li>
                      <li>• سيتم تخصيص كود واحد فقط لكل طلب</li>
                      <li>• الأكواد المستخدمة لن تظهر للمشترين الآخرين</li>
                      <li>• إذا نفدت الأكواد، ستصبح الحزمة غير متاحة</li>
                    </ul>
                  </div>

                  <textarea
                    value={getPackageDigitalCodesText(pkg)}
                    onChange={(e) => updatePackageDigitalCodes(index, e.target.value)}
                    placeholder="أدخل الأكواد الرقمية (كود واحد في كل سطر)&#10;مثال:&#10;AB12-XY34-ZZ78&#10;CD56-PL90-QW12&#10;9GHT-LMK3-992Z"
                    rows={4}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 font-mono text-sm resize-none text-white"
                  />

                  {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                    <div className="mt-2 text-sm text-green-400">
                      ✅ تم إضافة {pkg.digitalCodes.length} كود رقمي
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-end mt-4">
                  <button
                    onClick={() => removePackage(index)}
                    className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-all duration-300"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Custom Fields */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-white">الحقول المخصصة</h4>
            <button
              onClick={addCustomField}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>إضافة حقل</span>
            </button>
          </div>

          <div className="space-y-4">
            {formData.fields?.map((field, index) => (
              <div
                key={index}
                className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-4">
                  <input
                    type="text"
                    value={field.label}
                    onChange={(e) => updateCustomField(index, "label", e.target.value)}
                    placeholder="تسمية الحقل"
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  />
                  <select
                    value={field.type}
                    onChange={(e) => updateCustomField(index, "type", e.target.value)}
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  >
                    <option value="text">نص</option>
                    <option value="email">بريد إلكتروني</option>
                    <option value="number">رقم</option>
                  </select>
                  <input
                    type="text"
                    value={field.placeholder}
                    onChange={(e) => updateCustomField(index, "placeholder", e.target.value)}
                    placeholder="النص التوضيحي"
                    className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="flex items-center space-x-2 space-x-reverse text-white">
                    <input
                      type="checkbox"
                      checked={field.required}
                      onChange={(e) => updateCustomField(index, "required", e.target.checked)}
                      className="rounded"
                    />
                    <span>مطلوب</span>
                  </label>
                  <button
                    onClick={() => removeCustomField(index)}
                    className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-all duration-300"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50">
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <span>{isEditing ? "تحديث المنتج" : "إضافة المنتج"}</span>
            )}
          </button>
          <button
            onClick={onCancel}
            disabled={isLoading}
            className="flex-1 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            إلغاء
          </button>
        </div>
      </div>
    </div>
  )
}
