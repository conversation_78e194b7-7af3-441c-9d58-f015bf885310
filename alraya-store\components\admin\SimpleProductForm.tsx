"use client"

import { useState, useRef, useEffect } from "react"
import { Plus, Edit, Trash2, X, Upload, Key, AlertCircle, Package, Type, Camera, Crop } from "lucide-react"
import { ProductTemplate, ProductPackage, DynamicField } from "@/lib/types"
import { createProduct, updateProduct } from "@/lib/services/productService"
import { getCategories } from "@/lib/services/categoryService"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

interface SimpleProductFormProps {
  product?: ProductTemplate
  onSave: (product: ProductTemplate) => void
  onCancel: () => void
  isEditing?: boolean
}

export default function SimpleProductForm({ product, onSave, onCancel, isEditing = false }: SimpleProductFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isPackageDialogOpen, setIsPackageDialogOpen] = useState(false)
  const [isFieldDialogOpen, setIsFieldDialogOpen] = useState(false)
  const [editingPackageIndex, setEditingPackageIndex] = useState<number | null>(null)
  const [editingFieldIndex, setEditingFieldIndex] = useState<number | null>(null)

  // Image upload and cropping state
  const [isImageCropDialogOpen, setIsImageCropDialogOpen] = useState(false)
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [croppedImage, setCroppedImage] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState<Partial<ProductTemplate>>({
    name: product?.name || "",
    description: product?.description || "",
    category: product?.category || "",
    tags: product?.tags || [],
    image: product?.image || "",
    packages: product?.packages || [],
    fields: product?.fields || [],
    features: product?.features || [],
    isActive: product?.isActive ?? true,
    isFeatured: product?.isFeatured || false,
    deliveryType: product?.deliveryType || "code_based",
    productType: product?.productType || "digital",
    processingType: product?.processingType || "instant",
  })

  // Initialize cropped image if editing existing product
  useEffect(() => {
    if (product?.image) {
      setCroppedImage(product.image)
    }
  }, [product?.image])

  // Package dialog form state
  const [packageForm, setPackageForm] = useState({
    name: "",
    amount: "",
    price: 0,
    originalPrice: 0,
    discount: 0,
    description: "",
    popular: false,
    digitalCodes: ""
  })

  // Field dialog form state
  const [fieldForm, setFieldForm] = useState({
    label: "",
    type: "text" as "text" | "email" | "number",
    placeholder: "",
    required: false
  })

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "",
      tags: [],
      image: "",
      packages: [],
      fields: [],
      features: [],
      isActive: true,
      isFeatured: false,
      deliveryType: "code_based",
      productType: "digital",
      processingType: "instant",
    })
    setCroppedImage(null)
    setImagePreview(null)
    setSelectedImageFile(null)
  }

  // Image upload functions
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.type.startsWith('image/')) {
        setSelectedImageFile(file)
        const reader = new FileReader()
        reader.onload = (e) => {
          setImagePreview(e.target?.result as string)
          setIsImageCropDialogOpen(true)
        }
        reader.readAsDataURL(file)
      } else {
        alert('يرجى اختيار ملف صورة صحيح')
      }
    }
  }

  const handleImageCrop = (croppedImageData: string) => {
    setCroppedImage(croppedImageData)
    setFormData(prev => ({ ...prev, image: croppedImageData }))
    setIsImageCropDialogOpen(false)
  }

  const removeImage = () => {
    setCroppedImage(null)
    setImagePreview(null)
    setSelectedImageFile(null)
    setFormData(prev => ({ ...prev, image: "" }))
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const openImageSelector = () => {
    fileInputRef.current?.click()
  }

  const handleSave = async () => {
    setIsLoading(true)

    // Basic validation
    if (!formData.name?.trim()) {
      alert("يرجى إدخال اسم المنتج")
      setIsLoading(false)
      return
    }

    if (!formData.category?.trim()) {
      alert("يرجى إدخال فئة المنتج")
      setIsLoading(false)
      return
    }

    if (!formData.packages || formData.packages.length === 0) {
      alert("يرجى إضافة حزمة واحدة على الأقل")
      setIsLoading(false)
      return
    }

    try {
      const productData: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name!,
        description: formData.description,
        category: formData.category!,
        image: formData.image,
        deliveryType: formData.deliveryType!,
        productType: formData.productType!,
        processingType: formData.processingType!,
        fields: formData.fields!,
        packages: formData.packages!,
        features: formData.features!,
        tags: formData.tags!,
        isActive: formData.isActive!,
        isFeatured: formData.isFeatured!,
        createdBy: undefined // TODO: Get from auth
      }

      let savedProduct: ProductTemplate

      if (isEditing && product) {
        savedProduct = await updateProduct(product.id, productData)
      } else {
        savedProduct = await createProduct(productData)
      }

      onSave(savedProduct)
    } catch (error) {
      console.error("Error saving product:", error)
      alert("حدث خطأ أثناء حفظ المنتج")
    } finally {
      setIsLoading(false)
    }
  }

  // Reset package form
  const resetPackageForm = () => {
    setPackageForm({
      name: "",
      amount: "",
      price: 0,
      originalPrice: 0,
      discount: 0,
      description: "",
      popular: false,
      digitalCodes: ""
    })
  }

  // Reset field form
  const resetFieldForm = () => {
    setFieldForm({
      label: "",
      type: "text",
      placeholder: "",
      required: false
    })
  }

  // Open package dialog for creating new package
  const openPackageDialog = () => {
    resetPackageForm()
    setEditingPackageIndex(null)
    setIsPackageDialogOpen(true)
  }

  // Open package dialog for editing existing package
  const editPackage = (index: number) => {
    const pkg = formData.packages![index]
    setPackageForm({
      name: pkg.name,
      amount: pkg.amount,
      price: pkg.price,
      originalPrice: pkg.originalPrice || 0,
      discount: pkg.discount || 0,
      description: pkg.description || "",
      popular: pkg.popular || false,
      digitalCodes: pkg.digitalCodes?.map(code => code.key).join('\n') || ""
    })
    setEditingPackageIndex(index)
    setIsPackageDialogOpen(true)
  }

  // Save package from dialog
  const savePackage = () => {
    if (!packageForm.name.trim()) {
      alert("يرجى إدخال اسم الحزمة")
      return
    }

    if (packageForm.price <= 0) {
      alert("يرجى إدخال سعر صحيح")
      return
    }

    // Process digital codes
    const digitalCodes = packageForm.digitalCodes
      .split('\n')
      .map(line => line.trim())
      .filter(Boolean)
      .map((key, i) => ({
        id: `${Date.now()}-${i}`,
        key,
        used: false,
        assignedToOrderId: null
      }))

    const newPackage: ProductPackage = {
      id: editingPackageIndex !== null ? formData.packages![editingPackageIndex].id : Date.now().toString(),
      name: packageForm.name,
      amount: packageForm.amount,
      price: packageForm.price,
      originalPrice: packageForm.originalPrice || undefined,
      discount: packageForm.discount || undefined,
      description: packageForm.description || undefined,
      popular: packageForm.popular,
      isActive: true,
      digitalCodes
    }

    setFormData((prev) => {
      const packages = [...(prev.packages || [])]
      if (editingPackageIndex !== null) {
        packages[editingPackageIndex] = newPackage
      } else {
        packages.push(newPackage)
      }
      return { ...prev, packages }
    })

    setIsPackageDialogOpen(false)
    resetPackageForm()
  }

  // Remove package
  const removePackage = (index: number) => {
    if (confirm("هل أنت متأكد من حذف هذه الحزمة؟")) {
      setFormData((prev) => ({
        ...prev,
        packages: prev.packages?.filter((_, i) => i !== index) || [],
      }))
    }
  }

  // Open field dialog for creating new field
  const openFieldDialog = () => {
    resetFieldForm()
    setEditingFieldIndex(null)
    setIsFieldDialogOpen(true)
  }

  // Open field dialog for editing existing field
  const editField = (index: number) => {
    const field = formData.fields![index]
    setFieldForm({
      label: field.label,
      type: field.type as "text" | "email" | "number",
      placeholder: field.placeholder || "",
      required: field.required
    })
    setEditingFieldIndex(index)
    setIsFieldDialogOpen(true)
  }

  // Save field from dialog
  const saveField = () => {
    if (!fieldForm.label.trim()) {
      alert("يرجى إدخال تسمية الحقل")
      return
    }

    const newField: DynamicField = {
      id: editingFieldIndex !== null ? formData.fields![editingFieldIndex].id : Date.now().toString(),
      type: fieldForm.type,
      name: editingFieldIndex !== null ? formData.fields![editingFieldIndex].name : `field_${Date.now()}`,
      label: fieldForm.label,
      placeholder: fieldForm.placeholder,
      required: fieldForm.required,
      isActive: true,
      validation: {}
    }

    setFormData((prev) => {
      const fields = [...(prev.fields || [])]
      if (editingFieldIndex !== null) {
        fields[editingFieldIndex] = newField
      } else {
        fields.push(newField)
      }
      return { ...prev, fields }
    })

    setIsFieldDialogOpen(false)
    resetFieldForm()
  }

  // Remove field
  const removeField = (index: number) => {
    if (confirm("هل أنت متأكد من حذف هذا الحقل؟")) {
      setFormData((prev) => ({
        ...prev,
        fields: prev.fields?.filter((_, i) => i !== index) || [],
      }))
    }
  }

  return (
    <div className="bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl">
      <div className="p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl">
              <Package className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-white">
                {isEditing ? "تعديل المنتج" : "إنشاء منتج جديد"}
              </h3>
              <p className="text-gray-400 text-sm">
                {isEditing ? "قم بتحديث معلومات المنتج" : "أضف منتج جديد إلى المتجر"}
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onCancel}
            className="border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white"
          >
            <X className="w-4 h-4 mr-2" />
            إلغاء
          </Button>
        </div>
      </div>

      <div className="p-6 md:p-8 space-y-8">
        {/* Basic Info */}
        <div className="bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg">
              <Package className="w-5 h-5 text-white" />
            </div>
            المعلومات الأساسية
          </h4>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-3 text-white">اسم المنتج *</label>
                <input
                  type="text"
                  value={formData.name || ""}
                  onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400"
                  placeholder="أدخل اسم المنتج"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-3 text-white">الفئة *</label>
                <input
                  type="text"
                  value={formData.category || ""}
                  onChange={(e) => setFormData((prev) => ({ ...prev, category: e.target.value }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400"
                  placeholder="مثل: MOBA, RPG, باتل رويال"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-3 text-white">الوصف</label>
              <textarea
                value={formData.description || ""}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none"
                placeholder="وصف المنتج"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-3 text-white">العلامات</label>
              <input
                type="text"
                value={formData.tags?.join(", ") || ""}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    tags: e.target.value
                      .split(",")
                      .map((tag) => tag.trim())
                      .filter(Boolean),
                  }))
                }
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400"
                placeholder="شائع, مميز, جديد (مفصولة بفاصلة)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-3 text-white">صورة الغلاف</label>

              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageSelect}
                className="hidden"
              />

              {croppedImage || formData.image ? (
                <div className="space-y-4">
                  {/* Image Preview */}
                  <div className="relative group">
                    <img
                      src={croppedImage || formData.image}
                      alt="صورة المنتج"
                      className="w-full h-48 object-cover rounded-xl border border-gray-600/50"
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-xl flex items-center justify-center gap-3">
                      <Button
                        type="button"
                        size="sm"
                        onClick={openImageSelector}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Camera className="w-4 h-4 mr-2" />
                        تغيير
                      </Button>
                      <Button
                        type="button"
                        size="sm"
                        variant="destructive"
                        onClick={removeImage}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        حذف
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  onClick={openImageSelector}
                  className="border-2 border-dashed border-gray-600 rounded-xl p-8 text-center cursor-pointer hover:border-purple-500 hover:bg-purple-500/5 transition-all duration-300"
                >
                  <div className="flex flex-col items-center gap-4">
                    <div className="p-4 bg-gray-700/50 rounded-full">
                      <Camera className="w-8 h-8 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-white font-medium mb-1">اختر صورة الغلاف</p>
                      <p className="text-gray-400 text-sm">PNG, JPG, GIF حتى 10MB</p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      className="border-purple-600 text-purple-400 hover:bg-purple-600/10"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      رفع صورة
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Status Toggles */}
            <div className="flex flex-wrap gap-6 pt-4 border-t border-gray-600/30">
              <label className="flex items-center gap-3 text-white cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.isFeatured || false}
                  onChange={(e) => setFormData((prev) => ({ ...prev, isFeatured: e.target.checked }))}
                  className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                />
                <span>منتج مميز</span>
              </label>
              <label className="flex items-center gap-3 text-white cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.isActive ?? true}
                  onChange={(e) => setFormData((prev) => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                />
                <span>منتج نشط</span>
              </label>
            </div>
          </div>
        </div>

        {/* Packages */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Package className="w-6 h-6 text-purple-400" />
              <h4 className="text-xl font-semibold text-white">الحزم</h4>
              <span className="text-sm text-gray-400">({formData.packages?.length || 0})</span>
            </div>
            <Button
              onClick={openPackageDialog}
              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              إضافة حزمة
            </Button>
          </div>

          {formData.packages && formData.packages.length > 0 ? (
            <div className="grid gap-4">
              {formData.packages.map((pkg, index) => (
                <div
                  key={pkg.id}
                  className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h5 className="text-lg font-semibold text-white mb-1">{pkg.name}</h5>
                      <p className="text-purple-300 font-bold text-xl">${pkg.price}</p>
                      {pkg.amount && (
                        <p className="text-gray-300 text-sm">{pkg.amount}</p>
                      )}
                      {pkg.description && (
                        <p className="text-gray-400 text-sm mt-2">{pkg.description}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {pkg.popular && (
                        <span className="bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs">
                          شائع
                        </span>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => editPackage(index)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        تعديل
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removePackage(index)}
                        className="border-red-600 text-red-400 hover:bg-red-600/10"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>

                  {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                    <div className="flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                      <Key className="w-4 h-4 text-blue-400" />
                      <span className="text-blue-300 text-sm">
                        {pkg.digitalCodes.length} كود رقمي متاح
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600">
              <Package className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">لم يتم إضافة أي حزم بعد</p>
              <Button
                onClick={openPackageDialog}
                variant="outline"
                className="border-purple-600 text-purple-400 hover:bg-purple-600/10"
              >
                <Plus className="w-4 h-4 mr-2" />
                إضافة أول حزمة
              </Button>
            </div>
          )}
        </div>

        {/* Custom Fields */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Type className="w-6 h-6 text-blue-400" />
              <h4 className="text-xl font-semibold text-white">الحقول المخصصة</h4>
              <span className="text-sm text-gray-400">({formData.fields?.length || 0})</span>
            </div>
            <Button
              onClick={openFieldDialog}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              إضافة حقل
            </Button>
          </div>

          {formData.fields && formData.fields.length > 0 ? (
            <div className="grid gap-4">
              {formData.fields.map((field, index) => (
                <div
                  key={field.id}
                  className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h5 className="text-lg font-semibold text-white mb-1">{field.label}</h5>
                      <div className="flex items-center gap-4 text-sm text-gray-400">
                        <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">
                          {field.type === "text" ? "نص" : field.type === "email" ? "بريد إلكتروني" : "رقم"}
                        </span>
                        {field.required && (
                          <span className="bg-red-500/20 text-red-300 px-2 py-1 rounded-full">
                            مطلوب
                          </span>
                        )}
                      </div>
                      {field.placeholder && (
                        <p className="text-gray-400 text-sm mt-2">"{field.placeholder}"</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => editField(index)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        تعديل
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeField(index)}
                        className="border-red-600 text-red-400 hover:bg-red-600/10"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600">
              <Type className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">لم يتم إضافة أي حقول مخصصة بعد</p>
              <Button
                onClick={openFieldDialog}
                variant="outline"
                className="border-blue-600 text-blue-400 hover:bg-blue-600/10"
              >
                <Plus className="w-4 h-4 mr-2" />
                إضافة أول حقل
              </Button>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30">
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg"
            size="lg"
          >
            {isLoading ? (
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>جاري الحفظ...</span>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Package className="w-5 h-5" />
                <span>{isEditing ? "تحديث المنتج" : "إنشاء المنتج"}</span>
              </div>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg"
            size="lg"
          >
            إلغاء
          </Button>
        </div>
      </div>

      {/* Package Dialog */}
      <Dialog open={isPackageDialogOpen} onOpenChange={setIsPackageDialogOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              <Package className="w-5 h-5 text-purple-400" />
              {editingPackageIndex !== null ? "تعديل الحزمة" : "إضافة حزمة جديدة"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Basic Package Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">اسم الحزمة *</label>
                <input
                  type="text"
                  value={packageForm.name}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="مثل: 60 يوسي"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الكمية</label>
                <input
                  type="text"
                  value={packageForm.amount}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, amount: e.target.value }))}
                  placeholder="مثل: 60 يوسي"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>

            {/* Pricing */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">السعر *</label>
                <input
                  type="number"
                  step="0.01"
                  value={packageForm.price}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, price: Number(e.target.value) }))}
                  placeholder="0.00"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">السعر الأصلي</label>
                <input
                  type="number"
                  step="0.01"
                  value={packageForm.originalPrice}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, originalPrice: Number(e.target.value) }))}
                  placeholder="0.00"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">نسبة الخصم (%)</label>
                <input
                  type="number"
                  value={packageForm.discount}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, discount: Number(e.target.value) }))}
                  placeholder="0"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium mb-2">الوصف</label>
              <textarea
                value={packageForm.description}
                onChange={(e) => setPackageForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف الحزمة (اختياري)"
                rows={3}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none"
              />
            </div>

            {/* Digital Codes */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Key className="w-5 h-5 text-blue-400" />
                <label className="block text-sm font-medium">الأكواد الرقمية</label>
                <span className="text-xs text-gray-400">(اختياري)</span>
              </div>

              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3">
                <p className="text-sm text-blue-300 mb-2">💡 إرشادات:</p>
                <ul className="text-xs text-blue-200 space-y-1">
                  <li>• أدخل كود واحد في كل سطر</li>
                  <li>• سيتم تخصيص كود واحد فقط لكل طلب</li>
                  <li>• الأكواد المستخدمة لن تظهر للمشترين الآخرين</li>
                </ul>
              </div>

              <textarea
                value={packageForm.digitalCodes}
                onChange={(e) => setPackageForm(prev => ({ ...prev, digitalCodes: e.target.value }))}
                placeholder="أدخل الأكواد الرقمية (كود واحد في كل سطر)&#10;مثال:&#10;AB12-XY34-ZZ78&#10;CD56-PL90-QW12"
                rows={6}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none"
              />
            </div>

            {/* Options */}
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-white">
                <input
                  type="checkbox"
                  checked={packageForm.popular}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, popular: e.target.checked }))}
                  className="rounded"
                />
                <span>حزمة شائعة</span>
              </label>
            </div>
          </div>

          {/* Dialog Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={() => setIsPackageDialogOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              إلغاء
            </Button>
            <Button
              onClick={savePackage}
              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800"
            >
              {editingPackageIndex !== null ? "تحديث الحزمة" : "إضافة الحزمة"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Field Dialog */}
      <Dialog open={isFieldDialogOpen} onOpenChange={setIsFieldDialogOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              <Type className="w-5 h-5 text-blue-400" />
              {editingFieldIndex !== null ? "تعديل الحقل" : "إضافة حقل جديد"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Field Label */}
            <div>
              <label className="block text-sm font-medium mb-2">تسمية الحقل *</label>
              <input
                type="text"
                value={fieldForm.label}
                onChange={(e) => setFieldForm(prev => ({ ...prev, label: e.target.value }))}
                placeholder="مثل: اسم المستخدم"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
              />
            </div>

            {/* Field Type */}
            <div>
              <label className="block text-sm font-medium mb-2">نوع الحقل</label>
              <select
                value={fieldForm.type}
                onChange={(e) => setFieldForm(prev => ({ ...prev, type: e.target.value as "text" | "email" | "number" }))}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
              >
                <option value="text">نص</option>
                <option value="email">بريد إلكتروني</option>
                <option value="number">رقم</option>
              </select>
            </div>

            {/* Placeholder */}
            <div>
              <label className="block text-sm font-medium mb-2">النص التوضيحي</label>
              <input
                type="text"
                value={fieldForm.placeholder}
                onChange={(e) => setFieldForm(prev => ({ ...prev, placeholder: e.target.value }))}
                placeholder="مثل: أدخل اسم المستخدم"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
              />
            </div>

            {/* Required */}
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="field-required"
                checked={fieldForm.required}
                onChange={(e) => setFieldForm(prev => ({ ...prev, required: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="field-required" className="text-white">
                حقل مطلوب
              </label>
            </div>
          </div>

          {/* Dialog Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={() => setIsFieldDialogOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              إلغاء
            </Button>
            <Button
              onClick={saveField}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              {editingFieldIndex !== null ? "تحديث الحقل" : "إضافة الحقل"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Crop Dialog */}
      <Dialog open={isImageCropDialogOpen} onOpenChange={setIsImageCropDialogOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              <Crop className="w-5 h-5 text-green-400" />
              قص وتعديل الصورة
            </DialogTitle>
          </DialogHeader>

          {imagePreview && (
            <ImageCropper
              imageSrc={imagePreview}
              onCrop={handleImageCrop}
              onCancel={() => setIsImageCropDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Simple Image Cropper Component
interface ImageCropperProps {
  imageSrc: string
  onCrop: (croppedImage: string) => void
  onCancel: () => void
}

function ImageCropper({ imageSrc, onCrop, onCancel }: ImageCropperProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [cropArea, setCropArea] = useState({ x: 0, y: 0, width: 300, height: 200 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imageLoaded, setImageLoaded] = useState(false)

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    const rect = canvasRef.current?.getBoundingClientRect()
    if (rect) {
      setDragStart({
        x: e.clientX - rect.left - cropArea.x,
        y: e.clientY - rect.top - cropArea.y
      })
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !canvasRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()
    const newX = Math.max(0, Math.min(e.clientX - rect.left - dragStart.x, rect.width - cropArea.width))
    const newY = Math.max(0, Math.min(e.clientY - rect.top - dragStart.y, rect.height - cropArea.height))

    setCropArea(prev => ({ ...prev, x: newX, y: newY }))
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleCrop = () => {
    const canvas = canvasRef.current
    const image = imageRef.current
    if (!canvas || !image) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size to crop area
    canvas.width = cropArea.width
    canvas.height = cropArea.height

    // Calculate scale factors
    const scaleX = image.naturalWidth / image.width
    const scaleY = image.naturalHeight / image.height

    // Draw cropped image
    ctx.drawImage(
      image,
      cropArea.x * scaleX,
      cropArea.y * scaleY,
      cropArea.width * scaleX,
      cropArea.height * scaleY,
      0,
      0,
      cropArea.width,
      cropArea.height
    )

    // Convert to base64
    const croppedImageData = canvas.toDataURL('image/jpeg', 0.8)
    onCrop(croppedImageData)
  }

  return (
    <div className="space-y-6 py-4">
      <div className="text-center">
        <p className="text-gray-300 mb-4">اسحب المربع الأخضر لتحديد منطقة القص</p>

        <div
          ref={containerRef}
          className="relative inline-block bg-gray-900 rounded-lg overflow-hidden"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <img
            ref={imageRef}
            src={imageSrc}
            alt="صورة للقص"
            className="max-w-full max-h-96 object-contain block"
            onLoad={() => {
              if (imageRef.current) {
                const { width, height } = imageRef.current
                setCropArea({
                  x: Math.max(0, (width - 300) / 2),
                  y: Math.max(0, (height - 200) / 2),
                  width: Math.min(300, width),
                  height: Math.min(200, height)
                })
                setImageLoaded(true)
              }
            }}
          />

          {/* Dark overlay */}
          {imageLoaded && (
            <div className="absolute inset-0 bg-black/50 pointer-events-none">
              {/* Clear area for crop */}
              <div
                className="absolute bg-transparent"
                style={{
                  left: cropArea.x,
                  top: cropArea.y,
                  width: cropArea.width,
                  height: cropArea.height,
                  boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)'
                }}
              />
            </div>
          )}

          {/* Crop selection box */}
          {imageLoaded && (
            <div
              className="absolute border-2 border-green-400 cursor-move select-none"
              style={{
                left: cropArea.x,
                top: cropArea.y,
                width: cropArea.width,
                height: cropArea.height
              }}
              onMouseDown={handleMouseDown}
            >
              <div className="absolute inset-0 border border-dashed border-green-300/70"></div>

              {/* Corner handles */}
              <div className="absolute -top-1 -left-1 w-3 h-3 bg-green-400 border border-white rounded-full"></div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 border border-white rounded-full"></div>
              <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-green-400 border border-white rounded-full"></div>
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border border-white rounded-full"></div>

              {/* Center indicator */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-green-400 rounded-full"></div>
            </div>
          )}
        </div>
      </div>

      <canvas ref={canvasRef} className="hidden" />

      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <p className="text-blue-300 text-sm mb-2">💡 إرشادات القص:</p>
        <ul className="text-blue-200 text-xs space-y-1">
          <li>• اسحب المربع الأخضر لتحديد منطقة القص</li>
          <li>• يمكنك تحريك منطقة القص بالسحب</li>
          <li>• الصورة ستُحفظ بجودة عالية</li>
        </ul>
      </div>

      <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
        <Button
          variant="outline"
          onClick={onCancel}
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          إلغاء
        </Button>
        <Button
          onClick={handleCrop}
          className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
        >
          <Crop className="w-4 h-4 mr-2" />
          قص الصورة
        </Button>
      </div>
    </div>
  )
}
