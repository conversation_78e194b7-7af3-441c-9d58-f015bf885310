"use client";
import React, { lazy, Suspense, useEffect, useState } from "react";
import { cn } from "@/lib/utils";

// Lazy load the Boxes component to improve initial load time
const LazyBoxes = lazy(() => import("./background-boxes").then(mod => ({ default: mod.Boxes })));

export function BackgroundBoxesDemo() {
  // Only render the boxes after the component has mounted
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    // Set a short delay before showing boxes for smoother loading
    const timer = setTimeout(() => {
      setIsClient(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="h-full w-full absolute inset-0 overflow-hidden bg-black">
      {/* Gradient overlay for better visibility of content */}
      <div className="absolute inset-0 w-full h-full bg-black z-20 [mask-image:radial-gradient(transparent,white)] pointer-events-none opacity-70" />
      
      {/* Background boxes container - only render when needed */}
      {isClient && (
        <div className="absolute inset-0 w-[200vw] h-[200vh] left-[-50vw] top-[-50vh]">
          <Suspense fallback={null}>
            <LazyBoxes className="w-full h-full" />
          </Suspense>
        </div>
      )}
    </div>
  );
} 