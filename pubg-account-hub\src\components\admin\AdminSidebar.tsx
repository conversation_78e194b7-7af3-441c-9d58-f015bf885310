import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  LayoutDashboard, 
  Users, 
  Package, 
  FileText, 
  Bell, 
  Settings,
  LogOut,
  Info
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const AdminSidebar: React.FC = () => {
  const location = useLocation();
  const { logout } = useAuth();
  
  const navigationItems = [
    { name: "لوحة التحكم", href: "/admin", icon: LayoutDashboard },
    { name: "الحسابات", href: "/admin/accounts", icon: Users },
    { name: "باقات الشحن", href: "/admin/packages", icon: Package },
    { name: "المقالات", href: "/admin/blog", icon: FileText },
    { name: "الإشعارات", href: "/admin/notifications", icon: Bell },
    { name: "صفحة من نحن", href: "/admin/about", icon: Info },
    { name: "الإعدادات", href: "/admin/settings", icon: Settings },
  ];

  return (
    <div className="h-screen w-64 bg-background border-l border-border flex flex-col">
      <div className="p-4 border-b border-border">
        <img src="/logo.svg" alt="Logo" className="h-8" />
      </div>
      <ScrollArea className="flex-1">
        <nav className="p-2">
          {navigationItems.map((item) => {
            const isActive = location.pathname === item.href || 
                            (item.href !== '/admin' && location.pathname.startsWith(item.href));
            
            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-foreground my-1",
                  isActive && "bg-muted text-foreground font-medium"
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </ScrollArea>
      <div className="p-4 border-t border-border">
        <Button 
          variant="outline" 
          className="w-full justify-start text-muted-foreground hover:text-foreground"
          onClick={logout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          تسجيل الخروج
        </Button>
      </div>
    </div>
  );
};

export default AdminSidebar; 