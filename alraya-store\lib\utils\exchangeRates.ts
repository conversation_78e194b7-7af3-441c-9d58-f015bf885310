"use client"

import {
  Currency,
  ExchangeRate,
  ExchangeRateResponse,
  ExchangeRateUpdateRequest,
  ExchangeRateUpdateResponse,
  ExchangeRateValidation,
  CurrencySystemHealth
} from "@/lib/types"
import { validateExchangeRate } from "./currency"

// =====================================================
// EXCHANGE RATE MANAGEMENT
// =====================================================

/**
 * Exchange rate cache for performance
 */
class ExchangeRateCache {
  private cache = new Map<string, { rate: number; timestamp: Date }>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  set(fromCurrency: Currency, toCurrency: Currency, rate: number): void {
    const key = `${fromCurrency}-${toCurrency}`
    this.cache.set(key, { rate, timestamp: new Date() })
  }

  get(fromCurrency: Currency, toCurrency: Currency): number | null {
    const key = `${fromCurrency}-${toCurrency}`
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    const isExpired = Date.now() - cached.timestamp.getTime() > this.CACHE_DURATION
    if (isExpired) {
      this.cache.delete(key)
      return null
    }
    
    return cached.rate
  }

  clear(): void {
    this.cache.clear()
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

const exchangeRateCache = new ExchangeRateCache()

/**
 * Get current exchange rate between two currencies
 */
export async function getExchangeRate(
  fromCurrency: Currency,
  toCurrency: Currency,
  useCache: boolean = true
): Promise<number> {
  // Same currency
  if (fromCurrency === toCurrency) {
    return 1.0
  }

  // Check cache first
  if (useCache) {
    const cachedRate = exchangeRateCache.get(fromCurrency, toCurrency)
    if (cachedRate !== null) {
      return cachedRate
    }
  }

  try {
    // In production, this would call Supabase function or external API
    const rate = await fetchExchangeRateFromDatabase(fromCurrency, toCurrency)
    
    // Cache the result
    if (useCache) {
      exchangeRateCache.set(fromCurrency, toCurrency, rate)
    }
    
    return rate
  } catch (error) {
    console.error(`Failed to get exchange rate for ${fromCurrency} to ${toCurrency}:`, error)
    throw new Error(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`)
  }
}

/**
 * Get multiple exchange rates at once
 */
export async function getMultipleExchangeRates(
  baseCurrency: Currency,
  targetCurrencies: Currency[]
): Promise<Record<Currency, number>> {
  const rates: Record<Currency, number> = {}
  
  const promises = targetCurrencies.map(async (currency) => {
    try {
      const rate = await getExchangeRate(baseCurrency, currency)
      rates[currency] = rate
    } catch (error) {
      console.warn(`Failed to get rate for ${baseCurrency} to ${currency}:`, error)
      // Don't include failed rates in result
    }
  })
  
  await Promise.allSettled(promises)
  return rates
}

/**
 * Convert amount using exchange rate
 */
export async function convertCurrency(
  amount: number,
  fromCurrency: Currency,
  toCurrency: Currency
): Promise<{ convertedAmount: number; exchangeRate: number; timestamp: Date }> {
  const exchangeRate = await getExchangeRate(fromCurrency, toCurrency)
  const convertedAmount = amount * exchangeRate
  
  return {
    convertedAmount,
    exchangeRate,
    timestamp: new Date()
  }
}

/**
 * Fetch exchange rate from database (placeholder)
 * TODO: Replace with actual Supabase function call
 */
async function fetchExchangeRateFromDatabase(
  fromCurrency: Currency,
  toCurrency: Currency
): Promise<number> {
  // Mock implementation - replace with actual database call
  const mockRates: Record<string, Record<string, number>> = {
    'USD': {
      'USD': 1.0,
      'SDG': 450.0,
      'EGP': 30.8,
      'SAR': 3.75,
      'AED': 3.67,
      'EUR': 0.85,
      'GBP': 0.73
    },
    'SDG': {
      'USD': 1/450.0,
      'SDG': 1.0,
      'EGP': 30.8/450.0
    },
    'EGP': {
      'USD': 1/30.8,
      'EGP': 1.0,
      'SDG': 450.0/30.8
    }
  }
  
  const rate = mockRates[fromCurrency]?.[toCurrency]
  if (rate === undefined) {
    // Try to calculate via USD
    const usdFromRate = mockRates['USD']?.[fromCurrency]
    const usdToRate = mockRates['USD']?.[toCurrency]
    
    if (usdFromRate && usdToRate) {
      return usdToRate / usdFromRate
    }
    
    throw new Error(`Exchange rate not found for ${fromCurrency} to ${toCurrency}`)
  }
  
  return rate
}

// =====================================================
// EXCHANGE RATE UPDATES
// =====================================================

/**
 * Update exchange rates
 */
export async function updateExchangeRates(
  request: ExchangeRateUpdateRequest
): Promise<ExchangeRateUpdateResponse> {
  const updatedRates: ExchangeRate[] = []
  const errors: string[] = []
  
  for (const rateUpdate of request.rates) {
    try {
      // Validate the rate
      const validation = validateExchangeRate(
        rateUpdate.rate,
        rateUpdate.fromCurrency,
        rateUpdate.toCurrency
      )
      
      if (!validation.isValid) {
        errors.push(`Invalid rate for ${rateUpdate.fromCurrency} to ${rateUpdate.toCurrency}: ${validation.errors.join(', ')}`)
        continue
      }
      
      // In production, this would update the database
      const updatedRate = await updateExchangeRateInDatabase(rateUpdate)
      updatedRates.push(updatedRate)
      
      // Clear cache for this currency pair
      exchangeRateCache.clear()
      
    } catch (error) {
      errors.push(`Failed to update rate for ${rateUpdate.fromCurrency} to ${rateUpdate.toCurrency}: ${error}`)
    }
  }
  
  return {
    success: errors.length === 0,
    message: errors.length === 0 
      ? `Successfully updated ${updatedRates.length} exchange rates`
      : `Updated ${updatedRates.length} rates with ${errors.length} errors`,
    updatedRates,
    errors: errors.length > 0 ? errors : undefined
  }
}

/**
 * Update single exchange rate in database (placeholder)
 */
async function updateExchangeRateInDatabase(
  rateUpdate: { fromCurrency: Currency; toCurrency: Currency; rate: number }
): Promise<ExchangeRate> {
  // Mock implementation - replace with actual database update
  return {
    id: `rate_${Date.now()}`,
    fromCurrencyCode: rateUpdate.fromCurrency,
    toCurrencyCode: rateUpdate.toCurrency,
    rate: rateUpdate.rate,
    effectiveDate: new Date(),
    createdAt: new Date(),
    isActive: true
  }
}

// =====================================================
// SYSTEM HEALTH AND MONITORING
// =====================================================

/**
 * Check currency system health
 */
export async function checkCurrencySystemHealth(): Promise<CurrencySystemHealth> {
  const issues: string[] = []
  const missingRates: string[] = []
  
  try {
    // Check if we can get basic rates
    const testCurrencies = ['USD', 'SDG', 'EGP']
    const rateChecks = []
    
    for (const from of testCurrencies) {
      for (const to of testCurrencies) {
        if (from !== to) {
          rateChecks.push({ from, to })
        }
      }
    }
    
    let successfulRates = 0
    for (const check of rateChecks) {
      try {
        await getExchangeRate(check.from, check.to, false) // Don't use cache for health check
        successfulRates++
      } catch (error) {
        missingRates.push(`${check.from} to ${check.to}`)
      }
    }
    
    if (missingRates.length > 0) {
      issues.push(`Missing exchange rates: ${missingRates.join(', ')}`)
    }
    
    const healthStatus = issues.length === 0 ? 'healthy' : 
                        missingRates.length < rateChecks.length / 2 ? 'warning' : 'error'
    
    return {
      status: healthStatus,
      currencies: {
        total: testCurrencies.length,
        active: testCurrencies.length,
        inactive: 0
      },
      exchangeRates: {
        total: rateChecks.length,
        active: successfulRates,
        stale: 0,
        missing: missingRates
      },
      lastRateUpdate: new Date(),
      issues
    }
    
  } catch (error) {
    return {
      status: 'error',
      currencies: { total: 0, active: 0, inactive: 0 },
      exchangeRates: { total: 0, active: 0, stale: 0, missing: [] },
      issues: [`System health check failed: ${error}`]
    }
  }
}

/**
 * Get conversion preview for checkout
 */
export async function getConversionPreview(
  amount: number,
  fromCurrency: Currency,
  toCurrency: Currency
): Promise<{
  originalAmount: number
  originalCurrency: Currency
  convertedAmount: number
  targetCurrency: Currency
  exchangeRate: number
  conversionFee?: number
  timestamp: Date
}> {
  const conversion = await convertCurrency(amount, fromCurrency, toCurrency)

  return {
    originalAmount: amount,
    originalCurrency: fromCurrency,
    convertedAmount: conversion.convertedAmount,
    targetCurrency: toCurrency,
    exchangeRate: conversion.exchangeRate,
    conversionFee: 0, // No fees for now
    timestamp: conversion.timestamp
  }
}

/**
 * Clear exchange rate cache
 */
export function clearExchangeRateCache(): void {
  exchangeRateCache.clear()
}

/**
 * Get cache statistics
 */
export function getExchangeRateCacheStats(): { size: number; keys: string[] } {
  return exchangeRateCache.getStats()
}
