(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7965:(e,s,a)=>{"use strict";a.d(s,{w:()=>d});var t=a(60687),l=a(43210),r=a(14163),i="horizontal",o=["horizontal","vertical"],n=l.forwardRef((e,s)=>{var a;let{decorative:l,orientation:n=i,...c}=e,d=(a=n,o.includes(a))?n:i;return(0,t.jsx)(r.sG.div,{"data-orientation":d,...l?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:s})});n.displayName="Separator";var c=a(96241);let d=l.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...l},r)=>(0,t.jsx)(n,{ref:r,decorative:a,orientation:s,className:(0,c.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...l}));d.displayName=n.displayName},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10403:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var t=a(60687),l=a(43210),r=a(14163),i=l.forwardRef((e,s)=>(0,t.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var o=a(24224),n=a(96241);let c=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef(({className:e,...s},a)=>(0,t.jsx)(i,{ref:a,className:(0,n.cn)(c(),e),...s}));d.displayName=i.displayName},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16023:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},17199:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\app\\profile\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23264:(e,s,a)=>{Promise.resolve().then(a.bind(a,66077))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40083:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},55532:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=a(65239),l=a(48088),r=a(88170),i=a.n(r),o=a(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);a.d(s,n);let c={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,17199)),"D:\\VS-projects\\try\\alraya-store\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"D:\\VS-projects\\try\\alraya-store\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["D:\\VS-projects\\try\\alraya-store\\app\\profile\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59712:(e,s,a)=>{Promise.resolve().then(a.bind(a,17199))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66077:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>I});var t=a(60687),l=a(43210),r=a(16189),i=a(55192),o=a(24934),n=a(68988),c=a(10403),d=a(59821),x=a(70373),m=a(7965),h=a(35438),p=a(74513),u=a(72184),N=a(37826),j=a(62688);let v=(0,j.A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var g=a(16023);let b=(0,j.A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var f=a(11860),w=a(13964),y=a(93613),P=a(96241);function k({onImageUpload:e,currentImage:s,maxSizeInMB:a=5,acceptedFormats:r=["image/jpeg","image/png","image/webp"],className:i}){let[n,c]=(0,l.useState)(!1),[d,x]=(0,l.useState)(!1),[m,h]=(0,l.useState)(null),[p,u]=(0,l.useState)(!1),N=(0,l.useRef)(null),j=e=>e.size>1048576*a?`حجم الملف كبير جداً. الحد الأقصى ${a} ميجابايت`:r.includes(e.type)?null:"نوع الملف غير مدعوم. يرجى استخدام JPG، PNG، أو WebP",k=(0,l.useCallback)(async s=>{h(null),u(!1);let a=j(s);if(a){h(a);return}x(!0);try{let a=URL.createObjectURL(s);await new Promise(e=>setTimeout(e,1500)),e(a),u(!0),setTimeout(()=>u(!1),3e3)}catch(e){console.error("Upload error:",e),h("حدث خطأ أثناء رفع الصورة. يرجى المحاولة مرة أخرى")}finally{x(!1)}},[e,a,r]),A=(0,l.useCallback)(e=>{e.preventDefault(),c(!0)},[]),C=(0,l.useCallback)(e=>{e.preventDefault(),c(!1)},[]),S=(0,l.useCallback)(e=>{e.preventDefault(),c(!1);let s=Array.from(e.dataTransfer.files);s.length>0&&k(s[0])},[k]),z=(0,l.useCallback)(e=>{let s=e.target.files;s&&s.length>0&&k(s[0])},[k]);return(0,t.jsxs)("div",{className:(0,P.cn)("space-y-4",i),children:[(0,t.jsxs)("div",{className:(0,P.cn)("relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200",n?"border-yellow-400 bg-yellow-400/10":"border-slate-600 hover:border-slate-500",d&&"opacity-50 pointer-events-none"),onDragOver:A,onDragLeave:C,onDrop:S,children:[(0,t.jsx)("input",{ref:N,type:"file",accept:r.join(","),onChange:z,className:"hidden"}),(0,t.jsx)("div",{className:"space-y-4",children:d?(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"}),(0,t.jsx)("p",{className:"text-sm text-slate-300",children:"جاري رفع الصورة..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"p-3 bg-slate-700/50 rounded-full",children:(0,t.jsx)(v,{className:"h-8 w-8 text-yellow-400"})})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-slate-300 font-medium",children:"اسحب وأفلت صورة هنا أو انقر للاختيار"}),(0,t.jsxs)("p",{className:"text-xs text-slate-400",children:["JPG، PNG، WebP - حتى ",a," ميجابايت"]})]}),(0,t.jsxs)(o.$,{onClick:()=>{N.current?.click()},variant:"outline",size:"sm",className:"border-slate-600 text-slate-300 hover:bg-slate-700/50",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 ml-2"}),"اختيار صورة"]})]})})]}),s&&(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-700/30 rounded-lg border border-slate-600/50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,t.jsx)("div",{className:"p-2 bg-slate-600/50 rounded",children:(0,t.jsx)(b,{className:"h-4 w-4 text-slate-300"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-slate-300",children:"صورة الملف الشخصي"}),(0,t.jsx)("p",{className:"text-xs text-slate-400",children:"تم تحديد الصورة"})]})]}),(0,t.jsx)(o.$,{onClick:()=>{e(""),N.current&&(N.current.value="")},variant:"ghost",size:"sm",className:"text-slate-400 hover:text-red-400 h-8 w-8 p-0",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})}),p&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse p-3 bg-green-500/10 border border-green-500/20 rounded-lg",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-green-400"}),(0,t.jsx)("p",{className:"text-sm text-green-400",children:"تم رفع الصورة بنجاح!"})]}),m&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse p-3 bg-red-500/10 border border-red-500/20 rounded-lg",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-red-400"}),(0,t.jsx)("p",{className:"text-sm text-red-400",children:m})]})]})}var A=a(58869),C=a(19169);let S=(0,j.A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var z=a(12597),J=a(13861),L=a(8819);function D({isOpen:e,onClose:s,user:a,onUpdateProfile:r,isLoading:i=!1}){let[d,m]=(0,l.useState)(!1),[h,p]=(0,l.useState)(!1),[u,j]=(0,l.useState)(a.avatarUrl||null),[v,g]=(0,l.useState)({firstName:a.firstName,lastName:a.lastName,displayName:a.displayName,email:a.email,phone:a.phone,currentPassword:"",newPassword:"",confirmPassword:""}),b=(e,s)=>{g(a=>({...a,[e]:s}))},w=async()=>{try{if(v.newPassword&&v.newPassword!==v.confirmPassword)throw Error("كلمات المرور غير متطابقة");let e={firstName:v.firstName,lastName:v.lastName,displayName:v.displayName,email:v.email,phone:v.phone,avatarUrl:u||void 0};await r(e),g(e=>({...e,currentPassword:"",newPassword:"",confirmPassword:""})),s()}catch(e){console.error("Profile update error:",e)}};return(0,t.jsx)(N.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(N.Cf,{className:"bg-slate-800/95 border-slate-700/50 backdrop-blur-xl text-white max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(N.c7,{children:(0,t.jsxs)(N.L3,{className:"text-xl font-bold text-center flex items-center justify-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-6 w-6 text-yellow-400"}),"تعديل الملف الشخصي"]})}),(0,t.jsxs)("div",{className:"space-y-6 p-2",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)(x.eu,{className:"h-24 w-24 border-4 border-yellow-400/20",children:[(0,t.jsx)(x.BK,{src:u||a.avatarUrl,alt:a.displayName}),(0,t.jsx)(x.q5,{className:"bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-2xl font-bold",children:a.displayName.charAt(0)})]})}),(0,t.jsx)(k,{onImageUpload:e=>{j(e)},currentImage:u})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-yellow-400 flex items-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-5 w-5"}),"المعلومات الشخصية"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"الاسم الأول"}),(0,t.jsx)(n.p,{value:v.firstName,onChange:e=>b("firstName",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400",placeholder:"أدخل الاسم الأول"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"الاسم الأخير"}),(0,t.jsx)(n.p,{value:v.lastName,onChange:e=>b("lastName",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400",placeholder:"أدخل الاسم الأخير"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"الاسم المعروض"}),(0,t.jsx)(n.p,{value:v.displayName,onChange:e=>b("displayName",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400",placeholder:"أدخل الاسم المعروض"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-yellow-400 flex items-center gap-2",children:[(0,t.jsx)(C.A,{className:"h-5 w-5"}),"معلومات الاتصال"]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"البريد الإلكتروني"}),(0,t.jsx)(n.p,{type:"email",value:v.email,onChange:e=>b("email",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400",placeholder:"أدخل البريد الإلكتروني"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"رقم الهاتف"}),(0,t.jsx)(n.p,{type:"tel",value:v.phone,onChange:e=>b("phone",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400",placeholder:"أدخل رقم الهاتف"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-yellow-400 flex items-center gap-2",children:[(0,t.jsx)(S,{className:"h-5 w-5"}),"تغيير كلمة المرور (اختياري)"]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"كلمة المرور الحالية"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{type:d?"text":"password",value:v.currentPassword,onChange:e=>b("currentPassword",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400 pr-10",placeholder:"أدخل كلمة المرور الحالية"}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 text-slate-400 hover:text-white",onClick:()=>m(!d),children:d?(0,t.jsx)(z.A,{className:"h-4 w-4"}):(0,t.jsx)(J.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"كلمة المرور الجديدة"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{type:h?"text":"password",value:v.newPassword,onChange:e=>b("newPassword",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400 pr-10",placeholder:"أدخل كلمة المرور الجديدة"}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 text-slate-400 hover:text-white",onClick:()=>p(!h),children:h?(0,t.jsx)(z.A,{className:"h-4 w-4"}):(0,t.jsx)(J.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"تأكيد كلمة المرور"}),(0,t.jsx)(n.p,{type:"password",value:v.confirmPassword,onChange:e=>b("confirmPassword",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400",placeholder:"أعد إدخال كلمة المرور"})]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-4",children:[(0,t.jsxs)(o.$,{onClick:w,disabled:i,className:"flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-medium",children:[(0,t.jsx)(L.A,{className:"h-4 w-4 ml-2"}),i?"جاري الحفظ...":"حفظ التغييرات"]}),(0,t.jsxs)(o.$,{onClick:()=>{g({firstName:a.firstName,lastName:a.lastName,displayName:a.displayName,email:a.email,phone:a.phone,currentPassword:"",newPassword:"",confirmPassword:""}),j(a.avatarUrl||null),s()},variant:"outline",className:"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700/50",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 ml-2"}),"إلغاء"]})]})]})]})})}var _=a(54278),M=a(94511),V=a(5336),q=a(48730),G=a(64398),R=a(75034),$=a(35583),T=a(71057),U=a(48340),O=a(40083);function Z({user:e,onUpdateProfile:s,onLogout:a,onNavigateToWallet:r,onNavigateToOrders:N}){let[j,g]=(0,l.useState)(!1),[b,f]=(0,l.useState)(!1),[w,y]=(0,l.useState)(!1),[P,k]=(0,l.useState)(!1),[C,L]=(0,l.useState)(!1),[Z,E]=(0,l.useState)(!1),[W,B]=(0,l.useState)({firstName:e.firstName,lastName:e.lastName,displayName:e.displayName,email:e.email,phone:e.phone,currentPassword:"",newPassword:"",confirmPassword:""}),I=(e,s)=>{B(a=>({...a,[e]:s}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900",children:[(0,t.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,t.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"})]}),(0,t.jsx)(h.j,{onMenuOpen:()=>L(!0)}),(0,t.jsx)(u.c,{}),(0,t.jsx)(p.p,{isOpen:C,onClose:()=>L(!1)}),(0,t.jsxs)("div",{className:"relative z-10 container mx-auto px-4 py-8 max-w-4xl pt-32 pb-32",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4",children:"صفحة الحساب"}),(0,t.jsx)("p",{className:"text-slate-300 text-lg",children:"إدارة معلوماتك الشخصية وإعدادات حسابك"})]}),(0,t.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-8",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(x.eu,{className:"h-24 w-24 border-4 border-yellow-400/20",children:[(0,t.jsx)(x.BK,{src:e.avatarUrl,alt:e.displayName}),(0,t.jsx)(x.q5,{className:"bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-2xl font-bold",children:e.displayName.charAt(0)})]}),(0,t.jsx)(o.$,{size:"sm",className:"absolute -bottom-2 -right-2 h-8 w-8 rounded-full bg-slate-700 hover:bg-slate-600 p-0",children:(0,t.jsx)(v,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex-1 text-center md:text-right",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center md:justify-start gap-2 mb-2",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-white",children:["مرحبًا، ",e.displayName,"!"]}),e.isVerified&&(0,t.jsx)(V.A,{className:"h-5 w-5 text-green-400"})]}),(0,t.jsxs)("p",{className:"text-slate-300 mb-1",children:["رقم حسابك في رايه شوب: ",(0,t.jsx)("span",{className:"font-mono text-yellow-400",children:e.accountId})]}),(0,t.jsxs)("div",{className:"flex items-center justify-center md:justify-start gap-4 text-sm text-slate-400",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),"عضو منذ ",(0,M.Yq)(e.joinDate)]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),"آخر دخول ",(0,M.Yq)(e.lastLogin)]})]})]}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsxs)(o.$,{onClick:()=>E(!0),className:"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900",children:[(0,t.jsx)(R.A,{className:"h-4 w-4 ml-1"}),"تعديل الملف الشخصي"]})})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,t.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm cursor-pointer hover:bg-slate-700/50 transition-colors",onClick:r,children:(0,t.jsxs)(i.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-bold text-lg mb-1",children:"رصيد محفظتك"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"text-yellow-400 font-bold",children:(0,_.vv)(e.walletBalance.sdg,"SDG")}),(0,t.jsx)("div",{className:"text-orange-400 font-bold",children:(0,_.vv)(e.walletBalance.egp,"EGP")})]})]}),(0,t.jsx)("div",{className:"bg-gradient-to-br from-yellow-400 to-orange-500 p-3 rounded-xl",children:(0,t.jsx)($.A,{className:"h-8 w-8 text-slate-900"})})]}),(0,t.jsx)(o.$,{variant:"ghost",className:"w-full mt-4 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400/10",children:"اضغط هنا لعرض تفاصيل المحفظة"})]})}),(0,t.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm cursor-pointer hover:bg-slate-700/50 transition-colors",onClick:N,children:(0,t.jsxs)(i.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-bold text-lg mb-1",children:"طلباتي"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:e.ordersCount}),(0,t.jsx)("div",{className:"text-slate-400 text-sm",children:"إجمالي الطلبات"})]}),(0,t.jsx)("div",{className:"bg-gradient-to-br from-blue-400 to-blue-600 p-3 rounded-xl",children:(0,t.jsx)(T.A,{className:"h-8 w-8 text-white"})})]}),(0,t.jsx)(o.$,{variant:"ghost",className:"w-full mt-4 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10",children:"اضغط هنا لعرض طلباتك"})]})})]}),j&&(0,t.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-8",children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"text-white text-xl flex items-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-6 w-6 text-yellow-400"}),"تعديل البيانات الشخصية"]})}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"الاسم الأول"}),(0,t.jsx)(n.p,{value:W.firstName,onChange:e=>I("firstName",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"الاسم الأخير"}),(0,t.jsx)(n.p,{value:W.lastName,onChange:e=>I("lastName",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"اسم العرض"}),(0,t.jsx)(n.p,{value:W.displayName,onChange:e=>I("displayName",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white"}),(0,t.jsx)("div",{className:"text-slate-400 text-sm",children:"بهذه الطريقة سيتم عرض اسمك في الحسابات والمراجعات"})]}),(0,t.jsx)(m.w,{className:"bg-slate-600/50"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h4",{className:"text-white font-semibold flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-5 w-5 text-blue-400"}),"معلومات الاتصال"]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"رقم الهاتف"}),(0,t.jsx)(n.p,{value:W.phone,onChange:e=>I("phone",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white",placeholder:"+249xxxxxxxxx"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"البريد الإلكتروني"}),(0,t.jsx)(n.p,{type:"email",value:W.email,onChange:e=>I("email",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white"})]})]}),(0,t.jsx)(m.w,{className:"bg-slate-600/50"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h4",{className:"text-white font-semibold flex items-center gap-2",children:[(0,t.jsx)(S,{className:"h-5 w-5 text-red-400"}),"تغيير كلمة المرور"]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"كلمة المرور الحالية"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{type:w?"text":"password",value:W.currentPassword,onChange:e=>I("currentPassword",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white pl-10",placeholder:"اترك الحقل فارغاً إذا لم تغير"}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>y(!w),children:w?(0,t.jsx)(z.A,{className:"h-4 w-4 text-slate-400"}):(0,t.jsx)(J.A,{className:"h-4 w-4 text-slate-400"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"كلمة المرور الجديدة"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{type:P?"text":"password",value:W.newPassword,onChange:e=>I("newPassword",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white pl-10",placeholder:"اترك الحقل فارغاً إذا لم تغير"}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute left-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>k(!P),children:P?(0,t.jsx)(z.A,{className:"h-4 w-4 text-slate-400"}):(0,t.jsx)(J.A,{className:"h-4 w-4 text-slate-400"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"text-slate-300 font-medium",children:"تأكيد كلمة المرور الجديدة"}),(0,t.jsx)(n.p,{type:"password",value:W.confirmPassword,onChange:e=>I("confirmPassword",e.target.value),className:"bg-slate-700/50 border-slate-600/50 text-white",placeholder:"أعد إدخال كلمة المرور الجديدة"})]})]}),W.newPassword&&W.confirmPassword&&W.newPassword!==W.confirmPassword&&(0,t.jsx)("div",{className:"text-red-400 text-sm",children:"كلمات المرور غير متطابقة"})]})]})]}),(0,t.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{className:"text-white text-xl",children:"إعدادات الحساب"})}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600/30",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-white font-medium",children:"حالة التحقق"}),(0,t.jsx)("div",{className:"text-slate-400 text-sm",children:e.isVerified?"تم التحقق من حسابك":"لم يتم التحقق من حسابك"})]}),(0,t.jsx)(d.E,{className:e.isVerified?"bg-green-500":"bg-yellow-500",children:e.isVerified?"محقق":"غير محقق"})]}),(0,t.jsx)(m.w,{className:"bg-slate-600/50"}),(0,t.jsxs)(o.$,{onClick:a,variant:"outline",className:"w-full bg-red-500/10 border-red-500/50 text-red-400 hover:bg-red-500/20 hover:border-red-500",children:[(0,t.jsx)(O.A,{className:"h-4 w-4 ml-2"}),"تسجيل الخروج"]})]})]})]}),(0,t.jsx)(D,{isOpen:Z,onClose:()=>E(!1),user:e,onUpdateProfile:s||(()=>Promise.resolve()),isLoading:b})]})}var E=a(74186),W=a(50197);let B={id:"user_123456",accountId:"RYS_789012",displayName:"أحمد محمد",firstName:"أحمد",lastName:"محمد",email:"<EMAIL>",phone:"+************",avatarUrl:"",walletBalance:{sdg:15750,egp:2340},ordersCount:12,joinDate:"2024-01-15T10:30:00Z",lastLogin:"2024-06-25T14:20:00Z",isVerified:!0};function I(){let e=(0,r.useRouter)(),[s,a]=(0,l.useState)(B),[i,o]=(0,l.useState)(!1),[n,c]=(0,l.useState)("profile"),d=async e=>{o(!0);try{await new Promise(e=>setTimeout(e,1500)),a(s=>({...s,...e})),console.log("Profile updated:",e)}catch(e){throw console.error("Profile update error:",e),e}finally{o(!1)}},x=async()=>{try{console.log("Logging out..."),e.push("/")}catch(e){console.error("Logout error:",e)}},m=s=>{"wallet"===s?e.push("/wallet"):"profile"===s?e.push("/profile"):"shop"===s?e.push("/shop"):"home"===s?e.push("/"):"support"===s?e.push("/contact"):c(s)};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(Z,{user:s,onUpdateProfile:d,onLogout:x,onNavigateToWallet:()=>{e.push("/wallet")},onNavigateToOrders:()=>{e.push("/wallet")}}),(0,t.jsx)(E.v,{activeTab:n,onTabChange:m}),(0,t.jsx)(W.G,{activeTab:n,onTabChange:m})]})}},75034:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},93613:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,228,935,984,349,50,176,838],()=>a(55532));module.exports=t})();