import { NextRequest, NextResponse } from 'next/server'
import { Currency, UserCurrencyPreferences } from '@/lib/types'

// MOCK VERSION - No Supabase dependency required
const mockUserPreferences: Record<string, any> = {
  'user-id': {
    id: '1',
    user_id: 'user-id',
    preferred_currency_code: 'USD',
    display_currency_code: 'USD',
    enable_currency_conversion: true,
    conversion_confirmation_required: true,
    preferences: {},
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
}

/**
 * GET /api/user/preferences
 * Get user currency preferences
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User ID is required' 
        },
        { status: 400 }
      )
    }

    // Get preferences from mock data
    const preferences = mockUserPreferences[userId]

    // If no preferences found, return defaults
    if (!preferences) {
      const defaultPreferences: Partial<UserCurrencyPreferences> = {
        userId,
        preferredCurrency: 'USD',
        displayCurrency: 'USD',
        enableCurrencyConversion: true,
        conversionConfirmationRequired: true
      }

      return NextResponse.json({
        success: true,
        preferences: defaultPreferences,
        isDefault: true
      })
    }

    // Transform database response to match interface
    const userPreferences: UserCurrencyPreferences = {
      id: preferences.id,
      userId: preferences.user_id,
      preferredCurrency: preferences.preferred_currency_code,
      displayCurrency: preferences.display_currency_code,
      enableCurrencyConversion: preferences.enable_currency_conversion,
      conversionConfirmationRequired: preferences.conversion_confirmation_required,
      preferences: preferences.preferences || {},
      createdAt: new Date(preferences.created_at),
      updatedAt: new Date(preferences.updated_at)
    }

    return NextResponse.json({
      success: true,
      preferences: userPreferences
    })

  } catch (error) {
    console.error('Unexpected error in GET /api/user/preferences:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/user/preferences
 * Create or update user currency preferences
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, preferences } = body

    if (!userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User ID is required' 
        },
        { status: 400 }
      )
    }

    if (!preferences) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Preferences data is required' 
        },
        { status: 400 }
      )
    }

    // Validate currency codes
    const validCurrencies = ['USD', 'SDG', 'EGP', 'EUR', 'GBP', 'SAR', 'AED']
    
    if (preferences.preferredCurrency && !validCurrencies.includes(preferences.preferredCurrency)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid preferred currency' 
        },
        { status: 400 }
      )
    }

    if (preferences.displayCurrency && !validCurrencies.includes(preferences.displayCurrency)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid display currency' 
        },
        { status: 400 }
      )
    }

    // Prepare data for database
    const dbData = {
      user_id: userId,
      preferred_currency_code: preferences.preferredCurrency || 'USD',
      display_currency_code: preferences.displayCurrency || 'USD',
      enable_currency_conversion: preferences.enableCurrencyConversion !== false,
      conversion_confirmation_required: preferences.conversionConfirmationRequired !== false,
      preferences: preferences.preferences || {}
    }

    // Mock implementation - save to memory
    const existingPrefs = mockUserPreferences[userId]

    // Update or create preferences in mock data
    const data = {
      id: existingPrefs?.id || '1',
      user_id: userId,
      preferred_currency_code: preferences.preferredCurrency || 'USD',
      display_currency_code: preferences.displayCurrency || 'USD',
      enable_currency_conversion: preferences.enableCurrencyConversion !== false,
      conversion_confirmation_required: preferences.conversionConfirmationRequired !== false,
      preferences: preferences.preferences || {},
      created_at: existingPrefs?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Save to mock storage
    mockUserPreferences[userId] = data

    // Transform response to match interface
    const savedPreferences: UserCurrencyPreferences = {
      id: data.id,
      userId: data.user_id,
      preferredCurrency: data.preferred_currency_code,
      displayCurrency: data.display_currency_code,
      enableCurrencyConversion: data.enable_currency_conversion,
      conversionConfirmationRequired: data.conversion_confirmation_required,
      preferences: data.preferences || {},
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }

    return NextResponse.json({
      success: true,
      message: 'User preferences saved successfully',
      preferences: savedPreferences
    })

  } catch (error) {
    console.error('Unexpected error in POST /api/user/preferences:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/user/preferences
 * Reset user preferences to defaults
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId } = body

    if (!userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User ID is required' 
        },
        { status: 400 }
      )
    }

    // Reset to default preferences
    const defaultData = {
      user_id: userId,
      preferred_currency_code: 'USD',
      display_currency_code: 'USD',
      enable_currency_conversion: true,
      conversion_confirmation_required: true,
      preferences: {}
    }

    const { data, error } = await supabase
      .from('user_preferences')
      .upsert(defaultData)
      .select()
      .single()

    if (error) {
      console.error('Error resetting user preferences:', error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to reset user preferences' 
        },
        { status: 500 }
      )
    }

    // Transform response
    const resetPreferences: UserCurrencyPreferences = {
      id: data.id,
      userId: data.user_id,
      preferredCurrency: data.preferred_currency_code,
      displayCurrency: data.display_currency_code,
      enableCurrencyConversion: data.enable_currency_conversion,
      conversionConfirmationRequired: data.conversion_confirmation_required,
      preferences: data.preferences || {},
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }

    return NextResponse.json({
      success: true,
      message: 'User preferences reset to defaults',
      preferences: resetPreferences
    })

  } catch (error) {
    console.error('Unexpected error in PUT /api/user/preferences:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
