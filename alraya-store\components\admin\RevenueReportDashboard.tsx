"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Download,
  RefreshCw,
  BarChart3,
  PieChart,
  Loader2,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { 
  Currency, 
  RevenueConsolidated,
  RevenueCalculationOptions
} from "@/lib/types"
import { formatCurrency } from "@/lib/utils/currency"
import { cn } from "@/lib/utils"

interface RevenueReportDashboardProps {
  className?: string
}

export function RevenueReportDashboard({ className }: RevenueReportDashboardProps) {
  const [revenueData, setRevenueData] = useState<RevenueConsolidated | null>(null)
  const [summaryData, setSummaryData] = useState<any>(null)
  const [breakdownData, setBreakdownData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<string>("thisMonth")
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>("USD")
  const [groupBy, setGroupBy] = useState<string>("month")

  useEffect(() => {
    loadRevenueData()
  }, [selectedPeriod, selectedCurrency, groupBy])

  const loadRevenueData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Calculate date range based on selected period
      const now = new Date()
      let startDate: Date | undefined
      let endDate: Date | undefined

      switch (selectedPeriod) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
          break
        case 'thisWeek':
          const weekStart = new Date(now)
          weekStart.setDate(now.getDate() - now.getDay())
          startDate = weekStart
          endDate = new Date()
          break
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          endDate = new Date()
          break
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1)
          endDate = new Date()
          break
        case 'last30Days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          endDate = new Date()
          break
        case 'last90Days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          endDate = new Date()
          break
      }

      // Load consolidated revenue
      const revenueOptions: RevenueCalculationOptions = {
        startDate,
        endDate,
        primaryCurrency: selectedCurrency,
        includePending: false,
        groupByCurrency: true,
        includeExchangeRates: true
      }

      const revenueResponse = await fetch('/api/reports/revenue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(revenueOptions)
      })

      const revenueResult = await revenueResponse.json()

      if (!revenueResult.success) {
        throw new Error(revenueResult.message || 'Failed to load revenue data')
      }

      setRevenueData(revenueResult.report)

      // Load summary data
      const summaryResponse = await fetch('/api/reports/revenue/summary')
      const summaryResult = await summaryResponse.json()

      if (summaryResult.success) {
        setSummaryData(summaryResult.summary)
      }

      // Load breakdown data
      const breakdownResponse = await fetch(
        `/api/reports/revenue/breakdown?groupBy=${groupBy}&currency=${selectedCurrency}` +
        (startDate ? `&startDate=${startDate.toISOString()}` : '') +
        (endDate ? `&endDate=${endDate.toISOString()}` : '')
      )
      const breakdownResult = await breakdownResponse.json()

      if (breakdownResult.success) {
        setBreakdownData(breakdownResult.breakdown)
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load revenue data')
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await loadRevenueData()
  }

  const handleExportData = () => {
    if (!revenueData) return

    const exportData = {
      period: selectedPeriod,
      primaryCurrency: selectedCurrency,
      totalRevenue: revenueData.totalRevenue,
      revenueByCurrency: revenueData.revenueByCurrency,
      exchangeRates: revenueData.exchangeRatesUsed,
      generatedAt: new Date().toISOString(),
      breakdown: breakdownData
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `revenue-report-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (isLoading) {
    return (
      <Card className={cn("bg-slate-800/50 backdrop-blur-xl border-slate-700/50", className)}>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
          <span className="ml-2 text-slate-400">Loading revenue data...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Controls */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl text-white flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-400" />
              Revenue Analytics
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="bg-slate-600/50 border-slate-500 text-white hover:bg-slate-500/50"
              >
                <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleExportData}
                disabled={!revenueData}
                className="bg-slate-600/50 border-slate-500 text-white hover:bg-slate-500/50"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm text-slate-300">Time Period</label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="thisWeek">This Week</SelectItem>
                  <SelectItem value="thisMonth">This Month</SelectItem>
                  <SelectItem value="thisYear">This Year</SelectItem>
                  <SelectItem value="last30Days">Last 30 Days</SelectItem>
                  <SelectItem value="last90Days">Last 90 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm text-slate-300">Primary Currency</label>
              <Select value={selectedCurrency} onValueChange={(value) => setSelectedCurrency(value as Currency)}>
                <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD - US Dollar</SelectItem>
                  <SelectItem value="SDG">SDG - Sudanese Pound</SelectItem>
                  <SelectItem value="EGP">EGP - Egyptian Pound</SelectItem>
                  <SelectItem value="EUR">EUR - Euro</SelectItem>
                  <SelectItem value="GBP">GBP - British Pound</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm text-slate-300">Group By</label>
              <Select value={groupBy} onValueChange={setGroupBy}>
                <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Daily</SelectItem>
                  <SelectItem value="week">Weekly</SelectItem>
                  <SelectItem value="month">Monthly</SelectItem>
                  <SelectItem value="year">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm text-slate-300">Last Updated</label>
              <div className="text-sm text-slate-400 bg-slate-700/30 p-2 rounded">
                {revenueData?.calculatedAt ? 
                  new Date(revenueData.calculatedAt).toLocaleString() : 
                  'Never'
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert className="bg-red-900/20 border-red-700/50">
          <AlertTriangle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-100">{error}</AlertDescription>
        </Alert>
      )}

      {/* Revenue Summary Cards */}
      {revenueData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-r from-green-600/20 to-green-500/20 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">Total Revenue</p>
                  <p className="text-2xl font-bold text-white">
                    {formatCurrency(revenueData.totalRevenue, selectedCurrency)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-600/20 to-blue-500/20 border-blue-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">Currencies</p>
                  <p className="text-2xl font-bold text-white">
                    {Object.keys(revenueData.revenueByCurrency).length}
                  </p>
                </div>
                <PieChart className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-600/20 to-purple-500/20 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm font-medium">Exchange Rates</p>
                  <p className="text-2xl font-bold text-white">
                    {Object.keys(revenueData.exchangeRatesUsed).length}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-600/20 to-orange-500/20 border-orange-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm font-medium">Period</p>
                  <p className="text-lg font-bold text-white capitalize">
                    {selectedPeriod.replace(/([A-Z])/g, ' $1').trim()}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Revenue Breakdown by Currency */}
      {revenueData && Object.keys(revenueData.revenueByCurrency).length > 0 && (
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-xl text-white">Revenue by Currency</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(revenueData.revenueByCurrency).map(([currency, amount]) => {
                const percentage = (amount / revenueData.totalRevenue) * 100
                const exchangeRateKey = `${currency}_to_${selectedCurrency}`
                const exchangeRate = revenueData.exchangeRatesUsed[exchangeRateKey]

                return (
                  <div key={currency} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-white border-slate-500">
                          {currency}
                        </Badge>
                        <span className="text-slate-300">
                          {formatCurrency(amount, currency)}
                        </span>
                        {exchangeRate && exchangeRate !== 1 && (
                          <span className="text-xs text-slate-400">
                            (Rate: {exchangeRate.toFixed(6)})
                          </span>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium">
                          {percentage.toFixed(1)}%
                        </div>
                        {currency !== selectedCurrency && (
                          <div className="text-xs text-slate-400">
                            ≈ {formatCurrency(amount * (exchangeRate || 1), selectedCurrency)}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Time Series Breakdown */}
      {breakdownData.length > 0 && (
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-xl text-white">Revenue Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {breakdownData.map((item, index) => (
                <div
                  key={item.period}
                  className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="text-sm font-medium text-white">
                      {item.period}
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {item.orders} orders
                    </Badge>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-white">
                      {formatCurrency(item.revenue, selectedCurrency)}
                    </div>
                    {Object.keys(item.revenueByCurrency).length > 1 && (
                      <div className="text-xs text-slate-400">
                        {Object.keys(item.revenueByCurrency).length} currencies
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Exchange Rates Used */}
      {revenueData && Object.keys(revenueData.exchangeRatesUsed).length > 1 && (
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-xl text-white">Exchange Rates Applied</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(revenueData.exchangeRatesUsed).map(([rateKey, rate]) => {
                const [from, to] = rateKey.split('_to_')
                return (
                  <div
                    key={rateKey}
                    className="p-3 bg-slate-700/30 rounded-lg border border-slate-600/50"
                  >
                    <div className="text-sm text-slate-300 mb-1">
                      {from} → {to}
                    </div>
                    <div className="text-lg font-bold text-white">
                      {rate === 1 ? '1.000000' : rate.toFixed(6)}
                    </div>
                    <div className="text-xs text-slate-400">
                      1 {from} = {rate.toFixed(6)} {to}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
