@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 90%;

    --card: 0 0% 13%;
    --card-foreground: 0 0% 90%;

    --popover: 0 0% 13%;
    --popover-foreground: 0 0% 90%;

    --primary: 35 100% 50%;
    --primary-foreground: 0 0% 10%;

    --secondary: 0 0% 53%;
    --secondary-foreground: 0 0% 0%;

    --muted: 0 0% 13%;
    --muted-foreground: 0 0% 63%;

    --accent: 35 100% 50%;
    --accent-foreground: 0 0% 10%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 35 100% 50%;

    --radius: 0.75rem;
    
    /* Global site colors that can be referenced everywhere */
    --pubg-orange-color: #F2A900;
    --pubg-black-color: #000000;
    --pubg-darkGray-color: #222222;
    --pubg-gray-color: #888888;
    --pubg-lightGray-color: #E5E5E5;
    
    /* Colors with opacity variants */
    --pubg-orange-10: rgba(242, 169, 0, 0.1);
    --pubg-orange-20: rgba(242, 169, 0, 0.2);
    --pubg-orange-30: rgba(242, 169, 0, 0.3);
    --pubg-orange-50: rgba(242, 169, 0, 0.5);
    --pubg-orange-80: rgba(242, 169, 0, 0.8);
    
    --pubg-gray-10: rgba(136, 136, 136, 0.1);
    --pubg-gray-20: rgba(136, 136, 136, 0.2);
    --pubg-gray-30: rgba(136, 136, 136, 0.3);
    --pubg-gray-50: rgba(136, 136, 136, 0.5);
    
    /* Semi-transparent background colors for cards, inputs, etc. */
    --bg-card-dark: rgba(0, 0, 0, 0.95);
    --bg-input-dark: rgba(20, 20, 20, 0.8);
    --bg-input-hover: rgba(30, 30, 30, 0.9);
    --bg-section-item: rgba(20, 20, 20, 0.9);
    --bg-section-item-hover: rgba(30, 30, 30, 0.95);
    
    /* Border colors */
    --border-gray: rgba(50, 50, 50, 0.7);
    --border-light: rgba(50, 50, 50, 0.4);
    --border-orange: rgba(242, 169, 0, 0.5);
  }

  * {
    @apply border-border;
    font-family: 'Cairo', sans-serif !important;
  }

  html {
    direction: rtl;
    font-family: 'Cairo', sans-serif !important;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Cairo', sans-serif !important;
    overflow-x: hidden;
  }

  /* Simplify by forcing Cairo everywhere, we'll handle English text later if needed */
  [dir="rtl"] *,
  [dir="ltr"] *,
  [lang="ar"],
  *:lang(ar),
  button,
  input,
  select,
  textarea,
  h1, h2, h3, h4, h5, h6,
  p, span, div {
    font-family: 'Cairo', sans-serif !important;
  }

  ::selection {
    @apply bg-[var(--pubg-orange-30)] text-white;
  }
}

@layer components {
  .glass-card {
    @apply bg-card/80 backdrop-blur-md border border-white/10 shadow-xl;
  }
  
  .section-title {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 text-white relative inline-block after:content-[''] after:absolute after:w-1/2 after:h-1 after:bg-pubg-orange after:bottom-0 after:right-0;
  }
  
  .button-primary {
    @apply bg-pubg-orange text-pubg-black font-bold px-4 py-2 md:px-6 md:py-3 text-sm md:text-base rounded-lg transition-all duration-300 hover:bg-pubg-orange/90 hover:translate-y-[-2px] shadow-lg hover:shadow-pubg-orange/30 active:translate-y-0;
  }
  
  .button-secondary {
    @apply bg-pubg-gray text-white font-bold px-4 py-2 md:px-6 md:py-3 text-sm md:text-base rounded-lg transition-all duration-300 hover:bg-pubg-gray/90 hover:translate-y-[-2px] shadow-lg hover:shadow-pubg-gray/30 active:translate-y-0;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:translate-y-[-5px] hover:shadow-xl;
  }

  .responsive-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6;
  }
  
  .responsive-padding {
    @apply py-6 md:py-8 lg:py-12;
  }

  /* Admin panel responsive components */
  .admin-card {
    @apply glass-card rounded-xl p-3 md:p-6 mb-4 md:mb-6;
  }

  .admin-title {
    @apply text-lg md:text-xl font-bold text-white mb-3 md:mb-4;
  }

  .admin-subtitle {
    @apply text-sm md:text-base font-semibold text-white mb-2;
  }

  .admin-form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4;
  }

  .admin-form-group {
    @apply space-y-1 md:space-y-2;
  }

  .admin-label {
    @apply text-sm md:text-base;
  }

  .admin-input {
    @apply h-9 md:h-10 text-sm md:text-base;
  }

  .admin-textarea {
    @apply h-24 md:h-32 text-sm md:text-base min-h-[100px] md:min-h-[150px];
  }

  .admin-button {
    @apply h-9 md:h-10 text-xs md:text-sm;
  }

  .admin-card-content {
    @apply p-3 md:p-4;
  }

  .admin-list-item {
    @apply glass-card rounded-lg p-3 md:p-4 mb-3;
  }

  .admin-item-title {
    @apply text-sm md:text-base font-medium text-white truncate;
  }

  .admin-item-subtitle {
    @apply text-xs md:text-sm text-muted-foreground truncate;
  }

  .admin-badge {
    @apply inline-flex items-center px-2 py-0.5 rounded text-xs font-medium;
  }

  .admin-sticky-header {
    @apply sticky top-16 z-10 bg-background/80 backdrop-blur-md py-2 mb-4;
  }

  /* Mobile optimized scrolling and overflow */
  .mobile-scroll-container {
    @apply max-h-[80vh] sm:max-h-none overflow-auto sm:overflow-visible;
    scrollbar-width: thin;
  }

  .mobile-dialog-content {
    @apply max-h-[90vh] overflow-auto;
    scrollbar-width: thin;
  }

  .fixed-bottom-actions {
    @apply sticky bottom-0 bg-background/80 backdrop-blur-md py-2 border-t border-border mt-4;
  }

  .truncate-text {
    @apply overflow-hidden text-ellipsis whitespace-nowrap;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;  
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;  
    overflow: hidden;
  }

  /* Custom scrollbar for description */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(242, 169, 0, 0.5);
    border-radius: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(242, 169, 0, 0.7);
  }

  /* Custom Cart Button Animations */
  .custom-cart-button {
    position: relative;
    overflow: hidden;
  }
  
  .custom-cart-button:active {
    transform: scale(0.95);
  }
  
  .custom-cart-button.clicked .cart-icon {
    animation: cart-move 1.5s ease-in-out forwards;
  }
  
  .custom-cart-button.clicked .box-icon {
    animation: box-move 1.5s ease-in-out forwards;
  }
  
  .custom-cart-button.clicked .animate-txt1 {
    animation: text-hide 1.5s ease-in-out forwards;
  }
  
  .custom-cart-button.clicked .animate-txt2 {
    animation: text-show 1.5s ease-in-out forwards;
  }
  
  @keyframes cart-move {
    0% {
      left: -10%;
    }
    40%, 60% {
      left: 50%;
    }
    100% {
      left: 110%;
    }
  }
  
  @keyframes box-move {
    0%, 40% {
      top: -20%;
    }
    60% {
      top: 40%;
      left: 52%;
    }
    100% {
      top: 40%;
      left: 112%;
    }
  }
  
  @keyframes text-hide {
    0% {
      opacity: 1;
    }
    20%, 100% {
      opacity: 0;
    }
  }
  
  @keyframes text-show {
    0%, 70% {
      opacity: 0;
      transform: translate(-50%, -80%);
    }
    85% {
      opacity: 1;
      transform: translate(-50%, -30%);
    }
    92% {
      transform: translate(-50%, -60%);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%);
    }
  }
}

/* Floating animations for particles */
@keyframes float-slow {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-10px) translateX(5px);
  }
}

@keyframes float-medium {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-15px) translateX(-5px);
  }
}

@keyframes float-fast {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-20px) translateX(8px);
  }
}

/* Mobile-first responsive media queries */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .responsive-heading-xl {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold;
  }
  
  .responsive-heading-lg {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold;
  }
  
  .responsive-heading-md {
    @apply text-xl md:text-2xl lg:text-3xl font-bold;
  }
  
  .responsive-heading-sm {
    @apply text-lg md:text-xl lg:text-2xl font-bold;
  }
  
  .responsive-text {
    @apply text-sm md:text-base lg:text-lg;
  }
}

/* Fixed height handling for smaller screens */
@media (max-width: 640px) {
  .glass-card {
    @apply p-3;
  }
  
  .section-title:after {
    @apply w-1/3 h-[3px];
  }
  
  .responsive-flex-col {
    @apply flex-col;
  }
}

/* Performance optimizations for background animations */
@media (prefers-reduced-motion: reduce) {
  .auth-page-background * {
    animation: none !important;
    transition: none !important;
  }
}

/* Add hardware acceleration for smoother animations */
.hardware-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* Auth page animations and styles */
.auth-page-background {
  position: relative;
  background-color: rgb(0 0 0); /* pure black */
  overflow: hidden;
  min-height: 100vh;
  width: 100vw;
  max-width: 100%;
}

/* Keep other animations but remove bg-pulse as it's no longer needed */
@keyframes float-up-down {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.2;
    filter: blur(50px);
  }
  50% {
    opacity: 0.5;
    filter: blur(80px);
  }
}

/* Enhanced form element stylings */
.form-input-glowing:focus {
  box-shadow: 0 0 0 2px rgba(242, 169, 0, 0.2), 
              0 0 15px rgba(242, 169, 0, 0.15), 
              0 0 0 1px rgba(242, 169, 0, 0.2);
  border-color: rgba(242, 169, 0, 0.5);
}

.form-button-glowing {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.form-button-glowing:after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(242, 169, 0, 0) 0%,
    rgba(242, 169, 0, 0.1) 50%,
    rgba(242, 169, 0, 0) 100%
  );
  transform: rotate(45deg);
  animation: button-shine 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes button-shine {
  0% {
    left: -100%;
    top: -100%;
  }
  100% {
    left: 100%;
    top: 100%;
  }
}

/* Tab animation effects */
.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: var(--pubg-orange);
  transition: all 0.3s ease;
}

/* Animated loader */
.spinner-loading {
  @apply inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite];
}

@keyframes spinner-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Global Loader - Yin Yang Style */
.loader {
  width: 96px;
  box-sizing: content-box;
  height: 48px;
  background: #FFF;
  border-color: var(--pubg-orange-color);
  border-style: solid;
  border-width: 2px 2px 50px 2px;
  border-radius: 100%;
  position: relative;
  animation: 3s yinYang linear infinite;
}

.loader:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  background: #FFF;
  border: 18px solid var(--pubg-orange-color);
  border-radius: 100%;
  width: 12px;
  height: 12px;
  box-sizing: content-box;
}

.loader:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  background: var(--pubg-orange-color);
  border: 18px solid #FFF;
  border-radius: 100%;
  width: 12px;
  height: 12px;
  box-sizing: content-box;
}

@keyframes yinYang {
  100% {transform: rotate(360deg)}
}

/* New Language Switcher Design */
.lang-switcher-container {
  isolation: isolate;
}

.neo-lang-switcher {
  background: rgba(20, 20, 30, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
              inset 0 1px 1px rgba(255, 255, 255, 0.07);
  transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
  overflow: hidden;
}

.neo-lang-switcher::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.07) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  z-index: -1;
}

.neo-lang-menu {
  background: rgba(12, 12, 20, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.06);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4),
              inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.neo-lang-menu-header {
  background: linear-gradient(
    to right,
    rgba(30, 30, 50, 0.6),
    rgba(25, 25, 45, 0.6)
  );
}

.neo-lang-item {
  position: relative;
  transition: all 0.2s ease;
  overflow: hidden;
}

.neo-lang-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 136, 0, 0.05) 0%,
    rgba(255, 136, 0, 0) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.neo-lang-item:hover::before {
  opacity: 1;
}

.neo-lang-active {
  background: rgba(255, 126, 0, 0.08);
}

.neo-lang-active span:first-of-type {
  color: rgba(255, 126, 0, 1);
}

.flag-container {
  position: relative;
  z-index: 1;
}

.glow-effect {
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
}

.flag-container:hover .glow-effect {
  opacity: 1;
}

.flag-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 3px;
  transform-style: preserve-3d;
  perspective: 800px;
}

.lang-flag-glow {
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 3px;
}

.flag-wrapper:hover .lang-flag-glow {
  opacity: 1;
  animation: pulse-subtle 2s infinite;
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* When the language switcher appears on the login page */
.auth-page-background .neo-lang-switcher {
  background: rgba(12, 12, 20, 0.3);
  border-color: rgba(255, 255, 255, 0.05);
}

.auth-page-background .neo-lang-switcher:hover {
  background: rgba(20, 20, 30, 0.5);
  border-color: rgba(255, 126, 0, 0.15);
}

.auth-page-background .neo-lang-menu {
  background: rgba(10, 10, 18, 0.95);
}
