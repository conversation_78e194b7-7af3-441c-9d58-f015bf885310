import React, { createContext, useState, useContext, useEffect } from "react";
import { auth } from "../lib/firebase";
import { onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, User, updateProfile, updatePassword, EmailAuthProvider, reauthenticateWithCredential } from "firebase/auth";
import { useToast } from "@/hooks/use-toast";
import { doc, setDoc, getDoc } from "firebase/firestore";
import { db } from "../lib/firebase";

interface AuthContextType {
  currentUser: User | null;
  isAdmin: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<any>;
  signup: (email: string, password: string) => Promise<any>;
  logout: () => Promise<any>;
  updateUserProfile: (displayName?: string, photoURL?: string) => Promise<void>;
  updateUserPassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const { toast } = useToast();

  // Check if user is admin by fetching their role from Firestore
  const checkAdminStatus = async (user: User) => {
    try {
      const userDoc = await getDoc(doc(db, "users", user.uid));
      if (userDoc.exists()) {
        setIsAdmin(userDoc.data().role === "admin");
      }
    } catch (error) {
      console.error("Error checking admin status:", error);
      setIsAdmin(false);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      
      if (user) {
        await checkAdminStatus(user);
      } else {
        setIsAdmin(false);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      await checkAdminStatus(result.user);
      return result;
    } catch (error: any) {
      console.error("Firebase login error:", error.code, error.message);
      throw error;
    }
  };

  const signup = async (email: string, password: string) => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      
      // Create a user document in Firestore
      const userRef = doc(db, "users", result.user.uid);
      await setDoc(userRef, {
        email: result.user.email,
        username: result.user.displayName || email.split('@')[0],
        role: 'user', // Default role
        createdAt: new Date(),
      });
      
      return result;
    } catch (error: any) {
      console.error("Firebase signup error:", error.code, error.message);
      if (error.code === "auth/email-already-in-use") {
        throw new Error("البريد الإلكتروني مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر.");
      }
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      setIsAdmin(false);
      toast({
        title: "تم تسجيل الخروج بنجاح",
        description: "نتطلع لرؤيتك مرة أخرى",
      });
    } catch (error) {
      console.error("Logout error:", error);
      toast({
        title: "خطأ في تسجيل الخروج",
        description: "حدث خطأ أثناء تسجيل الخروج",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Add this new function to update user profile
  const updateUserProfile = async (displayName?: string, photoURL?: string) => {
    if (!currentUser) return;
    
    try {
      const updates: { displayName?: string; photoURL?: string } = {};
      
      if (displayName) updates.displayName = displayName;
      if (photoURL) updates.photoURL = photoURL;
      
      // Only proceed if we have updates to make
      if (Object.keys(updates).length > 0) {
        console.log("Updating Auth profile with:", updates);
        await updateProfile(currentUser, updates);
        console.log("Auth profile updated successfully");
        
        // Force a refresh of the user to get updated properties
        const user = auth.currentUser;
        if (user) {
          console.log("Reloading user data...");
          await user.reload();
          // This is important - we need to set the current user again to trigger re-renders
          setCurrentUser({...user});
          console.log("User data reloaded, new photoURL:", user.photoURL);
        }
      }
    } catch (error) {
      console.error("Error updating user profile:", error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحديث معلومات الملف الشخصي",
        variant: "destructive",
      });
    }
  };

  // Add function to update password with reauthentication
  const updateUserPassword = async (currentPassword: string, newPassword: string) => {
    if (!currentUser || !currentUser.email) {
      throw new Error("User not logged in or missing email");
    }
    
    try {
      // Re-authenticate the user first (required for sensitive operations)
      const credential = EmailAuthProvider.credential(
        currentUser.email, 
        currentPassword
      );
      
      await reauthenticateWithCredential(currentUser, credential);
      
      // Then update the password
      await updatePassword(currentUser, newPassword);
      
      toast({
        title: "تم تحديث كلمة المرور",
        description: "تم تغيير كلمة المرور بنجاح",
      });
    } catch (error: any) {
      console.error("Error updating password:", error);
      
      // Handle specific error cases
      if (error.code === 'auth/wrong-password') {
        toast({
          title: "خطأ",
          description: "كلمة المرور الحالية غير صحيحة",
          variant: "destructive",
        });
      } else if (error.code === 'auth/weak-password') {
        toast({
          title: "خطأ",
          description: "كلمة المرور الجديدة ضعيفة جدًا",
          variant: "destructive",
        });
      } else {
        toast({
          title: "خطأ",
          description: "حدث خطأ أثناء تحديث كلمة المرور",
          variant: "destructive",
        });
      }
      
      throw error;
    }
  };

  const value = {
    currentUser,
    isAdmin,
    loading,
    login,
    signup,
    logout,
    updateUserProfile,
    updateUserPassword
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
