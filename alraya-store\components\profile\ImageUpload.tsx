"use client"

import { useState, useRef, useCallback } from "react"
import { Button } from "@/components/ui/button"
import { 
  Camera, 
  Upload, 
  X, 
  Image as ImageIcon,
  AlertCircle,
  Check
} from "lucide-react"
import { cn } from "@/lib/utils"

interface ImageUploadProps {
  onImageUpload: (imageUrl: string) => void
  currentImage?: string | null
  maxSizeInMB?: number
  acceptedFormats?: string[]
  className?: string
}

export function ImageUpload({ 
  onImageUpload, 
  currentImage,
  maxSizeInMB = 5,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  className 
}: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSizeInMB * 1024 * 1024) {
      return `حجم الملف كبير جداً. الحد الأقصى ${maxSizeInMB} ميجابايت`
    }

    // Check file type
    if (!acceptedFormats.includes(file.type)) {
      return "نوع الملف غير مدعوم. يرجى استخدام JPG، PNG، أو WebP"
    }

    return null
  }

  const handleFileUpload = useCallback(async (file: File) => {
    setUploadError(null)
    setUploadSuccess(false)
    
    const validationError = validateFile(file)
    if (validationError) {
      setUploadError(validationError)
      return
    }

    setIsUploading(true)

    try {
      // Create a preview URL for the image
      const imageUrl = URL.createObjectURL(file)
      
      // ## In a real implementation, you would upload to your storage service here
      // ## For now, we'll simulate an upload and use the blob URL
      // ## Replace this with actual upload logic to Supabase Storage or similar
      
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // For demo purposes, we'll use the blob URL
      // In production, this would be the uploaded file URL from your storage service
      onImageUpload(imageUrl)
      
      setUploadSuccess(true)
      setTimeout(() => setUploadSuccess(false), 3000)
      
    } catch (error) {
      console.error("Upload error:", error)
      setUploadError("حدث خطأ أثناء رفع الصورة. يرجى المحاولة مرة أخرى")
    } finally {
      setIsUploading(false)
    }
  }, [onImageUpload, maxSizeInMB, acceptedFormats])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }, [handleFileUpload])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
  }, [handleFileUpload])

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  const handleRemoveImage = () => {
    onImageUpload("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <div
        className={cn(
          "relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200",
          isDragging 
            ? "border-yellow-400 bg-yellow-400/10" 
            : "border-slate-600 hover:border-slate-500",
          isUploading && "opacity-50 pointer-events-none"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="space-y-4">
          {isUploading ? (
            <div className="flex flex-col items-center space-y-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
              <p className="text-sm text-slate-300">جاري رفع الصورة...</p>
            </div>
          ) : (
            <>
              <div className="flex justify-center">
                <div className="p-3 bg-slate-700/50 rounded-full">
                  <Camera className="h-8 w-8 text-yellow-400" />
                </div>
              </div>
              
              <div className="space-y-2">
                <p className="text-slate-300 font-medium">
                  اسحب وأفلت صورة هنا أو انقر للاختيار
                </p>
                <p className="text-xs text-slate-400">
                  JPG، PNG، WebP - حتى {maxSizeInMB} ميجابايت
                </p>
              </div>
              
              <Button
                onClick={handleButtonClick}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700/50"
              >
                <Upload className="h-4 w-4 ml-2" />
                اختيار صورة
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Current Image Preview */}
      {currentImage && (
        <div className="relative">
          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg border border-slate-600/50">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="p-2 bg-slate-600/50 rounded">
                <ImageIcon className="h-4 w-4 text-slate-300" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-300">صورة الملف الشخصي</p>
                <p className="text-xs text-slate-400">تم تحديد الصورة</p>
              </div>
            </div>
            
            <Button
              onClick={handleRemoveImage}
              variant="ghost"
              size="sm"
              className="text-slate-400 hover:text-red-400 h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Success Message */}
      {uploadSuccess && (
        <div className="flex items-center space-x-2 space-x-reverse p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
          <Check className="h-4 w-4 text-green-400" />
          <p className="text-sm text-green-400">تم رفع الصورة بنجاح!</p>
        </div>
      )}

      {/* Error Message */}
      {uploadError && (
        <div className="flex items-center space-x-2 space-x-reverse p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <AlertCircle className="h-4 w-4 text-red-400" />
          <p className="text-sm text-red-400">{uploadError}</p>
        </div>
      )}
    </div>
  )
}
