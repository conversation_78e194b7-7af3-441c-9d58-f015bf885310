import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { getMods, ModModel } from "@/services/firestore";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/contexts/LanguageContext";
import { Input } from "@/components/ui/input";
import { Grid, List, Search, X, ShoppingCart, Info, Check, Laptop, Smartphone } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useConfig } from "@/contexts/ConfigContext";
import { useCart } from "@/contexts/CartContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { shouldShowLoader } from "@/lib/utils";
import GlobalLoader from "@/components/ui/GlobalLoader";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Add animation for cart button hover effect
const pulseAnimation = {
  scale: [1, 1.05, 1],
  boxShadow: [
    "0 0 0 0 rgba(240, 120, 40, 0)",
    "0 0 0 15px rgba(240, 120, 40, 0.4)",
    "0 0 0 0 rgba(240, 120, 40, 0)"
  ],
  transition: {
    duration: 1.8,
    repeat: Infinity,
    ease: "easeInOut"
  }
};

// Card hover animation
const cardHoverVariants = {
  idle: { 
    scale: 1,
    boxShadow: "0 0 0 0 rgba(240, 120, 40, 0)" 
  },
  hover: { 
    scale: 1.04, 
    boxShadow: "0 0 25px 5px rgba(240, 120, 40, 0.25)",
    transition: { 
      type: "spring", 
      stiffness: 300, 
      damping: 20 
    }
  }
};

// Magic circle animation for card hover
const magicCircleVariants = {
  initial: { 
    scale: 0,
    opacity: 0
  },
  animate: {
    scale: 1.8,
    opacity: [0, 0.3, 0],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Helper to get displayed text based on language and format "AR | EN"
const getLocalizedText = (text: string, language: string): string => {
  const parts = text.split(' | ');
  if (parts.length === 2) {
    // Format is "Arabic | English" so for Arabic language return first part, for English return second part
    return language === 'en' ? parts[1] : parts[0];
  }
  return text;
};

const Mods = () => {
  const { toast } = useToast();
  const { addItem, items } = useCart();
  const { contactInfo } = useConfig();
  const { language, isRTL } = useLanguage();
  const [mods, setMods] = useState<ModModel[]>([]);
  const [filteredMods, setFilteredMods] = useState<ModModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchTerm, setSearchTerm] = useState("");
  const [searchVisible, setSearchVisible] = useState(false);
  const [selectedMod, setSelectedMod] = useState<ModModel | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [platformFilter, setPlatformFilter] = useState<"all" | "pc" | "mobile">("all");
  const [mobileTypeFilter, setMobileTypeFilter] = useState<"all" | "android" | "ios">("all");

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const modsData = await getMods();
        setMods(modsData);
        setFilteredMods(modsData);
      } catch (error) {
        console.error("Error fetching mods:", error);
        toast({
          title: language === 'en' ? "Loading Error" : "خطأ في التحميل",
          description: language === 'en' ? "Failed to load hacks, please try again later" : "تعذر تحميل الهاكات، يرجى المحاولة مرة أخرى لاحقًا",
          variant: "destructive",
        });
        setMods([]);
        setFilteredMods([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Filter mods when search term or platform filters change
  useEffect(() => {
    let filtered = [...mods];
    
    // Filter by platform
    if (platformFilter !== "all") {
      filtered = filtered.filter(mod => mod.platform === platformFilter);
      
      // Filter mobile by type
      if (platformFilter === "mobile" && mobileTypeFilter !== "all") {
        filtered = filtered.filter(mod => mod.mobileType === mobileTypeFilter);
      }
    }
    
    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(mod => {
        const titleField = language === 'en' && mod.title_en ? mod.title_en : mod.title;
        const descriptionField = language === 'en' && mod.description_en ? mod.description_en : mod.description;
        
        // Also search in features 
        const featuresMatch = mod.features.some(feature => 
          getLocalizedText(feature, language).toLowerCase().includes(searchTerm.toLowerCase())
        );
        
        return titleField.toLowerCase().includes(searchTerm.toLowerCase()) ||
               descriptionField.toLowerCase().includes(searchTerm.toLowerCase()) ||
               featuresMatch;
      });
    }
    
    setFilteredMods(filtered);
  }, [searchTerm, mods, language, platformFilter, mobileTypeFilter]);

  const clearFilters = () => {
    setSearchTerm("");
    setPlatformFilter("all");
    setMobileTypeFilter("all");
    setFilteredMods(mods);
    setSearchVisible(false);
  };

  const handleAddToCart = (mod: ModModel) => {
    addItem(mod, 'mod');
    toast({
      title: language === 'en' ? "Added to Cart" : "تمت الإضافة إلى السلة",
      description: (
        <div className="flex flex-col">
          <span dir={language === 'en' ? 'ltr' : 'rtl'} className="font-medium">
            {language === 'en' 
              ? `${mod.title_en || mod.title} added to cart` 
              : `تمت إضافة ${mod.title} إلى سلة التسوق`
            }
          </span>
          <a 
            href="/cart" 
            className="text-pubg-orange hover:text-pubg-orange/80 mt-2 text-sm flex items-center transition-colors relative group overflow-hidden"
            dir={language === 'en' ? 'ltr' : 'rtl'}
          >
            <motion.span 
              className="absolute bottom-0 left-0 right-0 h-[1px] bg-pubg-orange"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.3 }}
            />
            <ShoppingCart className={`h-3.5 w-3.5 ${language === 'en' ? 'mr-1.5' : 'ml-1.5'} group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform`} />
            {language === 'en' ? 'Go to Cart' : 'الذهاب إلى السلة'}
          </a>
        </div>
      ),
      className: "bg-gray-900/90 border border-pubg-blue/30 backdrop-blur-sm",
    });
  };

  const isInCart = (modId: string) => {
    return items.some(item => item.id === modId);
  };

  const openModDetails = (mod: ModModel) => {
    setSelectedMod(mod);
    setShowDetails(true);
  };

  if (shouldShowLoader(isLoading)) {
    return (
      <div className="min-h-screen pt-24 pb-16">
        <GlobalLoader fullPage />
      </div>
    );
  }

  if (filteredMods.length === 0 && !searchTerm && platformFilter === "all") {
    return (
      <div className="min-h-screen pt-24 pb-16 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-3xl font-bold text-white mb-4">
            {language === 'en' ? 'Hacks' : 'الهاكات'}
          </h1>
          <p className="text-muted-foreground mb-8" dir={language === 'en' ? 'ltr' : 'rtl'}>
            {language === 'en' 
              ? 'No hacks are currently available. Please check back later.' 
              : 'لا توجد هاكات متاحة حاليًا. يرجى التحقق مرة أخرى لاحقًا.'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <div className="container mx-auto">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          className="mb-6 md:mb-6 flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {language === 'en' ? 'Hacks' : 'الهاكات'}
            </h1>
            <p className="text-muted-foreground text-sm mb-4 md:mb-0" dir={language === 'en' ? 'ltr' : 'rtl'}>
              {language === 'en' 
                ? 'The best PUBG Hacks available with the latest features and technologies' 
                : 'أفضل هاكات PUBG المتاحة مع أحدث الميزات والتقنيات'
              }
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Search toggle button */}
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setSearchVisible(!searchVisible)}
              className={`px-3 py-1 h-9 ${searchVisible ? "bg-pubg-blue/20 text-pubg-blue" : ""}`}
            >
              <Search size={16} />
            </Button>
            
            {/* View mode toggle */}
            <div className="border border-border rounded-md flex overflow-hidden">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setViewMode("grid")}
                className={`px-3 py-1 h-9 ${viewMode === "grid" ? "bg-pubg-blue/20 text-pubg-blue" : ""}`}
              >
                <Grid size={16} />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setViewMode("list")}
                className={`px-3 py-1 h-9 ${viewMode === "list" ? "bg-pubg-blue/20 text-pubg-blue" : ""}`}
              >
                <List size={16} />
              </Button>
            </div>
          </div>
        </motion.div>
        
        {/* Search */}
        {searchVisible && (
          <motion.div 
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-3 glass-card p-3 rounded-lg border border-pubg-blue/20 shadow-lg shadow-pubg-blue/5 backdrop-blur-md"
          >
            <div className="flex flex-col md:flex-row md:items-center gap-2">
              <div className="relative flex-1 group">
                <Search className="absolute right-2 top-2 h-4 w-4 text-muted-foreground group-focus-within:text-pubg-blue transition-colors" />
                <Input
                  placeholder={language === 'en' ? "Search hacks..." : "ابحث في الهاكات..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-2 pr-8 h-8 text-sm focus:border-pubg-blue/50 transition-colors"
                />
                {searchTerm && (
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="absolute left-1 top-1 h-6 w-6 hover:bg-pubg-blue/10 transition-colors" 
                    onClick={() => setSearchTerm("")}
                  >
                    <X size={12} />
                  </Button>
                )}
              </div>
              
              {/* Platform filter */}
              <div className="flex flex-wrap gap-1">
                <Badge 
                  variant={platformFilter === "all" ? "default" : "outline"}
                  className="cursor-pointer text-xs py-0.5"
                  onClick={() => {
                    setPlatformFilter("all");
                    setMobileTypeFilter("all");
                  }}
                >
                  {language === 'en' ? 'All' : 'الكل'}
                </Badge>
                <Badge 
                  variant={platformFilter === "pc" ? "default" : "outline"}
                  className="cursor-pointer flex items-center gap-1 text-xs py-0.5"
                  onClick={() => {
                    setPlatformFilter("pc");
                    setMobileTypeFilter("all");
                  }}
                >
                  <Laptop className="h-3 w-3" />
                  PC
                </Badge>
                <Badge 
                  variant={platformFilter === "mobile" ? "default" : "outline"}
                  className="cursor-pointer flex items-center gap-1 text-xs py-0.5"
                  onClick={() => setPlatformFilter("mobile")}
                >
                  <Smartphone className="h-3 w-3" />
                  {language === 'en' ? 'Mobile' : 'موبايل'}
                </Badge>
                
                {/* Mobile type filters, only shown when mobile is selected */}
                {platformFilter === "mobile" && (
                  <div className="flex gap-1 ml-1 pl-1 border-l border-border">
                    <Badge 
                      variant={mobileTypeFilter === "android" ? "default" : "outline"}
                      className="cursor-pointer bg-green-800/80 border-green-600/50 text-xs py-0.5"
                      onClick={() => setMobileTypeFilter("android")}
                    >
                      Android
                    </Badge>
                    <Badge 
                      variant={mobileTypeFilter === "ios" ? "default" : "outline"}
                      className="cursor-pointer bg-blue-800/80 border-blue-600/50 text-xs py-0.5"
                      onClick={() => setMobileTypeFilter("ios")}
                    >
                      iOS
                    </Badge>
                  </div>
                )}
              </div>
              
              {(searchTerm || platformFilter !== "all" || mobileTypeFilter !== "all") && (
                <Button variant="ghost" size="sm" className="h-7 text-xs" onClick={clearFilters}>
                  {language === 'en' ? 'Clear' : 'مسح'}
                </Button>
              )}
            </div>
            
            <div className="mt-2 text-xs text-muted-foreground">
              {filteredMods.length === 0 ? (
                <p>
                  {language === 'en' ? 'No results found' : 'لا توجد نتائج'}
                </p>
              ) : (
                <p>
                  {language === 'en' ? `Found ${filteredMods.length} hacks` : `تم العثور على ${filteredMods.length} هاك`}
                </p>
              )}
            </div>
          </motion.div>
        )}

        {/* Mods grid/list */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
          className={
            viewMode === "grid"
              ? "grid grid-cols-2 md:grid-cols-3 gap-3"
              : "flex flex-col gap-3"
          }
        >
          {filteredMods.map((mod) => (
            <motion.div key={mod.id} variants={fadeInUp}>
              <motion.div 
                className="cursor-pointer"
                initial="idle"
                whileHover="hover"
                variants={cardHoverVariants}
                onClick={() => openModDetails(mod)}
              >
                <Card className="glass-card border-none h-full transition-all duration-300 backdrop-blur-sm overflow-hidden relative group">
                  {/* Magic glow circle that appears on hover */}
                  <motion.div 
                    className="absolute -inset-1 rounded-lg bg-gradient-to-r from-pubg-orange/0 via-pubg-orange/30 to-pubg-orange/0 pointer-events-none z-0 opacity-0"
                    initial="initial"
                    whileHover="animate"
                    variants={magicCircleVariants}
                  />
                  
                  {viewMode === "grid" ? (
                    <div className="flex flex-col h-full">
                      <div className="relative overflow-hidden">
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-br from-pubg-orange/30 to-yellow-500/20 opacity-0 z-10"
                          initial={{ opacity: 0 }}
                          whileHover={{ opacity: 0.6, transition: { duration: 0.3 } }}
                        />
                        <img
                          src={mod.image}
                          alt={mod.title}
                          className="w-full h-[120px] object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-pubg-orange/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-3 z-20">
                          <motion.span 
                            className="text-white text-xs px-4 py-1.5 rounded-full bg-pubg-orange/90 backdrop-blur-sm font-medium shadow-lg shadow-pubg-orange/30"
                            initial={{ y: 10, opacity: 0 }}
                            whileHover={{ y: 0, opacity: 1, scale: 1.05 }}
                          >
                            {language === 'en' ? 'View Details' : 'عرض التفاصيل'}
                          </motion.span>
                        </div>
                        {isInCart(mod.id!) && (
                          <div className="absolute top-2 right-2 bg-pubg-orange text-pubg-dark text-xs px-2 py-1 rounded-full shadow-lg shadow-pubg-orange/20 animate-pulse">
                            {language === 'en' ? 'In Cart' : 'في السلة'}
                          </div>
                        )}
                        
                        {/* Platform badge */}
                        <div className="absolute top-2 left-2 flex items-center gap-1">
                          {mod.platform === "pc" ? (
                            <Badge variant="outline" className="bg-slate-800/80 text-white text-xs flex items-center gap-1 border-none">
                              <Laptop className="h-3 w-3" />
                              PC
                            </Badge>
                          ) : mod.mobileType === "android" ? (
                            <Badge variant="outline" className="bg-green-800/80 text-white text-xs flex items-center gap-1 border-none">
                              <Smartphone className="h-3 w-3" />
                              Android
                              {mod.rootRequired && (
                                <span className="ml-1 text-[10px] bg-yellow-600/80 px-1 rounded">ROOT</span>
                              )}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-blue-800/80 text-white text-xs flex items-center gap-1 border-none">
                              <Smartphone className="h-3 w-3" />
                              iOS
                              {mod.jailbreakRequired && (
                                <span className="ml-1 text-[10px] bg-yellow-600/80 px-1 rounded">JB</span>
                              )}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="p-2 flex flex-col flex-grow">
                        <h3 className="text-sm font-medium text-white line-clamp-1" dir={language === 'en' ? 'ltr' : 'rtl'}>
                          {language === 'en' && mod.title_en ? mod.title_en : mod.title}
                        </h3>
                        <p className="text-xs text-muted-foreground mt-1">
                          ${mod.priceUSD}
                          {mod.localCurrency ? ` / ${mod.localCurrency} ${mod.localCurrencyCode || ''}` : ''}
                        </p>
                      </div>
                      <div className="px-2 pb-2 mt-auto" onClick={(e) => e.stopPropagation()}>
                        <Button 
                          className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 w-full relative overflow-hidden transition-all group h-9 text-sm font-medium hover:shadow-lg hover:shadow-pubg-orange/30"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddToCart(mod);
                          }}
                          disabled={isInCart(mod.id!)}
                        >
                          {isInCart(mod.id!) ? (
                            <>
                              <motion.div 
                                className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-green-600/20"
                                initial={false}
                                animate={{ opacity: [0.5, 0.8, 0.5] }}
                                transition={{ duration: 2, repeat: Infinity }}
                              />
                              <Check className={`${language === 'en' ? 'mr-2' : 'ml-2'} h-4 w-4 relative z-10`} />
                              <span className="relative z-10">{language === 'en' ? 'Added' : 'في السلة'}</span>
                            </>
                          ) : (
                            <>
                              <motion.div 
                                className="absolute inset-0 bg-gradient-to-r from-pubg-orange/20 to-yellow-500/30 opacity-0 group-hover:opacity-100"
                                initial={false}
                                animate={pulseAnimation}
                              />
                              <ShoppingCart className={`${language === 'en' ? 'mr-2' : 'ml-2'} h-4 w-4 relative z-10 group-hover:scale-110 transition-transform`} />
                              <span className="relative z-10">{language === 'en' ? 'Add to Cart' : 'إضافة للسلة'}</span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // List view layout
                    <div className="flex">
                      <div className="w-[100px] relative overflow-hidden">
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-br from-pubg-orange/30 to-yellow-500/20 opacity-0 z-10"
                          initial={{ opacity: 0 }}
                          whileHover={{ opacity: 0.6, transition: { duration: 0.3 } }}
                        />
                        <img
                          src={mod.image}
                          alt={mod.title}
                          className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-r from-pubg-orange/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center z-20">
                          <motion.span 
                            className="text-white text-xs px-4 py-1.5 rounded-full bg-pubg-orange/90 backdrop-blur-sm font-medium shadow-lg shadow-pubg-orange/30"
                            initial={{ x: -10, opacity: 0 }}
                            whileHover={{ x: 0, opacity: 1, scale: 1.05 }}
                          >
                            {language === 'en' ? 'View Details' : 'عرض التفاصيل'}
                          </motion.span>
                        </div>
                        {isInCart(mod.id!) && (
                          <div className="absolute top-2 right-2 bg-pubg-orange text-pubg-dark text-xs px-1 py-0.5 rounded-full">
                            {language === 'en' ? 'In Cart' : 'في السلة'}
                          </div>
                        )}
                        
                        {/* Platform badge */}
                        <div className="absolute top-2 left-2">
                          {mod.platform === "pc" ? (
                            <Badge variant="outline" className="bg-slate-800/80 text-white text-[10px] flex items-center gap-0.5 border-none px-1 py-0">
                              <Laptop className="h-2 w-2" />
                              PC
                            </Badge>
                          ) : mod.mobileType === "android" ? (
                            <Badge variant="outline" className="bg-green-800/80 text-white text-[10px] flex items-center gap-0.5 border-none px-1 py-0">
                              <Smartphone className="h-2 w-2" />
                              Android
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-blue-800/80 text-white text-[10px] flex items-center gap-0.5 border-none px-1 py-0">
                              <Smartphone className="h-2 w-2" />
                              iOS
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex-1 p-3 flex flex-col">
                        <div>
                          <h3 className="font-bold text-white text-sm" dir={language === 'en' ? 'ltr' : 'rtl'}>
                            {language === 'en' && mod.title_en ? mod.title_en : mod.title}
                          </h3>
                          <p className="text-xs text-muted-foreground mt-1">
                            ${mod.priceUSD}
                            {mod.localCurrency ? ` / ${mod.localCurrency} ${mod.localCurrencyCode || ''}` : ''}
                          </p>
                        </div>
                        <div className="mt-auto pt-2" onClick={(e) => e.stopPropagation()}>
                          <Button 
                            size="sm" 
                            className="h-8 bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 relative overflow-hidden group w-full text-xs font-medium hover:shadow-md hover:shadow-pubg-orange/30"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddToCart(mod);
                            }}
                            disabled={isInCart(mod.id!)}
                          >
                            {isInCart(mod.id!) ? (
                              <>
                                <motion.div 
                                  className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-green-600/20"
                                  initial={false}
                                  animate={{ opacity: [0.5, 0.8, 0.5] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                />
                                <Check className={`${language === 'en' ? 'mr-1' : 'ml-1'} h-3 w-3 relative z-10`} />
                                <span className="relative z-10">{language === 'en' ? 'Added' : 'في السلة'}</span>
                              </>
                            ) : (
                              <>
                                <motion.div 
                                  className="absolute inset-0 bg-gradient-to-r from-pubg-orange/20 to-yellow-500/30 opacity-0 group-hover:opacity-100"
                                  initial={false}
                                  animate={pulseAnimation}
                                />
                                <ShoppingCart className={`${language === 'en' ? 'mr-1' : 'ml-1'} h-3 w-3 relative z-10 group-hover:scale-110 transition-transform`} />
                                <span className="relative z-10">{language === 'en' ? 'Add to Cart' : 'إضافة للسلة'}</span>
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </Card>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>
        
        {/* Mod Details Dialog */}
        <Dialog open={showDetails} onOpenChange={setShowDetails}>
          <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto p-3 sm:p-4 border-pubg-blue/30 backdrop-blur-sm">
            {selectedMod && (
              <>
                <DialogHeader className="mb-2 sm:mb-3 space-y-1">
                  <div className="flex items-center gap-2">
                    <DialogTitle className="text-base sm:text-lg text-pubg-blue" dir={language === 'en' ? 'ltr' : 'rtl'}>
                      {language === 'en' && selectedMod.title_en ? selectedMod.title_en : selectedMod.title}
                    </DialogTitle>
                    
                    {/* Platform badge */}
                    {selectedMod.platform === "pc" ? (
                      <Badge variant="outline" className="bg-slate-800/80 text-white text-xs flex items-center gap-1 border-none shadow-md">
                        <Laptop className="h-3 w-3" />
                        PC
                      </Badge>
                    ) : selectedMod.mobileType === "android" ? (
                      <Badge variant="outline" className="bg-green-800/80 text-white text-xs flex items-center gap-1 border-none shadow-md">
                        <Smartphone className="h-3 w-3" />
                        Android
                        {selectedMod.rootRequired && (
                          <span className="ml-1 text-[10px] bg-yellow-600/80 px-1 rounded">ROOT</span>
                        )}
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-blue-800/80 text-white text-xs flex items-center gap-1 border-none shadow-md">
                        <Smartphone className="h-3 w-3" />
                        iOS
                        {selectedMod.jailbreakRequired && (
                          <span className="ml-1 text-[10px] bg-yellow-600/80 px-1 rounded">JB</span>
                        )}
                      </Badge>
                    )}
                  </div>
                  <DialogDescription className="text-xs sm:text-sm">
                    ${selectedMod.priceUSD}
                    {selectedMod.localCurrency ? ` / ${selectedMod.localCurrency} ${selectedMod.localCurrencyCode || ''}` : ''}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-3 my-2">
                  <div className="w-full flex justify-center">
                    <img 
                      src={selectedMod.image} 
                      alt={selectedMod.title} 
                      className="w-full max-h-[180px] object-cover rounded-lg" 
                    />
                  </div>
                  <div>
                    <div className="mb-2">
                      <h3 className="text-sm sm:text-base font-semibold text-white mb-1">
                        {language === 'en' ? 'Description' : 'الوصف'}
                      </h3>
                      <p className="text-xs sm:text-sm text-muted-foreground" dir={language === 'en' ? 'ltr' : 'rtl'}>
                        {language === 'en' && selectedMod.description_en ? selectedMod.description_en : selectedMod.description}
                      </p>
                    </div>
                    
                    <div className="mb-2">
                      <h3 className="text-sm sm:text-base font-semibold text-white mb-1">
                        {language === 'en' ? 'Features' : 'المميزات'}
                      </h3>
                      <ul className="space-y-1">
                        {selectedMod.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-xs sm:text-sm text-muted-foreground">
                            <Check className={`h-3 w-3 text-pubg-blue ${language === 'en' ? 'mr-1' : 'ml-1'} flex-shrink-0`} />
                            <span dir={language === 'en' ? 'ltr' : 'rtl'}>
                              {getLocalizedText(feature, language)}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
                <DialogFooter className="mt-3 gap-2 flex-col sm:flex-row">
                  <Button 
                    variant="outline" 
                    className="w-full sm:w-auto order-2 sm:order-1 hover:bg-gray-800/50 transition-colors text-sm h-8"
                    onClick={() => setShowDetails(false)}
                  >
                    {language === 'en' ? 'Close' : 'إغلاق'}
                  </Button>
                  <Button 
                    className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 w-full sm:w-auto order-1 sm:order-2 relative overflow-hidden group text-sm h-9 font-medium hover:shadow-lg hover:shadow-pubg-orange/30"
                    onClick={() => {
                      handleAddToCart(selectedMod);
                      setShowDetails(false);
                    }}
                    disabled={isInCart(selectedMod.id!)}
                  >
                    {isInCart(selectedMod.id!) ? (
                      <>
                        <motion.div 
                          className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-green-600/20"
                          initial={false}
                          animate={{ opacity: [0.5, 0.8, 0.5] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                        <Check className={`${language === 'en' ? 'mr-1' : 'ml-1'} h-3 w-3 relative z-10`} />
                        <span className="relative z-10">{language === 'en' ? 'Added' : 'تمت الإضافة'}</span>
                      </>
                    ) : (
                      <>
                        <motion.div 
                          className="absolute inset-0 bg-gradient-to-r from-pubg-orange/20 to-yellow-500/30 opacity-0 group-hover:opacity-100"
                          initial={false}
                          animate={pulseAnimation}
                        />
                        <ShoppingCart className={`${language === 'en' ? 'mr-1' : 'ml-1'} h-4 w-4 relative z-10 group-hover:scale-110 transition-transform`} />
                        <span className="relative z-10">{language === 'en' ? 'Add to Cart' : 'إضافة للسلة'}</span>
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Mods;