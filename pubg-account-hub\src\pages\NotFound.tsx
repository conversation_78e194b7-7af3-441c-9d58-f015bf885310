
import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
        className="text-center"
      >
        <h1 className="text-9xl font-bold text-pubg-orange mb-4">404</h1>
        <p className="text-2xl text-white mb-8">عذراً، الصفحة غير موجودة</p>
        <p className="text-muted-foreground mb-8 max-w-md mx-auto">
          يبدو أن الصفحة التي تبحث عنها غير موجودة أو ربما تم نقلها أو حذفها.
        </p>
        <Button asChild className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 font-bold px-8">
          <Link to="/">العودة للصفحة الرئيسية</Link>
        </Button>
      </motion.div>
    </div>
  );
};

export default NotFound;
