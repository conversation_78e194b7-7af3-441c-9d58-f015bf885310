import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Pencil, Trash2, Save, X, UserPlus, Users, Shield, UserCog, UserX } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { UserModel, getUsers, getUserById, updateUser, deleteUser, addUser } from "@/services/firestore";
import { Timestamp } from "firebase/firestore";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tabs,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const UsersManager = () => {
  const { toast } = useToast();
  const [users, setUsers] = useState<UserModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserModel | null>(null);
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [userFormData, setUserFormData] = useState<Partial<UserModel>>({
    uid: "",
    email: "",
    displayName: "",
    phoneNumber: "",
    photoURL: "",
    role: "user",
    isActive: true,
  });
  
  useEffect(() => {
    fetchUsers();
  }, []);
  
  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const fetchedUsers = await getUsers();
      setUsers(fetchedUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل بيانات المستخدمين",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    if (type === "checkbox") {
      setUserFormData({ ...userFormData, [name]: checked });
    } else {
      setUserFormData({ ...userFormData, [name]: value });
    }
  };
  
  const handleRoleChange = (role: string) => {
    setUserFormData({ ...userFormData, role: role as "admin" | "user" });
  };
  
  const handleEditUser = (user: UserModel) => {
    setSelectedUser(user);
    setUserFormData({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || "",
      phoneNumber: user.phoneNumber || "",
      photoURL: user.photoURL || "",
      role: user.role,
      isActive: user.isActive !== undefined ? user.isActive : true,
    });
    setIsUpdating(true);
  };
  
  const handleCreateUser = () => {
    setUserFormData({
      uid: "",
      email: "",
      displayName: "",
      phoneNumber: "",
      photoURL: "",
      role: "user",
      isActive: true,
    });
    setIsCreating(true);
  };
  
  const formatDate = (timestamp: any) => {
    if (!timestamp) return "غير متوفر";
    
    if (timestamp instanceof Timestamp) {
      return new Date(timestamp.seconds * 1000).toLocaleString("ar-EG");
    }
    
    if (timestamp.toDate) {
      return timestamp.toDate().toLocaleString("ar-EG");
    }
    
    return "غير متوفر";
  };
  
  const handleSubmitUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser?.id) return;
    
    // Create a clean copy of user data without undefined values
    const cleanUserData = { ...userFormData };
    
    // Make sure uid is defined and not undefined
    if (!cleanUserData.uid || cleanUserData.uid === undefined) {
      cleanUserData.uid = selectedUser.uid;
    }
    
    // Ensure isActive is defined and not undefined
    if (cleanUserData.isActive === undefined) {
      cleanUserData.isActive = selectedUser.isActive !== undefined ? selectedUser.isActive : true;
    }
    
    // Filter out any undefined values
    Object.keys(cleanUserData).forEach(key => {
      if (cleanUserData[key] === undefined) {
        delete cleanUserData[key];
      }
    });
    
    setIsLoading(true);
    try {
      await updateUser(selectedUser.id, cleanUserData);
      
      // Update local state
      setUsers(users.map(user => 
        user.id === selectedUser.id 
          ? { ...user, ...cleanUserData } 
          : user
      ));
      
      toast({
        title: "تم بنجاح",
        description: "تم تحديث بيانات المستخدم بنجاح",
      });
      
      setIsUpdating(false);
      setSelectedUser(null);
    } catch (error) {
      console.error("Error updating user:", error);
      toast({
        title: "خطأ",
        description: "فشل في تحديث بيانات المستخدم",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSubmitCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userFormData.email || !userFormData.uid) {
      toast({
        title: "خطأ",
        description: "البريد الإلكتروني والمعرف مطلوبان",
        variant: "destructive",
      });
      return;
    }
    
    // Create a clean copy of user data without undefined values
    const cleanUserData = { ...userFormData };
    
    // Ensure isActive is defined
    if (cleanUserData.isActive === undefined) {
      cleanUserData.isActive = true;
    }
    
    // Filter out any undefined values
    Object.keys(cleanUserData).forEach(key => {
      if (cleanUserData[key] === undefined) {
        delete cleanUserData[key];
      }
    });
    
    setIsLoading(true);
    try {
      const userId = await addUser(cleanUserData as Omit<UserModel, "id" | "createdAt" | "updatedAt">);
      
      // Update local state
      const newUser = { 
        ...cleanUserData, 
        id: userId,
        createdAt: Timestamp.now()
      } as UserModel;
      
      setUsers([...users, newUser]);
      
      toast({
        title: "تم بنجاح",
        description: "تم إضافة المستخدم بنجاح",
      });
      
      setIsCreating(false);
    } catch (error) {
      console.error("Error creating user:", error);
      toast({
        title: "خطأ",
        description: "فشل في إضافة المستخدم",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDeleteUser = async (id: string) => {
    if (!window.confirm("هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.")) {
      return;
    }
    
    setIsLoading(true);
    try {
      await deleteUser(id);
      
      // Update local state
      setUsers(users.filter(user => user.id !== id));
      
      toast({
        title: "تم بنجاح",
        description: "تم حذف المستخدم بنجاح",
      });
    } catch (error) {
      console.error("Error deleting user:", error);
      toast({
        title: "خطأ",
        description: "فشل في حذف المستخدم",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const filteredUsers = users.filter(user => {
    if (activeTab === "admins" && user.role !== "admin") return false;
    if (activeTab === "users" && user.role !== "user") return false;
    
    // Search filtering
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return (
        user.email.toLowerCase().includes(term) ||
        (user.displayName && user.displayName.toLowerCase().includes(term)) ||
        (user.phoneNumber && user.phoneNumber.includes(term)) ||
        user.uid.toLowerCase().includes(term)
      );
    }
    
    return true;
  });
  
  // Get role icon and color
  const getUserRoleInfo = (role: string) => {
    switch (role) {
      case "admin":
        return { icon: Shield, color: "text-pubg-orange", label: "مدير" };
      case "user":
        return { icon: Users, color: "text-pubg-blue", label: "مستخدم" };
      default:
        return { icon: UserCog, color: "text-gray-400", label: "غير محدد" };
    }
  };
  
  // User status badge
  const getUserStatusBadge = (user: UserModel) => {
    if (user.isActive === false) {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 ml-2">
          <UserX className="w-3 h-3 ml-1" />
          محظور
        </span>
      );
    }
    return null;
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-pubg-orange"></div>
        <span className="mr-2 text-sm md:text-base">جاري تحميل البيانات...</span>
      </div>
    );
  }
  
  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-6">
        <h2 className="text-lg md:text-xl font-bold text-white">إدارة المستخدمين</h2>
        <Button
          onClick={handleCreateUser}
          className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 w-full sm:w-auto text-xs md:text-sm h-9 md:h-10"
        >
          <UserPlus className="ml-2 h-4 w-4" />
          إضافة مستخدم
        </Button>
      </div>
      
      <div className="mb-6 space-y-4">
        <div className="flex flex-col md:flex-row gap-3">
          <Input
            placeholder="بحث عن مستخدم..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="md:flex-1 h-9 md:h-10 text-sm md:text-base"
          />
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
            <TabsList className="grid grid-cols-3 h-9 md:h-10">
              <TabsTrigger value="all" className="text-xs md:text-sm">الكل</TabsTrigger>
              <TabsTrigger value="users" className="text-xs md:text-sm">المستخدمين</TabsTrigger>
              <TabsTrigger value="admins" className="text-xs md:text-sm">المديرين</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      
      <div className="space-y-3 md:space-y-4">
        {filteredUsers.length > 0 ? (
          filteredUsers.map((user) => {
            const roleInfo = getUserRoleInfo(user.role);
            const RoleIcon = roleInfo.icon;
            
            return (
              <div
                key={user.id}
                className="glass-card rounded-lg p-3 md:p-4 flex flex-col sm:flex-row gap-3 justify-between"
              >
                <div className="flex flex-1 items-center min-w-0">
                  <div className="ml-3 md:ml-4">
                    {user.photoURL ? (
                      <div className="w-10 h-10 md:w-12 md:h-12 rounded-full overflow-hidden">
                        <img
                          src={user.photoURL}
                          alt={user.displayName || "مستخدم"}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className={`w-10 h-10 md:w-12 md:h-12 rounded-full bg-pubg-dark/50 flex items-center justify-center ${roleInfo.color}`}>
                        <RoleIcon className="w-5 h-5" />
                      </div>
                    )}
                  </div>
                  
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center flex-wrap">
                      <h3 className="text-sm md:text-base font-medium text-white truncate ml-2">
                        {user.displayName || "مستخدم بدون اسم"}
                      </h3>
                      {getUserStatusBadge(user)}
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-pubg-dark/50 ${roleInfo.color}`}>
                        <RoleIcon className="w-3 h-3 ml-1" />
                        {roleInfo.label}
                      </span>
                    </div>
                    <p className="text-xs md:text-sm text-muted-foreground truncate">{user.email}</p>
                    {user.phoneNumber && (
                      <p className="text-xs text-muted-foreground truncate">{user.phoneNumber}</p>
                    )}
                    <p className="text-xs text-muted-foreground truncate mt-1">
                      آخر تسجيل دخول: {formatDate(user.lastLogin)}
                    </p>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-2 space-x-reverse">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditUser(user)}
                    className="text-pubg-blue hover:text-pubg-blue hover:bg-pubg-blue/10 h-8 w-8 md:h-9 md:w-9 p-0"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteUser(user.id!)}
                    className="text-red-500 hover:text-red-500 hover:bg-red-500/10 h-8 w-8 md:h-9 md:w-9 p-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center py-6 border border-dashed border-border rounded-lg">
            <p className="text-sm md:text-base text-muted-foreground">
              {searchTerm ? "لا توجد نتائج مطابقة للبحث" : "لا يوجد مستخدمين حاليًا"}
            </p>
          </div>
        )}
      </div>

      {/* Update User Dialog */}
      <Dialog open={isUpdating} onOpenChange={(open) => !open && setIsUpdating(false)}>
        <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-lg">تعديل بيانات المستخدم</DialogTitle>
            <DialogDescription className="text-sm">
              قم بتعديل بيانات المستخدم المحدد.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmitUpdate} className="space-y-3 md:space-y-4">
            <div className="space-y-1 md:space-y-2">
              <Label htmlFor="uid" className="text-sm md:text-base">معرف المستخدم (UID)</Label>
              <Input
                id="uid"
                name="uid"
                value={userFormData.uid}
                onChange={handleInputChange}
                disabled
                className="h-9 md:h-10 text-sm md:text-base bg-muted"
              />
            </div>
            
            <div className="space-y-1 md:space-y-2">
              <Label htmlFor="email" className="text-sm md:text-base">البريد الإلكتروني</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={userFormData.email}
                onChange={handleInputChange}
                required
                className="h-9 md:h-10 text-sm md:text-base"
              />
            </div>
            
            <div className="space-y-1 md:space-y-2">
              <Label htmlFor="displayName" className="text-sm md:text-base">الاسم المعروض</Label>
              <Input
                id="displayName"
                name="displayName"
                value={userFormData.displayName}
                onChange={handleInputChange}
                className="h-9 md:h-10 text-sm md:text-base"
              />
            </div>
            
            <div className="space-y-1 md:space-y-2">
              <Label htmlFor="phoneNumber" className="text-sm md:text-base">رقم الهاتف</Label>
              <Input
                id="phoneNumber"
                name="phoneNumber"
                value={userFormData.phoneNumber}
                onChange={handleInputChange}
                className="h-9 md:h-10 text-sm md:text-base"
              />
            </div>
            
            <div className="space-y-1 md:space-y-2">
              <Label htmlFor="photoURL" className="text-sm md:text-base">رابط الصورة الشخصية</Label>
              <Input
                id="photoURL"
                name="photoURL"
                value={userFormData.photoURL}
                onChange={handleInputChange}
                className="h-9 md:h-10 text-sm md:text-base"
              />
            </div>
            
            <div className="space-y-1 md:space-y-2">
              <Label htmlFor="role" className="text-sm md:text-base">دور المستخدم</Label>
              <Select value={userFormData.role} onValueChange={handleRoleChange}>
                <SelectTrigger id="role" className="h-9 md:h-10 text-sm md:text-base">
                  <SelectValue placeholder="اختر دور المستخدم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">مستخدم عادي</SelectItem>
                  <SelectItem value="admin">مدير</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center pt-2">
              <Switch
                id="isActive"
                name="isActive"
                checked={userFormData.isActive !== false}
                onCheckedChange={(checked) => setUserFormData({ ...userFormData, isActive: checked })}
              />
              <Label htmlFor="isActive" className="mr-2 text-sm md:text-base">
                المستخدم نشط
              </Label>
            </div>
            
            <DialogFooter className="mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsUpdating(false)}
                className="ml-2 h-9 md:h-10 text-xs md:text-sm"
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-pubg-blue text-white hover:bg-pubg-blue/90 h-9 md:h-10 text-xs md:text-sm"
              >
                {isLoading ? "جاري الحفظ..." : "حفظ التغييرات"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create User Dialog */}
      <Dialog open={isCreating} onOpenChange={(open) => !open && setIsCreating(false)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>إضافة مستخدم جديد</DialogTitle>
            <DialogDescription>
              قم بإدخال بيانات المستخدم الجديد.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmitCreate} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="create-uid">معرف المستخدم (UID)</Label>
              <Input
                id="create-uid"
                name="uid"
                value={userFormData.uid}
                onChange={handleInputChange}
                placeholder="معرف المستخدم"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="create-email">البريد الإلكتروني</Label>
              <Input
                id="create-email"
                name="email"
                type="email"
                value={userFormData.email}
                onChange={handleInputChange}
                placeholder="البريد الإلكتروني"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="create-displayName">الاسم المعروض</Label>
              <Input
                id="create-displayName"
                name="displayName"
                value={userFormData.displayName}
                onChange={handleInputChange}
                placeholder="الاسم المعروض"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="create-phoneNumber">رقم الهاتف</Label>
              <Input
                id="create-phoneNumber"
                name="phoneNumber"
                value={userFormData.phoneNumber}
                onChange={handleInputChange}
                placeholder="رقم الهاتف"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="create-photoURL">رابط الصورة الشخصية</Label>
              <Input
                id="create-photoURL"
                name="photoURL"
                value={userFormData.photoURL}
                onChange={handleInputChange}
                placeholder="رابط الصورة"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="create-role">دور المستخدم</Label>
              <Select value={userFormData.role} onValueChange={handleRoleChange}>
                <SelectTrigger id="create-role">
                  <SelectValue placeholder="اختر دور المستخدم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">مستخدم عادي</SelectItem>
                  <SelectItem value="admin">مدير</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreating(false)}
                className="ml-2"
              >
                <X className="ml-2 h-4 w-4" />
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90"
              >
                <UserPlus className="ml-2 h-4 w-4" />
                إضافة المستخدم
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UsersManager; 