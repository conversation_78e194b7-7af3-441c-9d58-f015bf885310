"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/ProductDashboard.tsx":
/*!***********************************************!*\
  !*** ./components/admin/ProductDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductDashboard: () => (/* binding */ ProductDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_services_categoryService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/services/categoryService */ \"(app-pages-browser)/./lib/services/categoryService.ts\");\n/* harmony import */ var _SimpleProductForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./SimpleProductForm */ \"(app-pages-browser)/./components/admin/SimpleProductForm.tsx\");\n/* harmony import */ var _CategoryManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CategoryManager */ \"(app-pages-browser)/./components/admin/CategoryManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductDashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductDashboard() {\n    _s();\n    // ## TODO: Add user authentication check\n    // ## TODO: Implement real-time updates with Supabase subscriptions\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sub-navigation state\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"products\");\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load products and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            (0,_lib_services_categoryService__WEBPACK_IMPORTED_MODULE_8__.initializeCategories)() // Initialize default categories\n            ;\n            loadData();\n        }\n    }[\"ProductDashboard.useEffect\"], []);\n    // Apply filters when search or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"ProductDashboard.useEffect\"], [\n        products,\n        searchQuery,\n        filters\n    ]);\n    /**\n   * ## TODO: Replace with Supabase real-time subscription\n   * Load products and statistics\n   */ const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [productsData, statsData] = await Promise.all([\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_7__.getProducts)().catch((err)=>{\n                    console.error(\"Error loading products:\", err);\n                    return [];\n                }),\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_7__.getProductStats)().catch((err)=>{\n                    console.error(\"Error loading stats:\", err);\n                    return {\n                        totalProducts: 0,\n                        activeProducts: 0,\n                        digitalProducts: 0,\n                        physicalProducts: 0,\n                        totalPackages: 0,\n                        totalOrders: 0,\n                        popularCategories: []\n                    };\n                })\n            ]);\n            setProducts(Array.isArray(productsData) ? productsData : []);\n            setStats(statsData);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            // Set fallback data\n            setProducts([]);\n            setStats({\n                totalProducts: 0,\n                activeProducts: 0,\n                digitalProducts: 0,\n                physicalProducts: 0,\n                totalPackages: 0,\n                totalOrders: 0,\n                popularCategories: []\n            });\n        // ## TODO: Show error toast notification\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Apply search and filters to products\n   */ const applyFilters = ()=>{\n        // Ensure products is a valid array\n        const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.name && p.category) : [];\n        let filtered = [\n            ...validProducts\n        ];\n        // Apply search\n        if (searchQuery.trim()) {\n            const query = searchQuery.toLowerCase();\n            filtered = filtered.filter((product)=>{\n                var _product_name, _product_description, _product_category;\n                return ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.toLowerCase().includes(query)) || ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(query)) || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.toLowerCase().includes(query));\n            });\n        }\n        // Apply filters\n        if (filters.category) {\n            filtered = filtered.filter((p)=>p.category === filters.category);\n        }\n        if (filters.productType) {\n            filtered = filtered.filter((p)=>p.productType === filters.productType);\n        }\n        if (filters.processingType) {\n            filtered = filtered.filter((p)=>p.processingType === filters.processingType);\n        }\n        if (filters.isActive !== undefined) {\n            filtered = filtered.filter((p)=>p.isActive === filters.isActive);\n        }\n        setFilteredProducts(filtered);\n    };\n    /**\n   * Handle product creation\n   */ const handleProductCreate = async (product)=>{\n        setProducts((prev)=>[\n                ...prev,\n                product\n            ]);\n        setIsCreateDialogOpen(false);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product update\n   */ const handleProductUpdate = async (product)=>{\n        setProducts((prev)=>prev.map((p)=>p.id === product.id ? product : p));\n        setIsEditDialogOpen(false);\n        setSelectedProduct(null);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product deletion\n   */ const handleProductDelete = async (productId)=>{\n        if (!confirm(\"هل أنت متأكد من حذف هذا المنتج؟\")) return;\n        try {\n            await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_7__.deleteProduct)(productId);\n            setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n            await loadData() // Refresh stats\n            ;\n        } catch (error) {\n            console.error(\"Error deleting product:\", error);\n        // ## TODO: Show error toast notification\n        }\n    };\n    /**\n   * Get unique categories for filter dropdown\n   */ const getCategories = ()=>{\n        const categories = [\n            ...new Set(products.map((p)=>p.category))\n        ];\n        return categories.sort();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 p-4 lg:p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl lg:text-3xl font-bold text-white\",\n                                children: \"إدارة المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400 mt-1\",\n                                children: \"إنشاء وتعديل وإدارة منتجات المتجر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>setIsCreateDialogOpen(true),\n                        className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 min-h-[44px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            \"إضافة منتج جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            isCreateDialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    onSave: handleProductCreate,\n                    onCancel: ()=>setIsCreateDialogOpen(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 bg-slate-800/50 rounded-lg p-1 border border-slate-700/50 overflow-x-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"products\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"products\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"منتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"categories\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"categories\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"الفئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"فئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            activeTab === \"categories\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CategoryManager__WEBPACK_IMPORTED_MODULE_10__.CategoryManager, {\n                onCategoryChange: loadData\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي المنتجات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: stats.totalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات النشطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: stats.activeProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات الرقمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-400\",\n                                                        children: stats.digitalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-8 w-8 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي الحزم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-400\",\n                                                        children: stats.totalPackages\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"البحث في المنتجات...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-slate-700 border-slate-600 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.category || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"الفئة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الفئات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            getCategories().map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 21\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.productType || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            productType: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الأنواع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"digital\",\n                                                                children: \"رقمي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"physical\",\n                                                                children: \"مادي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"service\",\n                                                                children: \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredProducts.map((product)=>{\n                            var _product_packages;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-white text-lg mb-2\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: product.isActive ? \"default\" : \"secondary\",\n                                                                children: product.isActive ? \"نشط\" : \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: product.productType === \"digital\" ? \"border-purple-500 text-purple-400\" : product.productType === \"physical\" ? \"border-blue-500 text-blue-400\" : \"border-green-500 text-green-400\",\n                                                                children: product.productType === \"digital\" ? \"رقمي\" : product.productType === \"physical\" ? \"مادي\" : \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            product.processingType === \"instant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-green-500/20 text-green-400 border-green-500/30\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"فوري\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm mb-3 line-clamp-2\",\n                                                children: product.description || \"لا يوجد وصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-sm text-slate-400 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"الفئة: \",\n                                                            product.category\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            ((_product_packages = product.packages) === null || _product_packages === void 0 ? void 0 : _product_packages.length) || 0,\n                                                            \" حزمة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>{\n                                                            setSelectedProduct(product);\n                                                            setIsEditDialogOpen(true);\n                                                        },\n                                                        className: \"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"تعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleProductDelete(product.id),\n                                                        className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 7\n                    }, this),\n                    filteredProducts.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-16 w-16 text-slate-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"لا توجد منتجات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-400 mb-4\",\n                                    children: searchQuery || Object.keys(filters).length > 0 ? \"لم يتم العثور على منتجات تطابق البحث أو الفلاتر\" : \"ابدأ بإنشاء منتجك الأول\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                !searchQuery && Object.keys(filters).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setIsCreateDialogOpen(true),\n                                    className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"إضافة منتج جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this),\n                    isEditDialogOpen && selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            product: selectedProduct,\n                            onSave: handleProductUpdate,\n                            onCancel: ()=>{\n                                setIsEditDialogOpen(false);\n                                setSelectedProduct(null);\n                            },\n                            isEditing: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDashboard, \"w8YCEC8amsgFyatH9jatbW86Xe8=\");\n_c = ProductDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProductDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/ProductDashboard.tsx\n"));

/***/ })

});