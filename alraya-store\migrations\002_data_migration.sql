-- =====================================================
-- Al-Raya Store Multi-Currency Platform Migration
-- Phase 2: Data Migration and Initial Setup
-- =====================================================

-- =====================================================
-- 1. INSERT DEFAULT CURRENCIES
-- =====================================================
INSERT INTO currencies (code, name, symbol, arabic_name, decimal_places, is_rtl, is_active, sort_order) VALUES
  ('USD', 'US Dollar', '$', 'الدولار الأمريكي', 2, false, true, 1),
  ('SDG', 'Sudanese Pound', 'ج.س.', 'الجنيه السوداني', 2, true, true, 2),
  ('EGP', 'Egyptian Pound', 'ج.م.', 'الجنيه المصري', 2, true, true, 3),
  ('SAR', 'Saudi Riyal', 'ر.س', 'الريال السعودي', 2, true, false, 4),
  ('AED', 'UAE Dirham', 'د.إ', 'الدرهم الإماراتي', 2, true, false, 5),
  ('EUR', 'Euro', '€', 'اليورو', 2, false, false, 6),
  ('GBP', 'British Pound', '£', 'الجنيه الإسترليني', 2, false, false, 7);

-- =====================================================
-- 2. INSERT DEFAULT EXCHANGE RATES (USD as base)
-- =====================================================
-- Note: These are example rates - update with real rates before production
INSERT INTO exchange_rates (from_currency_code, to_currency_code, rate, effective_date, is_active) VALUES
  ('USD', 'USD', 1.0000, NOW(), true),
  ('USD', 'SDG', 450.0000, NOW(), true),
  ('USD', 'EGP', 30.8000, NOW(), true),
  ('USD', 'SAR', 3.7500, NOW(), true),
  ('USD', 'AED', 3.6700, NOW(), true),
  ('USD', 'EUR', 0.8500, NOW(), true),
  ('USD', 'GBP', 0.7300, NOW(), true);

-- Insert reverse rates for convenience (though we'll calculate these dynamically)
INSERT INTO exchange_rates (from_currency_code, to_currency_code, rate, effective_date, is_active) VALUES
  ('SDG', 'USD', 1.0/450.0000, NOW(), true),
  ('EGP', 'USD', 1.0/30.8000, NOW(), true),
  ('SAR', 'USD', 1.0/3.7500, NOW(), true),
  ('AED', 'USD', 1.0/3.6700, NOW(), true),
  ('EUR', 'USD', 1.0/0.8500, NOW(), true),
  ('GBP', 'USD', 1.0/0.7300, NOW(), true);

-- =====================================================
-- 3. INSERT DEFAULT CLIENT CURRENCY SETTINGS (USD as Primary)
-- =====================================================
INSERT INTO client_currency_settings (
  client_id,
  primary_currency_code,
  enabled_currencies,
  enable_multi_currency,
  enable_currency_conversion,
  enable_advanced_reporting,
  auto_update_rates,
  rate_update_frequency_hours
) VALUES (
  NULL, -- Single tenant setup
  'USD', -- USD as primary currency
  ARRAY['USD', 'SDG', 'EGP'], -- USD first, then others
  true,
  true,
  false,
  true,
  24
);

-- =====================================================
-- 4. MIGRATE EXISTING USER WALLETS DATA
-- =====================================================
-- Check if old user_wallets table exists and migrate data
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_wallets') THEN
    -- Migrate existing wallet data
    INSERT INTO user_wallets_new (
      user_id,
      currency_code,
      balance,
      created_at,
      updated_at
    )
    SELECT 
      user_id,
      currency,
      balance,
      created_at,
      updated_at
    FROM user_wallets
    WHERE currency IN ('SDG', 'EGP');
    
    RAISE NOTICE 'Migrated % wallet records', (SELECT COUNT(*) FROM user_wallets);
  ELSE
    RAISE NOTICE 'No existing user_wallets table found - skipping migration';
  END IF;
END $$;

-- =====================================================
-- 5. MIGRATE EXISTING WALLET TRANSACTIONS DATA
-- =====================================================
-- Check if old wallet_transactions table exists and migrate data
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'wallet_transactions') THEN
    -- First, create wallet_id mappings for existing transactions
    INSERT INTO wallet_transactions_new (
      user_id,
      wallet_id,
      transaction_type,
      amount,
      currency_code,
      description,
      reference_number,
      status,
      created_at,
      processed_at
    )
    SELECT 
      wt.user_id,
      uw.id as wallet_id,
      wt.type as transaction_type,
      wt.amount,
      wt.currency as currency_code,
      COALESCE(wt.description, 'Migrated transaction'),
      wt.reference,
      COALESCE(wt.status, 'completed'),
      wt.created_at,
      wt.created_at as processed_at
    FROM wallet_transactions wt
    JOIN user_wallets_new uw ON (uw.user_id = wt.user_id AND uw.currency_code = wt.currency)
    WHERE wt.currency IN ('SDG', 'EGP');
    
    RAISE NOTICE 'Migrated % transaction records', (SELECT COUNT(*) FROM wallet_transactions);
  ELSE
    RAISE NOTICE 'No existing wallet_transactions table found - skipping migration';
  END IF;
END $$;

-- =====================================================
-- 6. MIGRATE EXISTING PRODUCT ORDERS DATA
-- =====================================================
-- Check if old product_orders table exists and migrate data
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_orders') THEN
    -- Migrate existing order data with enhanced structure
    INSERT INTO product_orders_new (
      id,
      user_id,
      template_id,
      template_name,
      template_category,
      product_data,
      pricing_data,
      currency_code,
      total_price,
      customer_name,
      customer_email,
      customer_phone,
      status,
      processing_type,
      timeline_events,
      metadata,
      created_at,
      updated_at,
      completed_at
    )
    SELECT 
      po.id,
      po.user_id,
      po.template_id,
      po.template_name,
      COALESCE(po.template_category, 'gaming'),
      COALESCE(po.product_data, '{}'),
      COALESCE(po.pricing, '{}'),
      COALESCE((po.pricing->>'currency')::VARCHAR(3), 'SDG'),
      COALESCE((po.pricing->>'totalPrice')::DECIMAL(18,8), 0),
      (po.user_details->>'fullName'),
      (po.user_details->>'email'),
      (po.user_details->>'phone'),
      COALESCE(po.status, 'pending'),
      COALESCE(po.processing_type, 'manual'),
      COALESCE(po.timeline, '[]'),
      COALESCE(po.metadata, '{}'),
      po.created_at,
      po.updated_at,
      po.completed_at
    FROM product_orders po;
    
    RAISE NOTICE 'Migrated % order records', (SELECT COUNT(*) FROM product_orders);
  ELSE
    RAISE NOTICE 'No existing product_orders table found - skipping migration';
  END IF;
END $$;

-- =====================================================
-- 7. CREATE DEFAULT USER PREFERENCES (USD as Default)
-- =====================================================
-- Create default preferences for existing users with USD as default
INSERT INTO user_preferences (user_id, preferred_currency_code, display_currency_code)
SELECT
  id as user_id,
  'USD' as preferred_currency_code, -- USD as default for all users
  'USD' as display_currency_code    -- USD as default display currency
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM user_preferences WHERE user_id IS NOT NULL);

-- =====================================================
-- 8. UPDATE WALLET TOTALS
-- =====================================================
-- Update wallet totals based on transaction history
UPDATE user_wallets_new SET
  total_deposits = COALESCE((
    SELECT SUM(amount) 
    FROM wallet_transactions_new 
    WHERE wallet_id = user_wallets_new.id 
    AND transaction_type = 'deposit' 
    AND status = 'completed'
  ), 0),
  total_withdrawals = COALESCE((
    SELECT SUM(amount) 
    FROM wallet_transactions_new 
    WHERE wallet_id = user_wallets_new.id 
    AND transaction_type = 'withdrawal' 
    AND status = 'completed'
  ), 0),
  total_purchases = COALESCE((
    SELECT SUM(amount) 
    FROM wallet_transactions_new 
    WHERE wallet_id = user_wallets_new.id 
    AND transaction_type = 'purchase' 
    AND status = 'completed'
  ), 0),
  last_transaction_at = (
    SELECT MAX(created_at) 
    FROM wallet_transactions_new 
    WHERE wallet_id = user_wallets_new.id
  );

-- =====================================================
-- 9. VALIDATION CHECKS
-- =====================================================
-- Verify data integrity after migration
DO $$
DECLARE
  currency_count INTEGER;
  rate_count INTEGER;
  wallet_count INTEGER;
  transaction_count INTEGER;
  order_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO currency_count FROM currencies WHERE is_active = true;
  SELECT COUNT(*) INTO rate_count FROM exchange_rates WHERE is_active = true;
  SELECT COUNT(*) INTO wallet_count FROM user_wallets_new;
  SELECT COUNT(*) INTO transaction_count FROM wallet_transactions_new;
  SELECT COUNT(*) INTO order_count FROM product_orders_new;
  
  RAISE NOTICE 'Migration Summary:';
  RAISE NOTICE '- Active currencies: %', currency_count;
  RAISE NOTICE '- Exchange rates: %', rate_count;
  RAISE NOTICE '- User wallets: %', wallet_count;
  RAISE NOTICE '- Wallet transactions: %', transaction_count;
  RAISE NOTICE '- Product orders: %', order_count;
  
  -- Basic validation
  IF currency_count < 2 THEN
    RAISE EXCEPTION 'Migration failed: Insufficient currencies loaded';
  END IF;
  
  IF rate_count < 2 THEN
    RAISE EXCEPTION 'Migration failed: Insufficient exchange rates loaded';
  END IF;
  
  RAISE NOTICE 'Migration validation passed successfully';
END $$;
