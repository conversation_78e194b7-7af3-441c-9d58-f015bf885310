import { Transaction } from "@/lib/types"

/**
 * Get the count of unread digital content notifications
 * This includes new transactions, completed orders, etc.
 */
export function getDigitalContentNotificationCount(transactions: Transaction[]): number {
  if (!transactions || transactions.length === 0) {
    return 0
  }

  // Count recent transactions (within last 24 hours) as notifications
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
  
  const recentTransactions = transactions.filter(transaction => {
    const transactionDate = new Date(transaction.createdAt)
    return transactionDate > oneDayAgo
  })

  return recentTransactions.length
}

/**
 * Get digital content notifications for display
 */
export function getDigitalContentNotifications(transactions: Transaction[]) {
  if (!transactions || transactions.length === 0) {
    return []
  }

  // Get recent transactions as notifications
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
  
  return transactions
    .filter(transaction => {
      const transactionDate = new Date(transaction.createdAt)
      return transactionDate > oneDayAgo
    })
    .map(transaction => ({
      id: transaction.id,
      title: transaction.type === 'credit' ? 'إيداع جديد' : 'عملية شراء',
      message: `${transaction.description} - ${transaction.amount} ${transaction.currency}`,
      timestamp: transaction.createdAt,
      type: transaction.type
    }))
}
