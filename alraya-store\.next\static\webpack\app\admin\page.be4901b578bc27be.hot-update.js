"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/ProductDashboard.tsx":
/*!***********************************************!*\
  !*** ./components/admin/ProductDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductDashboard: () => (/* binding */ ProductDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_services_categoryService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/categoryService */ \"(app-pages-browser)/./lib/services/categoryService.ts\");\n/* harmony import */ var _SimpleProductForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SimpleProductForm */ \"(app-pages-browser)/./components/admin/SimpleProductForm.tsx\");\n/* harmony import */ var _CategoryManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./CategoryManager */ \"(app-pages-browser)/./components/admin/CategoryManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductDashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductDashboard() {\n    _s();\n    // ## TODO: Add user authentication check\n    // ## TODO: Implement real-time updates with Supabase subscriptions\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sub-navigation state\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"products\");\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load products and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            (0,_lib_services_categoryService__WEBPACK_IMPORTED_MODULE_9__.initializeCategories)() // Initialize default categories\n            ;\n            loadData();\n        }\n    }[\"ProductDashboard.useEffect\"], []);\n    // Apply filters when search or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"ProductDashboard.useEffect\"], [\n        products,\n        searchQuery,\n        filters\n    ]);\n    /**\n   * ## TODO: Replace with Supabase real-time subscription\n   * Load products and statistics\n   */ const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [productsData, statsData] = await Promise.all([\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_8__.getProducts)().catch((err)=>{\n                    console.error(\"Error loading products:\", err);\n                    return [];\n                }),\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_8__.getProductStats)().catch((err)=>{\n                    console.error(\"Error loading stats:\", err);\n                    return {\n                        totalProducts: 0,\n                        activeProducts: 0,\n                        digitalProducts: 0,\n                        physicalProducts: 0,\n                        totalPackages: 0,\n                        totalOrders: 0,\n                        popularCategories: []\n                    };\n                })\n            ]);\n            setProducts(Array.isArray(productsData) ? productsData : []);\n            setStats(statsData);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            // Set fallback data\n            setProducts([]);\n            setStats({\n                totalProducts: 0,\n                activeProducts: 0,\n                digitalProducts: 0,\n                physicalProducts: 0,\n                totalPackages: 0,\n                totalOrders: 0,\n                popularCategories: []\n            });\n        // ## TODO: Show error toast notification\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Apply search and filters to products\n   */ const applyFilters = ()=>{\n        // Ensure products is a valid array\n        const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.name && p.category) : [];\n        let filtered = [\n            ...validProducts\n        ];\n        // Apply search\n        if (searchQuery.trim()) {\n            const query = searchQuery.toLowerCase();\n            filtered = filtered.filter((product)=>{\n                var _product_name, _product_description, _product_category;\n                return ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.toLowerCase().includes(query)) || ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(query)) || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.toLowerCase().includes(query));\n            });\n        }\n        // Apply filters\n        if (filters.category) {\n            filtered = filtered.filter((p)=>p.category === filters.category);\n        }\n        if (filters.productType) {\n            filtered = filtered.filter((p)=>p.productType === filters.productType);\n        }\n        if (filters.processingType) {\n            filtered = filtered.filter((p)=>p.processingType === filters.processingType);\n        }\n        if (filters.isActive !== undefined) {\n            filtered = filtered.filter((p)=>p.isActive === filters.isActive);\n        }\n        setFilteredProducts(filtered);\n    };\n    /**\n   * Handle product creation\n   */ const handleProductCreate = async (product)=>{\n        setProducts((prev)=>[\n                ...prev,\n                product\n            ]);\n        setIsCreateDialogOpen(false);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product update\n   */ const handleProductUpdate = async (product)=>{\n        setProducts((prev)=>prev.map((p)=>p.id === product.id ? product : p));\n        setIsEditDialogOpen(false);\n        setSelectedProduct(null);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product deletion\n   */ const handleProductDelete = async (productId)=>{\n        if (!confirm(\"هل أنت متأكد من حذف هذا المنتج؟\")) return;\n        try {\n            await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_8__.deleteProduct)(productId);\n            setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n            await loadData() // Refresh stats\n            ;\n        } catch (error) {\n            console.error(\"Error deleting product:\", error);\n        // ## TODO: Show error toast notification\n        }\n    };\n    /**\n   * Get unique categories for filter dropdown\n   */ const getCategories = ()=>{\n        const categories = [\n            ...new Set(products.map((p)=>p.category))\n        ];\n        return categories.sort();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 p-4 lg:p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl lg:text-3xl font-bold text-white\",\n                                children: \"إدارة المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400 mt-1\",\n                                children: \"إنشاء وتعديل وإدارة منتجات المتجر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: isCreateDialogOpen,\n                        onOpenChange: setIsCreateDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 min-h-[44px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إضافة منتج جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                                className: \"bg-slate-800 border-slate-700 text-white max-w-[95vw] sm:max-w-[90vw] lg:max-w-4xl max-h-[90vh] overflow-y-auto p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    onSave: handleProductCreate,\n                                    onCancel: ()=>setIsCreateDialogOpen(false)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 bg-slate-800/50 rounded-lg p-1 border border-slate-700/50 overflow-x-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"products\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"products\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"منتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"categories\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"categories\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"الفئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"فئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            activeTab === \"categories\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CategoryManager__WEBPACK_IMPORTED_MODULE_11__.CategoryManager, {\n                onCategoryChange: loadData\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي المنتجات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: stats.totalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات النشطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: stats.activeProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات الرقمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-400\",\n                                                        children: stats.digitalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-8 w-8 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي الحزم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-400\",\n                                                        children: stats.totalPackages\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"البحث في المنتجات...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-slate-700 border-slate-600 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.category || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"الفئة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الفئات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            getCategories().map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.productType || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            productType: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الأنواع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"digital\",\n                                                                children: \"رقمي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"physical\",\n                                                                children: \"مادي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"service\",\n                                                                children: \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredProducts.map((product)=>{\n                            var _product_packages;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-white text-lg mb-2\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: product.isActive ? \"default\" : \"secondary\",\n                                                                children: product.isActive ? \"نشط\" : \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: product.productType === \"digital\" ? \"border-purple-500 text-purple-400\" : product.productType === \"physical\" ? \"border-blue-500 text-blue-400\" : \"border-green-500 text-green-400\",\n                                                                children: product.productType === \"digital\" ? \"رقمي\" : product.productType === \"physical\" ? \"مادي\" : \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            product.processingType === \"instant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-green-500/20 text-green-400 border-green-500/30\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"فوري\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm mb-3 line-clamp-2\",\n                                                children: product.description || \"لا يوجد وصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-sm text-slate-400 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"الفئة: \",\n                                                            product.category\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            ((_product_packages = product.packages) === null || _product_packages === void 0 ? void 0 : _product_packages.length) || 0,\n                                                            \" حزمة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>{\n                                                            setSelectedProduct(product);\n                                                            setIsEditDialogOpen(true);\n                                                        },\n                                                        className: \"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"تعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleProductDelete(product.id),\n                                                        className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 7\n                    }, this),\n                    filteredProducts.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-16 w-16 text-slate-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"لا توجد منتجات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-400 mb-4\",\n                                    children: searchQuery || Object.keys(filters).length > 0 ? \"لم يتم العثور على منتجات تطابق البحث أو الفلاتر\" : \"ابدأ بإنشاء منتجك الأول\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                !searchQuery && Object.keys(filters).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setIsCreateDialogOpen(true),\n                                    className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"إضافة منتج جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: isEditDialogOpen,\n                        onOpenChange: setIsEditDialogOpen,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                            className: \"bg-slate-800 border-slate-700 text-white max-w-[95vw] sm:max-w-[90vw] lg:max-w-4xl max-h-[90vh] overflow-y-auto p-0\",\n                            children: selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                product: selectedProduct,\n                                onSave: handleProductUpdate,\n                                onCancel: ()=>{\n                                    setIsEditDialogOpen(false);\n                                    setSelectedProduct(null);\n                                },\n                                isEditing: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDashboard, \"w8YCEC8amsgFyatH9jatbW86Xe8=\");\n_c = ProductDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProductDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/ProductDashboard.tsx\n"));

/***/ })

});