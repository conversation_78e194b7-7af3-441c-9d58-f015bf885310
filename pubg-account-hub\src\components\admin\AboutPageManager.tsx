import React, { useState, useEffect, useRef, useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Save, Loader2, Image as ImageIcon, ChevronDown, Upload, X, CropIcon, Check, RefreshCw, GlobeIcon, BookOpen, PlusCircle, Star, Users, Award, HelpCircle, ChevronUp } from "lucide-react";
import { getAboutPageContent, updateAboutPageContent } from "@/services/firestore";
import { uploadImage } from "@/services/imgbb";
import ReactCrop, { centerCrop, makeAspectCrop, Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { useNavigate, useLocation } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";

// Define the type for About Page content
interface AboutPageContent {
  id?: string;
  header: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    backgroundImage: string;
  };
  story: {
    title: string;
    title_en?: string;
    content: string[];
    content_en?: string[];
    image: string;
  };
  values: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    items: Array<{
      icon: string;
      title: string;
      title_en?: string;
      description: string;
      description_en?: string;
    }>;
  };
  team: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    members: Array<{
      name: string;
      name_en?: string;
      role: string;
      role_en?: string;
      avatar: string;
      bio: string;
      bio_en?: string;
    }>;
  };
  whyChooseUs: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    points: Array<{
      icon: string;
      title: string;
      title_en?: string;
      description: string;
      description_en?: string;
    }>;
  };
  faq: {
    title: string;
    title_en?: string;
    subtitle: string;
    subtitle_en?: string;
    questions: Array<{
      question: string;
      question_en?: string;
      answer: string;
      answer_en?: string;
    }>;
  };
  createdAt?: any;
  updatedAt?: any;
}

const INITIAL_ABOUT_DATA: AboutPageContent = {
  header: {
    title: "من نحن",
    title_en: "About Us",
    subtitle: "نحن فريق متخصص في توفير أفضل حسابات PUBG وشحن UC بأسعار منافسة وجودة عالية. هدفنا تقديم خدمة آمنة وسريعة للاعبين في جميع أنحاء العالم العربي.",
    subtitle_en: "We are a specialized team providing the best PUBG accounts and UC charging at competitive prices and high quality. Our goal is to provide a safe and fast service for players throughout the Arab world.",
    backgroundImage: "https://images.unsplash.com/photo-*************-1ff1d85d1bdf?q=80&w=1920"
  },
  story: {
    title: "قصتنا",
    title_en: "Our Story",
    content: [
      "بدأنا رحلتنا عام 2020 كمجموعة من عشاق ألعاب الفيديو والمحترفين في مجال PUBG Mobile، مع رؤية واضحة لسد الفجوة في سوق حسابات اللعبة وشحن العملات الافتراضية.",
      "منذ ذلك الحين، نمت شبكتنا لتضم آلاف العملاء المخلصين، وتوسعت خدماتنا لتشمل الحسابات المميزة، والشحن الفوري لـ UC، والدعم الفني على مدار الساعة.",
      "نفتخر بكوننا أحد أكثر المواقع العربية موثوقية في مجال بيع وشراء حسابات PUBG، مع التزامنا الدائم بالجودة والأمان."
    ],
    content_en: [
      "We started our journey in 2020 as a group of video game enthusiasts and professionals in the field of PUBG Mobile, with a clear vision to bridge the gap in the market for game accounts and virtual currency charging.",
      "Since then, our network has grown to include thousands of loyal customers, and our services have expanded to include premium accounts, instant UC charging, and 24/7 technical support.",
      "We pride ourselves on being one of the most reliable Arabic websites in the field of buying and selling PUBG accounts, with our continuous commitment to quality and security."
    ],
    image: "https://images.unsplash.com/photo-**********-3ec5d502959f?q=80&w=1920"
  },
  values: {
    title: "قيمنا",
    title_en: "Our Values",
    subtitle: "تقود هذه المبادئ كل قرار نتخذه وكل خدمة نقدمها",
    subtitle_en: "These principles guide every decision we make and every service we provide",
    items: [
      {
        icon: "Shield",
        title: "الأمان",
        title_en: "Security",
        description: "نضع أمان معلوماتك وحساباتك في المقام الأول، مع استخدام أحدث تقنيات التشفير وحماية البيانات.",
        description_en: "We put the security of your information and accounts first, using the latest encryption and data protection technologies."
      },
      {
        icon: "Award", 
        title: "الجودة",
        title_en: "Quality",
        description: "نقدم فقط الحسابات عالية الجودة والمميزة، ونتحقق من كل حساب قبل عرضه للبيع.",
        description_en: "We only offer high-quality and distinctive accounts, and we verify each account before offering it for sale."
      },
      {
        icon: "Clock",
        title: "السرعة",
        title_en: "Speed",
        description: "نؤمن بأهمية الوقت، لذلك نعمل على توفير خدمات سريعة ومباشرة لجميع عملائنا.",
        description_en: "We believe in the importance of time, so we work to provide fast and direct services to all our customers."
      }
    ]
  },
  team: {
    title: "فريقنا",
    title_en: "Our Team",
    subtitle: "نحن مجموعة من المحترفين الشغوفين بعالم PUBG",
    subtitle_en: "We are a group of professionals passionate about the world of PUBG",
    members: [
      {
        name: "محمد أحمد",
        name_en: "Mohammed Ahmed",
        role: "المؤسس والمدير التنفيذي",
        role_en: "Founder & CEO",
        avatar: "https://randomuser.me/api/portraits/men/1.jpg",
        bio: "مؤسس الشركة وخبير في مجال ألعاب الفيديو والتسويق الرقمي. يمتلك خبرة تزيد عن 10 سنوات في مجال الألعاب الإلكترونية.",
        bio_en: "Founder of the company and expert in video games and digital marketing. Has over 10 years of experience in the field of electronic games."
      },
      {
        name: "سارة خالد",
        name_en: "Sarah Khalid",
        role: "مديرة خدمة العملاء",
        role_en: "Customer Service Manager",
        avatar: "https://randomuser.me/api/portraits/women/1.jpg",
        bio: "تدير فريق دعم العملاء وتضمن تجربة مستخدم استثنائية لجميع العملاء.",
        bio_en: "Manages the customer support team and ensures an exceptional user experience for all customers."
      },
      {
        name: "أحمد علي",
        name_en: "Ahmed Ali",
        role: "خبير حسابات PUBG",
        role_en: "PUBG Accounts Expert",
        avatar: "https://randomuser.me/api/portraits/men/2.jpg",
        bio: "محترف في لعبة PUBG مع خبرة واسعة في تقييم وتصنيف الحسابات.",
        bio_en: "Professional in PUBG with extensive experience in evaluating and classifying accounts."
      }
    ]
  },
  whyChooseUs: {
    title: "لماذا تختارنا؟",
    title_en: "Why Choose Us?",
    subtitle: "نقدم مزايا فريدة تجعلنا الخيار الأمثل لعشاق PUBG",
    subtitle_en: "We offer unique advantages that make us the best choice for PUBG fans",
    points: [
      {
        icon: "Shield",
        title: "ضمان 100%",
        title_en: "100% Guarantee",
        description: "نقدم ضمان كامل على جميع الحسابات والخدمات، مع سياسة استرداد واضحة وشفافة.",
        description_en: "We provide a full guarantee on all accounts and services, with a clear and transparent refund policy."
      },
      {
        icon: "Star",
        title: "حسابات مميزة",
        title_en: "Premium Accounts",
        description: "نختار بعناية أفضل الحسابات ذات المواصفات الفريدة والمحتوى النادر.",
        description_en: "We carefully select the best accounts with unique specifications and rare content."
      },
      {
        icon: "Clock",
        title: "دعم 24/7",
        title_en: "24/7 Support",
        description: "فريق دعم فني متاح على مدار الساعة للإجابة على جميع استفساراتك ومساعدتك.",
        description_en: "Technical support team available 24/7 to answer all your inquiries and assist you."
      },
      {
        icon: "DollarSign",
        title: "أسعار تنافسية",
        title_en: "Competitive Prices",
        description: "نقدم أفضل الأسعار في السوق مع خيارات دفع متعددة وآمنة.",
        description_en: "We offer the best prices in the market with multiple and secure payment options."
      }
    ]
  },
  faq: {
    title: "الأسئلة الشائعة",
    title_en: "Frequently Asked Questions",
    subtitle: "إجابات على الأسئلة الأكثر شيوعًا",
    subtitle_en: "Answers to the most common questions",
    questions: [
      {
        question: "كيف يمكنني شراء حساب؟",
        question_en: "How can I purchase an account?",
        answer: "يمكنك تصفح الحسابات المتاحة في صفحة الحسابات واختيار الحساب المناسب، ثم إضافته إلى السلة وإتمام عملية الشراء عبر تلجرام.",
        answer_en: "You can browse the available accounts on the Accounts page, select the appropriate account, add it to the cart, and complete the purchase process via Telegram."
      },
      {
        question: "ما هي طرق الدفع المتاحة؟",
        question_en: "What payment methods are available?",
        answer: "نوفر طرق دفع متعددة منها: فودافون كاش، بنوك مصرية، وي كاش، وباي بال. يمكنك الاستفسار أكثر عند التواصل معنا عبر تلجرام.",
        answer_en: "We provide multiple payment methods including: Vodafone Cash, Egyptian banks, WE Cash, and PayPal. You can inquire more when contacting us via Telegram."
      },
      {
        question: "هل يمكنني استرجاع المبلغ بعد الشراء؟",
        question_en: "Can I request a refund after purchase?",
        answer: "نعم، نوفر ضمان استرجاع المبلغ خلال 24 ساعة في حالة وجود أي مشكلة في الحساب أو المنتج المقدم.",
        answer_en: "Yes, we provide a money-back guarantee within 24 hours in case of any problem with the account or product provided."
      },
      {
        question: "كم يستغرق وقت الرد على استفساراتي؟",
        question_en: "How long does it take to respond to my inquiries?",
        answer: "نحن متواجدون للرد على استفساراتك خلال ساعات العمل من 10 صباحًا حتى 10 مساءً. نسعى دائمًا للرد في أسرع وقت ممكن.",
        answer_en: "We are available to respond to your inquiries during working hours from 10 AM to 10 PM. We always strive to respond as quickly as possible."
      }
    ]
  }
};

// Function to generate a blob from an image with a crop applied
function getCroppedImg(
  image: HTMLImageElement,
  crop: PixelCrop,
  fileName: string = 'cropped.jpg',
  qualityFactor: number = 0.98
): Promise<Blob> {
  const canvas = document.createElement('canvas');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  
  // Set to the actual pixel dimensions of the crop for high resolution
  canvas.width = crop.width * scaleX;
  canvas.height = crop.height * scaleY;
  
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('No 2d context');
  }

  // Use high quality rendering
  ctx.imageSmoothingQuality = 'high';
  ctx.imageSmoothingEnabled = true;

  ctx.drawImage(
    image,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width * scaleX,
    crop.height * scaleY
  );

  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'));
          return;
        }
        resolve(blob);
      },
      'image/jpeg',
      qualityFactor
    );
  });
}

// Image Preview Component
const ImageUploadPreview = ({ 
  imageUrl, 
  onChange, 
  label = "صورة",
  fieldName = "image",
  placeholder = "أدخل رابط الصورة",
  aspectRatio = 16 / 9
}) => {
  const { toast } = useToast();
  const [previewUrl, setPreviewUrl] = useState(imageUrl);
  const [isUploading, setIsUploading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [imgSrc, setImgSrc] = useState<string | null>(null);
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  
  useEffect(() => {
    setPreviewUrl(imageUrl);
  }, [imageUrl]);

  const onSelectFile = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار ملف صورة صالح",
        variant: "destructive",
      });
      return;
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "خطأ",
        description: "حجم الصورة يجب أن يكون أقل من 10 ميجابايت",
        variant: "destructive",
      });
      return;
    }
    
    const reader = new FileReader();
    reader.addEventListener('load', () => {
      setImgSrc(reader.result?.toString() || '');
      setIsDialogOpen(true);
    });
    reader.readAsDataURL(file);
  }, [toast]);

  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    
    // Initialize with centered crop
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        aspectRatio,
        width,
        height
      ),
      width,
      height
    );
    
    setCrop(crop);
  }, [aspectRatio]);

  const handleCropComplete = useCallback(async () => {
    if (!imgRef.current || !completedCrop) return;
    
    try {
      setIsUploading(true);
      
      // Always use high quality
      const qualityFactor = 0.98;
      
      const croppedBlob = await getCroppedImg(
        imgRef.current, 
        completedCrop, 
        `cropped_image.jpg`,
        qualityFactor
      );
      
      const file = new File([croppedBlob], `cropped_image.jpg`, { 
        type: "image/jpeg" 
      });
      
      const imageUrl = await uploadImage(file);
      
      // Create a mock event to pass to the onChange handler
      const mockEvent = { 
        target: { 
          name: fieldName, 
          value: imageUrl 
        } 
      };
      
      onChange(mockEvent);
      setPreviewUrl(imageUrl);
      setIsDialogOpen(false);
      
      toast({
        title: "تم بنجاح",
        description: "تم رفع الصورة بنجاح",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "خطأ",
        description: "فشل في رفع الصورة",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      setImgSrc(null);
    }
  }, [completedCrop, fieldName, onChange, toast]);

  const handleInputChange = (e) => {
    onChange(e);
    setPreviewUrl(e.target.value);
  };

  return (
    <div className="space-y-3">
      <Label htmlFor={fieldName} className="flex items-center">
        <ImageIcon className="h-4 w-4 ml-1" />
        {label}
      </Label>
      
      <div className="flex flex-col space-y-2">
        <div className="flex gap-2">
          <Input
            id={`input-${fieldName}`}
            name={fieldName}
            value={imageUrl}
            onChange={handleInputChange}
            placeholder={placeholder}
            className="flex-1"
          />
          
          <div className="flex gap-2">
            <div className="relative">
              <Input
                id={fieldName}
                name="file-upload"
                type="file"
                accept="image/*"
                onChange={onSelectFile}
                className="hidden"
              />
              <Button
                type="button"
                variant="secondary"
                onClick={() => document.getElementById(fieldName)?.click()}
                disabled={isUploading}
                className="whitespace-nowrap h-10"
                title="رفع صورة"
              >
                <Upload className="ml-2 h-4 w-4" />
                رفع صورة
              </Button>
            </div>
          </div>
        </div>
        
        <p className="text-xs text-muted-foreground">
          يمكنك إدخال رابط للصورة أو رفع صورة مباشرة
        </p>
      </div>
      
      {previewUrl ? (
        <div className="relative mt-2 rounded-md overflow-hidden border border-border group">
          <img
            src={previewUrl}
            alt={`معاينة ${label}`}
            className="w-full h-40 object-cover"
            onError={() => setPreviewUrl(null)}
          />
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
            <Button 
              variant="destructive" 
              size="sm"
              className="rounded-full w-8 h-8 p-0"
              onClick={() => {
                const mockEvent = { target: { name: fieldName, value: "" } };
                onChange(mockEvent);
                setPreviewUrl("");
              }}
              title="حذف الصورة"
            >
              <X className="h-4 w-4" />
            </Button>
            
            <Input
              id={`edit-${fieldName}`}
              name="file-upload-edit"
              type="file"
              accept="image/*"
              onChange={onSelectFile}
              className="hidden"
            />
            <Button
              variant="secondary"
              size="sm"
              className="rounded-full w-8 h-8 p-0"
              onClick={() => document.getElementById(`edit-${fieldName}`)?.click()}
              title="تعديل/استبدال الصورة"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
          <div className="absolute bottom-0 right-0 p-2 bg-background/80 text-xs rounded-tl-md">
            معاينة الصورة
          </div>
        </div>
      ) : (
        <div className="border border-dashed border-border rounded-md flex flex-col items-center justify-center py-6 px-4 mt-2">
          <Upload className="h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">أدخل رابط الصورة أو قم برفع صورة للمعاينة</p>
        </div>
      )}
      
      {/* Image Crop Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>تعديل وضبط الصورة</DialogTitle>
            <DialogDescription>
              قم بضبط الصورة بالشكل المناسب قبل رفعها
            </DialogDescription>
          </DialogHeader>
          
          <div className="crop-container overflow-hidden max-h-[60vh]">
            {imgSrc && (
              <ReactCrop
                crop={crop}
                onChange={(c) => setCrop(c)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={aspectRatio}
                circularCrop={false}
                ruleOfThirds
              >
                <img
                  ref={imgRef}
                  alt="للقص"
                  src={imgSrc}
                  className="w-full"
                  onLoad={onImageLoad}
                />
              </ReactCrop>
            )}
          </div>
          
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" className="gap-2">
                <X className="h-4 w-4" />
                إلغاء
              </Button>
            </DialogClose>
            <Button 
              onClick={handleCropComplete}
              disabled={isUploading}
              className="gap-2"
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  تطبيق وحفظ
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const AboutPageManager = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Tab sections available in the About Page Manager
  const availableTabs = ["header", "story", "values", "team", "whyChooseUs", "faq"];
  
  // Get the inner tab from URL query params or default to "header"
  const getInnerTabFromUrl = () => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('section');
    // Validate that the section exists in availableTabs
    return availableTabs.includes(tab) ? tab : "header";
  };
  
  const [activeTab, setActiveTab] = useState(getInnerTabFromUrl());
  const [aboutData, setAboutData] = useState<AboutPageContent>(INITIAL_ABOUT_DATA);
  
  // Update URL when tab changes
  const handleTabChange = (value) => {
    setActiveTab(value);
    const params = new URLSearchParams(location.search);
    
    // Keep the main tab parameter
    const mainTab = params.get('tab') || 'about';
    
    // Update section parameter
    params.set('tab', mainTab);
    params.set('section', value);
    
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });
  };
  
  // Update active tab if URL changes
  useEffect(() => {
    const newTab = getInnerTabFromUrl();
    if (newTab !== activeTab) {
      setActiveTab(newTab);
    }
  }, [location.search]);

  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        setIsLoading(true);
        const data = await getAboutPageContent();
        
        if (data) {
          // Make sure the faq property exists in data
          const updatedData = {
            ...data,
            // If faq doesn't exist, initialize it with default values
            faq: data.faq || {
              title: "الأسئلة الشائعة",
              subtitle: "إجابات على الأسئلة الأكثر شيوعًا",
              questions: [
                {
                  question: "كيف يمكنني شراء حساب؟",
                  answer: "يمكنك تصفح الحسابات المتاحة في صفحة الحسابات واختيار الحساب المناسب، ثم إضافته إلى السلة وإتمام عملية الشراء عبر تلجرام."
                }
              ]
            }
          };
          setAboutData(updatedData);
        } else {
          // If no data is returned (e.g., due to permissions), use default data
          console.log("Using default About page data");
          setAboutData(INITIAL_ABOUT_DATA);
        }
      } catch (error) {
        console.error("Error fetching about page data:", error);
        // Use default data on error
        console.log("Error occurred, using default About page data");
        setAboutData(INITIAL_ABOUT_DATA);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAboutData();
  }, [toast]);

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      const success = await updateAboutPageContent(aboutData);

      if (success) {
        toast({
          title: "تم بنجاح",
          description: "تم حفظ التغييرات على صفحة من نحن",
        });
      } else {
        throw new Error("Failed to update about page content");
      }
      
      setSubmitting(false);
    } catch (error) {
      console.error("Error saving about page data:", error);
      toast({
        title: "خطأ",
        description: "فشل في حفظ التغييرات",
        variant: "destructive",
      });
      setSubmitting(false);
    }
  };

  const handleHeaderChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setAboutData(prev => ({
      ...prev,
      header: {
        ...prev.header,
        [name]: value
      }
    }));
  };

  const handleStoryChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'content') {
      // Split by new lines to create an array of paragraphs
      const paragraphs = value.split('\n').filter(p => p.trim().length > 0);
      setAboutData({
        ...aboutData,
        story: {
          ...aboutData.story,
          content: paragraphs
        }
      });
    } else {
      setAboutData({
        ...aboutData,
        story: {
          ...aboutData.story,
          [name]: value
        }
      });
    }
  };

  const handleValueItemChange = (index: number, field: string, value: string) => {
    const updatedItems = [...aboutData.values.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };

    setAboutData({
      ...aboutData,
      values: {
        ...aboutData.values,
        items: updatedItems
      }
    });
  };

  const handleTeamMemberChange = (index: number, field: string, value: string) => {
    const updatedMembers = [...aboutData.team.members];
    updatedMembers[index] = {
      ...updatedMembers[index],
      [field]: value
    };

    setAboutData({
      ...aboutData,
      team: {
        ...aboutData.team,
        members: updatedMembers
      }
    });
  };

  const handleWhyChooseUsChange = (index: number, field: string, value: string) => {
    const updatedPoints = [...aboutData.whyChooseUs.points];
    updatedPoints[index] = {
      ...updatedPoints[index],
      [field]: value
    };

    setAboutData({
      ...aboutData,
      whyChooseUs: {
        ...aboutData.whyChooseUs,
        points: updatedPoints
      }
    });
  };

  const handleFaqChange = (field: string, value: string) => {
    setAboutData({
      ...aboutData,
      faq: {
        ...(aboutData.faq || {
          title: "الأسئلة الشائعة",
          subtitle: "إجابات على الأسئلة الأكثر شيوعًا",
          questions: []
        }),
        [field]: value
      }
    });
  };

  const handleFaqQuestionChange = (index: number, field: string, value: string) => {
    // If faq doesn't exist, initialize it
    if (!aboutData.faq) {
      setAboutData({
        ...aboutData,
        faq: {
          title: "الأسئلة الشائعة",
          subtitle: "إجابات على الأسئلة الأكثر شيوعًا",
          questions: [{
            question: "",
            answer: ""
          }]
        }
      });
      return;
    }
    
    const updatedQuestions = [...aboutData.faq.questions];
    updatedQuestions[index] = {
      ...updatedQuestions[index],
      [field]: value
    };
    
    setAboutData({
      ...aboutData,
      faq: {
        ...aboutData.faq,
        questions: updatedQuestions
      }
    });
  };

  const addFaqQuestion = () => {
    setAboutData({
      ...aboutData,
      faq: {
        ...(aboutData.faq || {
          title: "الأسئلة الشائعة",
          subtitle: "إجابات على الأسئلة الأكثر شيوعًا",
          questions: []
        }),
        questions: [
          ...(aboutData.faq?.questions || []),
          {
            question: "",
            answer: ""
          }
        ]
      }
    });
  };

  const removeFaqQuestion = (index: number) => {
    if (!aboutData.faq) return;
    
    const updatedQuestions = [...aboutData.faq.questions];
    updatedQuestions.splice(index, 1);
    
    setAboutData({
      ...aboutData,
      faq: {
        ...aboutData.faq,
        questions: updatedQuestions
      }
    });
  };

  const moveQuestion = (from: number, to: number) => {
    const questions = [...aboutData.faq.questions];
    const [movedQuestion] = questions.splice(from, 1);
    questions.splice(to, 0, movedQuestion);
    setAboutData({
      ...aboutData,
      faq: {
        ...aboutData.faq,
        questions: questions
      }
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h2 className="admin-title">إدارة صفحة من نحن</h2>
      
      {/* Language Info Section */}
      <div className="mb-6 p-4 rounded-lg bg-[rgba(18,20,25,0.95)] border border-[rgba(45,50,60,0.7)] shadow-md">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 rounded-full bg-pubg-orange/10">
            <GlobeIcon className="h-5 w-5 text-pubg-orange" />
          </div>
          <h3 className="text-white font-semibold">دعم اللغات المتعددة</h3>
        </div>
        <p className="text-sm text-gray-300 mb-3">
          يدعم الموقع اللغتين العربية والإنجليزية. تتيح لك حقول الإدخال المزدوجة إدخال المحتوى بكلتا اللغتين.
        </p>
        <div className="flex items-center justify-between rounded-md p-2 bg-[rgba(25,27,35,0.6)]">
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
              <span className="text-xs text-gray-300">الحقول الافتراضية (بدون لاحقة)</span>
            </div>
            <p className="text-xs text-gray-400 mt-1">مثال: <code className="text-pubg-orange">title</code></p>
          </div>
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
              <span className="text-xs text-gray-300">الحقول مع لاحقة "_en"</span>
            </div>
            <p className="text-xs text-gray-400 mt-1">مثال: <code className="text-pubg-orange">title_en</code></p>
          </div>
        </div>
        <p className="text-xs text-gray-400 mt-3">
          سيتم عرض المحتوى المناسب بناءً على تفضيل اللغة للمستخدم عند تصفح الموقع.
        </p>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-pubg-orange" />
          <span className="mr-2 text-sm md:text-base">جاري تحميل البيانات...</span>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
            <div>
              <h2 className="text-xl font-bold text-white">إدارة صفحة من نحن</h2>
              <p className="text-sm text-muted-foreground">تخصيص محتوى صفحة من نحن وأقسامها المختلفة</p>
            </div>
            <Button
              onClick={handleSubmit}
              disabled={submitting}
              className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 w-full sm:w-auto"
            >
              {submitting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Save className="ml-2 h-4 w-4" />}
              حفظ التغييرات
            </Button>
          </div>

          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="flex flex-wrap w-full h-auto bg-pubg-black border border-border rounded-lg p-1 mb-4">
              <TabsTrigger
                value="header"
                className="flex-1 min-w-[80px] data-[state=active]:bg-pubg-gray text-xs sm:text-sm"
              >
                الرأسية
              </TabsTrigger>
              <TabsTrigger
                value="story"
                className="flex-1 min-w-[80px] data-[state=active]:bg-pubg-gray text-xs sm:text-sm"
              >
                قصتنا
              </TabsTrigger>
              <TabsTrigger
                value="values"
                className="flex-1 min-w-[80px] data-[state=active]:bg-pubg-gray text-xs sm:text-sm"
              >
                قيمنا
              </TabsTrigger>
              <TabsTrigger
                value="team"
                className="flex-1 min-w-[80px] data-[state=active]:bg-pubg-gray text-xs sm:text-sm"
              >
                الفريق
              </TabsTrigger>
              <TabsTrigger
                value="whyChooseUs"
                className="flex-1 min-w-[80px] data-[state=active]:bg-pubg-gray text-xs sm:text-sm"
              >
                لماذا تختارنا
              </TabsTrigger>
              <TabsTrigger 
                value="faq" 
                className="flex-1 min-w-[80px] data-[state=active]:bg-pubg-gray text-xs sm:text-sm"
              >
                الأسئلة الشائعة
              </TabsTrigger>
            </TabsList>

            <div className="glass-card p-3 sm:p-6 rounded-lg">
              <TabsContent value="header" className="mt-0">
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <ImageIcon className="ml-2 h-5 w-5 text-pubg-orange" />
                      إعدادات الترويسة
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      قم بتحديث عنوان ووصف صفحة من نحن والصورة الخلفية
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">عنوان الصفحة</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="title" className="admin-label flex items-center">
                          <span>عنوان الصفحة (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="title"
                          name="title"
                          value={aboutData.header.title}
                          onChange={handleHeaderChange}
                          placeholder="عنوان الصفحة بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="title_en" className="admin-label flex items-center">
                          <span>Page Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="title_en"
                          name="title_en"
                          value={aboutData.header.title_en || ""}
                          onChange={handleHeaderChange}
                          placeholder="Page title in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-6 mb-2">
                      <h3 className="text-base font-semibold text-white">وصف الصفحة</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="subtitle" className="admin-label flex items-center">
                          <span>وصف الصفحة (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Textarea
                          id="subtitle"
                          name="subtitle"
                          value={aboutData.header.subtitle}
                          onChange={handleHeaderChange}
                          placeholder="وصف الصفحة بالعربية"
                          rows={3}
                          className="admin-textarea"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="subtitle_en" className="admin-label flex items-center">
                          <span>Page Description (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Textarea
                          id="subtitle_en"
                          name="subtitle_en"
                          value={aboutData.header.subtitle_en || ""}
                          onChange={handleHeaderChange}
                          placeholder="Page description in English"
                          rows={3}
                          className="admin-textarea"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    <div className="admin-form-group mt-2">
                      <Label htmlFor="backgroundImage" className="admin-label">صورة الخلفية</Label>
                      <div className="flex flex-col space-y-3">
                        <div className="relative w-full h-24 md:h-32 rounded-md overflow-hidden">
                          <img 
                            src={aboutData.header.backgroundImage} 
                            alt="Header Background" 
                            className="w-full h-full object-cover" 
                          />
                          <div className="absolute inset-0 bg-black/40"></div>
                        </div>
                        <ImageUploadPreview 
                          imageUrl={aboutData.header.backgroundImage}
                          onChange={(e) => handleHeaderChange({
                            target: { name: 'backgroundImage', value: e.target.value }
                          } as React.ChangeEvent<HTMLInputElement>)}
                          label="صورة الخلفية"
                          fieldName="backgroundImage"
                          placeholder="رابط صورة الخلفية"
                          aspectRatio={16/9}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="button" 
                      onClick={handleSubmit} 
                      disabled={submitting}
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      {submitting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Save className="ml-2 h-4 w-4" />}
                      حفظ التغييرات
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="story" className="mt-0">
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <BookOpen className="ml-2 h-5 w-5 text-pubg-orange" />
                      قسم قصتنا
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      تخصيص عنوان ومحتوى قسم قصتنا
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">عنوان القسم</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="storyTitle" className="admin-label flex items-center">
                          <span>العنوان (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="storyTitle"
                          name="title"
                          value={aboutData.story.title}
                          onChange={handleStoryChange}
                          placeholder="عنوان القسم بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="storyTitle_en" className="admin-label flex items-center">
                          <span>Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="storyTitle_en"
                          name="title_en"
                          value={aboutData.story.title_en || ""}
                          onChange={handleStoryChange}
                          placeholder="Section title in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>
                    
                    <div className="mt-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-base font-semibold text-white">محتوى القسم (عربي)</h3>
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                      </div>
                      {aboutData.story.content.map((paragraph, index) => (
                        <div key={index} className="space-y-2 mb-3">
                          <div className="flex justify-between items-center">
                            <Label htmlFor={`storyContent${index}`} className="flex items-center">
                              <span>الفقرة {index + 1}</span>
                              <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                            </Label>
                            {aboutData.story.content.length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const newContent = [...aboutData.story.content];
                                  newContent.splice(index, 1);
                                  setAboutData({
                                    ...aboutData,
                                    story: {
                                      ...aboutData.story,
                                      content: newContent
                                    }
                                  });
                                }}
                                className="h-8 w-8 p-0 text-red-500"
                              >
                                <X size={16} />
                              </Button>
                            )}
                          </div>
                          <Textarea
                            id={`storyContent${index}`}
                            value={paragraph}
                            onChange={(e) => {
                              const newContent = [...aboutData.story.content];
                              newContent[index] = e.target.value;
                              setAboutData({
                                ...aboutData,
                                story: {
                                  ...aboutData.story,
                                  content: newContent
                                }
                              });
                            }}
                            className="min-h-[100px]"
                            dir="rtl"
                          />
                        </div>
                      ))}
  
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setAboutData({
                            ...aboutData,
                            story: {
                              ...aboutData.story,
                              content: [...aboutData.story.content, ""]
                            }
                          });
                        }}
                        className="w-full sm:w-auto mb-6"
                      >
                        <PlusCircle className="h-4 w-4 ml-1" />
                        إضافة فقرة عربية
                      </Button>
                    </div>
                    
                    <div className="mt-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-base font-semibold text-white">Content (English)</h3>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                      {(aboutData.story.content_en || []).map((paragraph, index) => (
                        <div key={index} className="space-y-2 mb-3">
                          <div className="flex justify-between items-center">
                            <Label htmlFor={`storyContent_en${index}`} className="flex items-center">
                              <span>Paragraph {index + 1}</span>
                              <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                            </Label>
                            {(aboutData.story.content_en || []).length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const newContent = [...(aboutData.story.content_en || [])];
                                  newContent.splice(index, 1);
                                  setAboutData({
                                    ...aboutData,
                                    story: {
                                      ...aboutData.story,
                                      content_en: newContent
                                    }
                                  });
                                }}
                                className="h-8 w-8 p-0 text-red-500"
                              >
                                <X size={16} />
                              </Button>
                            )}
                          </div>
                          <Textarea
                            id={`storyContent_en${index}`}
                            value={paragraph}
                            onChange={(e) => {
                              const newContent = [...(aboutData.story.content_en || [])];
                              newContent[index] = e.target.value;
                              setAboutData({
                                ...aboutData,
                                story: {
                                  ...aboutData.story,
                                  content_en: newContent
                                }
                              });
                            }}
                            className="min-h-[100px]"
                            dir="ltr"
                          />
                        </div>
                      ))}
  
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setAboutData({
                            ...aboutData,
                            story: {
                              ...aboutData.story,
                              content_en: [...(aboutData.story.content_en || []), ""]
                            }
                          });
                        }}
                        className="w-full sm:w-auto mb-6"
                      >
                        <PlusCircle className="h-4 w-4 ml-1" />
                        Add English Paragraph
                      </Button>
                    </div>
  
                    <div className="admin-form-group mt-4 pt-4 border-t border-[rgba(45,50,60,0.5)]">
                      <Label htmlFor="storyImage" className="admin-label">صورة القسم</Label>
                      <div className="flex flex-col space-y-3">
                        <div className="relative w-full h-24 md:h-32 rounded-md overflow-hidden">
                          <img 
                            src={aboutData.story.image} 
                            alt="Story Section" 
                            className="w-full h-full object-cover" 
                          />
                          <div className="absolute inset-0 bg-black/40"></div>
                        </div>
                        <ImageUploadPreview 
                          imageUrl={aboutData.story.image}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              story: {
                                ...aboutData.story,
                                image: e.target.value
                              }
                            });
                          }}
                          label="صورة القسم"
                          fieldName="storyImage"
                          placeholder="رابط صورة القسم"
                          aspectRatio={4/3}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="button" 
                      onClick={handleSubmit} 
                      disabled={submitting}
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      {submitting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Save className="ml-2 h-4 w-4" />}
                      حفظ التغييرات
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="values" className="mt-0">
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <Star className="ml-2 h-5 w-5 text-pubg-orange" />
                      قسم قيمنا
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      تخصيص عنوان وقيم الشركة
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">عنوان القسم</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="valuesTitle" className="admin-label flex items-center">
                          <span>العنوان (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="valuesTitle"
                          name="title"
                          value={aboutData.values.title}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              values: {
                                ...aboutData.values,
                                title: e.target.value
                              }
                            });
                          }}
                          placeholder="عنوان القسم بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="valuesTitle_en" className="admin-label flex items-center">
                          <span>Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="valuesTitle_en"
                          name="title_en"
                          value={aboutData.values.title_en || ""}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              values: {
                                ...aboutData.values,
                                title_en: e.target.value
                              }
                            });
                          }}
                          placeholder="Section title in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-6 mb-2">
                      <h3 className="text-base font-semibold text-white">الوصف الفرعي</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="valuesSubtitle" className="admin-label flex items-center">
                          <span>الوصف الفرعي (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Textarea
                          id="valuesSubtitle"
                          name="subtitle"
                          value={aboutData.values.subtitle}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              values: {
                                ...aboutData.values,
                                subtitle: e.target.value
                              }
                            });
                          }}
                          placeholder="الوصف الفرعي بالعربية"
                          className="min-h-[80px]"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="valuesSubtitle_en" className="admin-label flex items-center">
                          <span>Subtitle (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Textarea
                          id="valuesSubtitle_en"
                          name="subtitle_en"
                          value={aboutData.values.subtitle_en || ""}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              values: {
                                ...aboutData.values,
                                subtitle_en: e.target.value
                              }
                            });
                          }}
                          placeholder="Subtitle in English"
                          className="min-h-[80px]"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="pt-4 mt-4 border-t border-[rgba(45,50,60,0.5)]">
                      <div className="flex items-center justify-between mb-4">
                        <Label className="text-base font-semibold text-white">قيم الشركة</Label>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                          <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                        </div>
                      </div>
                      <div className="space-y-4">
                        {aboutData.values.items.map((item, index) => (
                          <Card key={index} className="border-border bg-pubg-black/30">
                            <CardHeader className="p-3 pb-0">
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <Label htmlFor={`valueIcon${index}`} className="text-sm">أيقونة</Label>
                                  <Select
                                    value={item.icon}
                                    onValueChange={(value) => handleValueItemChange(index, 'icon', value)}
                                  >
                                    <SelectTrigger className="w-32 h-8 text-xs">
                                      <SelectValue placeholder="اختر أيقونة" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Shield">درع الحماية</SelectItem>
                                      <SelectItem value="Award">وسام</SelectItem>
                                      <SelectItem value="Clock">ساعة</SelectItem>
                                      <SelectItem value="Star">نجمة</SelectItem>
                                      <SelectItem value="Heart">قلب</SelectItem>
                                      <SelectItem value="Zap">برق</SelectItem>
                                      <SelectItem value="Check">صح</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                {aboutData.values.items.length > 1 && (
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const newItems = [...aboutData.values.items];
                                      newItems.splice(index, 1);
                                      setAboutData({
                                        ...aboutData,
                                        values: {
                                          ...aboutData.values,
                                          items: newItems
                                        }
                                      });
                                    }}
                                    className="h-8 w-8 p-0 text-red-500"
                                  >
                                    <X size={16} />
                                  </Button>
                                )}
                              </div>
                            </CardHeader>
                            <CardContent className="p-3 space-y-3">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`valueTitle${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>العنوان (عربي)</span>
                                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Input
                                    id={`valueTitle${index}`}
                                    value={item.title}
                                    onChange={(e) => handleValueItemChange(index, 'title', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="rtl"
                                    placeholder="عنوان القيمة بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`valueTitle_en${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>Title (English)</span>
                                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Input
                                    id={`valueTitle_en${index}`}
                                    value={item.title_en || ""}
                                    onChange={(e) => handleValueItemChange(index, 'title_en', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="ltr"
                                    placeholder="Value title in English"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`valueDescription${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>الوصف (عربي)</span>
                                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Textarea
                                    id={`valueDescription${index}`}
                                    value={item.description}
                                    onChange={(e) => handleValueItemChange(index, 'description', e.target.value)}
                                    className="min-h-[60px] text-sm"
                                    dir="rtl"
                                    placeholder="وصف القيمة بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`valueDescription_en${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>Description (English)</span>
                                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Textarea
                                    id={`valueDescription_en${index}`}
                                    value={item.description_en || ""}
                                    onChange={(e) => handleValueItemChange(index, 'description_en', e.target.value)}
                                    className="min-h-[60px] text-sm"
                                    dir="ltr"
                                    placeholder="Value description in English"
                                  />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setAboutData({
                          ...aboutData,
                          values: {
                            ...aboutData.values,
                            items: [
                              ...aboutData.values.items,
                              {
                                icon: "Shield",
                                title: "",
                                description: ""
                              }
                            ]
                          }
                        });
                      }}
                      className="w-full sm:w-auto mt-4"
                    >
                      <PlusCircle className="h-4 w-4 ml-1" />
                      إضافة قيمة جديدة
                    </Button>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="button" 
                      onClick={handleSubmit} 
                      disabled={submitting}
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      {submitting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Save className="ml-2 h-4 w-4" />}
                      حفظ التغييرات
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="team" className="mt-0">
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <Users className="ml-2 h-5 w-5 text-pubg-orange" />
                      قسم الفريق
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      تخصيص معلومات فريق العمل
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">عنوان القسم</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="teamTitle" className="admin-label flex items-center">
                          <span>العنوان (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="teamTitle"
                          name="title"
                          value={aboutData.team.title}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              team: {
                                ...aboutData.team,
                                title: e.target.value
                              }
                            });
                          }}
                          placeholder="عنوان القسم بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="teamTitle_en" className="admin-label flex items-center">
                          <span>Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="teamTitle_en"
                          name="title_en"
                          value={aboutData.team.title_en || ""}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              team: {
                                ...aboutData.team,
                                title_en: e.target.value
                              }
                            });
                          }}
                          placeholder="Section title in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-6 mb-2">
                      <h3 className="text-base font-semibold text-white">الوصف الفرعي</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="teamSubtitle" className="admin-label flex items-center">
                          <span>الوصف الفرعي (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Textarea
                          id="teamSubtitle"
                          name="subtitle"
                          value={aboutData.team.subtitle}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              team: {
                                ...aboutData.team,
                                subtitle: e.target.value
                              }
                            });
                          }}
                          placeholder="الوصف الفرعي بالعربية"
                          className="min-h-[80px]"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="teamSubtitle_en" className="admin-label flex items-center">
                          <span>Subtitle (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Textarea
                          id="teamSubtitle_en"
                          name="subtitle_en"
                          value={aboutData.team.subtitle_en || ""}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              team: {
                                ...aboutData.team,
                                subtitle_en: e.target.value
                              }
                            });
                          }}
                          placeholder="Subtitle in English"
                          className="min-h-[80px]"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="pt-4 mt-4 border-t border-[rgba(45,50,60,0.5)]">
                      <div className="flex items-center justify-between mb-4">
                        <Label className="text-base font-semibold text-white">أعضاء الفريق</Label>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                          <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                        </div>
                      </div>
                      <div className="space-y-4">
                        {aboutData.team.members.map((member, index) => (
                          <Card key={index} className="border-border bg-pubg-black/30">
                            <CardHeader className="p-3 pb-0">
                              <div className="flex justify-between items-center">
                                <CardTitle className="text-base flex items-center gap-2">
                                  <div className="w-8 h-8 rounded-full overflow-hidden border border-border flex-shrink-0">
                                    <img 
                                      src={member.avatar || "https://via.placeholder.com/150?text=?"}
                                      alt={member.name} 
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/150?text=?';
                                      }}
                                    />
                                  </div>
                                  <span>{member.name || `عضو ${index + 1}`}</span>
                                </CardTitle>
                                {aboutData.team.members.length > 1 && (
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const newMembers = [...aboutData.team.members];
                                      newMembers.splice(index, 1);
                                      setAboutData({
                                        ...aboutData,
                                        team: {
                                          ...aboutData.team,
                                          members: newMembers
                                        }
                                      });
                                    }}
                                    className="h-8 w-8 p-0 text-red-500"
                                  >
                                    <X size={16} />
                                  </Button>
                                )}
                              </div>
                            </CardHeader>
                            <CardContent className="p-3 space-y-3">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="text-sm font-medium text-white">معلومات العضو</h4>
                                <div className="flex items-center space-x-1 space-x-reverse">
                                  <span className="text-[10px] bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                                  <span className="text-[10px] bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`memberName${index}`} className="text-xs mb-1 block flex items-center">
                                    <span>الاسم (عربي)</span>
                                    <span className="mr-1 text-[10px] bg-red-500/20 text-red-500 px-1 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Input
                                    id={`memberName${index}`}
                                    value={member.name}
                                    onChange={(e) => handleTeamMemberChange(index, 'name', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="rtl"
                                    placeholder="اسم العضو بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`memberName_en${index}`} className="text-xs mb-1 block flex items-center">
                                    <span>Name (English)</span>
                                    <span className="mr-1 text-[10px] bg-blue-500/20 text-blue-500 px-1 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Input
                                    id={`memberName_en${index}`}
                                    value={member.name_en || ""}
                                    onChange={(e) => handleTeamMemberChange(index, 'name_en', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="ltr"
                                    placeholder="Member name in English"
                                  />
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`memberRole${index}`} className="text-xs mb-1 block flex items-center">
                                    <span>المنصب (عربي)</span>
                                    <span className="mr-1 text-[10px] bg-red-500/20 text-red-500 px-1 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Input
                                    id={`memberRole${index}`}
                                    value={member.role}
                                    onChange={(e) => handleTeamMemberChange(index, 'role', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="rtl"
                                    placeholder="منصب العضو بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`memberRole_en${index}`} className="text-xs mb-1 block flex items-center">
                                    <span>Role (English)</span>
                                    <span className="mr-1 text-[10px] bg-blue-500/20 text-blue-500 px-1 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Input
                                    id={`memberRole_en${index}`}
                                    value={member.role_en || ""}
                                    onChange={(e) => handleTeamMemberChange(index, 'role_en', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="ltr"
                                    placeholder="Member role in English"
                                  />
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`memberBio${index}`} className="text-xs mb-1 block flex items-center">
                                    <span>نبذة (عربي)</span>
                                    <span className="mr-1 text-[10px] bg-red-500/20 text-red-500 px-1 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Textarea
                                    id={`memberBio${index}`}
                                    value={member.bio}
                                    onChange={(e) => handleTeamMemberChange(index, 'bio', e.target.value)}
                                    className="min-h-[60px] text-sm"
                                    dir="rtl"
                                    placeholder="نبذة عن العضو بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`memberBio_en${index}`} className="text-xs mb-1 block flex items-center">
                                    <span>Bio (English)</span>
                                    <span className="mr-1 text-[10px] bg-blue-500/20 text-blue-500 px-1 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Textarea
                                    id={`memberBio_en${index}`}
                                    value={member.bio_en || ""}
                                    onChange={(e) => handleTeamMemberChange(index, 'bio_en', e.target.value)}
                                    className="min-h-[60px] text-sm"
                                    dir="ltr"
                                    placeholder="Member bio in English"
                                  />
                                </div>
                              </div>
                              
                              <ImageUploadPreview 
                                imageUrl={member.avatar}
                                onChange={(e) => handleTeamMemberChange(index, 'avatar', e.target.value)}
                                label="صورة العضو"
                                fieldName={`memberAvatar${index}`}
                                placeholder="رابط الصورة الشخصية"
                                aspectRatio={1} // 1:1 ratio for profile photos
                              />
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setAboutData({
                          ...aboutData,
                          team: {
                            ...aboutData.team,
                            members: [
                              ...aboutData.team.members,
                              {
                                name: "",
                                role: "",
                                avatar: "",
                                bio: ""
                              }
                            ]
                          }
                        });
                      }}
                      className="w-full sm:w-auto mt-4"
                    >
                      <PlusCircle className="h-4 w-4 ml-1" />
                      إضافة عضو جديد
                    </Button>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="button" 
                      onClick={handleSubmit} 
                      disabled={submitting}
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      {submitting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Save className="ml-2 h-4 w-4" />}
                      حفظ التغييرات
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="whyChooseUs" className="mt-0">
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <Award className="ml-2 h-5 w-5 text-pubg-orange" />
                      قسم لماذا تختارنا
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      تخصيص أسباب اختيار خدماتنا
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">عنوان القسم</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="whyChooseUsTitle" className="admin-label flex items-center">
                          <span>العنوان (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="whyChooseUsTitle"
                          name="title"
                          value={aboutData.whyChooseUs.title}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              whyChooseUs: {
                                ...aboutData.whyChooseUs,
                                title: e.target.value
                              }
                            });
                          }}
                          placeholder="عنوان القسم بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="whyChooseUsTitle_en" className="admin-label flex items-center">
                          <span>Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="whyChooseUsTitle_en"
                          name="title_en"
                          value={aboutData.whyChooseUs.title_en || ""}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              whyChooseUs: {
                                ...aboutData.whyChooseUs,
                                title_en: e.target.value
                              }
                            });
                          }}
                          placeholder="Section title in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-6 mb-2">
                      <h3 className="text-base font-semibold text-white">الوصف الفرعي</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="whyChooseUsSubtitle" className="admin-label flex items-center">
                          <span>الوصف الفرعي (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Textarea
                          id="whyChooseUsSubtitle"
                          name="subtitle"
                          value={aboutData.whyChooseUs.subtitle}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              whyChooseUs: {
                                ...aboutData.whyChooseUs,
                                subtitle: e.target.value
                              }
                            });
                          }}
                          placeholder="الوصف الفرعي بالعربية"
                          className="min-h-[80px]"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="whyChooseUsSubtitle_en" className="admin-label flex items-center">
                          <span>Subtitle (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Textarea
                          id="whyChooseUsSubtitle_en"
                          name="subtitle_en"
                          value={aboutData.whyChooseUs.subtitle_en || ""}
                          onChange={(e) => {
                            setAboutData({
                              ...aboutData,
                              whyChooseUs: {
                                ...aboutData.whyChooseUs,
                                subtitle_en: e.target.value
                              }
                            });
                          }}
                          placeholder="Subtitle in English"
                          className="min-h-[80px]"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="pt-4 mt-4 border-t border-[rgba(45,50,60,0.5)]">
                      <div className="flex items-center justify-between mb-4">
                        <Label className="text-base font-semibold text-white">النقاط</Label>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                          <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                        </div>
                      </div>
                      <div className="space-y-4">
                        {aboutData.whyChooseUs.points.map((point, index) => (
                          <Card key={index} className="border-border bg-pubg-black/30">
                            <CardHeader className="p-3 pb-0">
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <Label htmlFor={`pointIcon${index}`} className="text-sm">أيقونة</Label>
                                  <Select
                                    value={point.icon}
                                    onValueChange={(value) => handleWhyChooseUsChange(index, 'icon', value)}
                                  >
                                    <SelectTrigger className="w-32 h-8 text-xs">
                                      <SelectValue placeholder="اختر أيقونة" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Shield">درع الحماية</SelectItem>
                                      <SelectItem value="Star">نجمة</SelectItem>
                                      <SelectItem value="Clock">ساعة</SelectItem>
                                      <SelectItem value="DollarSign">دولار</SelectItem>
                                      <SelectItem value="Award">وسام</SelectItem>
                                      <SelectItem value="ThumbsUp">إعجاب</SelectItem>
                                      <SelectItem value="Check">صح</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                {aboutData.whyChooseUs.points.length > 1 && (
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const newPoints = [...aboutData.whyChooseUs.points];
                                      newPoints.splice(index, 1);
                                      setAboutData({
                                        ...aboutData,
                                        whyChooseUs: {
                                          ...aboutData.whyChooseUs,
                                          points: newPoints
                                        }
                                      });
                                    }}
                                    className="h-8 w-8 p-0 text-red-500"
                                  >
                                    <X size={16} />
                                  </Button>
                                )}
                              </div>
                            </CardHeader>
                            <CardContent className="p-3 space-y-3">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`pointTitle${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>العنوان (عربي)</span>
                                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Input
                                    id={`pointTitle${index}`}
                                    value={point.title}
                                    onChange={(e) => handleWhyChooseUsChange(index, 'title', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="rtl"
                                    placeholder="عنوان النقطة بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`pointTitle_en${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>Title (English)</span>
                                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Input
                                    id={`pointTitle_en${index}`}
                                    value={point.title_en || ""}
                                    onChange={(e) => handleWhyChooseUsChange(index, 'title_en', e.target.value)}
                                    className="h-8 text-sm"
                                    dir="ltr"
                                    placeholder="Point title in English"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`pointDescription${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>الوصف (عربي)</span>
                                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Textarea
                                    id={`pointDescription${index}`}
                                    value={point.description}
                                    onChange={(e) => handleWhyChooseUsChange(index, 'description', e.target.value)}
                                    className="min-h-[60px] text-sm"
                                    dir="rtl"
                                    placeholder="وصف النقطة بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`pointDescription_en${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>Description (English)</span>
                                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Textarea
                                    id={`pointDescription_en${index}`}
                                    value={point.description_en || ""}
                                    onChange={(e) => handleWhyChooseUsChange(index, 'description_en', e.target.value)}
                                    className="min-h-[60px] text-sm"
                                    dir="ltr"
                                    placeholder="Point description in English"
                                  />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setAboutData({
                          ...aboutData,
                          whyChooseUs: {
                            ...aboutData.whyChooseUs,
                            points: [
                              ...aboutData.whyChooseUs.points,
                              {
                                icon: "Shield",
                                title: "",
                                description: ""
                              }
                            ]
                          }
                        });
                      }}
                      className="w-full sm:w-auto mt-4"
                    >
                      <PlusCircle className="h-4 w-4 ml-1" />
                      إضافة نقطة جديدة
                    </Button>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="button" 
                      onClick={handleSubmit} 
                      disabled={submitting}
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      {submitting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Save className="ml-2 h-4 w-4" />}
                      حفظ التغييرات
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="faq" className="block">
                <Card className="admin-card">
                  <CardHeader className="px-3 py-4 md:p-6 border-b border-[rgba(45,50,60,0.7)]">
                    <CardTitle className="flex items-center text-white admin-subtitle">
                      <HelpCircle className="ml-2 h-5 w-5 text-pubg-orange" />
                      الأسئلة الشائعة
                    </CardTitle>
                    <CardDescription className="text-xs md:text-sm">
                      إدارة الأسئلة والإجابات الشائعة عن الخدمات
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 md:space-y-6 px-3 pb-4 md:p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-base font-semibold text-white">عنوان القسم</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="faqTitle" className="admin-label flex items-center">
                          <span>العنوان (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Input
                          id="faqTitle"
                          name="title"
                          value={aboutData.faq?.title || "الأسئلة الشائعة"}
                          onChange={(e) => handleFaqChange('title', e.target.value)}
                          placeholder="عنوان القسم بالعربية"
                          className="admin-input"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="faqTitle_en" className="admin-label flex items-center">
                          <span>Title (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Input
                          id="faqTitle_en"
                          name="title_en"
                          value={aboutData.faq?.title_en || ""}
                          onChange={(e) => handleFaqChange('title_en', e.target.value)}
                          placeholder="Section title in English"
                          className="admin-input"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-6 mb-2">
                      <h3 className="text-base font-semibold text-white">الوصف الفرعي</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                        <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="admin-form-group">
                        <Label htmlFor="faqSubtitle" className="admin-label flex items-center">
                          <span>الوصف الفرعي (عربي)</span>
                          <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                        </Label>
                        <Textarea
                          id="faqSubtitle"
                          name="subtitle"
                          value={aboutData.faq?.subtitle || "إجابات على الأسئلة الأكثر شيوعًا"}
                          onChange={(e) => handleFaqChange('subtitle', e.target.value)}
                          placeholder="الوصف الفرعي بالعربية"
                          className="min-h-[80px]"
                          dir="rtl"
                        />
                      </div>
                      
                      <div className="admin-form-group">
                        <Label htmlFor="faqSubtitle_en" className="admin-label flex items-center">
                          <span>Subtitle (English)</span>
                          <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                        </Label>
                        <Textarea
                          id="faqSubtitle_en"
                          name="subtitle_en"
                          value={aboutData.faq?.subtitle_en || ""}
                          onChange={(e) => handleFaqChange('subtitle_en', e.target.value)}
                          placeholder="Subtitle in English"
                          className="min-h-[80px]"
                          dir="ltr"
                        />
                      </div>
                    </div>

                    <div className="pt-4 mt-4 border-t border-[rgba(45,50,60,0.5)]">
                      <div className="flex items-center justify-between mb-4">
                        <Label className="text-base font-semibold text-white">الأسئلة والإجابات</Label>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <span className="text-xs bg-red-500/20 text-red-500 px-2 py-1 rounded-md">عربي</span>
                          <span className="text-xs bg-blue-500/20 text-blue-500 px-2 py-1 rounded-md">English</span>
                        </div>
                      </div>
                      
                      {aboutData.faq?.questions.length === 0 && (
                        <div className="p-4 bg-card/20 rounded-md text-center text-sm text-muted-foreground">
                          لا توجد أسئلة بعد. أضف سؤالًا باستخدام الزر أدناه.
                        </div>
                      )}
                      
                      <div className="space-y-6">
                        {aboutData.faq?.questions.map((question, index) => (
                          <Card key={index} className="border-border bg-pubg-black/30">
                            <CardHeader className="p-3 pb-2">
                              <div className="flex justify-between items-center gap-2">
                                <div className="flex-1 flex items-center gap-2">
                                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-pubg-orange/20 text-pubg-orange">
                                    {index + 1}
                                  </div>
                                  <div className="text-sm font-medium">سؤال {index + 1}</div>
                                </div>
                                <div className="flex items-center gap-1">
                                  {index > 0 && (
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => moveQuestion(index, index - 1)}
                                      className="h-7 w-7 p-0 text-gray-400 hover:text-white"
                                      title="نقل لأعلى"
                                    >
                                      <ChevronUp size={14} />
                                    </Button>
                                  )}
                                  {index < aboutData.faq.questions.length - 1 && (
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => moveQuestion(index, index + 1)}
                                      className="h-7 w-7 p-0 text-gray-400 hover:text-white"
                                      title="نقل لأسفل"
                                    >
                                      <ChevronDown size={14} />
                                    </Button>
                                  )}
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeFaqQuestion(index)}
                                    className="h-7 w-7 p-0 text-red-500"
                                    title="حذف السؤال"
                                  >
                                    <X size={14} />
                                  </Button>
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent className="p-3 space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`question${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>السؤال (عربي)</span>
                                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Input
                                    id={`question${index}`}
                                    value={question.question}
                                    onChange={(e) => handleFaqQuestionChange(index, 'question', e.target.value)}
                                    className="text-sm"
                                    dir="rtl"
                                    placeholder="أدخل السؤال بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`question_en${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>Question (English)</span>
                                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Input
                                    id={`question_en${index}`}
                                    value={question.question_en || ""}
                                    onChange={(e) => handleFaqQuestionChange(index, 'question_en', e.target.value)}
                                    className="text-sm"
                                    dir="ltr"
                                    placeholder="Enter question in English"
                                  />
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`answer${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>الإجابة (عربي)</span>
                                    <span className="mr-2 text-xs bg-red-500/20 text-red-500 px-1.5 py-0.5 rounded-md">عربي</span>
                                  </Label>
                                  <Textarea
                                    id={`answer${index}`}
                                    value={question.answer}
                                    onChange={(e) => handleFaqQuestionChange(index, 'answer', e.target.value)}
                                    className="min-h-[80px] text-sm"
                                    dir="rtl"
                                    placeholder="أدخل الإجابة بالعربية"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`answer_en${index}`} className="text-sm mb-1 block flex items-center">
                                    <span>Answer (English)</span>
                                    <span className="mr-2 text-xs bg-blue-500/20 text-blue-500 px-1.5 py-0.5 rounded-md">EN</span>
                                  </Label>
                                  <Textarea
                                    id={`answer_en${index}`}
                                    value={question.answer_en || ""}
                                    onChange={(e) => handleFaqQuestionChange(index, 'answer_en', e.target.value)}
                                    className="min-h-[80px] text-sm"
                                    dir="ltr"
                                    placeholder="Enter answer in English"
                                  />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={addFaqQuestion}
                      className="w-full sm:w-auto mt-4"
                    >
                      <PlusCircle className="h-4 w-4 ml-1" />
                      إضافة سؤال جديد
                    </Button>
                  </CardContent>
                  <CardFooter className="px-3 py-3 md:px-6 md:py-4 border-t border-[rgba(45,50,60,0.7)]">
                    <Button 
                      type="button" 
                      onClick={handleSubmit} 
                      disabled={submitting}
                      className="w-full admin-button bg-pubg-orange text-white hover:bg-pubg-orange/90"
                    >
                      {submitting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Save className="ml-2 h-4 w-4" />}
                      حفظ التغييرات
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      )}
    </div>
  );
};

export default AboutPageManager; 