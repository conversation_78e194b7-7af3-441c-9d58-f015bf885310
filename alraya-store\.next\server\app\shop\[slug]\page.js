(()=>{var e={};e.id=625,e.ids=[625],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3876:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]])},6943:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13964:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},13987:(e,s,t)=>{Promise.resolve().then(t.bind(t,85919))},16838:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(37413),l=t(85919);async function r({params:e}){let{slug:s}=await e;return(0,a.jsx)(l.DynamicProductPage,{productId:s})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28561:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33872:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},35438:(e,s,t)=>{"use strict";t.d(s,{j:()=>o});var a=t(60687),l=t(6943),r=t(24934),i=t(70373),c=t(30474),n=t(16189);function o({onMenuOpen:e}){let s=(0,n.useRouter)();return(0,a.jsxs)("header",{className:"fixed top-0 left-0 right-0 z-50 flex items-center justify-between p-4 lg:p-6 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-2xl",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(i.eu,{className:"h-10 w-10 border-2 border-yellow-400/20 cursor-pointer hover:border-yellow-400/40 transition-all duration-300 hover:scale-110",onClick:()=>{s.push("/profile")},children:[(0,a.jsx)(i.BK,{src:"",alt:"Profile"}),(0,a.jsx)(i.q5,{className:"bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-sm font-bold",children:"ر"})]})}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)(c.default,{src:"/logo-without-background.png",alt:"رايه شوب",width:200,height:80,className:"h-12 lg:h-16 w-auto object-contain",priority:!0})}),(0,a.jsx)(r.$,{variant:"ghost",size:"icon",className:"text-white hover:bg-white/10 transition-all duration-300 hover:scale-110",onClick:e,children:(0,a.jsx)(l.A,{className:"h-6 w-6"})})]})}},35583:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},40945:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]])},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44689:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},46001:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]])},50197:(e,s,t)=>{"use strict";t.d(s,{G:()=>o});var a=t(60687),l=t(58869),r=t(46001),i=t(32192),c=t(35583),n=t(84027);function o({activeTab:e,onTabChange:s}){let t=[{id:"profile",icon:(0,a.jsx)(l.A,{className:"h-6 w-6"}),label:"حسابي"},{id:"shop",icon:(0,a.jsx)(r.A,{className:"h-6 w-6"}),label:"المتجر"},{id:"home",icon:(0,a.jsx)(i.A,{className:"h-6 w-6"}),label:"الرئيسية"},{id:"wallet",icon:(0,a.jsx)(c.A,{className:"h-6 w-6"}),label:"المحفظة"},{id:"support",icon:(0,a.jsx)(n.A,{className:"h-6 w-6"}),label:"الدعم الفني"}];return(0,a.jsx)("footer",{className:"hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-8 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center gap-8",children:t.map(({id:t,icon:l,label:r})=>(0,a.jsxs)("button",{onClick:()=>s(t),className:`flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 hover:scale-105 ${e===t?"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg":"text-slate-400 hover:text-white hover:bg-white/10"}`,children:[l,(0,a.jsx)("span",{className:"text-sm font-medium",children:r})]},t))})})})}},56085:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},60020:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]])},62228:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>x,tree:()=>o});var a=t(65239),l=t(48088),r=t(88170),i=t.n(r),c=t(30893),n={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let o={children:["",{children:["shop",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,16838)),"D:\\VS-projects\\try\\alraya-store\\app\\shop\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"D:\\VS-projects\\try\\alraya-store\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["D:\\VS-projects\\try\\alraya-store\\app\\shop\\[slug]\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/shop/[slug]/page",pathname:"/shop/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70663:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},73259:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},74186:(e,s,t)=>{"use strict";t.d(s,{v:()=>h});var a=t(60687),l=t(58869),r=t(46001),i=t(32192),c=t(35583),n=t(33872),o=t(48176),d=t(70338);function h({activeTab:e,onTabChange:s,unreadChatCount:t=0,walletNotificationCount:h=0}){let{openChat:x}=(0,d.X)(),m=[{id:"profile",icon:(0,a.jsx)(l.A,{className:"h-5 w-5"}),label:"حسابي"},{id:"shop",icon:(0,a.jsx)(r.A,{className:"h-5 w-5"}),label:"المتجر"},{id:"home",icon:(0,a.jsx)(i.A,{className:"h-6 w-6"}),label:"",center:!0},{id:"wallet",icon:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),h>0&&(0,a.jsx)(o.MC,{count:h,className:"absolute -top-2 -right-2 scale-75"})]}),label:"المحفظة"},{id:"chat",icon:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),t>0&&(0,a.jsx)(o.MC,{count:t,className:"absolute -top-2 -right-2 scale-75"})]}),label:"المحادثة"}];return(0,a.jsx)("nav",{className:"lg:hidden fixed bottom-6 left-1/2 w-[calc(100%-2rem)] max-w-sm -translate-x-1/2 z-40",children:(0,a.jsx)("div",{className:"bg-slate-800/80 backdrop-blur-2xl rounded-3xl px-6 py-4 shadow-2xl border border-slate-700/50",children:(0,a.jsx)("div",{className:"flex items-center justify-around",children:m.map(({id:t,icon:l,label:r,center:i})=>i?(0,a.jsx)("button",{onClick:()=>s(t),className:"flex flex-col items-center p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg hover:scale-110 transition-all duration-300 text-slate-900",children:l},t):(0,a.jsxs)("button",{onClick:()=>{"chat"===t?x():s(t)},className:`flex flex-col items-center space-y-1 p-3 rounded-2xl transition-all duration-300 hover:scale-110 ${e===t?"bg-white/20 text-yellow-400 shadow-lg":"text-slate-400 hover:text-white hover:bg-white/10"}`,children:[l,(0,a.jsx)("span",{className:"text-xs font-medium",children:r})]},t))})})})}},74513:(e,s,t)=>{"use strict";t.d(s,{p:()=>u});var a=t(60687),l=t(11860),r=t(14952),i=t(24934),c=t(32192),n=t(46001),o=t(35583),d=t(58869),h=t(84027),x=t(48340);let m=[{icon:(0,a.jsx)(c.A,{className:"h-5 w-5"}),label:"الرئيسية",href:"/"},{icon:(0,a.jsx)(n.A,{className:"h-5 w-5"}),label:"المتجر",href:"/shop"},{icon:(0,a.jsx)(o.A,{className:"h-5 w-5"}),label:"المحفظة",href:"/wallet"},{icon:(0,a.jsx)(d.A,{className:"h-5 w-5"}),label:"حسابي",href:"/profile"},{icon:(0,a.jsx)(h.A,{className:"h-5 w-5"}),label:"لوحة الإدارة",href:"/admin"},{icon:(0,a.jsx)(x.A,{className:"h-5 w-5"}),label:"اتصل بنا",href:"/contact"}];var p=t(16189);function u({isOpen:e,onClose:s}){let t=(0,p.useRouter)(),c=e=>{s(),t.push(e)};return(0,a.jsxs)(a.Fragment,{children:[e&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300",onClick:s}),(0,a.jsx)("div",{className:`fixed top-0 right-0 h-full w-80 lg:w-96 bg-white/10 backdrop-blur-2xl z-50 transform transition-transform duration-300 ease-out border-l border-white/20 shadow-2xl ${e?"translate-x-0":"translate-x-full"}`,children:(0,a.jsxs)("div",{className:"p-6 pt-24 lg:pt-28",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent",children:"رايه شوب"}),(0,a.jsx)(i.$,{variant:"ghost",size:"icon",onClick:s,className:"text-white hover:bg-white/10 rounded-full",children:(0,a.jsx)(l.A,{className:"h-6 w-6"})})]}),(0,a.jsx)("nav",{className:"space-y-2",children:m.map((e,s)=>(0,a.jsxs)("button",{onClick:()=>c(e.href),className:"w-full flex items-center justify-between p-4 rounded-xl hover:bg-white/20 backdrop-blur-sm transition-all duration-300 group border border-transparent hover:border-white/30 text-right",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"text-yellow-400 group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,a.jsx)("span",{className:"font-medium text-lg text-white",children:e.label})]}),(0,a.jsx)(r.A,{className:"h-4 w-4 text-slate-400 group-hover:text-white transition-colors duration-300"})]},s))}),(0,a.jsx)("div",{className:"absolute bottom-6 left-6 right-6",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-yellow-400/30 to-orange-500/30 backdrop-blur-sm rounded-xl p-4 border border-yellow-400/40 shadow-lg",children:(0,a.jsx)("p",{className:"text-sm text-center text-yellow-400 font-medium",children:"\uD83C\uDFAE أفضل متجر للألعاب الرقمية"})})})]})})]})}},79643:(e,s,t)=>{Promise.resolve().then(t.bind(t,89943))},80428:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},81904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},85919:(e,s,t)=>{"use strict";t.d(s,{DynamicProductPage:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call DynamicProductPage() from the server but DynamicProductPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\components\\products\\DynamicProductPage.tsx","DynamicProductPage")},89943:(e,s,t)=>{"use strict";t.d(s,{DynamicProductPage:()=>M});var a=t(60687),l=t(43210),r=t(16189),i=t(55192),c=t(24934),n=t(59821),o=t(35438),d=t(74513),h=t(74186),x=t(50197),m=t(28559),p=t(80428),u=t(56085),b=t(45583),g=t(48730),j=t(31158),f=t(99891),v=t(13964);t(48196);var y=t(64398),N=t(28561),w=t(54278),k=t(32517);function A({template:e,onSubmit:s,currency:t,showPricing:r=!0,disabled:o=!1,className:d=""}){let[h,x]=(0,l.useState)(null),[m,p]=(0,l.useState)(1),[u,g]=(0,l.useState)({}),[j,f]=(0,l.useState)({}),[v,A]=(0,l.useState)(!1),{convertPrice:M}=(0,k.D)(),C=()=>h?h.price*m:0,P=e=>{let s=M(e,"USD",t);return(0,w.vv)(s,t)},D=e=>{x(e),f({})},S=(e,s)=>{g(t=>({...t,[e]:s})),j[e]&&f(s=>({...s,[e]:""}))},z=()=>{let s={};return e.fields.filter(e=>e.isActive).forEach(e=>{let t=u[e.name];e.required&&(!t||""===t.toString().trim())&&(s[e.name]=`${e.label} مطلوب`),t&&"email"===e.type&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)&&(s[e.name]="يرجى إدخال بريد إلكتروني صحيح"),t&&"number"===e.type&&isNaN(Number(t))&&(s[e.name]="يرجى إدخال رقم صحيح")}),s},E=async()=>{if(!h){alert("يرجى اختيار حزمة");return}let a=z();if(Object.keys(a).length>0){f(a);return}try{A(!0);let a={templateId:e.id,selectedPackage:h,quantity:m,customFields:u,totalPrice:C(),currency:t};await s(a)}catch(e){console.error("Error submitting form:",e),alert("حدث خطأ أثناء إرسال الطلب")}finally{A(!1)}},$=C(),q=e.fields.filter(e=>e.isActive);return(0,a.jsxs)("div",{className:`space-y-6 ${d}`,children:[(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5"}),"اختر الحزمة"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"grid gap-4",children:e.packages.map(e=>(0,a.jsxs)("div",{onClick:()=>!o&&D(e),className:`relative p-4 rounded-lg border-2 cursor-pointer transition-all ${h?.id===e.id?"border-blue-500 bg-blue-500/10":"border-slate-600 bg-slate-700/30 hover:border-slate-500"} ${o?"opacity-50 cursor-not-allowed":""}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:e.name}),e.amount&&(0,a.jsx)("p",{className:"text-slate-300 text-sm",children:e.amount}),e.description&&(0,a.jsx)("p",{className:"text-slate-400 text-sm mt-1",children:e.description})]}),(0,a.jsxs)("div",{className:"text-right",children:[e.originalPrice&&e.originalPrice>e.price&&(0,a.jsx)("p",{className:"text-slate-400 text-sm line-through",children:P(e.originalPrice)}),(0,a.jsx)("p",{className:"text-white font-bold",children:P(e.price)}),e.discount&&(0,a.jsxs)(n.E,{variant:"destructive",className:"mt-1",children:["خصم ",e.discount,"%"]})]})]}),e.popular&&(0,a.jsxs)(n.E,{className:"absolute top-2 left-2 bg-yellow-500 text-black",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"الأكثر شعبية"]})]},e.id))})})]}),q.length>0&&h&&(0,a.jsxs)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-white",children:"معلومات إضافية"})}),(0,a.jsx)(i.Wu,{className:"space-y-4",children:q.map(e=>{let s=u[e.name]||"",t=j[e.name];return(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-slate-300",children:[e.label,e.required&&(0,a.jsx)("span",{className:"text-red-400 ml-1",children:"*"})]}),"text"===e.type&&(0,a.jsx)("input",{type:"text",value:s,onChange:s=>S(e.name,s.target.value),placeholder:e.placeholder,disabled:o,className:`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${t?"border-red-500":"border-slate-600"}`}),"email"===e.type&&(0,a.jsx)("input",{type:"email",value:s,onChange:s=>S(e.name,s.target.value),placeholder:e.placeholder,disabled:o,className:`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${t?"border-red-500":"border-slate-600"}`}),"number"===e.type&&(0,a.jsx)("input",{type:"number",value:s,onChange:s=>S(e.name,s.target.value),placeholder:e.placeholder,disabled:o,className:`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${t?"border-red-500":"border-slate-600"}`}),t&&(0,a.jsx)("p",{className:"text-red-400 text-sm",children:t})]},e.id)})})]}),h&&(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:(0,a.jsxs)(i.Wu,{className:"p-6",children:[r&&(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4 text-white",children:[(0,a.jsx)("span",{className:"text-lg",children:"المجموع:"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-yellow-400",children:P($)})]}),(0,a.jsx)(c.$,{onClick:E,disabled:o||v||!h,className:"w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold py-3 text-lg",size:"lg",children:v?"جاري المعالجة...":(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"h-5 w-5 mr-2"}),"instant"===e.processingType?"اشتري الآن":"أضف للسلة",r&&` - ${P($)}`]})})]})})]})}function M({productId:e}){let s=(0,r.useRouter)(),[t,y]=(0,l.useState)("shop"),[N,w]=(0,l.useState)(!1),[k,M]=(0,l.useState)(null),[C,P]=(0,l.useState)(!0),[D,S]=(0,l.useState)(null),z=e=>{"wallet"===e?s.push("/wallet"):"profile"===e?s.push("/profile"):"shop"===e?s.push("/shop"):"home"===e?s.push("/"):"support"===e?s.push("/contact"):y(e)},E=async e=>{try{console.log("Purchase data:",e),s.push("/checkout/success")}catch(e){console.error("Error processing purchase:",e),alert("حدث خطأ أثناء معالجة الطلب")}};return C?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"⏳"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"جاري تحميل المنتج..."}),(0,a.jsx)("p",{className:"text-slate-400",children:"يرجى الانتظار"})]})}):D||!k?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"❌"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:D||"المنتج غير موجود"}),(0,a.jsx)("p",{className:"text-slate-400 mb-4",children:D||"لم نتمكن من العثور على المنتج المطلوب"}),!1,(0,a.jsx)(c.$,{onClick:()=>s.push("/shop"),className:"bg-yellow-500 hover:bg-yellow-600 text-slate-900",children:"العودة للمتجر"})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white",children:[(0,a.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"})]}),(0,a.jsx)(o.j,{onMenuOpen:()=>w(!0)}),(0,a.jsx)(d.p,{isOpen:N,onClose:()=>w(!1)}),(0,a.jsxs)("main",{className:"container mx-auto px-4 py-8 relative z-10",children:[(0,a.jsxs)(c.$,{variant:"ghost",onClick:()=>s.back(),className:"mb-6 text-slate-300 hover:text-white hover:bg-slate-800/50",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 ml-2"}),"العودة"]}),(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm overflow-hidden",children:(0,a.jsx)(i.Wu,{className:"p-0",children:(0,a.jsxs)("div",{className:"relative aspect-square",children:[k.previewImage?(0,a.jsx)("img",{src:k.previewImage,alt:k.name,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-24 w-24 text-slate-400"})}),(0,a.jsxs)("div",{className:"absolute top-4 right-4 space-y-2",children:["digital"===k.productType&&(0,a.jsxs)(n.E,{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold animate-pulse",children:[(0,a.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"منتج رقمي"]}),(0,a.jsx)(n.E,{className:`${"instant"===k.processingType?"bg-green-500/20 text-green-400 border-green-500/30":"bg-blue-500/20 text-blue-400 border-blue-500/30"}`,children:"instant"===k.processingType?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"h-3 w-3 mr-1"}),k.estimatedTime]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-3 w-3 mr-1"}),k.estimatedTime]})}),"digital"===k.productType&&(0,a.jsxs)(n.E,{className:"bg-purple-500/20 text-purple-400 border-purple-500/30",children:[(0,a.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"منتج رقمي"]})]})]})})}),(0,a.jsx)(i.Zp,{className:"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm",children:(0,a.jsxs)(i.Wu,{className:"p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-green-400"}),"مميزات المنتج"]}),(0,a.jsx)("div",{className:"space-y-3",children:k.features&&k.features.length>0?k.features.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-400 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-300 text-sm",children:e})]},s)):(0,a.jsxs)(a.Fragment,{children:["digital"===k.productType?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-400 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-300 text-sm",children:"\uD83D\uDE80 تسليم فوري للمحتوى الرقمي"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-400 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-300 text-sm",children:"\uD83D\uDD12 محتوى محمي ومشفر"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-400 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-300 text-sm",children:"⚡ شحن سريع وآمن"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-400 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-300 text-sm",children:"\uD83D\uDEE1️ ضمان الجودة"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-green-400 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-300 text-sm",children:"\uD83D\uDCAC دعم فني متخصص"})]})]})})]})})]}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(A,{template:k,onSubmit:E,currency:"USD",showPricing:!0})})]})})]}),(0,a.jsx)(h.v,{activeTab:t,onTabChange:z}),(0,a.jsx)(x.G,{activeTab:t,onTabChange:z})]})}},99891:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,228,935,50,176,540],()=>t(62228));module.exports=a})();