"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/ProductDashboard.tsx":
/*!***********************************************!*\
  !*** ./components/admin/ProductDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductDashboard: () => (/* binding */ ProductDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Edit,FolderOpen,Package,Plus,Search,Star,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_services_categoryService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/categoryService */ \"(app-pages-browser)/./lib/services/categoryService.ts\");\n/* harmony import */ var _SimpleProductForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SimpleProductForm */ \"(app-pages-browser)/./components/admin/SimpleProductForm.tsx\");\n/* harmony import */ var _CategoryManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./CategoryManager */ \"(app-pages-browser)/./components/admin/CategoryManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductDashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductDashboard() {\n    _s();\n    // ## TODO: Add user authentication check\n    // ## TODO: Implement real-time updates with Supabase subscriptions\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sub-navigation state\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"products\");\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load products and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            (0,_lib_services_categoryService__WEBPACK_IMPORTED_MODULE_9__.initializeCategories)() // Initialize default categories\n            ;\n            loadData();\n        }\n    }[\"ProductDashboard.useEffect\"], []);\n    // Apply filters when search or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDashboard.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"ProductDashboard.useEffect\"], [\n        products,\n        searchQuery,\n        filters\n    ]);\n    /**\n   * ## TODO: Replace with Supabase real-time subscription\n   * Load products and statistics\n   */ const loadData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [productsData, statsData] = await Promise.all([\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_8__.getProducts)().catch((err)=>{\n                    console.error(\"Error loading products:\", err);\n                    return [];\n                }),\n                (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_8__.getProductStats)().catch((err)=>{\n                    console.error(\"Error loading stats:\", err);\n                    return {\n                        totalProducts: 0,\n                        activeProducts: 0,\n                        digitalProducts: 0,\n                        physicalProducts: 0,\n                        totalPackages: 0,\n                        totalOrders: 0,\n                        popularCategories: []\n                    };\n                })\n            ]);\n            setProducts(Array.isArray(productsData) ? productsData : []);\n            setStats(statsData);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            // Set fallback data\n            setProducts([]);\n            setStats({\n                totalProducts: 0,\n                activeProducts: 0,\n                digitalProducts: 0,\n                physicalProducts: 0,\n                totalPackages: 0,\n                totalOrders: 0,\n                popularCategories: []\n            });\n        // ## TODO: Show error toast notification\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Apply search and filters to products\n   */ const applyFilters = ()=>{\n        // Ensure products is a valid array\n        const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.name && p.category) : [];\n        let filtered = [\n            ...validProducts\n        ];\n        // Apply search\n        if (searchQuery.trim()) {\n            const query = searchQuery.toLowerCase();\n            filtered = filtered.filter((product)=>{\n                var _product_name, _product_description, _product_category;\n                return ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.toLowerCase().includes(query)) || ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(query)) || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.toLowerCase().includes(query));\n            });\n        }\n        // Apply filters\n        if (filters.category) {\n            filtered = filtered.filter((p)=>p.category === filters.category);\n        }\n        if (filters.productType) {\n            filtered = filtered.filter((p)=>p.productType === filters.productType);\n        }\n        if (filters.processingType) {\n            filtered = filtered.filter((p)=>p.processingType === filters.processingType);\n        }\n        if (filters.isActive !== undefined) {\n            filtered = filtered.filter((p)=>p.isActive === filters.isActive);\n        }\n        setFilteredProducts(filtered);\n    };\n    /**\n   * Handle product creation\n   */ const handleProductCreate = async (product)=>{\n        setProducts((prev)=>[\n                ...prev,\n                product\n            ]);\n        setIsCreateDialogOpen(false);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product update\n   */ const handleProductUpdate = async (product)=>{\n        setProducts((prev)=>prev.map((p)=>p.id === product.id ? product : p));\n        setIsEditDialogOpen(false);\n        setSelectedProduct(null);\n        await loadData() // Refresh stats\n        ;\n    };\n    /**\n   * Handle product deletion\n   */ const handleProductDelete = async (productId)=>{\n        if (!confirm(\"هل أنت متأكد من حذف هذا المنتج؟\")) return;\n        try {\n            await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_8__.deleteProduct)(productId);\n            setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n            await loadData() // Refresh stats\n            ;\n        } catch (error) {\n            console.error(\"Error deleting product:\", error);\n        // ## TODO: Show error toast notification\n        }\n    };\n    /**\n   * Get unique categories for filter dropdown\n   */ const getCategories = ()=>{\n        const categories = [\n            ...new Set(products.map((p)=>p.category))\n        ];\n        return categories.sort();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 p-4 lg:p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl lg:text-3xl font-bold text-white\",\n                                children: \"إدارة المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400 mt-1\",\n                                children: \"إنشاء وتعديل وإدارة منتجات المتجر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>setIsCreateDialogOpen(true),\n                        className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 min-h-[44px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            \"إضافة منتج جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            isCreateDialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    onSave: handleProductCreate,\n                    onCancel: ()=>setIsCreateDialogOpen(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 bg-slate-800/50 rounded-lg p-1 border border-slate-700/50 overflow-x-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"products\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"products\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"منتجات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(\"categories\"),\n                        className: \"flex items-center gap-2 px-3 sm:px-4 py-2 rounded-md text-sm font-medium transition-all min-h-[44px] whitespace-nowrap \".concat(activeTab === \"categories\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-slate-400 hover:text-white hover:bg-slate-700/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden xs:inline\",\n                                children: \"الفئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"xs:hidden\",\n                                children: \"فئات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            activeTab === \"categories\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CategoryManager__WEBPACK_IMPORTED_MODULE_11__.CategoryManager, {\n                onCategoryChange: loadData\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي المنتجات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: stats.totalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات النشطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: stats.activeProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"المنتجات الرقمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-400\",\n                                                        children: stats.digitalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-8 w-8 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: \"إجمالي الحزم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-yellow-400\",\n                                                        children: stats.totalPackages\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"البحث في المنتجات...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10 bg-slate-700 border-slate-600 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.category || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"الفئة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الفئات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            getCategories().map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 21\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: filters.productType || \"all\",\n                                                onValueChange: (value)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            productType: value === \"all\" ? undefined : value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        className: \"w-40 bg-slate-700 border-slate-600 text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        className: \"bg-slate-700 border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"جميع الأنواع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"digital\",\n                                                                children: \"رقمي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"physical\",\n                                                                children: \"مادي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"service\",\n                                                                children: \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredProducts.map((product)=>{\n                            var _product_packages;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-white text-lg mb-2\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: product.isActive ? \"default\" : \"secondary\",\n                                                                children: product.isActive ? \"نشط\" : \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: product.productType === \"digital\" ? \"border-purple-500 text-purple-400\" : product.productType === \"physical\" ? \"border-blue-500 text-blue-400\" : \"border-green-500 text-green-400\",\n                                                                children: product.productType === \"digital\" ? \"رقمي\" : product.productType === \"physical\" ? \"مادي\" : \"خدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            product.processingType === \"instant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-green-500/20 text-green-400 border-green-500/30\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"فوري\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-sm mb-3 line-clamp-2\",\n                                                children: product.description || \"لا يوجد وصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-sm text-slate-400 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"الفئة: \",\n                                                            product.category\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            ((_product_packages = product.packages) === null || _product_packages === void 0 ? void 0 : _product_packages.length) || 0,\n                                                            \" حزمة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>{\n                                                            setSelectedProduct(product);\n                                                            setIsEditDialogOpen(true);\n                                                        },\n                                                        className: \"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"تعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleProductDelete(product.id),\n                                                        className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 7\n                    }, this),\n                    filteredProducts.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-slate-800/50 border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-16 w-16 text-slate-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"لا توجد منتجات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-400 mb-4\",\n                                    children: searchQuery || Object.keys(filters).length > 0 ? \"لم يتم العثور على منتجات تطابق البحث أو الفلاتر\" : \"ابدأ بإنشاء منتجك الأول\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                !searchQuery && Object.keys(filters).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setIsCreateDialogOpen(true),\n                                    className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Edit_FolderOpen_Package_Plus_Search_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"إضافة منتج جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: isEditDialogOpen,\n                        onOpenChange: setIsEditDialogOpen,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                            className: \"bg-slate-800 border-slate-700 text-white max-w-[95vw] sm:max-w-[90vw] lg:max-w-4xl max-h-[90vh] overflow-y-auto p-0\",\n                            children: selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                product: selectedProduct,\n                                onSave: handleProductUpdate,\n                                onCancel: ()=>{\n                                    setIsEditDialogOpen(false);\n                                    setSelectedProduct(null);\n                                },\n                                isEditing: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\ProductDashboard.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDashboard, \"w8YCEC8amsgFyatH9jatbW86Xe8=\");\n_c = ProductDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProductDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/ProductDashboard.tsx\n"));

/***/ })

});