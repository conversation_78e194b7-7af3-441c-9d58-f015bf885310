"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  DollarSign, 
  Plus, 
  Edit3, 
  Trash2, 
  CheckCircle, 
  AlertTriangle,
  Globe,
  TrendingUp,
  Loader2
} from "lucide-react"
import { Currency, CurrencyDisplay } from "@/lib/types"
import { CURRENCIES } from "@/lib/data/currencies"

interface CurrencyItem {
  code: string
  name: string
  symbol: string
  arabicName: string
  exchangeRate: number
  isActive: boolean
  isDefault: boolean
}

export function CurrencyManagement() {
  const [currencies, setCurrencies] = useState<CurrencyItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newCurrency, setNewCurrency] = useState({
    symbol: "",
    arabicName: "",
    exchangeRate: 1
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // Load existing currencies
  useEffect(() => {
    loadCurrencies()
  }, [])

  const loadCurrencies = async () => {
    setIsLoading(true)
    try {
      // ## TODO: Replace with Supabase query
      // const { data: currencies } = await supabase.from('currencies').select('*').order('sort_order')
      
      // Mock data based on existing currencies
      const mockCurrencies: CurrencyItem[] = [
        {
          code: "USD",
          name: "US Dollar",
          symbol: "$",
          arabicName: "الدولار الأمريكي",
          exchangeRate: 1, // Base currency
          isActive: true,
          isDefault: true
        },
        {
          code: "SDG",
          name: "Sudanese Pound",
          symbol: "ج.س",
          arabicName: "الجنيه السوداني",
          exchangeRate: 450.00,
          isActive: true,
          isDefault: false
        },
        {
          code: "EGP",
          name: "Egyptian Pound", 
          symbol: "ج.م",
          arabicName: "الجنيه المصري",
          exchangeRate: 30.80,
          isActive: true,
          isDefault: false
        }
      ]
      
      setCurrencies(mockCurrencies)
    } catch (error) {
      console.error('Error loading currencies:', error)
      setMessage({ type: 'error', text: 'فشل في تحميل العملات' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddCurrency = async () => {
    if (!newCurrency.arabicName || !newCurrency.symbol) {
      setMessage({ type: 'error', text: 'يرجى ملء جميع الحقول المطلوبة' })
      return
    }

    if (newCurrency.exchangeRate <= 0) {
      setMessage({ type: 'error', text: 'سعر الصرف يجب أن يكون أكبر من صفر' })
      return
    }

    setIsSubmitting(true)
    try {
      // Generate currency code from arabicName (or symbol if needed)
      const currencyCode = newCurrency.symbol.substring(0, 3).toUpperCase()
      
      // ## TODO: Database Integration
      // 1. Insert into currencies table:
      // const { data: currency } = await supabase.from('currencies').insert({
      //   code: currencyCode,
      //   name: newCurrency.name,
      //   symbol: newCurrency.symbol,
      //   arabic_name: newCurrency.arabicName,
      //   decimal_places: 2,
      //   is_rtl: true, // Assume RTL for Arabic currencies
      //   is_active: true,
      //   sort_order: currencies.length + 1
      // }).select().single()

      // 2. Insert exchange rate:
      // await supabase.from('exchange_rates').insert({
      //   from_currency_code: 'USD',
      //   to_currency_code: currencyCode,
      //   rate: newCurrency.exchangeRate,
      //   effective_date: new Date(),
      //   is_active: true,
      //   source: 'manual'
      // })

      // 3. Insert reverse exchange rate:
      // await supabase.from('exchange_rates').insert({
      //   from_currency_code: currencyCode,
      //   to_currency_code: 'USD',
      //   rate: 1 / newCurrency.exchangeRate,
      //   effective_date: new Date(),
      //   is_active: true,
      //   source: 'manual'
      // })

      // 4. Update client currency settings to include new currency:
      // const { data: settings } = await supabase.from('client_currency_settings')
      //   .select('enabled_currencies').single()
      // await supabase.from('client_currency_settings').update({
      //   enabled_currencies: [...settings.enabled_currencies, currencyCode]
      // }).eq('client_id', null)

      // Mock implementation for demo
      const newCurrencyItem: CurrencyItem = {
        code: currencyCode,
        name: newCurrency.arabicName,
        symbol: newCurrency.symbol,
        arabicName: newCurrency.arabicName,
        exchangeRate: newCurrency.exchangeRate,
        isActive: true,
        isDefault: false
      }

      setCurrencies(prev => [...prev, newCurrencyItem])
      setNewCurrency({ symbol: "", arabicName: "", exchangeRate: 1 })
      setShowAddForm(false)
      setMessage({ type: 'success', text: `تم إضافة العملة ${newCurrency.arabicName} بنجاح` })

      // ## Integration Points After Adding Currency:
      // 1. Product System: New currency will appear in product pricing selectors
      // 2. Wallet System: Users will get new wallet balance section for this currency
      // 3. Exchange Rates: All price calculations will use the new exchange rate
      // 4. Admin Dashboard: Currency will appear in all admin currency selectors

    } catch (error) {
      console.error('Error adding currency:', error)
      setMessage({ type: 'error', text: 'فشل في إضافة العملة' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const toggleCurrencyStatus = async (currencyCode: string) => {
    // ## TODO: Database update
    // await supabase.from('currencies')
    //   .update({ is_active: !currentStatus })
    //   .eq('code', currencyCode)

    setCurrencies(prev => 
      prev.map(currency => 
        currency.code === currencyCode 
          ? { ...currency, isActive: !currency.isActive }
          : currency
      )
    )
    setMessage({ type: 'success', text: 'تم تحديث حالة العملة' })
  }

  const updateExchangeRate = async (currencyCode: string, newRate: number) => {
    // ## TODO: Database update
    // await supabase.from('exchange_rates')
    //   .update({ 
    //     rate: newRate,
    //     effective_date: new Date(),
    //     updated_at: new Date()
    //   })
    //   .eq('from_currency_code', 'USD')
    //   .eq('to_currency_code', currencyCode)

    setCurrencies(prev =>
      prev.map(currency =>
        currency.code === currencyCode
          ? { ...currency, exchangeRate: newRate }
          : currency
      )
    )
    setMessage({ type: 'success', text: 'تم تحديث سعر الصرف' })
  }

  if (isLoading) {
    return (
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
          <span className="mr-2 text-slate-400">جاري تحميل العملات...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <DollarSign className="h-6 w-6 text-yellow-400" />
            إدارة العملات
          </h2>
          <p className="text-slate-400 mt-1">إضافة وإدارة العملات المدعومة في النظام</p>
        </div>
        <Button
          onClick={() => setShowAddForm(!showAddForm)}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Plus className="h-4 w-4 ml-2" />
          إضافة عملة جديدة
        </Button>
      </div>

      {/* Messages */}
      {message && (
        <Alert className={message.type === 'success' ? 'bg-green-900/20 border-green-700/50' : 'bg-red-900/20 border-red-700/50'}>
          {message.type === 'success' ? 
            <CheckCircle className="h-4 w-4 text-green-400" /> : 
            <AlertTriangle className="h-4 w-4 text-red-400" />
          }
          <AlertDescription className={message.type === 'success' ? 'text-green-100' : 'text-red-100'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Add Currency Form */}
      {showAddForm && (
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white">إضافة عملة جديدة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-slate-300">اسم العملة</Label>
                <Input
                  value={newCurrency.name}
                  onChange={(e) => setNewCurrency(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="مثال: الريال السعودي"
                  className="bg-slate-700/50 border-slate-600 text-white"
                  dir="rtl"
                />
              </div>
              <div>
                <Label className="text-slate-300">رمز العملة</Label>
                <Input
                  value={newCurrency.symbol}
                  onChange={(e) => setNewCurrency(prev => ({ ...prev, symbol: e.target.value }))}
                  placeholder="مثال: ر.س"
                  className="bg-slate-700/50 border-slate-600 text-white"
                />
              </div>
              <div>
                <Label className="text-slate-300">سعر الصرف (1 USD = ? {newCurrency.arabicName || 'العملة الجديدة'})</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={newCurrency.exchangeRate}
                  onChange={(e) => setNewCurrency(prev => ({ ...prev, exchangeRate: parseFloat(e.target.value) || 1 }))}
                  placeholder="مثال: 3.75"
                  className="bg-slate-700/50 border-slate-600 text-white"
                />
              </div>
            </div>
            <div className="flex gap-2 pt-4">
              <Button
                onClick={handleAddCurrency}
                disabled={isSubmitting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin ml-2" />
                    جاري الإضافة...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 ml-2" />
                    إضافة العملة
                  </>
                )}
              </Button>
              <Button
                onClick={() => setShowAddForm(false)}
                variant="outline"
                className="border-slate-600 text-slate-300"
              >
                إلغاء
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Currencies */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Globe className="h-5 w-5 text-blue-400" />
            العملات الحالية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {currencies.map((currency) => (
              <div
                key={currency.code}
                className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600/30"
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-white">{currency.symbol}</span>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-white">{currency.code}</span>
                        {currency.isDefault && (
                          <Badge className="bg-yellow-600 text-white text-xs">العملة الأساسية</Badge>
                        )}
                        <Badge className={currency.isActive ? 'bg-green-600' : 'bg-red-600'}>
                          {currency.isActive ? 'نشطة' : 'معطلة'}
                        </Badge>
                      </div>
                      <div className="text-sm text-slate-400">
                        {currency.arabicName} • {currency.name}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  {!currency.isDefault && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-slate-300">
                        1 USD = {currency.exchangeRate.toLocaleString()} {currency.symbol}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex gap-2">
                    {!currency.isDefault && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => toggleCurrencyStatus(currency.code)}
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          {currency.isActive ? 'تعطيل' : 'تفعيل'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Edit3 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Integration Info */}
      <Card className="bg-blue-900/20 backdrop-blur-xl border-blue-700/50">
        <CardHeader>
          <CardTitle className="text-blue-100 text-lg">نقاط التكامل مع النظام</CardTitle>
        </CardHeader>
        <CardContent className="text-blue-200 text-sm space-y-2">
          <div>• <strong>نظام المنتجات:</strong> العملات الجديدة ستظهر في حقول تسعير المنتجات</div>
          <div>• <strong>نظام المحافظ:</strong> سيتم إنشاء أقسام رصيد منفصلة للمستخدمين لكل عملة جديدة</div>
          <div>• <strong>أسعار الصرف:</strong> الأسعار الجديدة ستؤثر على حسابات الأسعار في جميع أنحاء النظام</div>
          <div>• <strong>لوحة الإدارة:</strong> العملات ستظهر في جميع محددات العملات في لوحة الإدارة</div>
        </CardContent>
      </Card>
    </div>
  )
}
