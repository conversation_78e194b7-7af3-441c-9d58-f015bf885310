(()=>{var e={};e.id=47,e.ids=[47],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6160:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>_,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>v});var t={};s.r(t),s.d(t,{GET:()=>d,POST:()=>u});var a=s(96559),c=s(48088),i=s(37719),n=s(32190);let o=[{id:"1",code:"USD",name:"الدولار الأمريكي",symbol:"$",decimalPlaces:2,isRTL:!1,isActive:!0,sortOrder:1,createdAt:new Date,updatedAt:new Date},{id:"2",code:"SDG",name:"الجنيه السوداني",symbol:"ج.س",decimalPlaces:2,isRTL:!0,isActive:!0,sortOrder:2,createdAt:new Date,updatedAt:new Date},{id:"3",code:"EGP",name:"الجنيه المصري",symbol:"ج.م",decimalPlaces:2,isRTL:!0,isActive:!0,sortOrder:3,createdAt:new Date,updatedAt:new Date}];async function d(e){try{let{searchParams:r}=new URL(e.url),s="true"===r.get("active"),t="true"===r.get("enabled"),a=[...o];s&&(a=a.filter(e=>e.isActive));let c=a;t&&(["USD","SDG","EGP"].select("enabled_currencies").is("client_id",null).single(),clientSettings?.enabled_currencies&&(c=a.filter(e=>clientSettings.enabled_currencies.includes(e.code))));let i=c.map(e=>({id:e.id,code:e.code,name:e.name,symbol:e.symbol,arabicName:e.arabic_name,decimalPlaces:e.decimal_places,isRTL:e.is_rtl,isActive:e.is_active,sortOrder:e.sort_order,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at)}));return n.NextResponse.json({success:!0,currencies:i})}catch(e){return console.error("Unexpected error in GET /api/currencies:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function u(e){try{let r;let s=await e.json();if(!s.action)return n.NextResponse.json({error:"Action is required"},{status:400});switch(s.action){case"create":r=await l(s.currency);break;case"update":r=await m(s.currency);break;case"delete":r=await p(s.currencyCode);break;case"activate":r=await y(s.currencyCode,!0);break;case"deactivate":r=await y(s.currencyCode,!1);break;default:return n.NextResponse.json({error:"Invalid action"},{status:400})}return n.NextResponse.json(r,{status:r.success?200:400})}catch(e){return console.error("Unexpected error in POST /api/currencies:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{if(!e.code||!e.name||!e.symbol)return{success:!1,message:"Code, name, and symbol are required",error:"Missing required fields"};let{data:r}=await supabase.from("currencies").select("id").eq("code",e.code).single();if(r)return{success:!1,message:"Currency already exists",error:"Duplicate currency code"};let{data:s,error:t}=await supabase.from("currencies").insert({code:e.code,name:e.name,symbol:e.symbol,arabic_name:e.arabicName,decimal_places:e.decimalPlaces||2,is_rtl:e.isRTL||!1,is_active:!1!==e.isActive,sort_order:e.sortOrder||0}).select().single();if(t)return console.error("Error creating currency:",t),{success:!1,message:"Failed to create currency",error:t.message};return{success:!0,message:"Currency created successfully",currency:{id:s.id,code:s.code,name:s.name,symbol:s.symbol,arabicName:s.arabic_name,decimalPlaces:s.decimal_places,isRTL:s.is_rtl,isActive:s.is_active,sortOrder:s.sort_order,createdAt:new Date(s.created_at),updatedAt:new Date(s.updated_at)}}}catch(e){return console.error("Error in createCurrency:",e),{success:!1,message:"Failed to create currency",error:"Internal server error"}}}async function m(e){try{if(!e.id&&!e.code)return{success:!1,message:"Currency ID or code is required for update",error:"Missing identifier"};let r={};e.name&&(r.name=e.name),e.symbol&&(r.symbol=e.symbol),e.arabicName&&(r.arabic_name=e.arabicName),void 0!==e.decimalPlaces&&(r.decimal_places=e.decimalPlaces),void 0!==e.isRTL&&(r.is_rtl=e.isRTL),void 0!==e.isActive&&(r.is_active=e.isActive),void 0!==e.sortOrder&&(r.sort_order=e.sortOrder);let s=supabase.from("currencies").update(r);s=e.id?s.eq("id",e.id):s.eq("code",e.code);let{data:t,error:a}=await s.select().single();if(a)return console.error("Error updating currency:",a),{success:!1,message:"Failed to update currency",error:a.message};return{success:!0,message:"Currency updated successfully",currency:{id:t.id,code:t.code,name:t.name,symbol:t.symbol,arabicName:t.arabic_name,decimalPlaces:t.decimal_places,isRTL:t.is_rtl,isActive:t.is_active,sortOrder:t.sort_order,createdAt:new Date(t.created_at),updatedAt:new Date(t.updated_at)}}}catch(e){return console.error("Error in updateCurrency:",e),{success:!1,message:"Failed to update currency",error:"Internal server error"}}}async function p(e){try{let{data:r}=await supabase.from("user_wallets").select("id").eq("currency_code",e).limit(1);if(r&&r.length>0)return{success:!1,message:"Cannot delete currency that is in use",error:"Currency in use"};let{error:s}=await supabase.from("currencies").delete().eq("code",e);if(s)return console.error("Error deleting currency:",s),{success:!1,message:"Failed to delete currency",error:s.message};return{success:!0,message:"Currency deleted successfully"}}catch(e){return console.error("Error in deleteCurrency:",e),{success:!1,message:"Failed to delete currency",error:"Internal server error"}}}async function y(e,r){try{let{data:s,error:t}=await supabase.from("currencies").update({is_active:r}).eq("code",e).select().single();if(t)return console.error("Error toggling currency status:",t),{success:!1,message:"Failed to update currency status",error:t.message};return{success:!0,message:`Currency ${r?"activated":"deactivated"} successfully`,currency:{id:s.id,code:s.code,name:s.name,symbol:s.symbol,arabicName:s.arabic_name,decimalPlaces:s.decimal_places,isRTL:s.is_rtl,isActive:s.is_active,sortOrder:s.sort_order,createdAt:new Date(s.created_at),updatedAt:new Date(s.updated_at)}}}catch(e){return console.error("Error in toggleCurrencyStatus:",e),{success:!1,message:"Failed to update currency status",error:"Internal server error"}}}let g=new a.AppRouteRouteModule({definition:{kind:c.RouteKind.APP_ROUTE,page:"/api/currencies/route",pathname:"/api/currencies",filename:"route",bundlePath:"app/api/currencies/route"},resolvedPagePath:"D:\\VS-projects\\try\\alraya-store\\app\\api\\currencies\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:b,workUnitAsyncStorage:v,serverHooks:_}=g;function f(){return(0,i.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:v})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580],()=>s(6160));module.exports=t})();