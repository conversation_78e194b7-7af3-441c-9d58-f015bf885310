/**
 * ## Chat Utility Functions
 * Helper functions for chat operations, security, and data processing
 * Includes message validation, sanitization, and formatting
 */

import { ChatMessage, ChatRoom } from '@/lib/types'

/**
 * ## Message Validation and Sanitization
 */

/**
 * Validate message content before sending
 */
export function validateMessage(message: string): {
  isValid: boolean
  error?: string
  sanitized: string
} {
  // Remove extra whitespace
  const sanitized = message.trim()

  // Check if empty
  if (!sanitized) {
    return {
      isValid: false,
      error: 'الرسالة لا يمكن أن تكون فارغة',
      sanitized: ''
    }
  }

  // Check length limits
  if (sanitized.length > 1000) {
    return {
      isValid: false,
      error: 'الرسالة طويلة جداً (الحد الأقصى 1000 حرف)',
      sanitized
    }
  }

  // Check for spam patterns
  if (isSpamMessage(sanitized)) {
    return {
      isValid: false,
      error: 'تم اكتشاف محتوى غير مرغوب فيه',
      sanitized
    }
  }

  // Basic HTML sanitization (remove script tags, etc.)
  const htmlSanitized = sanitizeHtml(sanitized)

  return {
    isValid: true,
    sanitized: htmlSanitized
  }
}

/**
 * Detect spam messages
 */
function isSpamMessage(message: string): boolean {
  const spamPatterns = [
    /(.)\1{10,}/, // Repeated characters
    /https?:\/\/[^\s]+/gi, // URLs (might want to allow in some cases)
    /\b(spam|scam|free money|click here)\b/gi, // Common spam words
  ]

  return spamPatterns.some(pattern => pattern.test(message))
}

/**
 * Basic HTML sanitization
 */
function sanitizeHtml(html: string): string {
  // Remove script tags and their content
  let sanitized = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  
  // Remove dangerous attributes
  sanitized = sanitized.replace(/on\w+="[^"]*"/gi, '')
  sanitized = sanitized.replace(/javascript:/gi, '')
  
  return sanitized
}

/**
 * ## Message Formatting and Display
 */

/**
 * Format message for display (handle line breaks, mentions, etc.)
 */
export function formatMessageForDisplay(message: string): string {
  // Convert line breaks to <br> tags
  let formatted = message.replace(/\n/g, '<br>')
  
  // Handle mentions (if implemented)
  formatted = formatted.replace(/@(\w+)/g, '<span class="text-blue-400">@$1</span>')
  
  // Handle order references
  formatted = formatted.replace(/#(PRD-[\w-]+)/g, '<span class="text-orange-400">#$1</span>')
  
  return formatted
}

/**
 * Extract order IDs from message
 */
export function extractOrderIds(message: string): string[] {
  const orderPattern = /#(PRD-[\w-]+)/g
  const matches = message.match(orderPattern)
  return matches ? matches.map(match => match.substring(1)) : []
}

/**
 * ## Chat Room Management
 */

/**
 * Sort chat rooms by priority (unread, last message, etc.)
 */
export function sortChatRooms(rooms: ChatRoom[]): ChatRoom[] {
  return rooms.sort((a, b) => {
    // Unread messages first
    if (a.unreadCount > 0 && b.unreadCount === 0) return -1
    if (b.unreadCount > 0 && a.unreadCount === 0) return 1
    
    // Online users first
    if (a.isOnline && !b.isOnline) return -1
    if (b.isOnline && !a.isOnline) return 1
    
    // Most recent activity
    const aTime = a.lastMessage?.createdAt || a.lastSeen
    const bTime = b.lastMessage?.createdAt || b.lastSeen
    return new Date(bTime).getTime() - new Date(aTime).getTime()
  })
}

/**
 * Filter chat rooms based on search query
 */
export function filterChatRooms(rooms: ChatRoom[], query: string): ChatRoom[] {
  if (!query.trim()) return rooms
  
  const searchTerm = query.toLowerCase().trim()
  
  return rooms.filter(room => 
    room.customerName.toLowerCase().includes(searchTerm) ||
    room.customerEmail.toLowerCase().includes(searchTerm) ||
    room.lastMessage?.message.toLowerCase().includes(searchTerm)
  )
}

/**
 * ## Message Processing
 */

/**
 * Group messages by date for display
 */
export function groupMessagesByDate(messages: ChatMessage[]): {
  date: string
  messages: ChatMessage[]
}[] {
  const groups: { [key: string]: ChatMessage[] } = {}
  
  messages.forEach(message => {
    const date = new Date(message.createdAt).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
  })
  
  return Object.entries(groups).map(([date, messages]) => ({
    date,
    messages: messages.sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    )
  }))
}

/**
 * Get unread message count for user
 */
export function getUnreadCount(messages: ChatMessage[], userType: 'customer' | 'admin'): number {
  return messages.filter(msg => 
    !msg.isRead && msg.senderType !== userType
  ).length
}

/**
 * ## Security and Rate Limiting
 */

/**
 * Rate limiting for message sending
 */
class MessageRateLimit {
  private attempts: Map<string, number[]> = new Map()
  private readonly maxMessages = 10 // Max messages per minute
  private readonly timeWindow = 60 * 1000 // 1 minute

  canSendMessage(userId: string): boolean {
    const now = Date.now()
    const userAttempts = this.attempts.get(userId) || []
    
    // Remove old attempts outside time window
    const recentAttempts = userAttempts.filter(time => now - time < this.timeWindow)
    
    // Check if under limit
    if (recentAttempts.length >= this.maxMessages) {
      return false
    }
    
    // Add current attempt
    recentAttempts.push(now)
    this.attempts.set(userId, recentAttempts)
    
    return true
  }

  getRemainingTime(userId: string): number {
    const userAttempts = this.attempts.get(userId) || []
    if (userAttempts.length < this.maxMessages) return 0
    
    const oldestAttempt = Math.min(...userAttempts)
    const timeUntilReset = this.timeWindow - (Date.now() - oldestAttempt)
    
    return Math.max(0, timeUntilReset)
  }
}

export const messageRateLimit = new MessageRateLimit()

/**
 * ## File Upload Validation
 */
export function validateFileUpload(file: File): {
  isValid: boolean
  error?: string
} {
  const maxSize = 5 * 1024 * 1024 // 5MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'حجم الملف كبير جداً (الحد الأقصى 5 ميجابايت)'
    }
  }
  
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'نوع الملف غير مدعوم (الأنواع المدعومة: JPEG, PNG, GIF, WebP)'
    }
  }
  
  return { isValid: true }
}

/**
 * ## Chat Analytics
 */

/**
 * Calculate chat statistics
 */
export function calculateChatStats(messages: ChatMessage[], rooms: ChatRoom[]) {
  const totalMessages = messages.length
  const totalCustomers = rooms.length
  const activeChats = rooms.filter(room => room.unreadCount > 0).length
  const onlineCustomers = rooms.filter(room => room.isOnline).length
  
  // Average response time (mock calculation)
  const avgResponseTime = messages.length > 0 ? 
    Math.random() * 30 + 5 : 0 // 5-35 minutes
  
  return {
    totalMessages,
    totalCustomers,
    activeChats,
    onlineCustomers,
    avgResponseTime: Math.round(avgResponseTime)
  }
}

/**
 * ## Typing Indicator Management
 */
export class TypingIndicator {
  private typingUsers: Map<string, NodeJS.Timeout> = new Map()
  private callbacks: Set<(users: string[]) => void> = new Set()

  startTyping(userId: string) {
    // Clear existing timeout
    const existingTimeout = this.typingUsers.get(userId)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      this.stopTyping(userId)
    }, 3000) // Stop typing after 3 seconds of inactivity

    this.typingUsers.set(userId, timeout)
    this.notifyCallbacks()
  }

  stopTyping(userId: string) {
    const timeout = this.typingUsers.get(userId)
    if (timeout) {
      clearTimeout(timeout)
      this.typingUsers.delete(userId)
      this.notifyCallbacks()
    }
  }

  getTypingUsers(): string[] {
    return Array.from(this.typingUsers.keys())
  }

  subscribe(callback: (users: string[]) => void) {
    this.callbacks.add(callback)
    return () => this.callbacks.delete(callback)
  }

  private notifyCallbacks() {
    const users = this.getTypingUsers()
    this.callbacks.forEach(callback => callback(users))
  }
}

export const typingIndicator = new TypingIndicator()
