"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { getCheckoutOrderById } from "@/lib/utils/localStorage"
import { formatCurrency } from "@/lib/data/currencies"
import { CheckoutOrder } from "@/lib/types"
import { cn } from "@/lib/utils"
import { 
  CheckCircle, 
  Clock, 
  Copy, 
  Calendar, 
  CreditCard, 
  Building2,
  ShoppingBag,
  ArrowLeft
} from "lucide-react"
import dayjs from "dayjs"

export function CheckoutSuccessPage() {
  const [activeTab, setActiveTab] = useState("checkout")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [order, setOrder] = useState<CheckoutOrder | null>(null)
  const [copied, setCopied] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const orderId = searchParams.get("orderId")
    if (orderId) {
      const foundOrder = getCheckoutOrderById(orderId)
      setOrder(foundOrder)
    }
  }, [searchParams])

  const handleCopyOrderId = async () => {
    if (!order) return
    
    try {
      await navigator.clipboard.writeText(order.id)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Failed to copy order ID:", error)
    }
  }

  const handleContinueShopping = () => {
    router.push("/shop")
  }

  const handleGoToWallet = () => {
    router.push("/wallet")
  }

  // Navigation handler for navbar
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
      router.refresh()
    } else {
      setActiveTab(tab)
    }
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
        <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
        <NewsTicket />
        <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />
        
        <main className="relative z-10 container mx-auto px-4 py-8 max-w-4xl pt-32 pb-32">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white mb-4">
              طلب غير موجود
            </h1>
            <p className="text-slate-400 mb-6">
              لم يتم العثور على الطلب المطلوب
            </p>
            <Button onClick={() => router.push("/checkout")}>
              العودة إلى الشحن
            </Button>
          </div>
        </main>

        <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
        <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-green-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-yellow-500/10 rounded-full blur-3xl" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-4xl pt-32 pb-32">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="p-4 bg-gradient-to-r from-green-400 to-green-500 rounded-full shadow-lg">
              <CheckCircle className="h-12 w-12 text-white" />
            </div>
          </div>

          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-green-400 to-green-500 bg-clip-text text-transparent mb-4">
            طلبك تحت المراجعة
          </h1>
          <p className="text-slate-300 text-lg">
            تم استلام طلب شحن المحفظة بنجاح
          </p>
        </div>

        {/* Order Details */}
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
          <CardHeader>
            <CardTitle className="text-2xl text-white text-center">
              تفاصيل الطلب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Order ID */}
            <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
              <div>
                <p className="text-slate-400 text-sm">رقم الطلب</p>
                <p className="text-white font-mono text-lg">{order.id}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyOrderId}
                className={cn(
                  "transition-colors duration-300",
                  copied ? "text-green-400" : "text-slate-400 hover:text-white"
                )}
              >
                {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>

            {/* Date */}
            <div className="flex items-center gap-3 p-4 bg-slate-700/50 rounded-lg">
              <Calendar className="h-5 w-5 text-yellow-400" />
              <div>
                <p className="text-slate-400 text-sm">تاريخ الطلب</p>
                <p className="text-white font-medium">
                  {dayjs(order.createdAt).format("DD/MM/YYYY - HH:mm")}
                </p>
              </div>
            </div>

            {/* Amount */}
            <div className="flex items-center gap-3 p-4 bg-slate-700/50 rounded-lg">
              <CreditCard className="h-5 w-5 text-yellow-400" />
              <div>
                <p className="text-slate-400 text-sm">مبلغ الشحن</p>
                <p className="text-white font-bold text-xl">
                  {formatCurrency(order.amount, order.currency)}
                </p>
              </div>
            </div>

            {/* Bank */}
            <div className="flex items-center gap-3 p-4 bg-slate-700/50 rounded-lg">
              <Building2 className="h-5 w-5 text-yellow-400" />
              <div>
                <p className="text-slate-400 text-sm">البنك المحول إليه</p>
                <p className="text-white font-medium">{order.selectedBank.name}</p>
                <p className="text-slate-400 text-sm font-mono">
                  {order.selectedBank.accountNumber}
                </p>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center gap-3 p-4 bg-yellow-400/10 border border-yellow-400/20 rounded-lg">
              <Clock className="h-5 w-5 text-yellow-400" />
              <div>
                <p className="text-yellow-400 text-sm">حالة الطلب</p>
                <p className="text-yellow-400 font-medium">قيد المراجعة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="bg-slate-800/30 backdrop-blur-xl border-slate-700/30">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-yellow-400 mb-4">
              الخطوات التالية
            </h3>
            <div className="space-y-3 text-slate-300">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                <p>سيتم مراجعة طلبك والتحقق من التحويل خلال 24 ساعة</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                <p>ستصلك رسالة تأكيد عند إتمام عملية الشحن</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                <p>يمكنك متابعة حالة طلبك من خلال صفحة المحفظة</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                <p>احتفظ برقم الطلب للمراجعة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={handleGoToWallet}
            className="px-8 py-3 text-lg font-semibold bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600 hover:scale-105 shadow-lg hover:shadow-yellow-400/25 transition-all duration-300"
          >
            عرض المحفظة
            <ArrowLeft className="h-5 w-5 mr-2" />
          </Button>
          
          <Button
            onClick={handleContinueShopping}
            variant="outline"
            className="px-8 py-3 text-lg font-semibold border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"
          >
            <ShoppingBag className="h-5 w-5 ml-2" />
            واصل التسوق
          </Button>
        </div>
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
