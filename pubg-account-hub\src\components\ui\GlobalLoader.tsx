import React from "react";
import { cn } from "@/lib/utils";

interface GlobalLoaderProps {
  className?: string;
  containerClassName?: string;
  fullPage?: boolean;
}

const GlobalLoader = ({ className, containerClassName, fullPage = false }: GlobalLoaderProps) => {
  return (
    <div className={cn(
      "flex items-center justify-center",
      fullPage && "min-h-screen",
      containerClassName
    )}>
      <span className={cn("loader", className)}></span>
    </div>
  );
};

export default GlobalLoader; 