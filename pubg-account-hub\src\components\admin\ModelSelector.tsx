import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Sparkles, Zap, Search, Layers, Globe } from "lucide-react";
import { AVAILABLE_MODELS, getSavedModel, saveModelPreference } from "@/services/gemini";

interface ModelSelectorProps {
  onModelChange?: (modelId: string) => void;
  className?: string;
}

// Helper to get model provider icon based on name
const getModelIcon = (modelName: string) => {
  if (modelName.includes("Meta")) return Globe;
  if (modelName.includes("Google")) return Search;
  if (modelName.includes("DeepSeek")) return Layers;
  if (modelName.includes("NVIDIA")) return Zap;
  return Sparkles;
};

export const ModelSelector: React.FC<ModelSelectorProps> = ({ 
  onModelChange,
  className = ""
}) => {
  const [selectedModel, setSelectedModel] = useState<string>(getSavedModel());

  useEffect(() => {
    setSelectedModel(getSavedModel());
  }, []);

  const handleModelChange = (value: string) => {
    setSelectedModel(value);
    saveModelPreference(value);
    if (onModelChange) {
      onModelChange(value);
    }
  };

  return (
    <div className={`${className}`}>
      <div className="flex items-center mb-2">
        <Sparkles className="w-4 h-4 ml-2 text-pubg-orange" />
        <Label htmlFor="model-selector" className="text-white">
          اختر نموذج الذكاء الاصطناعي
        </Label>
      </div>
      <Select value={selectedModel} onValueChange={handleModelChange}>
        <SelectTrigger id="model-selector" className="w-full">
          <SelectValue placeholder="اختر نموذجًا" />
        </SelectTrigger>
        <SelectContent>
          {AVAILABLE_MODELS.map((model) => {
            const ModelIcon = getModelIcon(model.name);
            return (
              <SelectItem key={model.id} value={model.id}>
                <div className="flex items-center gap-2">
                  <ModelIcon className="w-4 h-4 text-pubg-orange" />
                  <span>{model.name}</span>
                </div>
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
      <p className="text-xs text-muted-foreground mt-1">
        اختر نموذجًا للحصول على أفضل نتائج في المحتوى العربي مع الرموز التعبيرية
      </p>
    </div>
  );
};

export default ModelSelector; 