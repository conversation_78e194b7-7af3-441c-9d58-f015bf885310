(()=>{var e={};e.id=279,e.ids=[279],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10403:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var a=s(60687),r=s(43210),l=s(14163),n=r.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=s(24224),c=s(96241);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(n,{ref:s,className:(0,c.cn)(o(),e),...t}));d.displayName=n.displayName},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13964:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},16023:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},17313:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},18127:(e,t,s)=>{"use strict";s.d(t,{CheckoutPage:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call CheckoutPage() from the server but CheckoutPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\VS-projects\\try\\alraya-store\\components\\pages\\CheckoutPage.tsx","CheckoutPage")},18708:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>o});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,61528)),"D:\\VS-projects\\try\\alraya-store\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"D:\\VS-projects\\try\\alraya-store\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["D:\\VS-projects\\try\\alraya-store\\app\\checkout\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24981:(e,t,s)=>{Promise.resolve().then(s.bind(s,18127))},25193:(e,t,s)=>{"use strict";s.d(t,{CheckoutPage:()=>Q});var a=s(60687),r=s(43210),l=s(16189),n=s(35438),i=s(74513),c=s(72184),o=s(74186),d=s(50197);let u={step:1,amount:0,currency:"SDG",userDetails:null,selectedBank:null,referenceNumber:"",receiptFile:null,receiptPreview:null};function x(e,t){switch(t.type){case"SET_STEP":return{...e,step:t.payload};case"SET_AMOUNT":return{...e,amount:t.payload};case"SET_CURRENCY":return{...e,currency:t.payload};case"SET_USER_DETAILS":return{...e,userDetails:t.payload};case"SET_SELECTED_BANK":return{...e,selectedBank:t.payload};case"SET_REFERENCE_NUMBER":return{...e,referenceNumber:t.payload};case"SET_RECEIPT_FILE":return{...e,receiptFile:t.payload.file,receiptPreview:t.payload.preview};case"GO_TO_NEXT_STEP":return{...e,step:Math.min(3,e.step+1)};case"GO_TO_PREVIOUS_STEP":return{...e,step:Math.max(1,e.step-1)};case"RESET_CHECKOUT":return u;default:return e}}let h=(0,r.createContext)(void 0);function m({children:e}){let[t,s]=(0,r.useReducer)(x,u),l=()=>t.amount>0&&null!==t.userDetails&&""!==t.userDetails.firstName.trim()&&""!==t.userDetails.lastName.trim()&&""!==t.userDetails.phone.trim()&&""!==t.userDetails.email.trim();return(0,a.jsx)(h.Provider,{value:{state:t,dispatch:s,setStep:e=>{s({type:"SET_STEP",payload:e})},setAmount:e=>{s({type:"SET_AMOUNT",payload:e})},setCurrency:e=>{s({type:"SET_CURRENCY",payload:e})},setUserDetails:e=>{s({type:"SET_USER_DETAILS",payload:e})},setSelectedBank:e=>{s({type:"SET_SELECTED_BANK",payload:e})},setReferenceNumber:e=>{s({type:"SET_REFERENCE_NUMBER",payload:e})},setReceiptFile:(e,t)=>{s({type:"SET_RECEIPT_FILE",payload:{file:e,preview:t}})},goToNextStep:()=>{s({type:"GO_TO_NEXT_STEP"})},goToPreviousStep:()=>{s({type:"GO_TO_PREVIOUS_STEP"})},resetCheckout:()=>{s({type:"RESET_CHECKOUT"})},canProceedToStep2:()=>t.amount>0,canProceedToStep3:l,canSubmitOrder:()=>l()&&null!==t.selectedBank&&""!==t.referenceNumber.trim()&&null!==t.receiptFile},children:e})}function p(){let e=(0,r.useContext)(h);if(void 0===e)throw Error("useCheckout must be used within a CheckoutProvider");return e}var f=s(96241),g=s(35583),b=s(10022),y=s(85778),j=s(13964);function v({currentStep:e,className:t}){let s=[{id:1,title:"التفاصيل",description:"اختر مبلغ الشحن",icon:(0,a.jsx)(g.A,{className:"h-5 w-5"})},{id:2,title:"ملخص الطلب",description:"أدخل بياناتك",icon:(0,a.jsx)(b.A,{className:"h-5 w-5"})},{id:3,title:"الدفع",description:"أكمل عملية الدفع",icon:(0,a.jsx)(y.A,{className:"h-5 w-5"})}];return(0,a.jsxs)("div",{className:(0,f.cn)("w-full",t),children:[(0,a.jsx)("div",{className:"block lg:hidden",children:(0,a.jsx)("div",{className:"flex items-center justify-between mb-6",children:s.map((t,r)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:(0,f.cn)("flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300",t.id<e?"bg-gradient-to-r from-yellow-400 to-orange-500 border-yellow-400 text-slate-900":t.id===e?"bg-slate-800 border-yellow-400 text-yellow-400":"bg-slate-700 border-slate-600 text-slate-400"),children:t.id<e?(0,a.jsx)(j.A,{className:"h-5 w-5"}):t.icon}),(0,a.jsx)("div",{className:"mt-2 text-center",children:(0,a.jsx)("p",{className:(0,f.cn)("text-xs font-medium",t.id<=e?"text-yellow-400":"text-slate-400"),children:t.title})})]}),r<s.length-1&&(0,a.jsx)("div",{className:(0,f.cn)("flex-1 h-0.5 mx-4 transition-all duration-300",t.id<e?"bg-gradient-to-r from-yellow-400 to-orange-500":"bg-slate-600")})]},t.id))})}),(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"space-y-6",children:s.map((t,r)=>(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:(0,f.cn)("flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 flex-shrink-0",t.id<e?"bg-gradient-to-r from-yellow-400 to-orange-500 border-yellow-400 text-slate-900":t.id===e?"bg-slate-800 border-yellow-400 text-yellow-400":"bg-slate-700 border-slate-600 text-slate-400"),children:t.id<e?(0,a.jsx)(j.A,{className:"h-6 w-6"}):t.icon}),(0,a.jsxs)("div",{className:"mr-4 flex-1",children:[(0,a.jsx)("h3",{className:(0,f.cn)("text-lg font-semibold",t.id<=e?"text-yellow-400":"text-slate-400"),children:t.title}),(0,a.jsx)("p",{className:(0,f.cn)("text-sm mt-1",t.id<=e?"text-slate-300":"text-slate-500"),children:t.description})]}),r<s.length-1&&(0,a.jsx)("div",{className:"absolute right-6 mt-12 w-0.5 h-6 bg-slate-600"})]},t.id))})})]})}var N=s(55192),w=s(24934),k=s(68988),S=s(10403),$=s(63552),A=s(54278),_=s(75034);function D({selectedAmount:e,selectedCurrency:t,onAmountChange:s,onCurrencyChange:l,className:n}){let[i,c]=(0,r.useState)(""),[o,d]=(0,r.useState)(!1),[u,x]=(0,r.useState)([]),h=e=>{d(!1),c(""),s(e)},m=e=>{c(e);let t=parseInt(e)||0;t>0&&s(t)};return(0,a.jsxs)("div",{className:(0,f.cn)("space-y-6",n),children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(S.J,{className:"text-slate-300 font-medium",children:"العملة"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsx)(w.$,{type:"button",variant:"SDG"===t?"default":"outline",className:(0,f.cn)("h-12 text-lg font-semibold transition-all duration-300","SDG"===t?"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600":"border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"),onClick:()=>l("SDG"),children:"جنيه سوداني (SDG)"}),(0,a.jsx)(w.$,{type:"button",variant:"EGP"===t?"default":"outline",className:(0,f.cn)("h-12 text-lg font-semibold transition-all duration-300","EGP"===t?"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600":"border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"),onClick:()=>l("EGP"),children:"جنيه مصري (EGP)"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(S.J,{className:"text-slate-300 font-medium",children:"مبلغ الشحن"}),(0,a.jsxs)(w.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>{d(!o),o?(c(""),u.length>0&&s(u[0].amount)):c(e>0?e.toString():"")},className:"text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400/10",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 ml-2"}),o?"المبالغ المحددة مسبقاً":"مبلغ مخصص"]})]}),o?(0,a.jsx)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50",children:(0,a.jsx)(N.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(S.J,{htmlFor:"custom-amount",className:"text-slate-300",children:"أدخل المبلغ المطلوب"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(k.p,{id:"custom-amount",type:"number",placeholder:"0",value:i,onChange:e=>m(e.target.value),className:"text-xl font-semibold text-center bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",min:"1",step:"1"}),(0,a.jsx)("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-slate-400",children:t})]}),i&&parseInt(i)>0&&(0,a.jsx)("p",{className:"text-center text-yellow-400 font-medium",children:(0,A.vv)(parseInt(i),t)})]})})}):(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:u.map(t=>(0,a.jsx)(N.Zp,{className:(0,f.cn)("cursor-pointer transition-all duration-300 hover:scale-105","bg-slate-800/50 backdrop-blur-xl border-slate-700/50",e===t.amount?"border-yellow-400 bg-yellow-400/10 shadow-yellow-400/20":"hover:border-slate-600 hover:shadow-lg"),onClick:()=>h(t.amount),children:(0,a.jsxs)(N.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center gap-2 mb-2",children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-yellow-400"})}),(0,a.jsx)("p",{className:"text-xl font-bold text-white",children:(0,A.vv)(t.amount,t.currency)}),(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"SDG"===t.currency?"جنيه سوداني":"جنيه مصري"})]})},t.id))})]}),e>0&&(0,a.jsx)(N.Zp,{className:"bg-gradient-to-r from-yellow-400/10 to-orange-500/10 border-yellow-400/20",children:(0,a.jsxs)(N.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("p",{className:"text-slate-300 text-sm mb-1",children:"المبلغ المحدد"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:(0,A.vv)(e,t)})]})})]})}var M=s(28559);function C({onNext:e}){let{state:t,setAmount:s,setCurrency:r,canProceedToStep2:l}=p();return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:(0,a.jsxs)(N.aR,{className:"text-center pb-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center gap-3 mb-4",children:(0,a.jsx)("div",{className:"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg",children:(0,a.jsx)(g.A,{className:"h-8 w-8 text-slate-900"})})}),(0,a.jsx)(N.ZB,{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent",children:"تفاصيل الشحن"}),(0,a.jsx)("p",{className:"text-slate-300 text-base lg:text-lg",children:"اختر مبلغ الشحن والعملة المطلوبة"})]})}),(0,a.jsx)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:(0,a.jsx)(N.Wu,{className:"p-6 lg:p-8",children:(0,a.jsx)(D,{selectedAmount:t.amount,selectedCurrency:t.currency,onAmountChange:s,onCurrencyChange:r})})}),(0,a.jsx)(N.Zp,{className:"bg-slate-800/30 backdrop-blur-xl border-slate-700/30",children:(0,a.jsxs)(N.Wu,{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-yellow-400 mb-4",children:"تعليمات مهمة"}),(0,a.jsxs)("div",{className:"space-y-3 text-slate-300",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("p",{children:"اختر المبلغ الذي تريد شحنه في محفظتك"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("p",{children:"يمكنك اختيار من المبالغ المحددة مسبقاً أو إدخال مبلغ مخصص"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("p",{children:"تأكد من اختيار العملة الصحيحة (جنيه سوداني أو مصري)"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("p",{children:"سيتم إضافة المبلغ إلى محفظتك بعد تأكيد الدفع"})]})]})]})}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(w.$,{onClick:()=>{l()&&e()},disabled:!l(),className:(0,f.cn)("px-8 py-3 text-lg font-semibold transition-all duration-300",l()?"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600 hover:scale-105 shadow-lg hover:shadow-yellow-400/25":"bg-slate-700 text-slate-400 cursor-not-allowed"),children:["التالي",(0,a.jsx)(M.A,{className:"h-5 w-5 mr-2"})]})})]})}var E=s(58869),T=s(48340),P=s(19169),O=s(62688);let R=(0,O.A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function G({onNext:e,onPrevious:t}){let{state:s,setUserDetails:l,canProceedToStep3:n}=p(),[i,c]=(0,r.useState)({firstName:s.userDetails?.firstName||"",lastName:s.userDetails?.lastName||"",phone:s.userDetails?.phone||"",email:s.userDetails?.email||""}),[o,d]=(0,r.useState)({}),u=()=>{let e={};return i.firstName.trim()||(e.firstName="الاسم الأول مطلوب"),i.lastName.trim()||(e.lastName="الاسم الأخير مطلوب"),i.phone.trim()?/^[0-9+\-\s()]+$/.test(i.phone)||(e.phone="رقم الهاتف غير صحيح"):e.phone="رقم الهاتف مطلوب",i.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i.email)||(e.email="البريد الإلكتروني غير صحيح"):e.email="البريد الإلكتروني مطلوب",d(e),0===Object.keys(e).length},x=(e,t)=>{c(s=>({...s,[e]:t})),o[e]&&d(t=>({...t,[e]:""}))},h=()=>""!==i.firstName.trim()&&""!==i.lastName.trim()&&""!==i.phone.trim()&&""!==i.email.trim();return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:(0,a.jsxs)(N.aR,{className:"text-center pb-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center gap-3 mb-4",children:(0,a.jsx)("div",{className:"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg",children:(0,a.jsx)(b.A,{className:"h-8 w-8 text-slate-900"})})}),(0,a.jsx)(N.ZB,{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent",children:"ملخص الطلب"}),(0,a.jsx)("p",{className:"text-slate-300 text-base lg:text-lg",children:"أدخل بياناتك الشخصية وراجع تفاصيل الطلب"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl text-white",children:[(0,a.jsx)(E.A,{className:"h-6 w-6 text-yellow-400"}),"البيانات الشخصية"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"firstName",className:"text-slate-300 font-medium",children:"الاسم الأول *"}),(0,a.jsx)(k.p,{id:"firstName",type:"text",placeholder:"أدخل الاسم الأول",value:i.firstName,onChange:e=>x("firstName",e.target.value),className:(0,f.cn)("bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",o.firstName?"border-red-400 focus:border-red-400":"focus:border-yellow-400")}),o.firstName&&(0,a.jsx)("p",{className:"text-red-400 text-sm",children:o.firstName})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"lastName",className:"text-slate-300 font-medium",children:"الاسم الأخير *"}),(0,a.jsx)(k.p,{id:"lastName",type:"text",placeholder:"أدخل الاسم الأخير",value:i.lastName,onChange:e=>x("lastName",e.target.value),className:(0,f.cn)("bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",o.lastName?"border-red-400 focus:border-red-400":"focus:border-yellow-400")}),o.lastName&&(0,a.jsx)("p",{className:"text-red-400 text-sm",children:o.lastName})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(S.J,{htmlFor:"phone",className:"text-slate-300 font-medium",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 inline ml-2"}),"رقم الهاتف *"]}),(0,a.jsx)(k.p,{id:"phone",type:"tel",placeholder:"+249 123 456 789",value:i.phone,onChange:e=>x("phone",e.target.value),className:(0,f.cn)("bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",o.phone?"border-red-400 focus:border-red-400":"focus:border-yellow-400")}),o.phone&&(0,a.jsx)("p",{className:"text-red-400 text-sm",children:o.phone})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(S.J,{htmlFor:"email",className:"text-slate-300 font-medium",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 inline ml-2"}),"البريد الإلكتروني *"]}),(0,a.jsx)(k.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:i.email,onChange:e=>x("email",e.target.value),className:(0,f.cn)("bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500",o.email?"border-red-400 focus:border-red-400":"focus:border-yellow-400")}),o.email&&(0,a.jsx)("p",{className:"text-red-400 text-sm",children:o.email})]})]})]}),(0,a.jsxs)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl text-white",children:[(0,a.jsx)(g.A,{className:"h-6 w-6 text-yellow-400"}),"ملخص الطلب"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 border-b border-slate-700",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"المنتج"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"شحن المحفظة"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 border-b border-slate-700",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"المبلغ"}),(0,a.jsx)("span",{className:"text-white font-medium",children:(0,A.vv)(s.amount,s.currency)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-3 border-b border-slate-700",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"العملة"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"SDG"===s.currency?"جنيه سوداني":"جنيه مصري"})]}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-yellow-400/10 to-orange-500/10 border border-yellow-400/20 rounded-lg p-4 mt-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-yellow-400 font-semibold text-lg",children:"الإجمالي"}),(0,a.jsx)("span",{className:"text-yellow-400 font-bold text-xl",children:(0,A.vv)(s.amount,s.currency)})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 sm:justify-between",children:[(0,a.jsxs)(w.$,{onClick:t,variant:"outline",className:"w-full sm:w-auto px-6 py-3 text-lg border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500",children:[(0,a.jsx)(R,{className:"h-5 w-5 ml-2"}),"السابق"]}),(0,a.jsxs)(w.$,{onClick:()=>{u()&&(l(i),e())},disabled:!h(),className:(0,f.cn)("w-full sm:w-auto px-8 py-3 text-lg font-semibold transition-all duration-300",h()?"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600 hover:scale-105 shadow-lg hover:shadow-yellow-400/25":"bg-slate-700 text-slate-400 cursor-not-allowed"),children:["التالي",(0,a.jsx)(M.A,{className:"h-5 w-5 mr-2"})]})]})]})}var Z=s(17313),F=s(70615),U=s(30474);function H({bank:e,isSelected:t,onSelect:s,className:l}){let[n,i]=(0,r.useState)(!1),c=async t=>{t.stopPropagation();try{await navigator.clipboard.writeText(e.accountNumber),i(!0),setTimeout(()=>i(!1),2e3)}catch(e){console.error("Failed to copy account number:",e)}};return(0,a.jsx)(N.Zp,{className:(0,f.cn)("cursor-pointer transition-all duration-300 hover:scale-105","bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-lg",t?"border-yellow-400 bg-yellow-400/10 shadow-yellow-400/20":"hover:border-slate-600 hover:shadow-xl",l),onClick:()=>s(e),children:(0,a.jsxs)(N.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative w-12 h-12 rounded-xl overflow-hidden bg-slate-700/50 flex items-center justify-center",children:[e.logoUrl?(0,a.jsx)(U.default,{src:e.logoUrl,alt:e.name,width:48,height:48,className:"object-contain",onError:e=>{let t=e.target;t.style.display="none",t.nextElementSibling?.classList.remove("hidden")}}):null,(0,a.jsx)(Z.A,{className:(0,f.cn)("h-6 w-6 text-slate-400",e.logoUrl?"hidden":"block")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white text-lg",children:e.name}),(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"حساب بنكي"})]})]}),(0,a.jsx)("div",{className:(0,f.cn)("w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300",t?"border-yellow-400 bg-yellow-400":"border-slate-500"),children:t&&(0,a.jsx)(j.A,{className:"h-4 w-4 text-slate-900"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm font-medium",children:"رقم الحساب"}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-slate-700/50 rounded-lg p-3",children:[(0,a.jsx)("span",{className:"font-mono text-white text-lg tracking-wider",children:e.accountNumber}),(0,a.jsx)("button",{onClick:c,className:(0,f.cn)("p-2 rounded-lg transition-all duration-300","hover:bg-slate-600/50 active:scale-95",n?"text-green-400 bg-green-400/10":"text-slate-400 hover:text-white"),title:"نسخ رقم الحساب",children:n?(0,a.jsx)(j.A,{className:"h-4 w-4"}):(0,a.jsx)(F.A,{className:"h-4 w-4"})})]})]}),t&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-yellow-400/10 border border-yellow-400/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-yellow-400 text-sm text-center",children:"تم اختيار هذا البنك للتحويل"})})]})})}var B=s(93613),L=s(16023);let W=(0,O.A)("FileImage",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]);var I=s(11860);function Y({file:e,preview:t,onFileChange:s,className:l}){let[n,i]=(0,r.useState)(!1),[c,o]=(0,r.useState)(null),d=(0,r.useRef)(null),u=["image/jpeg","image/jpg","image/png","image/webp"],x=e=>u.includes(e.type)?e.size>5242880?"حجم الملف يجب أن يكون أقل من 5 ميجابايت":null:"يرجى اختيار صورة بصيغة JPG أو PNG أو WebP",h=e=>{let t=x(e);if(t){o(t);return}o(null);let a=URL.createObjectURL(e);s(e,a)};return(0,a.jsxs)("div",{className:(0,f.cn)("space-y-4",l),children:[!e&&(0,a.jsx)(N.Zp,{className:(0,f.cn)("border-2 border-dashed transition-all duration-300 cursor-pointer","bg-slate-800/30 backdrop-blur-xl",n?"border-yellow-400 bg-yellow-400/10":c?"border-red-400 bg-red-400/10":"border-slate-600 hover:border-slate-500 hover:bg-slate-800/50"),onDrop:e=>{e.preventDefault(),i(!1);let t=e.dataTransfer.files[0];t&&h(t)},onDragOver:e=>{e.preventDefault(),i(!0)},onDragLeave:e=>{e.preventDefault(),i(!1)},onClick:()=>{d.current?.click()},children:(0,a.jsx)(N.Wu,{className:"p-8 text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("div",{className:(0,f.cn)("p-4 rounded-full transition-colors duration-300",n?"bg-yellow-400/20 text-yellow-400":c?"bg-red-400/20 text-red-400":"bg-slate-700/50 text-slate-400"),children:c?(0,a.jsx)(B.A,{className:"h-8 w-8"}):(0,a.jsx)(L.A,{className:"h-8 w-8"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"ارفع صورة الإيصال"}),(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"اسحب الصورة هنا أو انقر للاختيار"}),(0,a.jsx)("p",{className:"text-slate-500 text-xs",children:"JPG, PNG, WebP (أقل من 5 ميجابايت)"})]}),(0,a.jsxs)(w.$,{type:"button",variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(W,{className:"h-4 w-4 ml-2"}),"اختر ملف"]})]})})}),e&&t&&(0,a.jsx)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50",children:(0,a.jsx)(N.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"relative w-24 h-24 rounded-lg overflow-hidden bg-slate-700/50 flex-shrink-0",children:(0,a.jsx)(U.default,{src:t,alt:"Receipt preview",fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-white truncate",children:e.name}),(0,a.jsxs)("p",{className:"text-slate-400 text-sm",children:[(e.size/1024/1024).toFixed(2)," ميجابايت"]}),(0,a.jsx)("p",{className:"text-green-400 text-sm mt-1",children:"تم رفع الملف بنجاح"})]}),(0,a.jsx)(w.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>{t&&URL.revokeObjectURL(t),s(null,null),o(null),d.current&&(d.current.value="")},className:"text-slate-400 hover:text-red-400 hover:bg-red-400/10",children:(0,a.jsx)(I.A,{className:"h-4 w-4"})})]})})}),c&&(0,a.jsx)("div",{className:"p-3 bg-red-400/10 border border-red-400/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-400 text-sm text-center",children:c})}),(0,a.jsx)("input",{ref:d,type:"file",accept:u.join(","),onChange:e=>{let t=e.target.files?.[0];t&&h(t)},className:"hidden"})]})}let z=(0,O.A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);function J({onPrevious:e,onSubmit:t}){let{state:s,setSelectedBank:l,setReferenceNumber:n,setReceiptFile:i,canSubmitOrder:c}=p(),[o,d]=(0,r.useState)([]),[u,x]=(0,r.useState)(!1),h=e=>{l(e)},m=e=>{n(e)},g=async()=>{if(c()){x(!0);try{await t()}finally{x(!1)}}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:(0,a.jsxs)(N.aR,{className:"text-center pb-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center gap-3 mb-4",children:(0,a.jsx)("div",{className:"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg",children:(0,a.jsx)(y.A,{className:"h-8 w-8 text-slate-900"})})}),(0,a.jsx)(N.ZB,{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent",children:"إتمام الدفع"}),(0,a.jsx)("p",{className:"text-slate-300 text-base lg:text-lg",children:"اختر البنك وأرفق إيصال التحويل"})]})}),(0,a.jsxs)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:[(0,a.jsxs)(N.aR,{children:[(0,a.jsx)(N.ZB,{className:"text-xl text-white",children:"اختر البنك للتحويل"}),(0,a.jsx)("p",{className:"text-slate-400",children:"اختر البنك الذي تريد التحويل إليه"})]}),(0,a.jsx)(N.Wu,{className:"space-y-4",children:o.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:o.map(e=>(0,a.jsx)(H,{bank:e,isSelected:s.selectedBank?.id===e.id,onSelect:h},e.id))}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-slate-400",children:"لا توجد حسابات بنكية متاحة حالياً"})})})]}),s.selectedBank&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:[(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl text-white",children:[(0,a.jsx)(z,{className:"h-6 w-6 text-yellow-400"}),"رقم المرجع"]}),(0,a.jsx)("p",{className:"text-slate-400",children:"أدخل رقم المرجع من إيصال التحويل"})]}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"reference",className:"text-slate-300 font-medium",children:"رقم المرجع *"}),(0,a.jsx)(k.p,{id:"reference",type:"text",placeholder:"أدخل رقم المرجع",value:s.referenceNumber,onChange:e=>m(e.target.value),className:"bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500 focus:border-yellow-400"})]}),(0,a.jsxs)("div",{className:"bg-slate-700/30 border border-slate-600/50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-yellow-400 font-medium mb-2",children:"تعليمات مهمة:"}),(0,a.jsxs)("ul",{className:"text-slate-300 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• تأكد من صحة رقم المرجع"}),(0,a.jsx)("li",{children:"• احتفظ بإيصال التحويل الأصلي"}),(0,a.jsx)("li",{children:"• سيتم التحقق من التحويل خلال 24 ساعة"})]})]})]})]}),(0,a.jsxs)(N.Zp,{className:"bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl",children:[(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl text-white",children:[(0,a.jsx)(L.A,{className:"h-6 w-6 text-yellow-400"}),"إيصال التحويل"]}),(0,a.jsx)("p",{className:"text-slate-400",children:"ارفع صورة واضحة لإيصال التحويل"})]}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(Y,{file:s.receiptFile,preview:s.receiptPreview,onFileChange:(e,t)=>{i(e,t)}})})]})]}),s.selectedBank&&(0,a.jsx)(N.Zp,{className:"bg-gradient-to-r from-yellow-400/10 to-orange-500/10 border-yellow-400/20 shadow-2xl",children:(0,a.jsxs)(N.Wu,{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-yellow-400 mb-4",children:"ملخص نهائي للطلب"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"المبلغ"}),(0,a.jsx)("p",{className:"text-white font-bold text-lg",children:(0,A.vv)(s.amount,s.currency)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"البنك المختار"}),(0,a.jsx)("p",{className:"text-white font-bold text-lg",children:s.selectedBank.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-400 text-sm",children:"طريقة الدفع"}),(0,a.jsx)("p",{className:"text-white font-bold text-lg",children:"تحويل بنكي"})]})]})]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 sm:justify-between",children:[(0,a.jsxs)(w.$,{onClick:e,variant:"outline",className:"w-full sm:w-auto px-6 py-3 text-lg border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500",children:[(0,a.jsx)(R,{className:"h-5 w-5 ml-2"}),"السابق"]}),(0,a.jsx)(w.$,{onClick:g,disabled:!c()||u,className:(0,f.cn)("w-full sm:w-auto px-8 py-3 text-lg font-semibold transition-all duration-300",c()&&!u?"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 hover:scale-105 shadow-lg hover:shadow-green-500/25":"bg-slate-700 text-slate-400 cursor-not-allowed"),children:u?"جاري المعالجة...":`إدفع ${(0,A.vv)(s.amount,s.currency)}`})]})]})}var q=s(85668),V=s.n(q);s(89878);var K=s(28561);function X(){let{state:e,setStep:t,resetCheckout:s}=p(),n=(0,l.useRouter)(),[i,c]=(0,r.useState)(null),[o,d]=(0,r.useState)(null),[u,x]=(0,r.useState)(!1),h=()=>{e.step<3&&t(e.step+1)},m=()=>{e.step>1&&t(e.step-1)},f=async()=>{if(e.userDetails&&e.selectedBank)try{let t=function(e="RCH"){let t=V()(),s=t.format("YYYYMMDD"),a=t.format("HHmmss"),r=Math.floor(9999*Math.random()).toString().padStart(4,"0");return`${e}-${s}-${a}-${r}`}(),a={id:t,amount:e.amount,currency:e.currency,userDetails:e.userDetails,selectedBank:e.selectedBank,referenceNumber:e.referenceNumber,receiptFileName:e.receiptFile?.name,status:"pending",createdAt:new Date,paymentCurrency:i||e.currency,conversionInfo:o?{originalAmount:o.originalAmount,originalCurrency:o.originalCurrency,convertedAmount:o.convertedAmount,targetCurrency:o.targetCurrency,exchangeRate:o.exchangeRate,conversionFee:o.conversionFee,timestamp:o.timestamp}:void 0};(0,$.YJ)(a),s(),n.push(`/checkout/success?orderId=${t}`)}catch(e){console.error("Error submitting order:",e)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent"}),(0,a.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"}),(0,a.jsxs)("main",{className:"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center gap-4 mb-4",children:(0,a.jsx)("div",{className:"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg",children:(0,a.jsx)(K.A,{className:"h-8 w-8 text-slate-900"})})}),(0,a.jsx)("h1",{className:"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4",children:"شحن المحفظة"}),(0,a.jsx)("p",{className:"text-slate-300 text-lg",children:"اشحن محفظتك بسهولة وأمان في خطوات بسيطة"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)("div",{className:"lg:sticky lg:top-8",children:(0,a.jsx)(v,{currentStep:e.step})})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(()=>{switch(e.step){case 1:default:return(0,a.jsx)(C,{onNext:h});case 2:return(0,a.jsx)(G,{onNext:h,onPrevious:m});case 3:return(0,a.jsx)(J,{onPrevious:m,onSubmit:f})}})()})]})]})]})}function Q(){let[e,t]=(0,r.useState)("checkout"),[s,u]=(0,r.useState)(!1),x=(0,l.useRouter)(),h=e=>{"wallet"===e?x.push("/wallet"):"profile"===e?x.push("/profile"):"shop"===e?x.push("/shop"):"home"===e?(x.push("/"),x.refresh()):t(e)};return(0,a.jsx)(m,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent"}),(0,a.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"}),(0,a.jsx)(n.j,{onMenuOpen:()=>u(!0)}),(0,a.jsx)(c.c,{}),(0,a.jsx)(i.p,{isOpen:s,onClose:()=>u(!1)}),(0,a.jsx)(X,{}),(0,a.jsx)(o.v,{activeTab:e,onTabChange:h}),(0,a.jsx)(d.G,{activeTab:e,onTabChange:h})]})})}},28561:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},61528:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(37413),r=s(18127);function l(){return(0,a.jsx)(r.CheckoutPage,{})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63552:(e,t,s)=>{"use strict";s.d(t,{F6:()=>p,J2:()=>l,JA:()=>i,NP:()=>x,YJ:()=>m,Yv:()=>d,az:()=>u,b8:()=>c,jF:()=>r,m_:()=>o,qt:()=>h,rf:()=>n});let a={bankAccounts:[{id:"bank_001",name:"بنك الخرطوم",accountNumber:"****************",logoUrl:"/banks/khartoum-bank.png",isActive:!0},{id:"bank_002",name:"بنك فيصل الإسلامي",accountNumber:"****************",logoUrl:"/banks/faisal-bank.png",isActive:!0},{id:"bank_003",name:"بنك السودان المركزي",accountNumber:"****************",logoUrl:"/banks/central-bank.png",isActive:!0}],rechargeOptions:[{id:"amount_001",amount:1e4,currency:"SDG",isActive:!0},{id:"amount_002",amount:25e3,currency:"SDG",isActive:!0},{id:"amount_003",amount:5e4,currency:"SDG",isActive:!0},{id:"amount_004",amount:1e5,currency:"SDG",isActive:!0},{id:"amount_005",amount:50,currency:"EGP",isActive:!0},{id:"amount_006",amount:100,currency:"EGP",isActive:!0},{id:"amount_007",amount:250,currency:"EGP",isActive:!0},{id:"amount_008",amount:500,currency:"EGP",isActive:!0}],notes:["يرجى التأكد من صحة رقم المرجع قبل الإرسال","سيتم مراجعة طلبك خلال 24 ساعة","احتفظ بإيصال التحويل للمراجعة"],lastUpdated:new Date};function r(){return a}function l(e){}function n(e){let t={...e,id:`bank_${Date.now()}`};return a.bankAccounts.push(t),t}function i(e,t){let s=a.bankAccounts.findIndex(t=>t.id===e);-1!==s&&(a.bankAccounts[s]={...a.bankAccounts[s],...t})}function c(e){a.bankAccounts=a.bankAccounts.filter(t=>t.id!==e)}function o(e){let t={...e,id:`amount_${Date.now()}`};return a.rechargeOptions.push(t),t}function d(e,t){let s=a.rechargeOptions.findIndex(t=>t.id===e);-1!==s&&(a.rechargeOptions[s]={...a.rechargeOptions[s],...t})}function u(e){a.rechargeOptions=a.rechargeOptions.filter(t=>t.id!==e)}function x(){return a.bankAccounts.filter(e=>e.isActive)}function h(e){return a.rechargeOptions.filter(t=>t.isActive&&t.currency===e)}function m(e){}function p(e){return[].find(t=>t.id===e)||null}},66837:(e,t,s)=>{Promise.resolve().then(s.bind(s,25193))},70615:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},75034:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},85668:function(e){var t,s,a,r,l,n,i,c,o,d,u,x,h,m,p,f,g,b,y,j,v,N;t="millisecond",s="second",a="minute",r="hour",l="week",n="month",i="quarter",c="year",o="date",d="Invalid Date",u=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,x=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,s){var a=String(e);return!a||a.length>=t?e:""+Array(t+1-a.length).join(s)+e},(p={})[m="en"]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],s=e%100;return"["+e+(t[(s-20)%10]||t[s]||"th")+"]"}},f="$isDayjsObject",g=function(e){return e instanceof v||!(!e||!e[f])},b=function e(t,s,a){var r;if(!t)return m;if("string"==typeof t){var l=t.toLowerCase();p[l]&&(r=l),s&&(p[l]=s,r=l);var n=t.split("-");if(!r&&n.length>1)return e(n[0])}else{var i=t.name;p[i]=t,r=i}return!a&&r&&(m=r),r||!a&&m},y=function(e,t){if(g(e))return e.clone();var s="object"==typeof t?t:{};return s.date=e,s.args=arguments,new v(s)},(j={s:h,z:function(e){var t=-e.utcOffset(),s=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(s/60),2,"0")+":"+h(s%60,2,"0")},m:function e(t,s){if(t.date()<s.date())return-e(s,t);var a=12*(s.year()-t.year())+(s.month()-t.month()),r=t.clone().add(a,n),l=s-r<0,i=t.clone().add(a+(l?-1:1),n);return+(-(a+(s-r)/(l?r-i:i-r))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return({M:n,y:c,w:l,d:"day",D:o,h:r,m:a,s:s,ms:t,Q:i})[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}}).l=b,j.i=g,j.w=function(e,t){return y(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})},N=(v=function(){function e(e){this.$L=b(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[f]=!0}var h=e.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,s=e.utc;if(null===t)return new Date(NaN);if(j.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var a=t.match(u);if(a){var r=a[2]-1||0,l=(a[7]||"0").substring(0,3);return s?new Date(Date.UTC(a[1],r,a[3]||1,a[4]||0,a[5]||0,a[6]||0,l)):new Date(a[1],r,a[3]||1,a[4]||0,a[5]||0,a[6]||0,l)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return j},h.isValid=function(){return this.$d.toString()!==d},h.isSame=function(e,t){var s=y(e);return this.startOf(t)<=s&&s<=this.endOf(t)},h.isAfter=function(e,t){return y(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<y(e)},h.$g=function(e,t,s){return j.u(e)?this[t]:this.set(s,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,t){var i=this,d=!!j.u(t)||t,u=j.p(e),x=function(e,t){var s=j.w(i.$u?Date.UTC(i.$y,t,e):new Date(i.$y,t,e),i);return d?s:s.endOf("day")},h=function(e,t){return j.w(i.toDate()[e].apply(i.toDate("s"),(d?[0,0,0,0]:[23,59,59,999]).slice(t)),i)},m=this.$W,p=this.$M,f=this.$D,g="set"+(this.$u?"UTC":"");switch(u){case c:return d?x(1,0):x(31,11);case n:return d?x(1,p):x(0,p+1);case l:var b=this.$locale().weekStart||0,y=(m<b?m+7:m)-b;return x(d?f-y:f+(6-y),p);case"day":case o:return h(g+"Hours",0);case r:return h(g+"Minutes",1);case a:return h(g+"Seconds",2);case s:return h(g+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(e,l){var i,d=j.p(e),u="set"+(this.$u?"UTC":""),x=((i={}).day=u+"Date",i[o]=u+"Date",i[n]=u+"Month",i[c]=u+"FullYear",i[r]=u+"Hours",i[a]=u+"Minutes",i[s]=u+"Seconds",i[t]=u+"Milliseconds",i)[d],h="day"===d?this.$D+(l-this.$W):l;if(d===n||d===c){var m=this.clone().set(o,1);m.$d[x](h),m.init(),this.$d=m.set(o,Math.min(this.$D,m.daysInMonth())).$d}else x&&this.$d[x](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[j.p(e)]()},h.add=function(e,t){var i,o=this;e=Number(e);var d=j.p(t),u=function(t){var s=y(o);return j.w(s.date(s.date()+Math.round(t*e)),o)};if(d===n)return this.set(n,this.$M+e);if(d===c)return this.set(c,this.$y+e);if("day"===d)return u(1);if(d===l)return u(7);var x=((i={})[a]=6e4,i[r]=36e5,i[s]=1e3,i)[d]||1,h=this.$d.getTime()+e*x;return j.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,s=this.$locale();if(!this.isValid())return s.invalidDate||d;var a=e||"YYYY-MM-DDTHH:mm:ssZ",r=j.z(this),l=this.$H,n=this.$m,i=this.$M,c=s.weekdays,o=s.months,u=s.meridiem,h=function(e,s,r,l){return e&&(e[s]||e(t,a))||r[s].slice(0,l)},m=function(e){return j.s(l%12||12,e,"0")},p=u||function(e,t,s){var a=e<12?"AM":"PM";return s?a.toLowerCase():a};return a.replace(x,function(e,a){return a||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return j.s(t.$y,4,"0");case"M":return i+1;case"MM":return j.s(i+1,2,"0");case"MMM":return h(s.monthsShort,i,o,3);case"MMMM":return h(o,i);case"D":return t.$D;case"DD":return j.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(s.weekdaysMin,t.$W,c,2);case"ddd":return h(s.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(l);case"HH":return j.s(l,2,"0");case"h":return m(1);case"hh":return m(2);case"a":return p(l,n,!0);case"A":return p(l,n,!1);case"m":return String(n);case"mm":return j.s(n,2,"0");case"s":return String(t.$s);case"ss":return j.s(t.$s,2,"0");case"SSS":return j.s(t.$ms,3,"0");case"Z":return r}return null}(e)||r.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,t,o){var d,u=this,x=j.p(t),h=y(e),m=(h.utcOffset()-this.utcOffset())*6e4,p=this-h,f=function(){return j.m(u,h)};switch(x){case c:d=f()/12;break;case n:d=f();break;case i:d=f()/3;break;case l:d=(p-m)/6048e5;break;case"day":d=(p-m)/864e5;break;case r:d=p/36e5;break;case a:d=p/6e4;break;case s:d=p/1e3;break;default:d=p}return o?d:j.a(d)},h.daysInMonth=function(){return this.endOf(n).$D},h.$locale=function(){return p[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var s=this.clone(),a=b(e,t,!0);return a&&(s.$L=a),s},h.clone=function(){return j.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},e}()).prototype,y.prototype=N,[["$ms",t],["$s",s],["$m",a],["$H",r],["$W","day"],["$M",n],["$y",c],["$D",o]].forEach(function(e){N[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),y.extend=function(e,t){return e.$i||(e(t,v,y),e.$i=!0),y},y.locale=b,y.isDayjs=g,y.unix=function(e){return y(1e3*e)},y.en=p[m],y.Ls=p,y.p={},e.exports=y},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,228,935,50,176,657],()=>s(18708));module.exports=a})();