import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation, Navigate } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { CartProvider } from "./contexts/CartContext";
import { AuthProvider } from "./contexts/AuthContext";
import { ConfigProvider, useConfig } from "./contexts/ConfigContext";
import { LanguageProvider } from "./contexts/LanguageContext";
import { useAuth } from "./contexts/AuthContext";
import Index from "./pages/Index";
import Accounts from "./pages/Accounts";
import UCStore from "./pages/UCStore";
import Blog from "./pages/Blog";
import BlogPost from "./pages/BlogPost";
import Contact from "./pages/Contact";
import Cart from "./pages/Cart";
import Admin from "./pages/Admin";
import Login from "./pages/Login";
import Profile from "./pages/Profile";
import NotFound from "./pages/NotFound";
import Mods from "./pages/Mods";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import About from "./pages/About";
import FAQ from "./pages/FAQ";
import { useEffect } from "react";

// Protected route component for user authentication
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { currentUser, loading } = useAuth();
  
  // If still loading auth state, show nothing
  if (loading) return null;
  
  // If not authenticated, redirect to login
  if (!currentUser) {
    return <Navigate to="/login" />;
  }
  
  return <>{children}</>;
};

// Admin route component that requires admin role
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { currentUser, isAdmin, loading } = useAuth();
  
  // If still loading auth state, show nothing
  if (loading) return null;
  
  // If not authenticated or not admin, redirect to login
  if (!currentUser || !isAdmin) {
    return <Navigate to="/login" />;
  }
  
  return <>{children}</>;
};

// Visibility-aware route component
const VisibleRoute = ({ 
  pageId, 
  element 
}: { 
  pageId: string;
  element: React.ReactNode;
}) => {
  const { isPageVisible, isLoading } = useConfig();
  
  // If still loading config, show nothing
  if (isLoading) return null;
  
  // If page is not visible, redirect to not found
  if (!isPageVisible(pageId)) {
    return <Navigate to="/not-found" />;
  }
  
  return <>{element}</>;
};

// ScrollToTop component to handle scroll restoration
function ScrollToTop() {
  const { pathname } = useLocation();
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);
  
  return null;
}

// Routes Component that uses visibility checks
const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<VisibleRoute pageId="home" element={<Index />} />} />
      <Route path="/accounts" element={<VisibleRoute pageId="accounts" element={<Accounts />} />} />
      <Route path="/uc-store" element={<VisibleRoute pageId="uc-store" element={<UCStore />} />} />
      <Route path="/blog" element={<VisibleRoute pageId="blog" element={<Blog />} />} />
      <Route path="/blog/:slug" element={<VisibleRoute pageId="blog" element={<BlogPost />} />} />
      <Route path="/contact" element={<VisibleRoute pageId="contact" element={<Contact />} />} />
      <Route path="/cart" element={<ProtectedRoute><Cart /></ProtectedRoute>} />
      <Route path="/login" element={<Login />} />
      <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
      <Route path="/mods" element={<VisibleRoute pageId="mods" element={<Mods />} />} />
      <Route path="/admin" element={<AdminRoute><Admin /></AdminRoute>} />
      <Route path="/about" element={<VisibleRoute pageId="about" element={<About />} />} />
      <Route path="/faq" element={<FAQ />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const queryClient = new QueryClient();

const App = () => {

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider>
          <AuthProvider>
            <LanguageProvider>
              <CartProvider>
                <TooltipProvider>
                  <Toaster />
                  <Sonner />
                  <BrowserRouter>
                    <ScrollToTop />
                    <div className="flex flex-col min-h-screen">
                      <Navbar />
                      <main className="flex-grow">
                        <AppRoutes />
                      </main>
                      <Footer />
                    </div>
                  </BrowserRouter>
                </TooltipProvider>
              </CartProvider>
            </LanguageProvider>
          </AuthProvider>
        </ConfigProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;
