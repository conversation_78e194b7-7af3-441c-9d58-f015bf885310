import * as React from "react"

import { cn } from "@/lib/utils"

// Define input variants that use our CSS variables
const inputVariants = {
  default: "bg-background border-input",
  admin: "bg-[var(--bg-input-dark)] border-[var(--border-gray)] focus:border-[var(--pubg-orange-color)]",
  dark: "bg-[var(--bg-input-dark)] border-[var(--border-gray)]",
}

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: keyof typeof inputVariants;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, variant = "default", ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          inputVariants[variant],
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
