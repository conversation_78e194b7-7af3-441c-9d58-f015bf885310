import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { motion } from "framer-motion";
import { UserCircle, Save, Phone, Mail, Lock, Eye, EyeOff } from "lucide-react";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/ui/use-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { doc, getDoc, setDoc, Timestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { UserModel } from "@/services/firestore";
import ImageUploader from "@/components/admin/ImageUploader";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};

// Create a function to get the profile form schema
const createProfileFormSchema = (t: any) => z.object({
  displayName: z.string()
    .min(3, { message: t('profile.validation.name_min') })
    .max(50, { message: t('profile.validation.name_max') }),
  phoneNumber: z.string()
    .min(10, { message: t('profile.validation.phone_min') })
    .max(15, { message: t('profile.validation.phone_max') })
    .regex(/^[+\d\s-]+$/, { message: t('profile.validation.phone_format') })
    .optional()
    .or(z.literal("")),
  photoURL: z.string().url({ message: t('profile.validation.photo_url') }).optional().or(z.literal("")),
});

// Create a function to get the password form schema
const createPasswordFormSchema = (t: any) => z.object({
  currentPassword: z.string()
    .min(1, { message: t('profile.password.current_password') + " " + t('common.is_required') }),
  newPassword: z.string()
    .min(6, { message: t('profile.password.password_min') })
    .max(50, { message: t('profile.password.password_max') }),
  confirmPassword: z.string()
    .min(1, { message: t('profile.password.confirm_password') + " " + t('common.is_required') }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: t('profile.password.password_mismatch'),
  path: ["confirmPassword"],
});

const Profile = () => {
  const { currentUser, updateUserProfile, updateUserPassword } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [userData, setUserData] = useState<Partial<UserModel> | null>(null);
  const [previewPhotoURL, setPreviewPhotoURL] = useState<string | null>(null);
  const avatarRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation('common');
  const { language } = useLanguage();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Create the form schema using the translation function
  const profileFormSchema = createProfileFormSchema(t);
  const passwordFormSchema = createPasswordFormSchema(t);

  useEffect(() => {
    const fetchUserData = async () => {
      if (currentUser) {
        try {
          setIsLoading(true);
          const userDoc = await getDoc(doc(db, "users", currentUser.uid));
          
          if (userDoc.exists()) {
            setUserData(userDoc.data() as Partial<UserModel>);
          } else {
            // Create a default user document if it doesn't exist
            const defaultUserData = {
              uid: currentUser.uid,
              email: currentUser.email,
              displayName: currentUser.displayName || "",
              photoURL: currentUser.photoURL || "",
              isActive: true,
              role: "user" as "admin" | "user",
              createdAt: Timestamp.now(),
            };
            
            await setDoc(doc(db, "users", currentUser.uid), defaultUserData);
            setUserData(defaultUserData);
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
          toast({
            title: t('profile.error'),
            description: t('profile.error_fetch'),
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    fetchUserData();
  }, [currentUser, toast, t]);

  // Redirect if not logged in
  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  // Initialize form with resolver
  const form = useForm({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      displayName: currentUser?.displayName || "",
      phoneNumber: userData?.phoneNumber || "",
      photoURL: currentUser?.photoURL || "",
    }
  });

  // Initialize password form
  const passwordForm = useForm({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    }
  });

  // Update form values when userData changes
  useEffect(() => {
    if (userData) {
      form.reset({
        displayName: userData.displayName || currentUser?.displayName || "",
        phoneNumber: userData.phoneNumber || "",
        photoURL: userData.photoURL || currentUser?.photoURL || "",
      });
    }
  }, [userData, form, currentUser]);

  // Update preview when form values change
  useEffect(() => {
    // Use the form's photoURL or the preview URL if available
    const currentPhoto = form?.watch("photoURL") || currentUser?.photoURL || "";
    setPreviewPhotoURL(currentPhoto);
  }, [form?.watch("photoURL"), currentUser?.photoURL]);

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);
      
      // Update or create user profile in Firestore
      if (currentUser) {
        // Create update data object
        const updateData = {
          displayName: data.displayName,
          phoneNumber: data.phoneNumber,
          photoURL: data.photoURL,
          email: currentUser.email,
          uid: currentUser.uid,
          updatedAt: Timestamp.now(),
        };
        
        // Remove any undefined values
        Object.keys(updateData).forEach(key => {
          if (updateData[key] === undefined) {
            delete updateData[key];
          }
        });
        
        const userRef = doc(db, "users", currentUser.uid);
        await setDoc(userRef, updateData, { merge: true }); // Using merge: true to create the document if it doesn't exist
        
        // Update Firebase Auth user profile
        await updateUserProfile(data.displayName, data.photoURL);
        
        // Update local state with clean data
        setUserData(prev => ({
          ...prev,
          displayName: data.displayName,
          phoneNumber: data.phoneNumber,
          photoURL: data.photoURL,
          uid: currentUser.uid,
        }));
        
        toast({
          title: t('profile.success_update'),
          description: t('profile.success_update_desc'),
        });
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: t('profile.error'),
        description: t('profile.error_update'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onPasswordSubmit = async (data) => {
    try {
      setIsPasswordLoading(true);
      
      // Call the updateUserPassword function from auth context
      await updateUserPassword(data.currentPassword, data.newPassword);
      
      // Reset form on success
      passwordForm.reset();
      
    } catch (error) {
      // Error handling is done in the updateUserPassword method
      console.error("Password update failed:", error);
    } finally {
      setIsPasswordLoading(false);
    }
  };

  const handleImageChange = async (e: any) => {
    const { name, value } = e.target;
    form.setValue(name as any, value);
    
    // Update preview immediately
    if (name === 'photoURL' && value) {
      setPreviewPhotoURL(value);
      
      // Add a subtle animation to the avatar when photo changes
      if (avatarRef.current) {
        avatarRef.current.classList.add('scale-105');
        setTimeout(() => {
          avatarRef.current?.classList.remove('scale-105');
        }, 300);
      }
      
      try {
        // Update auth profile
        await updateUserProfile(form.getValues('displayName'), value);
        
        // Also update Firestore to keep data in sync
        if (currentUser) {
          // Create update data with required fields
          const updateData = {
            photoURL: value,
            uid: currentUser.uid,
            updatedAt: Timestamp.now(),
          };
          
          // Remove any undefined values
          Object.keys(updateData).forEach(key => {
            if (updateData[key] === undefined) {
              delete updateData[key];
            }
          });
          
          const userRef = doc(db, "users", currentUser.uid);
          await setDoc(userRef, updateData, { merge: true });
        }
        
        console.log("Profile photo updated immediately");
      } catch (error) {
        console.error("Error updating profile photo:", error);
        toast({
          title: t('profile.error_photo'),
          description: t('profile.error_photo_desc'),
          variant: "destructive",
        });
      }
    }
  };

  return (
    <div className="min-h-screen pt-24 pb-16 px-4">
      <div className="container mx-auto max-w-5xl">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {t('profile.title')}
          </h1>
          <p className="text-muted-foreground">
            {t('profile.description')}
          </p>
        </motion.div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="mb-8">
            <TabsTrigger value="details" className="tab-trigger">{t('profile.personal_info')}</TabsTrigger>
            <TabsTrigger value="password" className="tab-trigger">{t('profile.password.change_password')}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="details">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
            >
              <div className="glass-card rounded-xl p-6 md:p-8">
                <div className="flex items-center mb-6">
                  <UserCircle className="ml-2 w-5 h-5 text-pubg-orange" />
                  <h2 className="text-xl font-bold text-white">{t('profile.personal_info')}</h2>
                </div>
                
                <div className="flex flex-col md:flex-row gap-8 items-start">
                  <div className="w-full md:w-1/3 flex flex-col items-center">
                    <motion.div 
                      ref={avatarRef}
                      className="relative"
                      initial={{ scale: 1 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Avatar 
                        className="w-32 h-32 mb-4 transition-all duration-300 ease-in-out" 
                        key={`profile-${previewPhotoURL || "placeholder"}-${Date.now()}`}
                      >
                        <AvatarImage 
                          src={previewPhotoURL || ""} 
                          alt={form.watch("displayName") || t('profile.default_name')}
                          className="object-cover"
                          style={{ width: '100%', height: '100%' }}
                        />
                        <AvatarFallback className="bg-pubg-orange text-pubg-dark text-4xl">
                          {form.watch("displayName")
                            ? form.watch("displayName").charAt(0).toUpperCase()
                            : currentUser.email?.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      
                      {/* Add photo change indicator */}
                      {previewPhotoURL !== currentUser?.photoURL && (
                        <span className="absolute -top-2 -right-2 bg-pubg-orange text-pubg-dark rounded-full px-2 py-1 text-xs font-bold">
                          {t('profile.new_badge')}
                        </span>
                      )}
                    </motion.div>
                    
                    <div className="text-center">
                      <h2 className="text-xl font-bold text-white mb-1">
                        {form.watch("displayName") || t('profile.default_name')}
                      </h2>
                      <p className="text-muted-foreground text-sm flex items-center justify-center gap-1">
                        <Mail className="h-3 w-3" /> {currentUser.email}
                      </p>
                      {form.watch("phoneNumber") && (
                        <p className="text-muted-foreground text-sm flex items-center justify-center gap-1 mt-1">
                          <Phone className="h-3 w-3" /> {form.watch("phoneNumber")}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="w-full md:w-2/3">
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6"
                      >
                        <FormField
                          control={form.control}
                          name="displayName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t('profile.display_name')}</FormLabel>
                              <FormControl>
                                <Input placeholder={t('profile.display_name_placeholder')} {...field} />
                              </FormControl>
                              <FormDescription>
                                {t('profile.display_name_desc')}
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="phoneNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t('profile.phone_number')}</FormLabel>
                              <FormControl>
                                <Input 
                                  type="tel" 
                                  placeholder={t('profile.phone_number_placeholder')} 
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                {t('profile.phone_number_desc')}
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormItem>
                          <FormLabel>{t('profile.profile_photo')}</FormLabel>
                          <ImageUploader
                            imageUrl={form.watch("photoURL") || ""}
                            onChange={handleImageChange}
                            fieldName="photoURL"
                            label={t('profile.photo_url')}
                            placeholder={t('profile.photo_url_placeholder')}
                            aspectRatio={1}
                            buttonLabels={{
                              apply: t('profile.apply_url'),
                              upload: t('profile.upload_photo')
                            }}
                          />
                          <FormDescription>
                            {t('profile.photo_desc')}
                            {previewPhotoURL !== currentUser?.photoURL && (
                              <span className="text-pubg-orange mr-2">
                                {t('profile.photo_changed')}
                              </span>
                            )}
                          </FormDescription>
                        </FormItem>

                        <Button
                          type="submit"
                          className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 w-full md:w-auto"
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            t('profile.saving')
                          ) : (
                            <>
                              <Save className="ml-2 h-4 w-4" />
                              {t('profile.save_changes')}
                            </>
                          )}
                        </Button>
                      </form>
                    </Form>
                  </div>
                </div>
              </div>
            </motion.div>
          </TabsContent>
          
          <TabsContent value="password">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={fadeInUp}
            >
              <div className="glass-card rounded-xl p-6 md:p-8">
                <div className="flex items-center mb-6">
                  <Lock className="ml-2 w-5 h-5 text-pubg-orange" />
                  <h2 className="text-xl font-bold text-white">{t('profile.password.change_password')}</h2>
                </div>
                
                <div className="max-w-xl mx-auto">
                  <Form {...passwordForm}>
                    <form
                      onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
                      className="space-y-6"
                    >
                      <FormField
                        control={passwordForm.control}
                        name="currentPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('profile.password.current_password')}</FormLabel>
                            <div className="relative">
                              <FormControl>
                                <Input 
                                  type={showCurrentPassword ? "text" : "password"} 
                                  placeholder={t('profile.password.current_password_placeholder')} 
                                  {...field} 
                                />
                              </FormControl>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="absolute right-2 top-2 h-6 w-6 text-muted-foreground hover:text-white"
                                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                              >
                                {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={passwordForm.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('profile.password.new_password')}</FormLabel>
                            <div className="relative">
                              <FormControl>
                                <Input 
                                  type={showNewPassword ? "text" : "password"} 
                                  placeholder={t('profile.password.new_password_placeholder')} 
                                  {...field} 
                                />
                              </FormControl>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="absolute right-2 top-2 h-6 w-6 text-muted-foreground hover:text-white"
                                onClick={() => setShowNewPassword(!showNewPassword)}
                              >
                                {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={passwordForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('profile.password.confirm_password')}</FormLabel>
                            <div className="relative">
                              <FormControl>
                                <Input 
                                  type={showConfirmPassword ? "text" : "password"} 
                                  placeholder={t('profile.password.confirm_password_placeholder')} 
                                  {...field} 
                                />
                              </FormControl>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="absolute right-2 top-2 h-6 w-6 text-muted-foreground hover:text-white"
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              >
                                {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <Button
                        type="submit"
                        className="bg-pubg-orange text-pubg-dark hover:bg-pubg-orange/90 w-full"
                        disabled={isPasswordLoading}
                      >
                        {isPasswordLoading ? (
                          t('profile.password.updating')
                        ) : (
                          <>
                            <Lock className="ml-2 h-4 w-4" />
                            {t('profile.password.update_password')}
                          </>
                        )}
                      </Button>
                    </form>
                  </Form>
                </div>
              </div>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Profile;
