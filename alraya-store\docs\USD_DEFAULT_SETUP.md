# USD as Default Currency Configuration

This document explains how the Al-Raya Store multi-currency platform has been configured with **USD as the default currency** while allowing users to change their preference anytime.

## 🎯 Overview

### What Changed
- **USD is now the primary default currency** for all new users
- **Users can change their preferred currency** anytime through the wallet settings
- **All existing functionality preserved** with enhanced flexibility
- **Backward compatibility maintained** for existing SDG/EGP users

### Key Benefits
- **Global Appeal**: USD is universally recognized and preferred by international users
- **Simplified Pricing**: Base prices in USD with automatic conversion to local currencies
- **User Choice**: Users can switch to any enabled currency (SDG, EGP, etc.) as their default
- **Consistent Experience**: All users start with the same currency baseline

## 🔧 Technical Implementation

### Database Changes
1. **Client Settings Updated**: Primary currency set to USD
2. **User Preferences**: New users default to USD preference
3. **Wallet Creation**: USD wallets created automatically for all users
4. **Exchange Rates**: USD as base currency for all conversions

### Code Changes
1. **Default Currency Constant**: Changed from `"SDG"` to `"USD"`
2. **Currency Order**: USD appears first in all currency lists
3. **Mock Data**: Updated to include USD transactions and balances
4. **User Preferences**: New component allows currency preference changes

## 🚀 User Experience

### For New Users
1. **Registration**: Automatically get USD as preferred currency
2. **First Wallet**: USD wallet created by default
3. **Transactions**: All operations default to USD
4. **Can Change**: Switch to SDG, EGP, or any other currency anytime

### For Existing Users
1. **Preserved Data**: All existing SDG/EGP balances maintained
2. **USD Wallet Added**: New USD wallet created with $0 balance
3. **Preference Update**: Can set USD as preferred currency if desired
4. **No Disruption**: Existing workflows continue unchanged

### Currency Preference Management
Users can change their default currency through:
- **Wallet Page**: "Change Default Currency Settings" button
- **User Preferences**: Complete currency preference management
- **Real-time Updates**: Changes apply immediately

## 📋 Setup Instructions

### 1. Database Setup
```sql
-- Run the main migrations first
\i migrations/001_multi_currency_schema.sql
\i migrations/002_data_migration.sql
\i migrations/003_rls_policies.sql
\i migrations/004_utility_functions.sql
\i migrations/005_table_cleanup.sql

-- Then set USD as default
\i scripts/set_user_currency_defaults.sql
```

### 2. Environment Configuration
```env
# Set USD as default in environment variables
NEXT_PUBLIC_DEFAULT_CURRENCY=USD
NEXT_PUBLIC_ENABLE_MULTI_CURRENCY=true
NEXT_PUBLIC_ENABLE_CURRENCY_CONVERSION=true
```

### 3. Verify Setup
```sql
-- Check that USD is the primary currency
SELECT primary_currency_code FROM client_currency_settings;
-- Should return: USD

-- Check user preferences
SELECT preferred_currency_code, COUNT(*) 
FROM user_preferences 
GROUP BY preferred_currency_code;

-- Check USD wallets exist
SELECT COUNT(DISTINCT user_id) as users_with_usd_wallet
FROM user_wallets 
WHERE currency_code = 'USD';
```

## 🎨 User Interface Changes

### Currency Selector
- **USD appears first** in all currency selection dropdowns
- **Default selection** is USD for new users
- **Visual priority** given to USD in the interface

### Wallet Page
- **USD wallet displayed prominently** at the top
- **Currency preferences button** allows easy switching
- **Conversion tools** help users move between currencies

### Admin Interface
- **USD set as primary** in client configuration
- **Exchange rates** managed with USD as base currency
- **Revenue reporting** consolidated in USD by default

## 🔄 Migration Path

### Option 1: Keep Existing Balances (Recommended)
- Existing users keep their SDG/EGP balances
- USD wallet added with $0 balance
- Users can convert between currencies as needed
- No disruption to existing operations

### Option 2: Convert Existing Balances to USD
- **Use with caution**: This converts all existing balances to USD
- Uncomment the conversion section in `set_user_currency_defaults.sql`
- Existing SDG/EGP balances converted using current exchange rates
- **Irreversible operation** - create backups first

## 🛠 User Preference Management

### Currency Preferences Component
```tsx
<CurrencyPreferences
  userId={userId}
  availableCurrencies={availableCurrencies}
  onPreferencesUpdate={(prefs) => {
    // Handle preference updates
    setSelectedCurrency(prefs.preferredCurrency)
  }}
/>
```

### API Endpoints
- `GET /api/user/preferences?userId={id}` - Get user preferences
- `POST /api/user/preferences` - Update user preferences
- `PUT /api/user/preferences` - Reset to defaults

### Preference Options
- **Preferred Currency**: Default currency for new transactions
- **Display Currency**: Currency used for showing prices
- **Enable Conversion**: Allow currency conversion in wallet
- **Confirmation Required**: Show confirmation before conversions

## 📊 Business Impact

### Advantages of USD Default
1. **International Appeal**: Attracts global users who prefer USD
2. **Simplified Pricing**: Easier to set consistent pricing across regions
3. **Reduced Complexity**: Single base currency for calculations
4. **Market Standard**: Aligns with international e-commerce practices

### Maintained Flexibility
1. **Local Currency Support**: SDG, EGP, and others still fully supported
2. **User Choice**: Complete freedom to choose preferred currency
3. **Regional Adaptation**: Can still cater to local market preferences
4. **Gradual Migration**: Users can transition at their own pace

## 🔍 Testing Checklist

### Verify USD Default Setup
- [ ] New user registration defaults to USD
- [ ] USD appears first in currency selectors
- [ ] USD wallet created automatically
- [ ] Client settings show USD as primary
- [ ] Exchange rates use USD as base

### Test User Preference Changes
- [ ] Users can change preferred currency
- [ ] Changes apply immediately
- [ ] Wallet operations use new preference
- [ ] Display currency updates correctly
- [ ] Conversion settings work properly

### Validate Existing User Experience
- [ ] Existing SDG/EGP users unaffected
- [ ] Historical transactions preserved
- [ ] Balances remain accurate
- [ ] Can still operate in original currencies
- [ ] Conversion between currencies works

## 🆘 Troubleshooting

### Common Issues

#### USD Not Showing as Default
```sql
-- Check client settings
SELECT * FROM client_currency_settings;

-- Update if needed
UPDATE client_currency_settings 
SET primary_currency_code = 'USD', 
    enabled_currencies = ARRAY['USD', 'SDG', 'EGP'];
```

#### User Preferences Not Updating
```sql
-- Check user preferences table
SELECT * FROM user_preferences WHERE user_id = 'user-id';

-- Create if missing
INSERT INTO user_preferences (user_id, preferred_currency_code, display_currency_code)
VALUES ('user-id', 'USD', 'USD');
```

#### Missing USD Wallets
```sql
-- Create USD wallets for users who don't have them
INSERT INTO user_wallets (user_id, currency_code, balance)
SELECT u.id, 'USD', 0.00
FROM auth.users u
WHERE NOT EXISTS (
  SELECT 1 FROM user_wallets uw 
  WHERE uw.user_id = u.id AND uw.currency_code = 'USD'
);
```

## 📈 Future Enhancements

### Planned Features
1. **Smart Currency Detection**: Auto-detect user's region and suggest appropriate currency
2. **Currency Conversion Rates**: Real-time rate updates from external APIs
3. **Multi-Currency Pricing**: Display prices in multiple currencies simultaneously
4. **Regional Defaults**: Different default currencies based on user location
5. **Currency Analytics**: Track user currency preferences and usage patterns

### Configuration Options
1. **Per-Client Defaults**: Different default currencies for different clients
2. **Regional Settings**: Automatic currency selection based on IP/location
3. **A/B Testing**: Test different default currencies for optimization
4. **Seasonal Adjustments**: Temporary currency promotions or preferences

## 📞 Support

For questions or issues with the USD default setup:

1. **Check the troubleshooting section** above
2. **Review the integration guide** for detailed setup instructions
3. **Test in development environment** before applying to production
4. **Contact the development team** with specific error messages

The USD default configuration provides a solid foundation for international growth while maintaining the flexibility that makes Al-Raya Store unique.
