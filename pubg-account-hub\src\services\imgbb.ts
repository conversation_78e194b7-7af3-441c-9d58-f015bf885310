const IMGBB_API_KEY = import.meta.env.VITE_IMGBB_API_KEY;

// Maximum file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;
// Allowed file types
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

export async function uploadImage(file: File): Promise<string> {
  try {
    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      throw new Error('حجم الملف كبير جدًا. الحد الأقصى هو 5 ميجابايت');
    }
    
    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      throw new Error('نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WEBP');
    }
    
    console.log('Starting image upload to ImgBB:', { 
      fileName: file.name, 
      fileSize: `${(file.size / 1024).toFixed(2)} KB`,
      fileType: file.type 
    });

    // Create FormData and append the file
    const formData = new FormData();
    formData.append('key', IMGBB_API_KEY);
    formData.append('image', file);
    
    // Request full-size image links, not thumbnails
    formData.append('expiration', '0'); // Never expire
    formData.append('format', 'json');

    console.log('Sending request to ImgBB API...');
    const response = await fetch('https://api.imgbb.com/1/upload', {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('ImgBB API error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error('فشل في رفع الصورة');
    }

    const data = await response.json();
    console.log('ImgBB upload successful:', {
      url: data.data?.url,
      directUrl: data.data?.image?.url,
      displayUrl: data.data?.display_url,
      size: data.data?.size,
      time: data.data?.time
    });

    if (data.success) {
      // Use the direct image URL, not the thumbnail
      // data.data.url is the page URL, data.data.image.url is the direct image URL
      return data.data.image.url;
    } else {
      console.error('ImgBB reported failure:', data);
      throw new Error('فشل في رفع الصورة');
    }
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error instanceof Error ? error : new Error('فشل في رفع الصورة');
  }
} 