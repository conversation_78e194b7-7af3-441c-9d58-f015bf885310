import * as React from "react"

// Multiple breakpoints for different device sizes
export const BREAKPOINTS = {
  xs: 480,  // Extra small devices
  sm: 640,  // Small devices
  md: 768,  // Medium devices
  lg: 1024, // Large devices
  xl: 1280  // Extra large devices
}

/**
 * Custom hook for media queries with SSR support and proper cleanup
 */
export function useMediaQuery(query: string) {
  // Initialize to false for SSR, will update on client
  const [matches, setMatches] = React.useState(false);
  
  React.useEffect(() => {
    // This will only run on the client
    const media = window.matchMedia(query);
    
    // Set initial value after mounting
    setMatches(media.matches);
    
    // Define listener function
    const listener = () => setMatches(media.matches);
    
    // Add listener
    media.addEventListener("change", listener);
    
    // Clean up
    return () => media.removeEventListener("change", listener);
  }, [query]);
  
  return matches;
}

/**
 * Hook to debounce any value changes
 */
export function useDebounce<T>(value: T, delay: number = 200): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);
  
  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

/**
 * Convenience hook for checking if viewport is mobile
 */
export function useIsMobile(debounceMs: number = 100) {
  const rawIsMobile = useMediaQuery(`(max-width: ${BREAKPOINTS.md - 1}px)`);
  return useDebounce(rawIsMobile, debounceMs);
}

/**
 * Convenience hook for checking if viewport is below a specific breakpoint
 */
export function useBreakpoint(breakpoint: keyof typeof BREAKPOINTS, debounceMs: number = 100) {
  const rawIsBelow = useMediaQuery(`(max-width: ${BREAKPOINTS[breakpoint] - 1}px)`);
  return useDebounce(rawIsBelow, debounceMs);
}

/**
 * Hook to provide all responsive breakpoints at once
 */
export function useResponsive(debounceMs: number = 100) {
  const isMobile = useMediaQuery(`(max-width: ${BREAKPOINTS.md - 1}px)`);
  const isTablet = useMediaQuery(`(min-width: ${BREAKPOINTS.md}px) and (max-width: ${BREAKPOINTS.lg - 1}px)`);
  const isDesktop = useMediaQuery(`(min-width: ${BREAKPOINTS.lg}px)`);
  
  const debouncedValues = {
    isMobile: useDebounce(isMobile, debounceMs),
    isTablet: useDebounce(isTablet, debounceMs),
    isDesktop: useDebounce(isDesktop, debounceMs),
    // Export breakpoint checks for convenience
    breakpoints: {
      isXs: useDebounce(useMediaQuery(`(max-width: ${BREAKPOINTS.xs - 1}px)`), debounceMs),
      isSm: useDebounce(useMediaQuery(`(max-width: ${BREAKPOINTS.sm - 1}px)`), debounceMs),
      isMd: useDebounce(useMediaQuery(`(max-width: ${BREAKPOINTS.md - 1}px)`), debounceMs),
      isLg: useDebounce(useMediaQuery(`(max-width: ${BREAKPOINTS.lg - 1}px)`), debounceMs),
      isXl: useDebounce(useMediaQuery(`(max-width: ${BREAKPOINTS.xl - 1}px)`), debounceMs),
    }
  };
  
  return debouncedValues;
} 