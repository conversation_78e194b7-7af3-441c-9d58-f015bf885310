"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Key,Package,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"text\",\n        placeholder: \"\",\n        required: false\n    });\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n    };\n    const handleSave = async ()=>{\n        var _formData_name, _formData_category;\n        setIsLoading(true);\n        // Basic validation\n        if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n            alert(\"يرجى إدخال اسم المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!((_formData_category = formData.category) === null || _formData_category === void 0 ? void 0 : _formData_category.trim())) {\n            alert(\"يرجى إدخال فئة المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!formData.packages || formData.packages.length === 0) {\n            alert(\"يرجى إضافة حزمة واحدة على الأقل\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const productData = {\n                name: formData.name,\n                description: formData.description,\n                category: formData.category,\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: formData.fields,\n                packages: formData.packages,\n                features: formData.features,\n                tags: formData.tags,\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.updateProduct)(product.id, productData);\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.createProduct)(productData);\n            }\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            alert(\"حدث خطأ أثناء حفظ المنتج\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"text\",\n            placeholder: \"\",\n            required: false\n        });\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            discount: pkg.discount || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800/90 backdrop-blur-md rounded-xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 md:p-6 border-b border-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg md:text-2xl font-bold text-white\",\n                            children: isEditing ? \"تعديل المنتج\" : \"إضافة منتج جديد\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onCancel,\n                            className: \"text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 md:w-6 md:h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 md:p-6 space-y-4 md:space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2 text-white\",\n                                        children: \"اسم المنتج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name || \"\",\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                        placeholder: \"أدخل اسم المنتج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium mb-2 text-white\",\n                                        children: \"الفئة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.category || \"\",\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    category: e.target.value\n                                                })),\n                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2 text-white\",\n                                children: \"الوصف\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: formData.description || \"\",\n                                onChange: (e)=>setFormData((prev)=>({\n                                            ...prev,\n                                            description: e.target.value\n                                        })),\n                                rows: 3,\n                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                placeholder: \"وصف المنتج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2 text-white\",\n                                children: \"العلامات (مفصولة بفاصلة)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                onChange: (e)=>setFormData((prev)=>({\n                                            ...prev,\n                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                        })),\n                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                placeholder: \"شائع, مميز, جديد\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2 text-white\",\n                                children: \"صورة الغلاف\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.image || \"\",\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    image: e.target.value\n                                                })),\n                                        className: \"flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                        placeholder: \"رابط الصورة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"رفع\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-6 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 space-x-reverse text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: formData.isFeatured || false,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    isFeatured: e.target.checked\n                                                })),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"منتج مميز\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 space-x-reverse text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    isActive: e.target.checked\n                                                })),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"منتج نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"الحقول المخصصة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addCustomField,\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"إضافة حقل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: field.label,\n                                                        onChange: (e)=>updateCustomField(index, \"label\", e.target.value),\n                                                        placeholder: \"تسمية الحقل\",\n                                                        className: \"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: field.type,\n                                                        onChange: (e)=>updateCustomField(index, \"type\", e.target.value),\n                                                        className: \"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"text\",\n                                                                children: \"نص\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"email\",\n                                                                children: \"بريد إلكتروني\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"number\",\n                                                                children: \"رقم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: field.placeholder,\n                                                        onChange: (e)=>updateCustomField(index, \"placeholder\", e.target.value),\n                                                        placeholder: \"النص التوضيحي\",\n                                                        className: \"bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center space-x-2 space-x-reverse text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: field.required,\n                                                                onChange: (e)=>updateCustomField(index, \"required\", e.target.checked),\n                                                                className: \"rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"مطلوب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeCustomField(index),\n                                                        className: \"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Key_Package_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: isEditing ? \"تحديث المنتج\" : \"إضافة المنتج\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"LE3g/gyLSgQ/gpklPFWvfGJTS+o=\");\n_c = SimpleProductForm;\nvar _c;\n$RefreshReg$(_c, \"SimpleProductForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});