import type { Product, User, Order, BannerSlide, HomepageSection } from "../types"

// TODO: Replace with Supabase data fetching
export const mockProducts: Product[] = [
  {
    id: "1",
    slug: "mobile-legends-diamonds",
    title: "موبايل ليجندز: بانغ بانغ - الماس",
    description: "احصل على الماس في موبايل ليجندز فوراً! خدمة شحن سريعة وآمنة وموثوقة متاحة على مدار الساعة.",
    coverImage: "/logo.jpg",
    category: "MOBA",
    tags: ["شائع", "MOBA", "موبايل"],
    rating: 4.8,
    commentCount: 1250,
    featured: true,
    popular: true,
    packages: [
      {
        id: "1-1",
        name: "86 ماسة",
        price: 1.99,
        originalPrice: 2.49,
        discount: 20,
        image: "/logo.jpg",
        description: "مثالي للمبتدئين",
        hasDigitalCodes: true,
        availableCodesCount: 5,
        digitalCodes: [
          {
            id: "dc1-1",
            key: "ML86-ABCD-1234-EFGH",
            used: false,
            assignedToOrderId: null,
          },
          {
            id: "dc1-2",
            key: "ML86-WXYZ-5678-IJKL",
            used: false,
            assignedToOrderId: null,
          },
          {
            id: "dc1-3",
            key: "ML86-MNOP-9012-QRST",
            used: true,
            assignedToOrderId: "1",
          },
          {
            id: "dc1-4",
            key: "ML86-UVWX-3456-YZZZ",
            used: false,
            assignedToOrderId: null,
          },
          {
            id: "dc1-5",
            key: "ML86-AAAA-7890-BBBB",
            used: false,
            assignedToOrderId: null,
          },
        ],
      },
      {
        id: "1-2",
        name: "172 ماسة",
        price: 3.99,
        originalPrice: 4.99,
        discount: 20,
        image: "/logo.jpg",
        description: "الخيار الأكثر شعبية",
        hasDigitalCodes: true,
        availableCodesCount: 3,
        digitalCodes: [
          {
            id: "dc2-1",
            key: "ML172-CCCC-1111-DDDD",
            used: false,
            assignedToOrderId: null,
          },
          {
            id: "dc2-2",
            key: "ML172-EEEE-2222-FFFF",
            used: false,
            assignedToOrderId: null,
          },
          {
            id: "dc2-3",
            key: "ML172-GGGG-3333-HHHH",
            used: false,
            assignedToOrderId: null,
          },
        ],
      },
    ],
    customFields: [
      {
        id: "user-id",
        label: "معرف المستخدم",
        type: "number",
        required: true,
        placeholder: "أدخل معرف المستخدم في موبايل ليجندز",
      },
      {
        id: "server-id",
        label: "معرف الخادم",
        type: "number",
        required: true,
        placeholder: "أدخل معرف الخادم",
      },
    ],
    dropdowns: [
      {
        id: "server-region",
        label: "منطقة الخادم",
        required: true,
        options: [
          { value: "sea", label: "جنوب شرق آسيا" },
          { value: "na", label: "أمريكا الشمالية" },
          { value: "eu", label: "أوروبا" },
          { value: "sa", label: "أمريكا الجنوبية" },
        ],
      },
    ],
  },
  {
    id: "2",
    slug: "pubg-mobile-uc",
    title: "ببجي موبايل UC",
    description: "اشحن UC في ببجي موبايل فوراً. جميع المناطق مدعومة مع التسليم السريع.",
    coverImage: "/logo.jpg",
    category: "باتل رويال",
    tags: ["شائع", "FPS", "باتل رويال"],
    rating: 4.7,
    commentCount: 890,
    featured: true,
    popular: true,
    packages: [
      {
        id: "2-1",
        name: "60 UC",
        price: 0.99,
        image: "/logo.jpg",
        hasDigitalCodes: false,
      },
      {
        id: "2-2",
        name: "325 UC",
        price: 4.99,
        image: "/logo.jpg",
        hasDigitalCodes: false,
      },
    ],
    customFields: [
      {
        id: "player-id",
        label: "معرف اللاعب",
        type: "number",
        required: true,
        placeholder: "أدخل معرف اللاعب في ببجي موبايل",
      },
    ],
  },
  {
    id: "3",
    slug: "steam-gift-cards",
    title: "بطاقات هدايا Steam",
    description: "بطاقات هدايا Steam رقمية فورية. استخدمها لشراء الألعاب والمحتوى من متجر Steam.",
    coverImage: "/logo.jpg",
    category: "بطاقات هدايا",
    tags: ["بطاقات هدايا", "Steam", "رقمي"],
    rating: 4.9,
    commentCount: 2100,
    featured: true,
    popular: false,
    packages: [
      {
        id: "3-1",
        name: "بطاقة هدية $10",
        price: 10.99,
        image: "/logo.jpg",
        description: "بطاقة هدية Steam بقيمة $10",
        hasDigitalCodes: true,
        availableCodesCount: 0,
        digitalCodes: [
          {
            id: "dc3-1",
            key: "STEAM10-XXXX-YYYY-ZZZZ-AAAA",
            used: true,
            assignedToOrderId: "2",
          },
        ],
      },
      {
        id: "3-2",
        name: "بطاقة هدية $25",
        price: 25.99,
        image: "/logo.jpg",
        description: "بطاقة هدية Steam بقيمة $25",
        hasDigitalCodes: true,
        availableCodesCount: 2,
        digitalCodes: [
          {
            id: "dc3-2",
            key: "STEAM25-BBBB-CCCC-DDDD-EEEE",
            used: false,
            assignedToOrderId: null,
          },
          {
            id: "dc3-3",
            key: "STEAM25-FFFF-GGGG-HHHH-IIII",
            used: false,
            assignedToOrderId: null,
          },
        ],
      },
    ],
  },
  {
    id: "4",
    slug: "genshin-impact-genesis",
    title: "جينشين إمباكت - بلورات التكوين",
    description: "اشحن بلورات التكوين لجينشين إمباكت. جميع الخوادم مدعومة.",
    coverImage: "/logo.jpg",
    category: "RPG",
    tags: ["RPG", "جاتشا", "مغامرة"],
    rating: 4.9,
    commentCount: 2100,
    featured: true,
    popular: false,
    packages: [
      {
        id: "4-1",
        name: "60 بلورة تكوين",
        price: 0.99,
        image: "/logo.jpg",
      },
      {
        id: "4-2",
        name: "300 بلورة تكوين",
        price: 4.99,
        image: "/logo.jpg",
      },
    ],
    customFields: [
      {
        id: "uid",
        label: "UID",
        type: "number",
        required: true,
        placeholder: "أدخل UID الخاص بك في جينشين إمباكت",
      },
    ],
    dropdowns: [
      {
        id: "server",
        label: "الخادم",
        required: true,
        options: [
          { value: "america", label: "أمريكا" },
          { value: "europe", label: "أوروبا" },
          { value: "asia", label: "آسيا" },
          { value: "sar", label: "تايوان، هونغ كونغ، ماكاو" },
        ],
      },
    ],
  },
]

// TODO: Replace with Supabase user management
export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "مدير النظام",
    role: "admin",
    walletBalance: 1000.0,
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "موزع",
    role: "distributor",
    walletBalance: 500.0,
  },
  {
    id: "3",
    email: "<EMAIL>",
    name: "مستخدم عادي",
    role: "user",
    walletBalance: 150.0,
  },
]

// TODO: Replace with Supabase order management
export const mockOrders: Order[] = [
  {
    id: "1",
    userId: "3",
    productId: "1",
    packageId: "1-1",
    amount: 1.99,
    status: "completed",
    createdAt: "2024-01-15T10:30:00Z",
    customData: {
      "user-id": "123456789",
      "server-id": "1234",
      "server-region": "sea",
    },
    digitalCode: {
      key: "ML86-MNOP-9012-QRST",
      revealed: false,
      viewCount: 0,
    },
  },
  {
    id: "2",
    userId: "3",
    productId: "3",
    packageId: "3-1",
    amount: 10.99,
    status: "completed",
    createdAt: "2024-01-14T15:45:00Z",
    digitalCode: {
      key: "STEAM10-XXXX-YYYY-ZZZZ-AAAA",
      revealed: true,
      revealedAt: "2024-01-14T16:00:00Z",
      viewCount: 3,
    },
  },
  {
    id: "3",
    userId: "3",
    productId: "2",
    packageId: "2-1",
    amount: 0.99,
    status: "pending",
    createdAt: "2024-01-13T12:00:00Z",
    customData: {
      "player-id": "987654321",
    },
  },
]

// TODO: Replace with Supabase homepage configuration
export const mockBanners: BannerSlide[] = [
  {
    id: "1",
    title: "موبايل ليجندز - عروض خاصة",
    subtitle: "احصل على الماس فوراً مع خدمة 24/7",
    image: "/logo.jpg",
    linkType: "product",
    linkValue: "mobile-legends-diamonds",
    active: true,
    order: 1,
  },
  {
    id: "2",
    title: "ببجي موبايل UC",
    subtitle: "شحن سريع وآمن لجميع المناطق",
    image: "/logo.jpg",
    linkType: "product",
    linkValue: "pubg-mobile-uc",
    active: true,
    order: 2,
  },
  {
    id: "3",
    title: "بطاقات هدايا Steam",
    subtitle: "بطاقات رقمية بأفضل الأسعار",
    image: "/logo.jpg",
    linkType: "product",
    linkValue: "steam-gift-cards",
    active: true,
    order: 3,
  },
]

// TODO: Replace with Supabase homepage sections
export const mockHomepageSections: HomepageSection[] = [
  {
    id: "1",
    title: "الألعاب الشائعة",
    emoji: "🔥",
    productIds: ["1", "2"],
    order: 1,
    active: true,
  },
  {
    id: "2",
    title: "المنتجات المميزة",
    emoji: "⭐",
    productIds: ["1", "2", "3", "4"],
    order: 2,
    active: true,
  },
  {
    id: "3",
    title: "ألعاب MOBA",
    emoji: "🎮",
    productIds: ["1"],
    order: 3,
    active: true,
  },
  {
    id: "4",
    title: "باتل رويال",
    emoji: "🔫",
    productIds: ["2"],
    order: 4,
    active: true,
  },
  {
    id: "5",
    title: "ألعاب RPG",
    emoji: "⚔️",
    productIds: ["4"],
    order: 5,
    active: true,
  },
]
