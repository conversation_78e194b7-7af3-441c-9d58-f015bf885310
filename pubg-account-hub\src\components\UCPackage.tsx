import React, { useState, useEffect } from "react";
import { useCart } from "@/contexts/CartContext";
import { ShoppingCart, Tag, Check, X, Sparkles, Star, Package } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { motion, AnimatePresence } from "framer-motion";
import { useTelegramUsername } from "@/contexts/ConfigContext";
import { useToast } from "@/components/ui/use-toast";
import { useTranslation } from "react-i18next";

// Extended interface to support new marketing fields
interface ExtendedUCPackage {
  id?: string;
  amount: number;
  priceUSD: number;
  priceLocal?: number | string;
  priceEGP?: number; // Legacy field
  localCurrencySymbol?: string;
  image: string;
  description?: string;
  description_en?: string;
  originalPriceUSD?: number | string;
  originalPriceLocal?: number | string;
  discountUSD?: number | string;
  discountLocal?: number | string;
  // Legacy fields for backward compatibility
  originalPrice?: number;
  discountPercent?: number;
  highlights?: string[];
  category?: string;
  notes?: string[];
  notes_en?: string[];
}

interface UCPackageProps {
  ucPackage: ExtendedUCPackage;
  listView?: boolean;
}

// Custom animated cart button component
const AnimatedCartButton = ({ onClick, isInCart }: { onClick: (e: React.MouseEvent<HTMLButtonElement>) => void, isInCart: boolean }) => {
  const [clicked, setClicked] = useState(false);
  const { t } = useTranslation('common');
  
  // Reset animation after completion
  useEffect(() => {
    if (clicked) {
      const timer = setTimeout(() => {
        setClicked(false);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [clicked]);
  
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!isInCart && !clicked) {
      setClicked(true);
      
      // Add a small physical button press animation
      const button = e.currentTarget;
      button.style.transform = 'scale(0.95)';
      
      setTimeout(() => {
        button.style.transform = '';
      }, 150);
      
      onClick(e);
    }
  };
  
  return (
    <button 
      className={`custom-cart-button relative px-2 sm:px-4 py-1 sm:py-2 w-full h-9 sm:h-12 border-0 rounded-md 
                 ${isInCart ? 'bg-gray-500/80 text-white cursor-default shadow-[0_0_15px_rgba(100,100,100,0.4)]' : 'bg-pubg-orange text-pubg-dark cursor-pointer hover:bg-pubg-orange/90'}
                 transition-all duration-300 overflow-hidden
                 active:scale-95 ${isInCart || clicked ? 'clicked' : ''}`}
      onClick={handleClick}
      disabled={isInCart}
    >
      {/* Shopping cart icon */}
      <ShoppingCart 
        className={`absolute z-20 top-1/2 ${isInCart ? 'left-[50%]' : 'left-[-10%]'} transform -translate-y-1/2 -translate-x-1/2 transition-all cart-icon ${clicked ? 'animate-cart' : ''}`} 
        size={20} 
      />
      
      {/* Package icon */}
      <Package 
        className={`absolute z-30 ${isInCart ? 'top-[40%] left-[112%]' : 'top-[-20%] left-[52%]'} transform -translate-y-1/2 -translate-x-1/2 transition-all box-icon ${clicked ? 'animate-box' : ''}`} 
        size={14} 
      />
      
      {/* "Add to cart" text */}
      <span 
        className={`absolute z-30 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 
                  text-xs sm:text-sm font-bold ${isInCart ? 'opacity-0' : 'opacity-100'} ${clicked ? 'animate-txt1' : ''}`}
      >
        {t('uc_package.add_to_cart')}
      </span>
      
      {/* "Added" text */}
      <span 
        className={`absolute z-30 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 
                  text-xs sm:text-sm font-bold ${isInCart ? 'opacity-100' : 'opacity-0'} ${clicked ? 'animate-txt2' : ''}`}
      >
        {t('uc_package.added_to_cart')}
      </span>
    </button>
  );
};

// Animated cart button specifically for the modal with auto-animation
const ModalAnimatedCartButton = ({ onClick, isInCart }: { onClick: (e: React.MouseEvent<HTMLButtonElement>) => void, isInCart: boolean }) => {
  const [clicked, setClicked] = useState(!isInCart); // Start with clicked=true if not in cart
  const { t } = useTranslation('common');
  
  // Reset animation after completion
  useEffect(() => {
    if (clicked) {
      const timer = setTimeout(() => {
        setClicked(false);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [clicked]);
  
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!isInCart && !clicked) {
      setClicked(true);
      onClick(e);
    }
  };
  
  return (
    <button 
      className={`custom-cart-button relative px-2 sm:px-4 py-1 sm:py-2 w-full h-9 sm:h-12 border-0 rounded-md 
                 ${isInCart ? 'bg-gray-500/80 text-white cursor-default shadow-[0_0_15px_rgba(100,100,100,0.4)]' : 'bg-pubg-orange text-pubg-dark cursor-pointer hover:bg-pubg-orange/90'}
                 transition-all duration-300 overflow-hidden
                 active:scale-95 ${isInCart || clicked ? 'clicked' : ''}`}
      onClick={handleClick}
      disabled={isInCart}
    >
      {/* Shopping cart icon */}
      <ShoppingCart 
        className={`absolute z-20 top-1/2 ${isInCart ? 'left-[50%]' : 'left-[-10%]'} transform -translate-y-1/2 -translate-x-1/2 transition-all cart-icon ${clicked ? 'animate-cart' : ''}`} 
        size={20} 
      />
      
      {/* Package icon */}
      <Package 
        className={`absolute z-30 ${isInCart ? 'top-[40%] left-[112%]' : 'top-[-20%] left-[52%]'} transform -translate-y-1/2 -translate-x-1/2 transition-all box-icon ${clicked ? 'animate-box' : ''}`} 
        size={14} 
      />
      
      {/* "Add to cart" text */}
      <span 
        className={`absolute z-30 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 
                  text-xs sm:text-sm font-bold ${isInCart ? 'opacity-0' : 'opacity-100'} ${clicked ? 'animate-txt1' : ''}`}
      >
        {t('uc_package.add_to_cart')}
      </span>
      
      {/* "Added" text */}
      <span 
        className={`absolute z-30 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 
                  text-xs sm:text-sm font-bold ${isInCart ? 'opacity-100' : 'opacity-0'} ${clicked ? 'animate-txt2' : ''}`}
      >
        {t('uc_package.added_to_cart')}
      </span>
    </button>
  );
};

const UCPackage = ({ ucPackage, listView = false }: UCPackageProps) => {
  const { addItem, items } = useCart();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [showDetails, setShowDetails] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const telegramUsername = useTelegramUsername();
  const { t, i18n } = useTranslation('common');
  
  const isInCart = ucPackage.id ? items.some(item => item.id === ucPackage.id) : false;

  const handleAddToCart = (e?: React.MouseEvent<HTMLButtonElement>) => {
    if (e) {
      e.stopPropagation(); // Prevent modal from opening when clicking add to cart
    }
    
    addItem(ucPackage, 'uc');
    
    toast({
      title: "تمت الإضافة إلى السلة",
      description: (
        <div className="flex flex-col">
          <span>تمت إضافة {ucPackage.amount} UC إلى سلة التسوق</span>
          <a 
            href="/cart" 
            className="text-pubg-orange hover:underline mt-2 text-sm flex items-center"
          >
            <ShoppingCart className="h-3.5 w-3.5 ml-1.5" />
            الذهاب إلى السلة
          </a>
        </div>
      ),
    });
  };

  if (listView) {
    return (
      <>
        <motion.div 
          className={`glass-card rounded-xl overflow-hidden transition-all duration-300 relative ${listView ? 'flex items-center' : 'flex flex-col'}`}
          onClick={() => setShowModal(true)}
          whileHover={{ 
            scale: 1.03, 
            boxShadow: "0 10px 25px -5px rgba(60, 100, 177, 0.2)",
            transition: { duration: 0.3 }
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
        >
          {/* Background glow effect */}
          <div className="absolute inset-0 bg-gradient-to-tr from-pubg-blue/5 to-pubg-orange/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          
          {/* Animated particles on hover */}
          <AnimatePresence>
            {isHovered && (
              <motion.div 
                className="absolute inset-0 pointer-events-none"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-pubg-blue rounded-full animate-float-slow"></div>
                <div className="absolute top-3/4 left-1/2 w-2 h-2 bg-pubg-orange rounded-full animate-float-medium"></div>
                <div className="absolute top-1/3 right-1/4 w-1.5 h-1.5 bg-pubg-green rounded-full animate-float-fast"></div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Discount badge */}
          {ucPackage.discountPercent && ucPackage.discountPercent > 0 && (
            <motion.div 
              className="absolute top-0 right-0 bg-green-500 text-white text-xs font-bold py-1 px-2 rounded-bl-md z-10"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <span className="flex items-center">
                <Sparkles size={12} className="ml-1" />
                {ucPackage.discountPercent}% خصم
              </span>
            </motion.div>
          )}
          
          {/* In Cart badge */}
          {isInCart && (
            <motion.div 
              className="absolute top-0 left-0 bg-gray-500/80 text-white text-xs font-bold py-1 px-2 rounded-br-md z-10"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <span className="flex items-center">
                <Check size={12} className="ml-1" />
                في السلة
              </span>
            </motion.div>
          )}
          
          <div className="w-28 h-28 sm:w-32 sm:h-32 shrink-0 overflow-hidden relative">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60 z-10"></div>
            <motion.img
              src={ucPackage.image}
              alt={`${ucPackage.amount} UC`}
              className="w-full h-full object-cover z-0"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            />
          </div>
          
          <div className="flex-grow mx-4 text-right relative z-20">
            <h3 className="text-lg sm:text-xl font-bold text-white mb-1">{ucPackage.amount} UC</h3>
            <p className="text-sm text-muted-foreground mb-1">
              {ucPackage.category && (
                <span className="inline-flex items-center">
                  <Tag size={12} className="ml-1" />
                  {ucPackage.category}
                </span>
              )}
            </p>
            
            {ucPackage.highlights && ucPackage.highlights.length > 0 && (
              <div className="mb-4">
                {ucPackage.highlights.map((highlight, index) => (
                  <p key={index} className="text-xs text-gray-400 flex items-center mb-1">
                    <Check size={12} className="ml-1 text-green-500" />
                    {t(`uc_package.highlight_items.${highlight.toLowerCase().replace(/ /g, '_')}`, { defaultValue: highlight })}
                  </p>
                ))}
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2 p-4 relative z-20">
            <div className="text-right">
              <div className="flex items-center justify-end space-x-2">
                <span className="text-pubg-orange font-bold text-base sm:text-lg">{ucPackage.priceEGP} ج.م</span>
                {ucPackage.originalPrice && ucPackage.originalPrice > ucPackage.priceEGP && (
                  <span className="text-muted-foreground text-xs line-through">{ucPackage.originalPrice} ج.م</span>
                )}
              </div>
              <span className="text-muted-foreground text-xs">${ucPackage.priceUSD}</span>
            </div>
            
            <div className="mt-6 w-24 sm:w-28">
              <div className="modal-cart-button">
                <AnimatedCartButton onClick={handleAddToCart} isInCart={isInCart} />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Modal for detailed view */}
        <AnimatePresence mode="wait">
          {showModal && (
            <UCPackageDetailModal 
              ucPackage={ucPackage} 
              onClose={() => setShowModal(false)} 
              onAddToCart={handleAddToCart}
              isInCart={isInCart}
              telegramUsername={telegramUsername}
            />
          )}
        </AnimatePresence>
      </>
    );
  }

  return (
    <>
      <motion.div 
        className="glass-card rounded-xl overflow-hidden relative cursor-pointer h-full group perspective-500"
        onClick={() => setShowModal(true)}
        whileHover={{ 
          scale: 1.05, 
          rotateY: 5,
          rotateX: 5,
          boxShadow: "0 15px 30px -10px rgba(60, 100, 177, 0.3)",
          transition: { duration: 0.3 }
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        {/* Background glow effect */}
        <div className="absolute inset-0 bg-gradient-to-tr from-pubg-blue/5 to-pubg-orange/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* Image background with gradient overlay */}
        <div className="aspect-square w-full relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/90 z-10"></div>
          <motion.img
            src={ucPackage.image}
            alt={`${ucPackage.amount} UC`}
            className="w-full h-full object-cover z-0"
            animate={isHovered ? { 
              scale: 1.05, 
              y: [-5, 5, -5], 
              transition: { 
                y: { repeat: Infinity, duration: 3, ease: "easeInOut" },
                scale: { duration: 0.3 }
              } 
            } : {}}
          />
          
          {/* Animated particles on hover */}
          <AnimatePresence>
            {isHovered && (
              <motion.div 
                className="absolute inset-0 pointer-events-none z-20"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-pubg-blue rounded-full animate-float-slow"></div>
                <div className="absolute top-3/4 left-1/2 w-2 h-2 bg-pubg-orange rounded-full animate-float-medium"></div>
                <div className="absolute top-1/3 right-1/4 w-1.5 h-1.5 bg-pubg-green rounded-full animate-float-fast"></div>
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Discount badge - moved to top left from top right */}
          {ucPackage.discountPercent && ucPackage.discountPercent > 0 && (
            <motion.div 
              className="absolute top-2 left-2 bg-green-500 text-white text-xs font-bold py-1 px-2 rounded-md z-20 flex items-center shadow-lg"
              initial={{ opacity: 0, scale: 0.8, rotate: -5 }}
              animate={{ opacity: 1, scale: 1, rotate: 0 }}
              transition={{ delay: 0.2, type: "spring" }}
            >
              <Sparkles size={12} className="ml-1" />
              خصم {ucPackage.discountPercent}%
            </motion.div>
          )}
          
          {/* In Cart badge */}
          {isInCart && (
            <motion.div 
              className="absolute top-2 right-2 bg-gray-500/80 text-white text-xs font-bold py-1 px-2 rounded-md z-20 flex items-center shadow-lg"
              initial={{ opacity: 0, scale: 0.8, rotate: 5 }}
              animate={{ opacity: 1, scale: 1, rotate: 0 }}
              transition={{ delay: 0.2, type: "spring" }}
            >
              <Check size={12} className="ml-1" />
              في السلة
            </motion.div>
          )}
          
          {/* Category tag - moved from top left to bottom left */}
          {ucPackage.category && (
            <motion.div 
              className="absolute bottom-2 left-2 bg-pubg-blue/80 text-white text-xs py-1 px-2 rounded-md flex items-center z-20 shadow-lg"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Tag size={12} className="ml-1" />
              {ucPackage.category}
            </motion.div>
          )}
          
          {/* Featured star for large packages */}
          {ucPackage.amount >= 1000 && (
            <motion.div
              className="absolute bottom-2 right-2 text-yellow-400 z-20"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1, rotate: [0, 20, 0] }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <Star className="drop-shadow-glow" />
            </motion.div>
          )}
        </div>
        
        <div className="p-4 flex flex-col relative z-20">
          <h3 className="text-lg font-bold text-white">{ucPackage.amount} UC</h3>
          
          <div className="mt-auto pt-2 flex justify-between items-end">
            <div className="flex items-start flex-col">
              <div className="flex flex-col">
                <span className="text-pubg-orange font-bold text-lg">
                  {ucPackage.priceEGP} {ucPackage.localCurrencySymbol || "ج.م"}
                </span>
                {ucPackage.originalPrice && ucPackage.originalPrice > ucPackage.priceEGP && (
                  <span className="text-muted-foreground text-xs line-through -mt-1">
                    {ucPackage.originalPrice} {ucPackage.localCurrencySymbol || "ج.م"}
                  </span>
                )}
              </div>
              <span className="text-muted-foreground text-xs">${ucPackage.priceUSD}</span>
            </div>
            
            <div className="mt-6 w-24 sm:w-28">
              <div className="modal-cart-button">
                <AnimatedCartButton onClick={handleAddToCart} isInCart={isInCart} />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
      
      {/* Modal for detailed view */}
      <AnimatePresence>
        {showModal && (
          <UCPackageDetailModal 
            ucPackage={ucPackage} 
            onClose={() => setShowModal(false)} 
            onAddToCart={handleAddToCart}
            isInCart={isInCart}
            telegramUsername={telegramUsername}
          />
        )}
      </AnimatePresence>
    </>
  );
};

interface UCPackageDetailModalProps {
  ucPackage: ExtendedUCPackage;
  onClose: () => void;
  onAddToCart: () => void;
  isInCart: boolean;
  telegramUsername?: string;
}

const UCPackageDetailModal = ({ ucPackage, onClose, onAddToCart, isInCart, telegramUsername }: UCPackageDetailModalProps) => {
  const { t, i18n } = useTranslation('common');
  
  const handleAddToCart = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    onAddToCart();
  };
  
  return (
    <motion.div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm overflow-y-auto"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div 
        className="bg-card rounded-xl overflow-hidden shadow-2xl relative w-full max-w-lg max-h-[90vh] flex flex-col"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        <button 
          className="absolute top-3 right-3 z-50 h-8 w-8 rounded-full bg-black/50 backdrop-blur-sm text-white hover:bg-pubg-orange transition-all hover:scale-110 flex items-center justify-center"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </button>
        
        <div className="relative h-40 sm:h-48 bg-card overflow-hidden">
          <div className="absolute inset-0 bg-[url('/patterns/noise.png')] opacity-10"></div>
          <motion.img
            src={ucPackage.image}
            alt={`${ucPackage.amount} UC`}
            className="w-full h-full object-cover object-center opacity-60"
            initial={{ y: -10, scale: 1.1 }}
            animate={{ y: 0, scale: 1 }}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-card via-transparent to-transparent"></div>
          
          <div className="absolute bottom-4 left-6 right-6">
            <motion.h2 
              className="text-2xl sm:text-3xl font-bold text-white drop-shadow-md"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              {ucPackage.amount.toString()} UC
            </motion.h2>
            
            <motion.div 
              className="flex items-center text-white/80 text-sm font-medium"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Tag size={14} className="ml-1" />
              {ucPackage.category || "رصيد لعبة PUBG Mobile"}
            </motion.div>
          </div>
        </div>
        
        <div className="p-6 flex-grow overflow-y-auto">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-8 mb-6">
            <div className="text-center sm:text-right flex-1">
              <div className="text-sm text-muted-foreground mb-1">{t('uc_package.price')}</div>
              <div className="text-2xl font-bold text-primary">
                {ucPackage.priceLocal || ucPackage.priceEGP} {ucPackage.localCurrencySymbol || "ج.م"}
              </div>
              <div className="text-sm text-muted-foreground">${ucPackage.priceUSD}</div>
              {/* Show original price if available (supporting both new and legacy fields) */}
              {((ucPackage.originalPriceLocal && Number(ucPackage.originalPriceLocal) > Number(ucPackage.priceLocal || 0)) || 
                (ucPackage.originalPrice && Number(ucPackage.originalPrice) > Number(ucPackage.priceEGP || 0))) && (
                <div className="text-sm text-muted-foreground line-through mt-1">
                  {ucPackage.originalPriceLocal || ucPackage.originalPrice} {ucPackage.localCurrencySymbol || "ج.م"}
                </div>
              )}
              {/* Show discount percentage if available */}
              {((ucPackage.discountLocal && Number(ucPackage.discountLocal) > 0) || 
                (ucPackage.discountPercent && Number(ucPackage.discountPercent) > 0)) && (
                <div className="text-xs bg-pubg-orange/90 text-white px-2 py-0.5 rounded-sm inline-block mt-1">
                  {t('uc_package.discount')} {ucPackage.discountLocal || ucPackage.discountPercent}%
                </div>
              )}
            </div>
            
            <div className="w-full sm:w-32 md:w-36">
              <ModalAnimatedCartButton onClick={handleAddToCart} isInCart={isInCart} />
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-white">{t('uc_package.product_description')}</h3>
            <p className="text-muted-foreground text-sm">
              {i18n.language === 'en' && ucPackage.description_en ? 
                ucPackage.description_en : 
                (ucPackage.description || 
                 t('uc_package.product_description', { 
                   amount: ucPackage.amount.toString(),
                   defaultValue: `Purchase ${ucPackage.amount} UC for your PUBG Mobile account at competitive prices with instant delivery. You can use UC to buy exclusive outfits, weapons, and accessories in the game.`
                 })
                )}
            </p>
          </div>
          
          {ucPackage.highlights && ucPackage.highlights.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 text-white">{t('uc_package.highlights')}</h3>
              <ul className="space-y-2">
                {ucPackage.highlights.map((highlight, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 ml-2 mt-0.5 flex-shrink-0" />
                    <span className="text-muted-foreground text-sm">
                      {highlight === 'instant_delivery' ? t('uc_package.highlight_items.instant_delivery') :
                       highlight === 'support' ? t('uc_package.highlight_items.support') :
                       highlight === 'secure_payment' ? t('uc_package.highlight_items.secure_payment') :
                       highlight}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="rounded-lg bg-muted/30 p-4 border border-border">
            <h3 className="text-base font-semibold mb-2 text-white">{t('uc_package.important_notes')}</h3>
            <ul className="space-y-1 text-xs text-muted-foreground">
              {i18n.language === 'en' && ucPackage.notes_en && ucPackage.notes_en.length > 0 ? (
                // Display English custom notes if available and language is English
                ucPackage.notes_en.map((note, index) => (
                  <li key={index} className="flex items-start">
                    <span className="ml-2 text-pubg-orange">•</span>
                    <span>{note}</span>
                  </li>
                ))
              ) : ucPackage.notes && ucPackage.notes.length > 0 ? (
                // Display Arabic custom notes if available
                ucPackage.notes.map((note, index) => (
                  <li key={index} className="flex items-start">
                    <span className="ml-2 text-pubg-orange">•</span>
                    <span>{note}</span>
                  </li>
                ))
              ) : (
                // Fall back to translations if no custom notes
                [
                  "Your UC will be credited after we receive your order and contact you via Telegram.",
                  "Please ensure your game ID is correct when communicating with us.",
                  "Delivery is instant in most cases but may take up to 24 hours during peak times."
                ].map((defaultNote, index) => (
                  <li key={index} className="flex items-start">
                    <span className="ml-2 text-pubg-orange">•</span>
                    <span>{t(`uc_package.notes.${index}`, { defaultValue: defaultNote })}</span>
                  </li>
                ))
              )}
            </ul>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default UCPackage;
