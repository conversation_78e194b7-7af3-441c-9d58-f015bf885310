(()=>{var e={};e.id=497,e.ids=[497],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23866:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>g,serverHooks:()=>_,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>y});var n={};t.r(n),t.d(n,{GET:()=>u,GET_HISTORY:()=>p,POST:()=>i});var s=t(96559),o=t(48088),a=t(37719),c=t(32190);async function i(e){try{let r=await e.json(),t=function(e){let r=[];return e.userId||r.push("User ID is required"),e.fromCurrency||r.push("From currency is required"),e.toCurrency||r.push("To currency is required"),e.fromCurrency===e.toCurrency&&r.push("From and to currencies must be different"),(!e.amount||e.amount<=0)&&r.push("Amount must be positive"),{isValid:0===r.length,errors:r}}(r);if(!t.isValid)return c.NextResponse.json({success:!1,error:"Invalid conversion request",message:t.errors.join(", ")},{status:400});let n=await d(r);return c.NextResponse.json(n,{status:n.success?200:400})}catch(e){return console.error("Error in currency conversion:",e),c.NextResponse.json({success:!1,error:"Failed to convert currency",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(e){try{let{searchParams:r}=new URL(e.url),t=r.get("from"),n=r.get("to"),s=parseFloat(r.get("amount")||"0");if(!t||!n||s<=0)return c.NextResponse.json({success:!1,error:"Invalid parameters",message:"from, to, and amount parameters are required"},{status:400});let o=await l(t,n,s);return c.NextResponse.json({success:!0,preview:o})}catch(e){return console.error("Error getting conversion preview:",e),c.NextResponse.json({success:!1,error:"Failed to get conversion preview",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function l(e,r,t){let{data:n,error:s}=await supabase.rpc("get_exchange_rate",{p_from_currency:e,p_to_currency:r});if(s||!n)throw Error(`Exchange rate not available for ${e} to ${r}`);let o=t*n,a=.01*o;return{originalAmount:t,originalCurrency:e,convertedAmount:o,targetCurrency:r,exchangeRate:n,conversionFee:a,totalReceived:o-a,timestamp:new Date}}async function d(e){try{let r;let{data:t,error:n}=await supabase.rpc("convert_wallet_balance",{p_user_id:e.userId,p_from_currency:e.fromCurrency,p_to_currency:e.toCurrency,p_amount:e.amount});if(n)return console.error("Error in wallet conversion RPC:",n),{success:!1,message:"Conversion failed",error:n.message};if(!t.success)return{success:!1,message:"Conversion failed",error:"Insufficient balance or invalid conversion"};let s={originalAmount:t.original_amount,originalCurrency:t.original_currency,convertedAmount:t.converted_amount,targetCurrency:t.target_currency,exchangeRate:t.exchange_rate,conversionFee:t.conversion_fee,timestamp:new Date},{data:o}=await supabase.from("wallet_transactions").select("*").eq("id",t.transaction_id).single();return o&&(r={id:o.id,userId:o.user_id,walletId:o.wallet_id,type:o.transaction_type,amount:o.amount,currency:o.currency_code,originalAmount:o.original_amount,originalCurrency:o.original_currency_code,exchangeRate:o.exchange_rate,conversionFee:o.conversion_fee,description:o.description,referenceNumber:o.reference_number,status:o.status,date:new Date(o.created_at),processedAt:o.processed_at?new Date(o.processed_at):void 0}),{success:!0,message:"Currency conversion completed successfully",conversion:s,transaction:r}}catch(e){return console.error("Error in performCurrencyConversion:",e),{success:!1,message:"Conversion failed due to internal error",error:e instanceof Error?e.message:"Unknown error"}}}async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("userId"),n=parseInt(r.get("limit")||"50"),s=parseInt(r.get("offset")||"0");if(!t)return c.NextResponse.json({success:!1,error:"User ID is required"},{status:400});let{data:o,error:a}=await supabase.from("wallet_transactions").select(`
        id,
        amount,
        currency_code,
        original_amount,
        original_currency_code,
        exchange_rate,
        conversion_fee,
        description,
        created_at,
        status
      `).eq("user_id",t).eq("transaction_type","currency_conversion").order("created_at",{ascending:!1}).range(s,s+n-1);if(a)return console.error("Error fetching conversion history:",a),c.NextResponse.json({success:!1,error:"Failed to fetch conversion history"},{status:500});let i=o?.map(e=>({id:e.id,originalAmount:e.original_amount,originalCurrency:e.original_currency_code,convertedAmount:e.amount,targetCurrency:e.currency_code,exchangeRate:e.exchange_rate,conversionFee:e.conversion_fee,timestamp:new Date(e.created_at),status:e.status}))||[];return c.NextResponse.json({success:!0,conversions:i,pagination:{limit:n,offset:s,total:i.length}})}catch(e){return console.error("Error getting conversion history:",e),c.NextResponse.json({success:!1,error:"Failed to get conversion history",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/wallet/convert/route",pathname:"/api/wallet/convert",filename:"route",bundlePath:"app/api/wallet/convert/route"},resolvedPagePath:"D:\\VS-projects\\try\\alraya-store\\app\\api\\wallet\\convert\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:m,workUnitAsyncStorage:y,serverHooks:_}=g;function v(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:y})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,580],()=>t(23866));module.exports=n})();