"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  Di<PERSON><PERSON><PERSON>le,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog"
import {
  Package,
  Calendar,
  DollarSign,
  Eye,
  EyeOff,
  Copy,
  CheckCircle,
  Clock,
  Gift,
  Zap,
  Download
} from "lucide-react"
import { Order, DigitalContent } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { formatDate } from "@/lib/utils/dateUtils"

interface WalletOrdersProps {
  orders: Order[]
  isLoading: boolean
  onOrderClick?: (order: Order) => void
}

export function WalletOrders({ orders, isLoading, onOrderClick }: WalletOrdersProps) {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [revealedCodes, setRevealedCodes] = useState<Set<string>>(new Set())

  /**
   * Get status color for order badge
   */
  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'processing':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'cancelled':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30'
    }
  }

  /**
   * Get status label in Arabic
   */
  const getStatusLabel = (status: Order['status']) => {
    switch (status) {
      case 'completed':
        return 'مكتمل'
      case 'pending':
        return 'في الانتظار'
      case 'processing':
        return 'قيد المعالجة'
      case 'cancelled':
        return 'ملغي'
      default:
        return 'غير معروف'
    }
  }

  /**
   * Get status icon
   */
  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-400" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-400" />
      case 'processing':
        return <Package className="h-5 w-5 text-blue-400" />
      case 'cancelled':
        return <Package className="h-5 w-5 text-red-400" />
      default:
        return <Package className="h-5 w-5 text-slate-400" />
    }
  }

  /**
   * Toggle code visibility
   */
  const toggleCodeVisibility = (codeId: string) => {
    setRevealedCodes(prev => {
      const newSet = new Set(prev)
      if (newSet.has(codeId)) {
        newSet.delete(codeId)
      } else {
        newSet.add(codeId)
      }
      return newSet
    })
  }

  /**
   * Copy code to clipboard
   */
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // ## TODO: Show toast notification
      console.log('Code copied to clipboard')
    } catch (error) {
      console.error('Failed to copy code:', error)
    }
  }

  /**
   * Check if order has digital content
   */
  const hasDigitalContent = (order: Order): boolean => {
    return order.digitalContent && order.digitalContent.contents.length > 0
  }

  /**
   * Get digital content for order
   */
  const getDigitalContent = (order: Order): DigitalContent[] => {
    return order.digitalContent?.contents || []
  }

  if (isLoading) {
    return (
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-white">طلباتك</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-slate-700/30 rounded-lg p-4 animate-pulse">
                <div className="h-4 bg-slate-600 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-slate-600 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (orders.length === 0) {
    return (
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-white">طلباتك</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Package className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">لا توجد طلبات</h3>
            <p className="text-slate-400">لم تقم بأي طلبات بعد</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-slate-800/50 border-slate-700/50">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Package className="h-5 w-5" />
          طلباتك ({orders.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {orders.map((order) => (
            <Card 
              key={order.id} 
              className="bg-slate-700/30 border-slate-600/50 hover:bg-slate-700/50 transition-colors cursor-pointer"
              onClick={() => {
                setSelectedOrder(order)
                onOrderClick?.(order)
              }}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-slate-600/50 rounded-lg">
                      {getStatusIcon(order.status)}
                    </div>
                    <div>
                      <h4 className="text-white font-medium">{order.templateName}</h4>
                      <p className="text-slate-400 text-sm">#{order.id}</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(order.status)}>
                    {getStatusLabel(order.status)}
                  </Badge>
                </div>

                <div className="flex items-center justify-between text-sm text-slate-400 mb-3">
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(order.createdAt)}
                  </span>
                  <span className="flex items-center gap-1">
                    <DollarSign className="h-3 w-3" />
                    {formatCurrency(order.pricing.totalPrice, order.pricing.currency)}
                  </span>
                </div>

                {/* Digital Content Indicator */}
                {hasDigitalContent(order) && order.status === 'completed' && (
                  <div className="flex items-center gap-2 text-sm text-green-400 bg-green-500/10 rounded-lg p-2">
                    <Gift className="h-4 w-4" />
                    <span>المحتوى الرقمي جاهز!</span>
                    <Badge className="bg-green-500/20 text-green-400 text-xs">
                      {getDigitalContent(order).length} عنصر
                    </Badge>
                  </div>
                )}

                {/* Processing Indicator */}
                {order.status === 'processing' && (
                  <div className="flex items-center gap-2 text-sm text-blue-400 bg-blue-500/10 rounded-lg p-2">
                    <Clock className="h-4 w-4" />
                    <span>جاري المعالجة...</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Order Details Dialog */}
        {selectedOrder && (
          <Dialog open={!!selectedOrder} onOpenChange={() => setSelectedOrder(null)}>
            <DialogContent className="bg-slate-800 border-slate-700 max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="text-white flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  تفاصيل الطلب #{selectedOrder.id}
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-6">
                {/* Order Info */}
                <div className="bg-slate-700/30 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-2">{selectedOrder.templateName}</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-slate-400">الحالة:</span>
                      <Badge className={`ml-2 ${getStatusColor(selectedOrder.status)}`}>
                        {getStatusLabel(selectedOrder.status)}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-slate-400">التاريخ:</span>
                      <span className="text-white ml-2">{formatDate(selectedOrder.createdAt)}</span>
                    </div>
                    <div>
                      <span className="text-slate-400">المبلغ:</span>
                      <span className="text-white ml-2">
                        {formatCurrency(selectedOrder.pricing.totalPrice, selectedOrder.pricing.currency)}
                      </span>
                    </div>
                    <div>
                      <span className="text-slate-400">الكمية:</span>
                      <span className="text-white ml-2">{selectedOrder.quantity}</span>
                    </div>
                  </div>
                </div>

                {/* Digital Content */}
                {hasDigitalContent(selectedOrder) && selectedOrder.status === 'completed' && (
                  <div className="space-y-4">
                    <h3 className="text-white font-medium flex items-center gap-2">
                      <Gift className="h-5 w-5 text-green-400" />
                      المحتوى الرقمي
                    </h3>
                    
                    {getDigitalContent(selectedOrder).map((content, index) => {
                      const codeId = `${selectedOrder.id}_${index}`
                      const isRevealed = revealedCodes.has(codeId)
                      
                      return (
                        <Card key={content.id} className="bg-slate-700/50 border-slate-600">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="text-white font-medium">{content.title}</h4>
                              <Badge className="bg-purple-500/20 text-purple-400">
                                {content.type === 'game_code' ? 'كود لعبة' :
                                 content.type === 'coupon' ? 'كوبون' :
                                 content.type === 'license' ? 'ترخيص' : 'محتوى رقمي'}
                              </Badge>
                            </div>

                            {/* Code Display */}
                            <div className="bg-slate-800/50 rounded-lg p-3 mb-3">
                              <div className="flex items-center justify-between">
                                <div className="font-mono text-lg">
                                  {isRevealed ? (
                                    <span className="text-yellow-400">{content.content}</span>
                                  ) : (
                                    <span className="text-slate-500">••••••••••••</span>
                                  )}
                                </div>
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => toggleCodeVisibility(codeId)}
                                    className="border-slate-600 text-slate-300"
                                  >
                                    {isRevealed ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                  </Button>
                                  {isRevealed && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => copyToClipboard(content.content)}
                                      className="border-slate-600 text-slate-300"
                                    >
                                      <Copy className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>

                            {/* Instructions */}
                            {content.instructions && (
                              <div className="text-sm text-slate-400 bg-slate-800/30 rounded p-3">
                                <strong>تعليمات الاستخدام:</strong>
                                <p className="mt-1">{content.instructions}</p>
                              </div>
                            )}

                            {/* Expiry Date */}
                            {content.expiryDate && (
                              <div className="text-xs text-slate-500 mt-2">
                                ينتهي في: {formatDate(content.expiryDate)}
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                )}

                {/* Custom Fields */}
                {selectedOrder.customFields && Object.keys(selectedOrder.customFields).length > 0 && (
                  <div className="space-y-2">
                    <h3 className="text-white font-medium">معلومات الطلب</h3>
                    <div className="bg-slate-700/30 rounded-lg p-4">
                      {Object.entries(selectedOrder.customFields).map(([key, value]) => (
                        <div key={key} className="flex justify-between text-sm mb-2 last:mb-0">
                          <span className="text-slate-400">{key}:</span>
                          <span className="text-white">{String(value)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </CardContent>
    </Card>
  )
}
