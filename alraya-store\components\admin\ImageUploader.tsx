"use client"

import React, { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Upload,
  Image as ImageIcon,
  X,
  Link
} from "lucide-react"

interface ImageUploaderProps {
  value: string
  onChange: (value: string) => void
  label?: string
  placeholder?: string
  className?: string
}

export function ImageUploader({
  value,
  onChange,
  label = "الصورة",
  placeholder = "اختر صورة...",
  className = ""
}: ImageUploaderProps) {
  const [activeTab, setActiveTab] = useState<"upload" | "url">("upload")
  const [imageUrl, setImageUrl] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت")
        return
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        alert("يرجى اختيار ملف صورة صالح")
        return
      }

      // Convert to base64 for localStorage
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        onChange(result)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleUrlSubmit = () => {
    if (!imageUrl.trim()) {
      alert("يرجى إدخال رابط الصورة")
      return
    }

    // Basic URL validation
    try {
      new URL(imageUrl)
      onChange(imageUrl.trim())
      setImageUrl("")
    } catch {
      alert("رابط غير صالح")
    }
  }

  const clearImage = () => {
    onChange("")
    setImageUrl("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className={className}>
      <Label className="text-slate-300 mb-2 block">{label} *</Label>
      
      {/* Current Image Preview */}
      {value && (
        <div className="mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center overflow-hidden">
                <img
                  src={value}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <p className="text-white text-sm font-medium">الصورة الحالية</p>
                <p className="text-slate-400 text-xs">
                  {value.startsWith('data:') ? "صورة مرفوعة" : "رابط خارجي"}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={clearImage}
              className="border-red-600 text-red-400 hover:bg-red-600/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Image Selection Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-slate-700/50">
          <TabsTrigger value="upload" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
            <Upload className="h-4 w-4 mr-1" />
            رفع صورة
          </TabsTrigger>
          <TabsTrigger value="url" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
            <Link className="h-4 w-4 mr-1" />
            رابط صورة
          </TabsTrigger>
        </TabsList>

        {/* File Upload */}
        <TabsContent value="upload" className="space-y-3">
          <p className="text-slate-400 text-sm">ارفع صورة من جهازك (أقل من 5 ميجابايت)</p>
          <div className="flex items-center gap-2">
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="bg-slate-600 border-slate-500 text-white file:bg-slate-700 file:text-white file:border-0 file:mr-2"
            />
          </div>
        </TabsContent>

        {/* URL Input */}
        <TabsContent value="url" className="space-y-3">
          <p className="text-slate-400 text-sm">أدخل رابط صورة من الإنترنت</p>
          <div className="flex gap-2">
            <Input
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="https://example.com/image.jpg"
              className="bg-slate-600 border-slate-500 text-white"
            />
            <Button onClick={handleUrlSubmit} className="bg-blue-600 hover:bg-blue-700">
              إضافة
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
