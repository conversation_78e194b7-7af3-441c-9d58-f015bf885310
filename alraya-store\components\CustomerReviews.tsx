"use client"

import { useState } from "react"
import { Star, Chevron<PERSON><PERSON><PERSON>, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function CustomerReviews() {
  const [currentReview, setCurrentReview] = useState(0)

  const reviews = [
    {
      name: "أحمد محمد",
      rating: 5,
      comment: "خدمة ممتازة وشحن سريع جداً. حصلت على UC ببجي خلال دقيقتين فقط!",
      product: "شحن ببجي UC",
    },
    {
      name: "فاطمة العلي",
      rating: 5,
      comment: "أفضل موقع للبطاقات الرقمية. الأسعار ممتازة والدعم الفني رائع.",
      product: "بطاقة آيتونز",
    },
    {
      name: "خالد السعد",
      rating: 5,
      comment: "تعامل راقي ومهني. أنصح الجميع بالتعامل مع رايه شوب.",
      product: "شحن فري فاير",
    },
    {
      name: "نورا أحمد",
      rating: 4,
      comment: "موقع موثوق وسريع. استخدمته عدة مرات ولم أواجه أي مشاكل.",
      product: "بطاقة جوجل بلاي",
    },
    {
      name: "محمد الخالد",
      rating: 5,
      comment: "الشحن فوري والأسعار منافسة. سأستمر في التعامل معهم.",
      product: "اشتراك نتفليكس",
    },
  ]

  const nextReview = () => {
    setCurrentReview((prev) => (prev + 1) % reviews.length)
  }

  const prevReview = () => {
    setCurrentReview((prev) => (prev - 1 + reviews.length) % reviews.length)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`h-5 w-5 ${i < rating ? "text-yellow-400 fill-current" : "text-gray-300"}`} />
    ))
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">آراء العملاء</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">اقرأ تجارب عملائنا الراضين عن خدماتنا المميزة</p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Main Review Display */}
          <div className="bg-slate-50 rounded-2xl p-8 md:p-12 text-center mb-8">
            <div className="flex justify-center mb-4">{renderStars(reviews[currentReview].rating)}</div>

            <blockquote className="text-xl md:text-2xl text-slate-700 mb-6 leading-relaxed">
              "{reviews[currentReview].comment}"
            </blockquote>

            <div className="text-slate-900">
              <p className="font-bold text-lg">{reviews[currentReview].name}</p>
              <p className="text-slate-600">اشترى: {reviews[currentReview].product}</p>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-center items-center gap-4 mb-8">
            <Button variant="outline" size="sm" onClick={prevReview} className="rounded-full p-2">
              <ChevronRight className="h-4 w-4" />
            </Button>

            <div className="flex gap-2">
              {reviews.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentReview(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentReview ? "bg-yellow-400" : "bg-slate-300"
                  }`}
                />
              ))}
            </div>

            <Button variant="outline" size="sm" onClick={nextReview} className="rounded-full p-2">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>

          {/* Review Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {reviews.slice(0, 3).map((review, index) => (
              <div
                key={index}
                className="bg-white border border-slate-200 rounded-xl p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex justify-center mb-3">{renderStars(review.rating)}</div>
                <p className="text-slate-600 text-sm mb-4 leading-relaxed">"{review.comment}"</p>
                <div className="text-center">
                  <p className="font-semibold text-slate-900">{review.name}</p>
                  <p className="text-xs text-slate-500">{review.product}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
