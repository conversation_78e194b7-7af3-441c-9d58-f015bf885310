
import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";

interface HeroProps {
  title: string;
  subtitle: string;
  ctaText: string;
  ctaLink: string;
  secondaryCtaText?: string;
  secondaryCtaLink?: string;
  backgroundImage: string;
}

const Hero = ({
  title,
  subtitle,
  ctaText,
  ctaLink,
  secondaryCtaText,
  secondaryCtaLink,
  backgroundImage,
}: HeroProps) => {
  const isMobile = useIsMobile();
  
  return (
    <div
      className="relative min-h-[80vh] md:min-h-screen flex items-center pt-16"
      style={{
        backgroundImage: `linear-gradient(to bottom, rgba(26, 31, 44, 0.7), rgba(26, 31, 44, 0.9)), url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <div className="container mx-auto px-4 sm:px-6 z-10">
        <div className="max-w-xl md:max-w-2xl lg:max-w-3xl">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6"
          >
            {title}
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-base sm:text-lg md:text-xl text-gray-300 mb-6 md:mb-8"
          >
            {subtitle}
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-wrap gap-3 md:gap-4"
          >
            <Link
              to={ctaLink}
              className="bg-pubg-orange text-pubg-dark font-bold px-6 py-2 md:px-8 md:py-3 text-sm md:text-base rounded-lg hover:bg-pubg-orange/90 transition-all duration-300 hover:translate-y-[-2px] shadow-lg hover:shadow-pubg-orange/30"
            >
              {ctaText}
            </Link>
            
            {secondaryCtaText && secondaryCtaLink && (
              <Link
                to={secondaryCtaLink}
                className="bg-transparent border-2 border-pubg-blue text-pubg-blue font-bold px-6 py-2 md:px-8 md:py-3 text-sm md:text-base rounded-lg hover:bg-pubg-blue/10 transition-all duration-300"
              >
                {secondaryCtaText}
              </Link>
            )}
          </motion.div>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 w-full overflow-hidden">
        <svg
          className="relative block w-full h-10 sm:h-16 md:h-24"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
            fill="#1A1F2C"
          ></path>
        </svg>
      </div>
    </div>
  );
};

export default Hero;
